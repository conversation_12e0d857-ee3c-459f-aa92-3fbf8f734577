// 背景
$bg: rgba(27, 32, 56, 1); // 表单,下拉框
$bgWeak: rgba(255, 255, 255, 0.12);
$bgLightGray: rgba(38, 43, 64, 1); // 日期
$bgShortGray: #d8d8d8;
$bgDark: #262b40; // Button
$bgDarkHover: rgba(0, 0, 0, 0.2);
$bgInput: rgba(10, 13, 26, 0.4); // 表单输入框背景
$bgPickerRangeHover: #023466;
$bgTableHeader: #2e334d;
$bgTableEven: #202335;
$bgTableOdd: #292d45;
$bgTableHover: #3a3f57;
$bgDropDown: #3b3f54;
// 白色
$white: #ffffff;
// 灰色
$gray: #666;
// 警告
$waring: #ff574d;

// 业务主色
$themeNormal: #0d85ff;
$themeHover: #0869cc;
$themeDisabled: #054e99;

// 边框
$borderFocus: #0d85ff; // focus 边框
$borderLightGray: rgba(54, 57, 77, 1); // 表单输入框边框
$borderDarkGray: rgba(10, 13, 26, 1); // 日期组件框线
$borderDark: rgba(255, 255, 255, 0.16); // Button边框

//字体
$themeFontColor: rgba(255, 255, 255, 0.8);
$fontPlaceholder: rgba(255, 255, 255, 0.4);

// 字体
$sizeLargest: 36px;
$sizeLarger: 24px;
$sizeLarge: 20px;
$sizeMedium: 16px;
$sizeNormal: 14px;
$sizeSmall: 12px;

// 高度
$height32: 32px;
.common-table {
  background-color: white;
  margin-top: 10px;
  .enable {
    color: green;
  }
  .unenable {
    color: red;
  }
  .middle-btn {
    margin-bottom: 10px;
    // height: 32px;
    Button {
      margin-right: 10px;
    }
  }

  a {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(60, 110, 240, 1);
    margin-right: 12px;
  }

  .#{$ant-prefix}-pagination {
    a {
      margin-right: 0px;
    }
  }
}
.dark-theme{
  /*table重置*/

.#{$ant-prefix}-table-container {
  border-top: 0 !important;
}

.#{$ant-prefix}-table {
  font-size: $sizeNormal;
  color: $themeFontColor;
  background: transparent;
  border: 0 !important;

  &-filter-trigger {
    font-size: $sizeNormal;
    color: $themeFontColor;

    &.active {
      color: #0098ff;
    }
  }

  &-filter-trigger-container {
    bottom: -1px;
  }

  tr.#{$ant-prefix}-table-expanded-row,
  tr.#{$ant-prefix}-table-expanded-row:hover {
    background: $bgTableHeader;
  }

  &-filter-trigger-container-open,
  &-filter-trigger-container:hover,
  &-thead th.#{$ant-prefix}-table-column-has-sorters:hover .#{$ant-prefix}-table-filter-trigger-container,
  &-thead th.#{$ant-prefix}-table-column-has-sorters:hover .#{$ant-prefix}-table-filter-trigger-container:hover {
    background: $bgTableHeader;
  }

  &-filter-trigger-container-open .#{$ant-prefix}-table-filter-trigger,
  &-filter-trigger:hover {
    color: $themeFontColor;
  }

  &-filter-dropdown-btns {
    border-top: none;
  }

  &-filter-dropdown {
    background: linear-gradient(180deg, #3b3f54 0%, #3b3f54 100%);
  }

  .#{$ant-prefix}-table-header > table {
    border-top: 0 !important;
  }

  &-thead {
    background: $bgTableHeader !important;

    tr {
      th {
        color: $themeFontColor !important;
        background: $bgTableHeader !important;
        border: 0 !important;
        padding: 13px 16px;
      }

      .#{$ant-prefix}-table-column-sort {
        background: $bgDark !important;
      }

      .#{$ant-prefix}-table-column-has-sorters:hover {
        background: $bgDark;
      }
    }
  }

  &-tbody {
    tr {
      &:nth-child(even) > td {
        background: $bgTableEven;
      }

      &:nth-child(odd) > td {
        background: $bgTableOdd;
      }

      td {
        color: $themeFontColor !important;
        padding: 13px 16px !important;
        border-bottom: 1px solid rgba(10, 13, 26, 0.4) !important;
      }

      &.#{$ant-prefix}-table-row:hover {
        td {
          background: #3a3f57 !important;
        }
      }

      td.#{$ant-prefix}-table-column-sort {
        background: $bgDark !important;
      }
    }
  }

  tr > th,
  tr > td,
  .#{$ant-prefix}-table-container,
  .#{$ant-prefix}-table-cell-fix-right-first::after {
    border-right: 0 !important;
    border-left: 0 !important;
  }

  .#{$ant-prefix}-table-cell-scrollbar {
    box-shadow: none;
  }

  tr.#{$ant-prefix}-table-measure-row {
    visibility: collapse;
  }

  tr.#{$ant-prefix}-table-expanded-row {
    .#{$ant-prefix}-table-cell {
      background: #13172e !important;
    }
  }
}

.#{$ant-prefix}-table-wrapper .#{$ant-prefix}-table-tbody > tr.#{$ant-prefix}-table-placeholder:hover > td {
  background: #3a3f57;
}

/* 自定义 Ant Design Table 滚动条样式 */
.#{$ant-prefix}-table-body::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.#{$ant-prefix}-table-body::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 8px;
}

.#{$ant-prefix}-table-body::-webkit-scrollbar-thumb {
  background: $bgTableOdd;
  border-radius: 10px;
}

.#{$ant-prefix}-table-body::-webkit-scrollbar-thumb:hover {
  background: $bgTableOdd;
}

/* Firefox 支持 */
.#{$ant-prefix}-table-body {
  scrollbar-width: thin;
  scrollbar-color: $bgTableOdd $bgTableOdd;
}
.#{$ant-prefix}-table-container table {
  background-color: $bgTableOdd;
  border-right: none;
}
}
