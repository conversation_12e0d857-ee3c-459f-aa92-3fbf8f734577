.map-search-container {
  top: 32px;
  left: 32px;
  position: absolute;
  z-index: 99;
  width: 330px;
  height: 48px;
  background: rgba(255, 255, 255, 1);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(35, 37, 43, 0.1);
  display: flex;
  .type-select {
    width: 150px;
  }

  .#{$ant-prefix}-select {
    height: 48px !important;
    .#{$ant-prefix}-select-selector {
      border: 0px !important;
    }
  }

  .#{$ant-prefix}-divider {
    height: 40px;
    top: 4px;
  }
}
