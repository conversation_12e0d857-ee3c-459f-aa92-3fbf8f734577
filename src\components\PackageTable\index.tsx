import React, { useEffect, useState } from 'react';
import { message, Table } from 'antd';
import './index.scss';
import CustomButton from '../CustomButton';
import {
  pageSizeOptions,
  SearchCondition,
  TableListType,
} from '../../utils/constant';
interface MiddleBtns {
  show: boolean;
  title: string;
  key: string;
  onClick: Function;
}
interface Props {
  tableListData: { list: object[]; totalNumber: number; totalPage: number };
  columns: any[];
  loading: boolean;
  middleBtns?: MiddleBtns[]; // 中间按键
  searchRef?: any;
  searchCondition: SearchCondition;
  onPageChange?: Function;
  rowKey: string;
  expandable?: any; // 配置展开属性
  rowSelection?: object;
  notPage?: boolean;
}
const PackageTable = (props: Props) => {
  const {
    middleBtns,
    loading,
    columns,
    searchRef,
    tableListData,
    searchCondition,
    onPageChange,
    rowKey,
    expandable,
    rowSelection,
    notPage,
  } = props;

  /* 计算搜索框高度，以使Table高度自适应屏幕高度 */
  const tableSize = () => {
    if (searchRef && searchRef.current && searchRef.current.clientHeight) {
      const height = `calc(100vh - ${searchRef.current.clientHeight + 100}px)`;
      return height;
    }
    return '65vh';
  };

  return (
    <div className="package-table">
      <div className="middle-btn">
        {middleBtns &&
          middleBtns.map((item: any) => {
            if (item.show) {
              return (
                <CustomButton
                  key={item.key}
                  title={item.title}
                  onSubmitClick={item.onClick}
                  otherCSSProperties={{ marginRight: '10px' }}
                />
              );
            }
          })}
      </div>
      <Table
        columns={columns}
        loading={loading}
        dataSource={tableListData.list}
        rowKey={(record: any) => record[rowKey]}
        expandable={expandable}
        style={{ overflowX: 'hidden' }}
        bordered
        scroll={{
          y: tableSize(),
        }}
        rowSelection={rowSelection}
        pagination={
          notPage
            ? false
            : {
                position: ['bottomCenter'],
                total: tableListData.totalNumber,
                current: searchCondition.page,
                pageSize: searchCondition.size,
                showQuickJumper: true,
                showSizeChanger: true,
                pageSizeOptions: pageSizeOptions,
                showTotal: (total) =>
                  `共 ${tableListData.totalPage}页,${total} 条记录`,
              }
        }
        onChange={(
          paginationData: any,
          filters: any,
          sorter: any,
          extra: any,
        ) => {
          if (extra.action === 'paginate') {
            const { current, pageSize } = paginationData;
            const newSearchValue = {
              ...searchCondition,
              page: current,
              size: pageSize,
            };
            onPageChange && onPageChange(newSearchValue);
          }
        }}
      />
    </div>
  );
};

export default React.memo(PackageTable);
