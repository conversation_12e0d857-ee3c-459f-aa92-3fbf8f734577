import { FormConfig } from '@/components/CommonForm/formConfig';

export const batteryFormConfig: FormConfig = {
  fields: [
    {
      label: '可接任务电量',
      fieldName: 'missionChargeLimit',
      type: 'inputNumber',
      min: 1,
      max: 100,
      placeholder: '请输入1-100数字',
      validatorRules: [{ required: true, message: '必填' }],
    },
    {
      label: '自动回充电量',
      fieldName: 'autoChargeLimit',
      type: 'inputNumber',
      min: 1,
      max: 100,
      placeholder: '请输入1-100数字',
      validatorRules: [{ required: true, message: '必填' }],
    },
    {
      label: '强制回充电量',
      fieldName: 'forceChargeLimit',
      type: 'inputNumber',
      min: 1,
      max: 100,
      placeholder: '请输入1-100数字',
      validatorRules: [{ required: true, message: '必填' }],
    },
  ],
};
