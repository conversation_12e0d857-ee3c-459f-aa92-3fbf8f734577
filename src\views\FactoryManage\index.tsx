import React, { useState, useEffect } from 'react';
import { CommonTable, CommonForm, TableOperateBtn } from '@/components';
import { searchConfig, tableColumns } from './utils/columns';
import { useTableData } from '@/components/CommonTable/useTableData';
import { FactoryApi } from '@/fetch/business';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/store';
import { useNavigate } from 'react-router-dom';
import { YESNO } from '@/utils/enum';
import { saveSearchValues } from '@/redux/reducer/searchForm';
import { HttpStatusCode } from '@/fetch/core/constant';
import { message } from 'antd';
import { PageType } from '@/utils/EditTitle';
import { TableListType } from '@/utils/constant';
import { Popconfirm } from 'antd';

const VendorManage = () => {
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const fetchApi = new FactoryApi();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const initSearchCondition = {
    searchForm: {
      factoryName: null,
      property: null,
      hardwareTypeId: null,
      enable: { value: YESNO.YES, label: '启用' },
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<any>(() => {
    return historySearchValues.searchValues
      ? historySearchValues.searchValues
      : initSearchCondition;
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [tableData, setTableData] = useState<TableListType>({
    list: [],
    total: 0,
    pages: 0,
  });

  useEffect(() => {
    fetchTableData();
  }, [searchCondition]);

  const middleBtns: any[] = [
    {
      show: true,
      title: '新建',
      onClick: () => handleEdit(PageType.ADD),
    },
  ];
  const formateColumns = () => {
    return tableColumns.map((col) => {
      switch (col.dataIndex) {
        case 'hardwareTypeList':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return (
                <span>
                  {record.hardwareTypeList?.length > 0
                    ? record.hardwareTypeList
                    : '-'}
                </span>
              );
            },
          };
        case 'enable':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              const enableStyle =
                record.enable === YESNO.YES ? 'enable' : 'unenable';
              return <div className={enableStyle}>{record.enableName}</div>;
            },
          };
        case 'operation':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              const { enable, id } = record;
              const enableStyle = enable === YESNO.YES ? 'unenable' : 'enable';
              const enableLabel = enable === YESNO.YES ? '停用' : '启用';
              return (
                <div className="operate">
                  <TableOperateBtn
                    title="编辑"
                    handleClick={() => handleEdit(PageType.EDIT, id)}
                  />
                  <Popconfirm
                    placement="left"
                    title={`请确认${enableLabel}吗？`}
                    onConfirm={() => {
                      handleUpdateEnable(
                        id,
                        enable === YESNO.YES ? YESNO.NO : YESNO.YES,
                      );
                    }}
                    okText="确定"
                    cancelText="取消"
                    overlayStyle={{ maxWidth: 800 }}
                  >
                    <a
                      className={enableStyle}
                      style={{ marginRight: '5px', marginLeft: '5px' }}
                    >
                      {enableLabel}
                    </a>
                  </Popconfirm>
                  <TableOperateBtn
                    title="查看"
                    handleClick={() => handleEdit(PageType.READONLY, id)}
                  />
                </div>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };
  const fetchTableData = async () => {
    try {
      setLoading(true);
      const params = {
        searchForm: {
          ...searchCondition.searchForm,
          enable: searchCondition.searchForm.enable?.value,
          hardwareTypeId: searchCondition.searchForm.hardwareTypeId?.value,
          property: searchCondition.searchForm.property?.value,
        },
        pageNum: searchCondition.pageNum,
        pageSize: searchCondition.pageSize,
      };
      const res: any = await fetchApi.fetchTableList(params);
      if (res.code === HttpStatusCode.Success) {
        setTableData({
          list: res.data.list,
          total: res.data.total,
          pages: res.data.pages,
        });
      }
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (type: PageType, id?: number) => {
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: searchCondition,
      }),
    );
    navigator(
      id
        ? `/app/factoryManage/edit?type=${type}&id=${id}`
        : `/app/factoryManage/edit?type=${type}`,
    );
  };

  const handleUpdateEnable = async (id: number, enable: YESNO) => {
    const res = await fetchApi.updateFactoryStatus({ id, enable });
    if (res.code === HttpStatusCode.Success) {
      message.success(res.message);
      fetchTableData();
    } else {
      message.error(res.message);
    }
  };

  const onSearchClick = (val) => {
    const data = {
      searchForm: val,
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(data);
  };
  return (
    <>
      <CommonForm
        formConfig={searchConfig}
        defaultValue={searchCondition.searchForm}
        layout="inline"
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={() => {
          setSearchCondition({ ...initSearchCondition });
        }}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        columns={formateColumns()}
        loading={loading}
        rowKey={'id'}
        middleBtns={middleBtns}
        searchCondition={searchCondition}
        onPageChange={(value: any) => setSearchCondition(value)}
      />
    </>
  );
};

export default VendorManage;
