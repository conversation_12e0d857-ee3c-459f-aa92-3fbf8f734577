import { RepairOrderResponse } from '@/types/repairOrder';
import { ColumnsType } from 'antd/es/table';

export const repairOrderColumns: ColumnsType<RepairOrderResponse> = [
  {
    title: '序号',
    dataIndex: 'index',
    align: 'center',
    width: 65,
    fixed: 'left',
  },
  {
    title: '维修单号',
    width: 170,
    dataIndex: 'number',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车牌号',
    width: 100,
    dataIndex: 'deviceName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车型名称',
    dataIndex: 'deviceTypeName',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '提报时间',
    dataIndex: 'createTime',
    align: 'center',
    width: 175,
    ellipsis: true,
  },
  {
    title: '报修标题',
    dataIndex: 'title',
    align: 'center',
    width: 140,
    ellipsis: true,
  },
  {
    title: '故障硬件类型',
    dataIndex: 'requireHardwareTypeNames',
    align: 'center',
    width: 130,
    ellipsis: true,
  },
  {
    title: '处理时长(h)',
    dataIndex: 'duration',
    align: 'center',
    width: 135,
    ellipsis: true,
  },
  {
    title: '最后操作时间',
    dataIndex: 'modifyTime',
    align: 'center',
    width: 175,
    ellipsis: true,
  },
  {
    title: '维修单状态',
    dataIndex: 'statusName',
    align: 'center',
    width: 105,
    ellipsis: true,
  },
  {
    title: '联系人erp',
    dataIndex: 'reportErp',
    align: 'center',
    width: 130,
    ellipsis: true,
  },
  {
    title: '服务费用(元)',
    dataIndex: 'cost',
    align: 'center',
    width: 135,
    ellipsis: true,
  },
  {
    title: '最后操作人',
    dataIndex: 'modifyUser',
    align: 'center',
    width: 130,
    ellipsis: true,
  },
  {
    title: '标定检测状态',
    dataIndex: 'checkStatusName',
    align: 'center',
    width: 145,
    ellipsis: true,
  },
  {
    title: '站点名称',
    dataIndex: 'stationName',
    align: 'center',
    width: 170,
    ellipsis: true,
  },
  {
    title: '是否影响运营',
    dataIndex: 'isInfluenceOperationName',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '影响运营时长(h)',
    dataIndex: 'affectOperationHours',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '影响运营天数',
    dataIndex: 'affectOperationDays',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'schedule',
    align: 'center',
    width: 260,
    fixed: 'right',
  },
];
export const hardwareInfoTableColums: any[] = [
  {
    title: '维修硬件名称',
    align: 'center',
    dataIndex: 'oldHardwareModelName',
    width: 20,
    ellipsis: true,
  },
  {
    title: '型号',
    width: 20,
    dataIndex: 'oldHardwareModelModel',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '更换后硬件名称',
    width: 20,
    dataIndex: 'newHardwareModelName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '更换后硬件型号',
    width: 20,
    dataIndex: 'newHardwareModelModel',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '更换后硬件序列号',
    width: 20,
    dataIndex: 'newHardwareModelNumber',
    align: 'center',
    ellipsis: true,
  },
];

export const repairRecordConfig = [
  {
    name: 'hardwareName',
    title: '维修硬件名称',
    contentType: 'select',
    loading: false,
    placeHolder: '请输入硬件名称',
    values: null,
  },
  {
    name: 'type',
    title: '型号',
    contentType: 'input',
    loading: false,
    placeHolder: '系统带入型号',
    values: null,
  },
  {
    name: 'changedHardwareName',
    title: '更换后硬件名称',
    contentType: 'select',
    loading: false,
    placeHolder: '请输入硬件名称',
    values: null,
  },
  {
    name: 'changedType',
    title: '更换后硬件型号',
    contentType: 'input',
    loading: false,
    placeHolder: '系统带入型号',
    values: null,
  },
  {
    name: 'serialNumber',
    title: '更换后硬件序列号',
    contentType: 'input',
    loading: false,
    placeHolder: '请输入设备序列号',
    values: null,
  },
  {
    name: 'price',
    title: '维修单价(元)',
    contentType: 'input',
    loading: false,
    placeHolder: '请填写金额',
    values: null,
  },
];

export const operationLogColumns: any[] = [
  {
    title: '操作',
    dataIndex: 'typeName',
    key: 'typeName',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '内容',
    dataIndex: 'remark',
    key: 'remark',
    align: 'center',
    width: 280,
    // ellipsis: true,
  },
  {
    title: '操作人',
    dataIndex: 'modifyUser',
    key: 'modifyUser',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    key: 'modifyTime',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
];

export const repairOrderConfigData = [
  {
    name: 'number',
    title: '维修单号',
    contentType: 'input',
    placeHolder: '请输入维修单号',
    values: null,
  },
  {
    name: 'vehicleName',
    title: '车牌号',
    contentType: 'input',
    placeHolder: '请输入车牌号',
    values: null,
  },
  {
    name: 'reportErp',
    title: '联系人',
    contentType: 'input',
    placeHolder: '请输入联系人erp',
    values: null,
  },
  {
    name: 'stationName',
    title: '站点名称',
    contentType: 'select',
    placeHolder: '请输入站点名称',
    showSearch: true,
    values: null,
  },
  {
    name: 'reportTime',
    title: '提报时间',
    contentType: 'datepicker',
    placeHolder: '请选择提报时间',
    values: null,
    isShowTime: true,
    xxl: 12,
    xl: 16,
    lg: 24,
    labelCol: { xxl: { span: 4 }, xl: { span: 4 }, lg: { span: 4 } },
  },
  {
    name: 'repairStatus',
    title: '维修单状态',
    contentType: 'multipleSelect',
    placeHolder: '请选择维修单状态',
    values: null,
    showSearch: false,
  },
  {
    name: 'isInfluenceOperation',
    title: '是否影响运营',
    contentType: 'select',
    placeHolder: '请选择',
    values: null,
    showSearch: false,
  },
];

export const isInfluenceOperationOption: any[] = [
  {
    label: '否',
    value: 0,
  },
  {
    label: '是',
    value: 1,
  },
];

export const hardwareEditTableColumns: any[] = [
  {
    title: '维修硬件名称',
    dataIndex: 'oldHardwareModelName',
    align: 'center',
    width: '20%',
    ellipsis: true,
  },
  {
    title: '型号',
    dataIndex: 'oldHardwareModelModel',
    align: 'center',
    width: '20%',
    ellipsis: true,
  },
  {
    title: '更换后硬件名称',
    dataIndex: 'newHardwareModelName',
    align: 'center',
    width: '20%',
    ellipsis: true,
  },
  {
    title: '更换后硬件型号',
    dataIndex: 'newHardwareModelModel',
    align: 'center',
    width: '20%',
    ellipsis: true,
  },
  {
    title: '更换后硬件序列号',
    dataIndex: 'newHardwareModelNumber',
    align: 'center',
    width: '20%',
    ellipsis: true,
  },
];

export const checkResultColumns: any[] = [
  {
    title: '操作人',
    dataIndex: 'modifyUser',
    key: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    key: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'checkOperate',
    key: 'checkOperate',
    align: 'center',
    ellipsis: true,
  },
];
