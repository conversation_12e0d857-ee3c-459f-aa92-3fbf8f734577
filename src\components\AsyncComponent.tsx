import React, { Component } from 'react';
const AsyncComponent = (importComponent: any): any => {
  return class extends Component<any, any> {
    constructor(props: any) {
      super(props);
      this.state = {
        component: null,
      };
    }
    componentDidMount() {
      importComponent().then((cmp: any) => {
        this.setState({ component: cmp.default }); //.default 是模块有default输出接口
      });
    }

    render() {
      const C = this.state.component;
      return C ? <C {...this.props} /> : null;
    }
  };
};
export default AsyncComponent;
