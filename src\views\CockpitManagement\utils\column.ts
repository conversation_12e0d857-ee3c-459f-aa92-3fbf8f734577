import { FormConfig } from '@/components';
import { request } from '@/fetch/core';
import { DropDownType } from '@/utils/constant';
import FetchApi from './fetchApi';
import { dropDownKey } from '@/utils/enum';

export const tableConfigData: any[] = [
  {
    title: '序号',
    dataIndex: 'rowIndex',
    width: 100,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '座席编号',
    dataIndex: 'cockpitNumber',
    width: 150,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '座席名称',
    dataIndex: 'cockpitName',
    width: 180,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '座席类型',
    dataIndex: 'cockpitTypeName',
    width: 180,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '归属远驾团队',
    dataIndex: 'cockpitTeamName',
    width: 200,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作人',
    dataIndex: 'modifyUser',
    width: 120,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    width: 180,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 260,
    align: 'center',
    ellipsis: true,
    fixed: 'right',
  },
];

export const searchConfig: any[] = [
  {
    name: 'cockpitNumber',
    title: '座席编号',
    placeHolder: '请输入座席编号',
    type: DropDownType.INPUT,
  },
  {
    name: 'cockpitType',
    title: '座席类型',
    placeHolder: '请选择',
    type: DropDownType.SELECT,
    dropDownKey: 'COCKPIT_TYPE',
    dropDownListKey: 'cockpitTypeList',
  },
  {
    name: 'cockpitTeamNumber',
    title: '归属远驾团队',
    placeHolder: '请选择',
    type: DropDownType.SELECT,
  },
];

export const editVehicleSearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'stationInfo',
      label: '省市站',
      placeholder: '请选择省市站信息',
      type: 'cascader',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      mapRelation: {
        label: 'name',
        value: 'id',
        children: 'children',
      },
      specialFetch: 'station',
    },
    {
      fieldName: 'vehicle',
      label: '车号',
      placeholder: '请选择车号/可多选',
      type: 'select',
      multiple: true,
    },
  ],
};

export const editVehicleTableColumns: any[] = [
  {
    title: '城市',
    width: 80,
    dataIndex: 'city',
    align: 'center',
    ellipsis: true,
    render: (text: any) => `${text || ''}`,
  },
  {
    title: '站点',
    width: 90,
    dataIndex: 'station',
    align: 'center',
    ellipsis: true,
    render: (text: any) => `${text || ''}`,
  },
  {
    title: '车号',
    width: 60,
    dataIndex: 'vehicle',
    align: 'center',
    ellipsis: true,
    render: (text: any) => `${text || ''}`,
  },
];
export const viewBindedVehicleColumns: any[] = [
  {
    title: '序号',
    width: 20,
    dataIndex: 'order',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '站点名称',
    width: 80,
    dataIndex: 'stationName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车牌号',
    width: 30,
    dataIndex: 'deviceName',
    align: 'center',
    ellipsis: true,
  },
];
