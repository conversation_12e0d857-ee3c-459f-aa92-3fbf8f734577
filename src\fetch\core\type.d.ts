declare interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | string;
  path?: string;
  absoluteURL?: string;
  contentType?:
    | 'application/json'
    | 'application/x-www-form-urlencoded'
    | 'multipart/form-data'
    | 'text/plain';
  urlParams?: {
    [key: string]: string | number;
  };
  body?: {
    [key: string]: any;
  };
  headers?: {
    [key: string]: string;
  };
  timeout?: number;
  useMock?: boolean;
  mockData?: any;
  newGeteway?: boolean;
}

declare interface HTTPFetchActionParams extends RequestOptions {
  nextActionFunc?: AnyFunc;
  actionType: string;
}

declare interface HTTPResponse {
  code: string | number;
  data: any;
  message?: string;
  errorCode?: number;
}
