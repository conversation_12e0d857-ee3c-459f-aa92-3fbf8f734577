// eslint-disable-next-line no-unused-vars
import { CommonSearchFilter } from "src/common/interface";


export enum VehicleStatusType {
  // eslint-disable-next-line no-unused-vars
  ENABLE = '可用',
  // eslint-disable-next-line no-unused-vars
  DISABLE = '不可用',
}
export interface UsingVehicleListType {
  id: number;
  number: string;
  typeName: string;
  stationName: string;
  stationUseCaseName: string;
  pointNumber: number;
  statusName: string;
  statusId: string;
}

export interface ScheduleVehicleListType {
  id: number;
  number: string;
  typeName: string;
}

export interface RepairingVehicleListType {
  id: number;
  number: string;
  typeName: string;
  stationName: string;
  stationUseCaseName: string;
}

export interface VehicleManagerType {
  filter: CommonSearchFilter;
  contentData: {
    usingVehicle: {
      page: number;
      size: number;
      totalPage: number;
      totalNumber: number;
      list: UsingVehicleListType[];
    };
    scheduleVehicle: {
      page: number;
      size: number;
      totalPage: number;
      totalNumber: number;
      list: ScheduleVehicleListType[];
    };
    repairingVehicle: {
      page: number;
      size: number;
      totalPage: number;
      totalNumber: number;
      list: RepairingVehicleListType[];
    }
  }
}

// 车辆编辑
export interface GridListType {
  id: number;
  length: number;
  width: number;
  height: number;
  volume: number;
}

export interface HomeListType {
  id: number;
  name: string;
  isLinked: boolean;
  waitingTime: number;
}

export interface BasicFormType {
  id: number;
  name: string;
  boxTemplateId: string;
  boxTemplateName: string;
  gridList: GridListType[];
}
export interface EditFormTypeInit {
  basicForm: BasicFormType;
  businessForm: {
    statusId: string;
    statusName: string;
    countryId: string;
    countryName: string;
    provinceId: string;
    provinceName: string;
    cityId: string;
    cityName: string;
    stationId: string;
    stationName: string;
    stationUseCaseId: string;
    stationUseCaseName: string;
    businessNumber: string;
    vehicleBusinessType: string;
  };
  linkPoint: {
    homeList: HomeListType[];
    loadList: HomeListType[];
    pickList: HomeListType[];
    unloadList: HomeListType[];
  }
}

export interface PointCheckType {
  label: string;
  value: string;
  isLinked: boolean;
  waitingTime: number;
}


export interface GirdInfoType {
  id: number;
  name: string;
  model: string;
  serialNo: string;
  deviceId: string;
  driverType: string;
  baudRate: number | undefined;
  protocol?: string;
  boxTemplateId: number;
}

export interface StationItemType {
  label: string;
  value: number;
  isLinked: boolean;
  waitingTime: number;
}

export interface StationBasic {
  id: number;
  waitingTime: number;
}

export interface TableListType {
  list: UsingVehicleListType[] | ScheduleVehicleListType[] | RepairingVehicleListType[];
  totalNumber: number;
  totalPage: number;
}

export interface whetherUnbind {
  resultType: string;
  scheduleName: string;
  orderNumber: string;
}

export interface OperateStationParams {
  homeList: StationBasic[];
  loadList: StationBasic[];
  pickList: StationBasic[];
  unloadList: StationBasic[];
  vendingList: StationBasic[];
  straddleList: StationBasic[];
  collectList: StationBasic[];
}

export interface VehicleRepairListType {
  id: number;
  vehicleName: string;
  vehicleTypeName: string;
  requireStatusName: string;
  ownerUseCaseName: string;
  hardwareStatusName: string;
  checkStatusName: string;
  reportErp: string;
  createTime: string;
}
export interface VehicleRepairTableType {
  list: VehicleRepairListType[];
  totalNumber: number;
  totalPage: number;
}