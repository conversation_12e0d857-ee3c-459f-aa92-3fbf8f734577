import React, { useEffect } from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from './redux/store';
import microApp from '@micro-zoe/micro-app';
import App from './App';
import { registerMicroApps, start } from 'qiankun';
import './utils/qiankun';

const rootNode = document.getElementById('app')!;
const root = ReactDOM.createRoot(rootNode);
registerMicroApps(
  [
    {
      name: 'OTAMicroApp',
      entry: process.env.JDX_APP_OTA_URL!,
      container: '#qiankun-root',
      activeRule: '/ota',
    },
  ],
  {
    beforeLoad: (app: any) => {
      console.log('before load %c%s', 'color: green;', app.name);
      return Promise.resolve();
    },
    beforeMount: (app: any) => {
      console.log(' before mount %c%s', 'color: green;', app.name);
      return Promise.resolve();
    },
    afterMount: (app: any) => {
      console.log('after mount %c%s', 'color: green;', app.name);
      return Promise.resolve();
    },
    beforeUnmount: (app: any) => {
      console.log('before unmount %c%s', 'color: green;', app.name);
      return Promise.resolve();
    },
    afterUnmount: (app: any) => {
      console.log('after unmount %c%s', 'color: green;', app.name);
      return Promise.resolve();
    },
  },
);
start();
root.render(
  <Provider store={store}>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </Provider>,
);

microApp.start();
