import { Card, Descriptions, Divider, message, Modal } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import './index.scss';
import {
  DeployPlanSession,
  DeployPlanType,
  VehicleSupplier,
} from '@/utils/enum';
import { CommonForm, FormConfig } from '@jd/x-coreui';
import { HttpStatusCode } from '@/fetch/core/constant';
import { DeployPlan, StationFetchApi } from '@/fetch/business';
import { CreateDeployForm1, CreateDeployForm2 } from './columns';
import dayjs from 'dayjs';
import { formatISODateToYYYYMMDD } from '@/utils/utils';

const CreateDeployPlan = ({
  showModal,
  planType,
  closeModal,
}: {
  showModal: boolean;
  planType: DeployPlanType;
  closeModal: any;
}) => {
  const requestApi = new StationFetchApi();
  const fetchApi = new DeployPlan();
  const [erpList, setErpList] = useState<any[]>([]);
  const [supplier, setSupplier] = useState<VehicleSupplier>(VehicleSupplier.JD);
  const [btnLoading, setBtnLoading] = useState<boolean>(false);
  const formRef1 = useRef<any>(null);
  const formRef2 = useRef<any>(null);
  const ModalTitleMap = new Map([
    [DeployPlanType.MAP_SUPPLEMENT_PLAN, '创建补图计划'],
    [DeployPlanType.VEHICLE_ALLOCATION_PLAN, '创建调车计划'],
    [DeployPlanType.STATION_DEPLOYMENT_PLAN, '创建部署计划'],
  ]);

  useEffect(() => {
    getUserInfoList();
  }, []);
  const getUserInfoList = async () => {
    const res = await requestApi.getUserInfoList();
    if (res.code === HttpStatusCode.Success && res.data) {
      setErpList(
        res.data?.map((v) => ({
          label: v.erp,
          value: v.erp,
        })),
      );
      return res.data;
    }
  };

  const formatCreateDeployForm1 = (): FormConfig => {
    return {
      fields: CreateDeployForm1.fields.map((v) => {
        switch (v.fieldName) {
          case 'stationBaseId':
            return {
              ...v,
              hidden: planType === DeployPlanType.STATION_DEPLOYMENT_PLAN,
            };
          case 'vehicleInfo':
            return {
              ...v,
              hidden: planType === DeployPlanType.MAP_SUPPLEMENT_PLAN,
            };
          default:
            return v;
        }
      }),
      linkRules: CreateDeployForm1.linkRules,
    };
  };
  const formatCreateDeployForm2 = (): FormConfig => {
    const fieldHandlers = {
      [DeployPlanType.STATION_DEPLOYMENT_PLAN]: (v) => handleCommonFields(v),
      [DeployPlanType.MAP_SUPPLEMENT_PLAN]: (v) => {
        if (
          [
            DeployPlanSession.MAP_COLLECTION,
            DeployPlanSession.ROUTE_TEST_RUN,
            DeployPlanSession.MAP_PRODUCTION,
            DeployPlanSession.ROUTE_CONFIRMATION,
          ].includes(v.fieldName.split('/')[0]) ||
          v.fieldName === 'mapVehicleSource'
        ) {
          return handleCommonFields(v);
        }
        return { ...v, hidden: true };
      },
      [DeployPlanType.VEHICLE_ALLOCATION_PLAN]: (v) => {
        if (
          [
            DeployPlanSession.VEHICLE_CONFIRMATION,
            DeployPlanSession.VEHICLE_ALLOCATION,
            DeployPlanSession.VEHICLE_INSURANCE,
          ].includes(v.fieldName.split('/')[0]) ||
          v.fieldName === 'mapVehicleSource'
        ) {
          return handleCommonFields(v);
        }
        return { ...v, hidden: true };
      },
    };
    return {
      fields: CreateDeployForm2.fields.map((v: any) =>
        fieldHandlers[planType](v),
      ),
      linkRules: CreateDeployForm2.linkRules,
    };
  };
  const handleCommonFields = (v) => {
    if (v.fieldName?.includes('/appointorErp')) {
      return { ...v, options: erpList };
    }
    if (v.fieldName?.includes('/expectedDate')) {
      return {
        ...v,
        disabledDate: (current) => {
          const today = dayjs().startOf('day');
          return current < today;
        },
      };
    }
    return v;
  };
  const createSupplementaryPlan = async (deploymentPlanInfo) => {
    try {
      setBtnLoading(true);
      const res = await fetchApi.createSupplementaryPlan({
        ...deploymentPlanInfo,
        planType: planType,
      });
      if (res.code === HttpStatusCode.Success) {
        message.success('创建成功');
        closeModal();
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    } finally {
      setBtnLoading(false);
    }
  };

  const createPlan = async () => {
    const [vals1, vals2] = await Promise.all([
      formRef1.current.validateFields(),
      formRef2.current.validateFields(),
    ]);

    const list = Object.entries(vals2).reduce((acc, [key, value]) => {
      const [stageType, field] = key.split('/');
      if (field) {
        acc[stageType] = {
          ...acc[stageType],
          stageType,
          [field]:
            field === 'expectedDate' ? formatISODateToYYYYMMDD(value) : value,
        };
      }
      return acc;
    }, {});

    const deploymentPlanInfo = {
      ...vals1,
      mapVehicleSource: vals2.mapVehicleSource,
      stageInfoList: Object.values(list),
    };

    planType === DeployPlanType.STATION_DEPLOYMENT_PLAN
      ? closeModal(deploymentPlanInfo, (loading) => setBtnLoading(loading))
      : createSupplementaryPlan(deploymentPlanInfo);
  };
  return (
    <>
      {showModal && (
        <Modal
          title={
            <h3 style={{ textAlign: 'center' }}>
              {ModalTitleMap.get(planType)}
            </h3>
          }
          width={800}
          open={showModal}
          maskClosable={false}
          confirmLoading={btnLoading}
          onCancel={() => {
            closeModal && closeModal();
          }}
          onOk={() => createPlan()}
        >
          <div className="create-plan-modal">
            <Divider orientation="left" orientationMargin="0">
              部署需求
            </Divider>
            <CommonForm
              colon={false}
              className="createDeployForm1"
              formConfig={formatCreateDeployForm1()}
              defaultValue={{ fetchData: true }}
              onValueChange={(v) => {
                v.supplier && setSupplier(v.supplier);
              }}
              getFormInstance={(ref) => (formRef1.current = ref)}
            />
            <Divider orientation="left" orientationMargin="0">
              部署环节
            </Divider>
            <CommonForm
              className="createDeployForm2"
              colon={false}
              formConfig={formatCreateDeployForm2()}
              defaultValue={{
                supplier: supplier,
              }}
              getFormInstance={(ref) => (formRef2.current = ref)}
            />
          </div>
        </Modal>
      )}
    </>
  );
};

export default React.memo(CreateDeployPlan);
