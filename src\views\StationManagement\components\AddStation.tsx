import React, { useEffect, useRef, useState } from 'react';
import EditModule from '@/components/EditModule';
import CommonBreadCrumb from '@/components/CommonBreadCrumb';
import { Divider, Form, message, Modal } from 'antd';
import CommonEditBtns, { EditBtnType } from '@/components/CommonEditBtns';
import './addStation.scss';
import { StationFetchApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { useNavigate } from 'react-router-dom';
import StationBasicInfo from './StationDetail/components/StationBasicInfo';
import BusinessInfo from './StationDetail/components/BusinessInfo';
import { DeployPlanType, ProductType } from '@/utils/enum';
import CreateDeployPlan from '@/views/DeployPlanManage/components/CreateDeployPlan';

const AddStation = () => {
  const requestApi = new StationFetchApi();
  const navigator = useNavigate();
  const [form] = Form.useForm();
  const [showBusinessInfo, setShowBusinessInfo] = useState(true);
  const [stationType, setStationType] = useState<
    ProductType | null | undefined
  >();
  const [locationInfo, setLocationInfo] = useState<any>({
    cityId: null,
    cityName: null,
    address: null,
    initPosition: null,
  });
  const [dropDownMap, setDropDownMap] = useState<any>({
    stationUseCase: [],
    productTypeList: [],
    stationTypeList: [],
    userList: [],
  });
  const [showCreatePlanModal, setShowCreatePlanModal] =
    useState<boolean>(false);
  const stationValsRef = useRef<any>(null);
  useEffect(() => {
    getAllDropDown();
  }, []);

  const getCommonDropDownList = async () => {
    const res = await requestApi.getCommonDropDown([
      'STATION_USE_CASE',
      'STATION_TYPE',
      'PRODUCT_TYPE',
    ]);
    if (res && res.code === HttpStatusCode.Success) {
      return res.data;
    }
  };

  const getUserInfoList = async () => {
    const res = await requestApi.getUserInfoList();
    if (res.code === HttpStatusCode.Success && res.data) {
      return res.data;
    }
  };

  const getAllDropDown = () => {
    Promise.all([getCommonDropDownList(), getUserInfoList()]).then((res) => {
      setDropDownMap({
        stationUseCase: res[0].stationUseCaseList?.map((item: any) => {
          return {
            label: item.name,
            value: item.code,
          };
        }),
        productTypeList: res[0].productTypeList?.map((item: any) => {
          return {
            label: item.name,
            value: item.code,
          };
        }),
        stationTypeList: res[0].stationTypeList?.map((item: any) => {
          return {
            label: item.name,
            value: item.code,
          };
        }),
        userList: res[1]?.map((item: any) => {
          return {
            label: `${item.realName}/${item.erp}`,
            value: `${item.userName}/${item.phone}`,
          };
        }),
      });
    });
  };
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (values && locationInfo) {
        stationValsRef.current = {
          cityId: locationInfo.cityId,
          address: locationInfo.address,
          contact: values.contact,
          minDropOffTime: values.minDropOffTime,
          name: values.name,
          number: values.number,
          productType: values.productType,
          type: values.type,
          useCase: values.useCase,
          personName: values.personName?.value?.split('/')?.[0],
          lat: locationInfo.initPosition.lat,
          lon: locationInfo.initPosition.lon,
          missionChargeLimit: values.missionChargeLimit,
          autoChargeLimit: values.autoChargeLimit,
          forceChargeLimit: values.forceChargeLimit,
          reminderTime: values.reminderTime,
          chargeType: values.chargeType,
          groupBusinessList: values?.groupBusinessList?.map((item: any) => ({
            code:item.code,
            name:item.name,
            shelfTypeId: item?.shelfTypeList?.map((i) => i.shelfTypeId),
          })),
        };
        if (values.productType !== ProductType.VEHICLE) {
          const res = await requestApi.addStation({
            ...stationValsRef.current,
          });
          if (res.code === HttpStatusCode.Success) {
            message.success('创建成功');
            navigator('/app/stationManagement');
          } else {
            message.error(res.message);
          }
        } else {
          setShowCreatePlanModal(true);
        }
      }
    } catch (errorInfo) {
      console.log('Failed:', errorInfo);
    }
  };

  return (
    <div className="add-station-container">
      <CommonBreadCrumb
        items={[
          {
            title: '站点管理',
            route: '',
          },
          {
            title: '新建站点',
            route: '',
          },
        ]}
      />
      <Form
        form={form}
        wrapperCol={{ span: 19 }}
        labelCol={{ span: 6 }}
        colon={false}
        onFieldsChange={(changedFields) => {
          changedFields.forEach((field) => {
            if (field.name.includes('productType')) {
              setStationType(field.value);
            }
          });
        }}
      >
        <EditModule title="站点信息">
          <StationBasicInfo
            dropDownMap={dropDownMap}
            locationInfo={locationInfo}
            setLocationInfo={setLocationInfo}
            form={form}
            setShowBusinessInfo={setShowBusinessInfo}
          />

          {showBusinessInfo && (
            <BusinessInfo form={form} stationType={stationType} />
          )}
        </EditModule>
      </Form>
      <CommonEditBtns
        btnList={[
          {
            title: '取消',
            type: EditBtnType.Cancel,
            onClick: () => {
              navigator('/app/stationManagement');
            },
          },
          {
            title: '确定',
            type: EditBtnType.Confirm,
            onClick: () => {
              handleSubmit();
            },
          },
        ]}
      />
      {showCreatePlanModal && (
        <CreateDeployPlan
          showModal={showCreatePlanModal}
          planType={DeployPlanType.STATION_DEPLOYMENT_PLAN}
          closeModal={async (val, changeBtnLoading) => {
            if (!val) {
              setShowCreatePlanModal(false);
              return;
            }
            const values = {
              ...stationValsRef.current,
              deploymentPlanInfo: {
                ...val,
              },
            };
            changeBtnLoading && changeBtnLoading(true);
            const res = await requestApi.addStation({
              ...values,
            });
            if (res.code === HttpStatusCode.Success) {
              message.success('创建成功');
              setShowCreatePlanModal(false);
              navigator('/app/stationManagement');
            } else {
              message.error(res.message);
            }
            changeBtnLoading && changeBtnLoading(false);
          }}
        />
      )}
    </div>
  );
};

export default React.memo(AddStation);
