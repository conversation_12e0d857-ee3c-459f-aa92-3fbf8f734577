(window as any)._QIANKUN_SDK_ = (window as any)._QIANKUN_SDK || {
  event: (() => {
    class Emitter {
      map: any = {};
      listenerConfigsForEvent(event: string) {
        let listeners = this.map[event];
        if (!listeners) {
          listeners = [];
          this.map[event] = listeners;
        }
        return listeners;
      }
      on(event: string, listener: Function) {
        const listeners = this.listenerConfigsForEvent(event);
        if (listeners.indexOf(listener) === -1) {
          listeners.push({
            listener,
          });
        }
      }
      remove(event: string, listener: Function) {
        const listeners = this.map[event];
        let findIndex = -1;
        const found = listeners.some((item: any, index: number) => {
          if (item.listener === listener) {
            findIndex = index;
            return true;
          }
          return false;
        });
        if (found) {
          listeners.splice(findIndex, 1);
        }
      }
      emit(event: string | number, args: any) {
        if (this.map[event]) {
          this.map[event].forEach((cb: any) => {
            cb.listener(args);
          });
        }
      }
    }
    return new Emitter();
  })(),
};
