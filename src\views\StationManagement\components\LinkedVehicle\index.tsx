import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { DEFAULT_PAGE, WorkMode, WorkModeText } from '@/utils/constant';
import { getLinkedVehicleColumns } from '../../utils/column';
import { CommonApi, StationFetchApi } from '@/fetch/business';
import {
  CommonForm,
  CommonTable,
  FormConfig,
  TableOperateBtn,
  showModal,
} from '@/components';
import { useTableData } from '@/components/CommonTable/useTableData';
import { RootState } from '@/redux/store';
import { saveSearchValues } from '@/redux/reducer/searchForm';
import { Button, Modal, Select, message } from 'antd';
import { HttpStatusCode } from '@/fetch/core/constant';
import { StationVehicleRequest, StationVehicleResponse } from '@/types';
import { useCommonDropDown } from '@/utils/hooks';
import { formatOptions, isEmpty } from '@/utils/utils';
import { handleVehicleManage } from '@/redux/reducer/vehicleManage';
import { AnyFunc } from '@/global';
import { isEqual } from 'lodash';
import { ProductType } from '@/utils/enum';
import { changeDeviceWordMode } from '@/fetch/business/integrate';
const fetchApi = new StationFetchApi();
const commonApi = new CommonApi();
const LinkedVehicle = (props: {
  stationBaseId: string;
  tableKey: string;
  setTableKey: AnyFunc;
  stationType: ProductType;
  updateDataCb: AnyFunc;
}) => {
  const { stationBaseId, tableKey, setTableKey, stationType } = props;
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const initSearchCondition = {
    searchForm: {
      stationBaseId: Number(stationBaseId),
      deviceName: null,
      serialNo: null,
      businessType: null,
      isRequire: null,
      deviceTypeBaseId: null,
      isVirtual: null,
    },
    ...DEFAULT_PAGE,
  };
  const [selectVehicle, setSelectVehicle] = useState<any>();
  const [searchCondition, setSearchCondition] = useState<StationVehicleRequest>(
    () => {
      return historySearchValues.routeName === location.pathname
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );
  const [workMode, setWorkMode] = useState<
    {
      labal: string;
      value: string;
    }[]
  >([]);
  const { tableData, loading } = useTableData<
    StationVehicleRequest,
    StationVehicleResponse
  >(
    searchCondition,
    stationType === ProductType.INTEGRATE
      ? fetchApi.getWarehouseTable
      : fetchApi.getStationVehicleTable,
    tableKey,
  );
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedRows(selectedRows);
    },
  };
  const [whetherUnlinkRes, setWhetherUnlinkRes] = useState<any>({
    resultType: '',
    scheduleName: '',
    orderNumber: '',
  });
  const [unBundModal, setUnBundModal] = useState<boolean>(false);
  const [cannotUnlinkModal, setCannotUnlinkModal] = useState<boolean>(false);
  const dropDownData = useCommonDropDown([
    'YES_OR_NO',
    'VEHICLE_TYPE',
    'PRODUCT_TYPE',
  ]);
  const [parkStopList, setParkStopList] = useState<
    {
      homePointName: string;
      homePointNo: string;
      type: string;
      typeName: string;
    }[]
  >([]);
  const vehicleSearchConfig: FormConfig = {
    fields: [
      {
        fieldName: 'deviceName',
        label: '车牌号',
        placeholder: '请输入车牌号',
        type: 'input',
      },
      {
        fieldName: 'serialNo',
        label: '车架号',
        placeholder: '请输入车架号',
        type: 'input',
      },
      {
        fieldName: 'deviceTypeBaseId',
        label: '车型',
        placeholder: '请选择车型',
        type: 'select',
        showSearch: true,
        labelInValue: false,
        options: formatOptions(dropDownData.deviceTypeBaseInfoList),
      },
      {
        fieldName: 'isRequire',
        label: '存在未完成维修单',
        placeholder: '请选择是否存在未完成维修单',
        type: 'select',
        labelInValue: false,
        options: formatOptions(dropDownData.yesOrNoList),
      },
      {
        fieldName: 'isVirtual',
        label: '是否为虚拟车',
        placeholder: '请选择是否为虚拟车',
        type: 'select',
        labelInValue: false,
        options: formatOptions(dropDownData.yesOrNoList),
      },
    ],
  };
  const middleBtns: any[] = [
    {
      show: true,
      title: '批量绑定停靠点',
      key: 'stopBatch',
      onClick: () => {
        if (isEmpty(selectedRows)) {
          message.error('请选择车辆！');
          return;
        } else {
          dispatch(handleVehicleManage(selectedRows));
          navigator(
            `/app/stationManagement/detail/bindStopPointBacth?stationBaseId=${stationBaseId}`,
          );
        }
      },
    },
  ];
  const onSearchClick = (val) => {
    const data = {
      searchForm: {
        ...searchCondition.searchForm,
        ...val,
      },
      pageNum: 1,
      pageSize: 10,
    };
    if (isEqual(data, searchCondition)) {
      setTableKey(new Date().getMilliseconds().toString());
    } else {
      setSearchCondition(data);
    }
  };
  const goUnBund = (record: any) => {
    setSelectVehicle(record);
    if (stationType === 'vehicle') {
      whetherUnlink(record);
    } else {
      setUnBundModal(true);
      setWhetherUnlinkRes({
        ...whetherUnlinkRes,
        resultType: 'CAN_UNLINK',
      });
    }
  };
  // 判断车辆是否能够解绑
  const whetherUnlink = async (record: any) => {
    try {
      const res: any = await fetchApi.isVehicleUnlinkable(record.deviceBaseId);
      if (res.code === HttpStatusCode.Success) {
        const response: any = {
          resultType: res.data && res.data.resultType,
          scheduleName: res.data && res.data.scheduleName,
          orderNumber: res.data && res.data.orderNumber,
        };
        setWhetherUnlinkRes(response);
        if (
          res.data.resultType === 'CAN_UNLINK' ||
          res.data.resultType === 'CAN_FORCIBLE_UNLINK'
        ) {
          setUnBundModal(true);
        } else if (res.data.resultType === 'CANNOT_UNLINK') {
          setCannotUnlinkModal(true);
        }
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    } finally {
      //
    }
  };
  // 车辆解绑
  const unBundHandleOk = async () => {
    try {
      const res: any = await fetchApi.unlinkVehicleStation(
        selectVehicle.deviceBaseId,
        props.stationType,
      );
      if (res.code === HttpStatusCode.Success) {
        message.success(res.message);
        props.updateDataCb();
      } else {
        message.error(`${res.message},车端解绑失败!`);
      }
    } catch (e) {
      console.log(e);
    } finally {
      setWhetherUnlinkRes({
        resultType: '',
        scheduleName: '',
        orderNumber: '',
      });
      setSelectVehicle(undefined);
      setUnBundModal(false);
      if (
        tableData &&
        tableData.list.length == 1 &&
        searchCondition.pageNum > 1
      ) {
        searchCondition.pageNum -= 1;
        dispatch(
          saveSearchValues({
            routeName: location.pathname,
            searchValues: {
              searchForm: searchCondition,
            },
          }),
        );
      } else {
        setTableKey(new Date().getMilliseconds().toString());
      }
    }
  };

  const handleCancel = () => {
    setWhetherUnlinkRes({
      resultType: '',
      scheduleName: '',
      orderNumber: '',
    });
    setSelectVehicle(undefined);
  };

  const settingNavigationTask = async (record: any) => {
    try {
      let formInstance: any = null;
      const res = await fetchApi.getParkingPoint(record.stationBaseId);
      if (res.code === HttpStatusCode.Success) {
        const options = res?.data?.map((item: any) => ({
          label: item.typeName,
          value: item.type,
          children: item?.pointList?.map(
            (i: { pointNo: string; name: string }) => ({
              label: i.name,
              value: i.pointNo,
            }),
          ),
        }));
        const formConf: FormConfig = {
          fields: [
            {
              fieldName: 'navigatePoint',
              label: '选择目标点',
              placeholder: '请选择目标点',
              type: 'cascader',
              options: options,
              validatorRules: [
                {
                  required: true,
                  message: '请选择目标点',
                },
              ],
            },
          ],
        };
        showModal({
          title: `设置导航任务(${record.deviceName})`,
          content: (
            <CommonForm
              formConfig={formConf}
              getFormInstance={(formRef: any) => {
                formInstance = formRef;
              }}
            />
          ),
          footer: {
            showOk: true,
            showCancel: true,
            okFunc: async (cb) => {
              try {
                const values = await formInstance.validateFields();
                const navigatePoint = values.navigatePoint;
                fetchApi
                  .deviceNavigation({
                    deviceBaseId: record.deviceBaseId,
                    pointNo: navigatePoint[1],
                    pointType: navigatePoint[0],
                  })
                  .then((res) => {
                    if (res.code === HttpStatusCode.Success) {
                      message.success('任务下发成功');
                      cb();
                    } else {
                      res.message && message.error(res.message);
                    }
                  });
              } catch (e) {}
            },
            cancelFunc: (cb) => {
              cb();
            },
          },
        });
      }
    } catch (e) {}
  };

  const getParkPoints = () => {
    fetchApi.getParkPoints(Number(stationBaseId)).then((res) => {
      if (res?.code === HttpStatusCode.Success) {
        setParkStopList(res?.data || []);
      }
    });
  };

  const updateDeviceWordMode = (record: any, workMode: WorkMode) => {
    changeDeviceWordMode(record.deviceBaseId, workMode).then((res) => {
      if (res?.code !== HttpStatusCode.Success) {
        message.error(res?.message || '设备工作模式更新失败');
      } else {
        message.success('任务模式更新成功');
      }
    });
  };
  useEffect(() => {
    getParkPoints();
    commonApi
      .getCommonDropDown({
        keyList: ['WAREHOUSE_WORK_MODE'],
      })
      .then((res) => {
        if (res?.code === HttpStatusCode.Success) {
          setWorkMode(
            res?.data?.warehouseWorkModeList?.map((i) => ({
              label: i.name,
              value: i.code,
            })) || [],
          );
        }
      });
  }, []);
  const formatColumns = () => {
    return getLinkedVehicleColumns(stationType).map((col) => {
      if ('dataIndex' in col) {
        switch (col.dataIndex) {
          case 'rowIndex':
            return {
              ...col,
              render: (
                text: string | number,
                record: StationVehicleResponse,
                index: number,
              ) => {
                return `${
                  (searchCondition.pageNum - 1) * searchCondition.pageSize +
                  index +
                  1
                }`;
              },
            };
          case 'enableName':
            return {
              ...col,
              render: (
                text: string | number,
                record: StationVehicleResponse,
                index: number,
              ) => {
                return (
                  <span style={{ color: record.enable === 1 ? '#000' : 'red' }}>
                    {text}
                  </span>
                );
              },
            };
          case 'operations':
            return {
              ...col,
              render: (text: string, record: StationVehicleResponse) => {
                return (
                  <div className="operate">
                    <TableOperateBtn
                      title={record.enable ? '停用' : '启用'}
                      show={record.productType === ProductType.INTEGRATE}
                      handleClick={() => {
                        Modal.confirm({
                          content: record.enable
                            ? '确定要停用吗？'
                            : '确定要启用吗？',
                          onOk: () => {
                            fetchApi
                              .updateDeviceStatus({
                                deviceBaseId: record.deviceBaseId,
                                enable: record.enable == 1 ? 0 : 1,
                              })
                              .then((res) => {
                                if (res.code === HttpStatusCode.Success) {
                                  message.success('操作成功');
                                  setTableKey(Date.now());
                                } else {
                                  res.message && message.error(res.message);
                                }
                              })
                              .catch((e) => {});
                          },
                        });
                      }}
                    />
                    <TableOperateBtn
                      title="车辆配置"
                      show={record.productType !== ProductType.INTEGRATE}
                      handleClick={() => {
                        if (record.productType === ProductType.INTEGRATE) {
                          navigator(
                            `/app/stationManagement/detail/warahouseedit?deviceBaseId=${record.deviceBaseId}&&businessType=${record.businessType}&stationBaseId=${stationBaseId}`,
                          );
                        } else {
                          navigator(
                            `/app/stationManagement/detail/vehicleEdit?deviceBaseId=${record.deviceBaseId}&stationBaseId=${stationBaseId}`,
                          );
                        }
                      }}
                    />
                    <TableOperateBtn
                      title="解绑"
                      show={true}
                      handleClick={() => goUnBund(record)}
                    />
                    <TableOperateBtn
                      title="导航"
                      show={record.productType === ProductType.INTEGRATE && !!record.enable}
                      handleClick={() => {
                        settingNavigationTask(record);
                      }}
                    />
                  </div>
                );
              },
            };
          case 'workMode':
            col.render = (text, record) => {
              return (
                <Select
                  style={{ width: '150px' }}
                  disabled={record.enable === 0}
                  key={record.deviceBaseId}
                  defaultValue={record.workMode || WorkMode.SYS}
                  options={workMode}
                  onChange={(value: any) => {
                    updateDeviceWordMode(record, value);
                  }}
                />
              );
            };
            return col;
          default:
            return {
              ...col,
              render: (text: any) => `${text || '-'}`,
            };
        }
      }
    });
  };
  return (
    <>
      <CommonForm
        formConfig={vehicleSearchConfig}
        defaultValue={searchCondition.searchForm}
        layout="inline"
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={() => setSearchCondition({ ...initSearchCondition })}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list || [],
          totalPage: tableData?.pages,
          totalNumber: tableData?.total,
        }}
        rowSelection={rowSelection}
        columns={formatColumns()}
        loading={loading}
        rowKey={'deviceBaseId'}
        middleBtns={stationType != ProductType.INTEGRATE ? middleBtns : []}
        searchCondition={searchCondition}
        onPageChange={(paginationData: any) => {
          setSearchCondition({
            ...searchCondition,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      />
      <Modal
        title="解绑车辆与站点的绑定关系"
        open={unBundModal}
        onOk={unBundHandleOk}
        onCancel={() => {
          handleCancel();
          setUnBundModal(false);
        }}
      >
        {whetherUnlinkRes.resultType === 'CAN_UNLINK' && (
          <div style={{ textAlign: 'center' }}>
            <p>车辆解绑后，该车将回归车辆调度中心，</p>
            <p>是否确认解绑？</p>
          </div>
        )}
        {whetherUnlinkRes.resultType === 'CAN_FORCIBLE_UNLINK' && (
          <div style={{ textAlign: 'center' }}>
            <p>
              {selectVehicle.name}存在调度单{whetherUnlinkRes.scheduleName}
              ，已装载订单数
              {whetherUnlinkRes.orderNumber ? whetherUnlinkRes.orderNumber : 0}
              ，
            </p>
            <p>请确认是否强制解绑？</p>
          </div>
        )}
      </Modal>
      <Modal
        title="解绑车辆与站点的绑定关系"
        open={cannotUnlinkModal}
        footer={
          <Button
            key="info"
            onClick={() => {
              handleCancel();
              setCannotUnlinkModal(false);
            }}
          >
            知道了
          </Button>
        }
        onCancel={() => {
          handleCancel();
          setCannotUnlinkModal(false);
        }}
      >
        {whetherUnlinkRes.resultType === 'CANNOT_UNLINK' && (
          <div style={{ textAlign: 'center' }}>
            <p>
              {selectVehicle.name}存在调度单{whetherUnlinkRes.scheduleName}
              ，已装载订单数
              {whetherUnlinkRes.orderNumber ? whetherUnlinkRes.orderNumber : 0}
              ，
            </p>
            <p>请在车端清空调度单再操作解绑？</p>
          </div>
        )}
      </Modal>
    </>
  );
};
export default React.memo(LinkedVehicle);
