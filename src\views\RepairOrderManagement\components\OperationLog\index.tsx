import { Col, Table } from 'antd';
import React from 'react';
import { operationLogColumns } from '../../utils/column';
import './index.scss';
interface Props {
  requireLog: any;
}
const OperationLog = (props: Props) => {
  const formatColumns = () => {
    return operationLogColumns.map((col: any) => {
      switch (col.dataIndex) {
        default:
          col.render = (text: any, record: any) => `${text || '-'}`;
      }
      return col;
    });
  };
  return (
    <div className="operationLog">
      <p className="operationLog-title">{'操作日志'}</p>
      <Col span={20} push={2}>
        <Table
          rowKey={(record) => record.id}
          columns={formatColumns()}
          dataSource={props.requireLog}
          bordered
          pagination={false}
        />
      </Col>
    </div>
  );
};

export default React.memo(OperationLog);
