import React, { useEffect, useRef, useState } from 'react';
import {
  CommonForm,
  EditModuleTitle,
  BreadCrumb,
  FormConfig,
  FieldItem,
} from '@/components';
import { batteryFormConfig } from './constants';
import { request } from '@/fetch/core';
import { formatLocation } from '@/utils/utils';
import { HttpStatusCode, Method } from '@/fetch/core/constant';
import { Button, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { StationDetailTabKeys } from '../../StationDetail';
import './index.scss';
const breadCrumbItems = [
  { title: '关联车辆', route: '' },
  { title: '车辆配置', route: '' },
];

const WarehouseEdit = () => {
  const navigator = useNavigate();
  const formInfoRef = useRef<any>(null);
  const formPointRef = useRef<any>(null);
  const locationMap = formatLocation(window.location.search);
  const stationBaseId = locationMap.stationBaseId;
  const businessType = locationMap.businessType;
  const deviceBaseId = locationMap.deviceBaseId;

  const configDetail = () => {
    request({
      path: '/k2/management/station_warehouse/get_device_config_detail',
      method: Method.GET,
      urlParams: {
        deviceBaseId,
      },
    })
      .then((res) => {
        if (res.code === HttpStatusCode.Success) {
          formInfoRef.current?.setFieldsValue(res?.data);
          formPointRef.current?.setFieldsValue(res?.data);
          formPointRef.current?.setFieldValue('homePointNo', {
            label: res?.data?.homePointName,
            value: res?.data?.homePointNo,
          });
        } else {
          res.message && message.error(res.message);
        }
      })
      .catch((e) => {});
  };

  const goBack = () => {
    navigator(
      `/app/stationManagement/detail?stationBaseId=${stationBaseId}&tabKey=${StationDetailTabKeys.LINKED_VEHICLE}`,
    );
  };

  const updateDeviceConfig = (opt: {
    deviceBaseId: number;
    autoChargeLimit: number;
    missionChargeLimit: number;
    forceChargeLimit: number;
    homePointNo: string;
  }) => {
    request({
      path: '/k2/management/station_warehouse/update_device_config',
      method: 'PUT',
      body: opt,
    })
      .then((res) => {
        if (res.code === HttpStatusCode.Success) {
          message.success('编辑成功');
          history.go(-1);
        } else {
          res.message && message.error(res.message);
        }
      })
      .catch((e) => {});
  };

  const submit = async () => {
    try {
      Promise.all([
        formInfoRef.current?.validateFields(),
        formPointRef.current?.validateFields(),
      ])
        .then((res) => {
          updateDeviceConfig({
            ...res[0],
            homePointNo: res[1]?.homePointNo?.value,
            deviceBaseId: Number(deviceBaseId),
          });
        })
        .catch((e) => {});
    } catch (e) {}
  };
  useEffect(() => {
    configDetail();
  }, []);
  return (
    <div className="warehouse-container">
      <BreadCrumb items={breadCrumbItems} />
      <div className="warehouse-content">
        <EditModuleTitle title="充电信息"></EditModuleTitle>
        <CommonForm
          layout="horizontal"
          formConfig={batteryFormConfig}
          getFormInstance={(formRef: any) => {
            formInfoRef.current = formRef;
          }}
        />
        <EditModuleTitle title="点位配置"></EditModuleTitle>
        <CommonForm
          layout="horizontal"
          formConfig={poiConf}
          getFormInstance={(formRef: any) => {
            formPointRef.current = formRef;
          }}
        />
        <div className="submit">
          <Button type="primary" onClick={submit}>
            确认
          </Button>
          <Button onClick={goBack} style={{ marginLeft: '20px' }}>
            取消
          </Button>
        </div>
      </div>
    </div>
  );
};

export default WarehouseEdit;
