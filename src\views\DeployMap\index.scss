.deploy-map {
  display: flex;
  height: 100%;
  position: relative;
  margin: 0px -12px -16px -12px;

  .map-container {
    height: 100%;
    position: relative;
    transition: width 0.3s;
    width: 100%;
    .map-scale-line {
      height: 8px;
      position: absolute;
      bottom: 60px;
      left: 25px;
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(3, 4, 13, 1);
      border: 1px solid rgba(3, 4, 13, 1);
      border-top: none;
      text-align: center;
      line-height: 0;
    }
    .ol-zoom {
      position: absolute;
      top: 70%;
      left: 25px;
      width: 40px;
      height: 81px;
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(35, 37, 43, 0.1);
      cursor: pointer;
      transition: all 0.3s ease;
      &:hover {
        transform: scale(1.2);
      }
      .ol-zoom-in {
        padding: 8px;
        height: 40px;
        width: 40px;
        border: 0px;
        border-radius: 8px 8px 0px 0px;
        border-bottom: 1px solid rgba(241, 242, 244, 1);
        background-color: rgba(255, 255, 255, 1);
        transition: all 0.3s ease;
        &:hover {
          transform: scale(1.1);
          box-shadow: 0 2px 12px 0 rgba(35, 37, 43, 0.1);
        }
      }
      .ol-zoom-out {
        padding: 8px;
        height: 40px;
        width: 40px;
        border: 0px;
        border-radius: 0px 0px 8px 8px;
        background-color: rgba(255, 255, 255, 1);
        transition: all 0.3s ease;
        &:hover {
          transform: scale(1.1);
          box-shadow: 0 2px 12px 0 rgba(35, 37, 43, 0.1);
        }
      }
    }
    .station-stop-marker-popover {
      display: flex;
      align-items: center;
      justify-content: center;
      .start {
        padding-left: 4px;
        padding-right: 12px;
        margin-right: 12px;
        border-right: 1px solid rgba(241, 242, 244, 1);
        cursor: pointer;
      }
      .end {
        padding-left: 4px;
        cursor: pointer;
      }
    }
  }

  .task-detail-overlay-container {
    background: #ffffff;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    min-width: 280px;
    z-index: 1;
    pointer-events: auto !important;
    position: relative;
    .task-detail-overlay-title {
      font-size: 14px;
      font-weight: 500;
      color: #23252b;
      line-height: 22px;
      margin-bottom: 8px;
    }

    .task-status-badge {
      display: inline-block;
      padding: 0 8px;
      height: 22px;
      line-height: 22px;
      color: #ffffff;
      border-radius: 2px;
      font-size: 12px;
      position: absolute;
      top: 0;
      right: 0;
      margin-bottom: 12px;
    }

    .task-detail-overlay-content {
      .task-detail-overlay-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .task-detail-overlay-item-label {
          width: 70px;
          font-size: 12px;
          color: rgba(35, 37, 43, 0.6);
          line-height: 20px;
          flex-shrink: 0;
        }

        .task-detail-overlay-item-value {
          flex: 1;
          font-size: 12px;
          color: #23252b;
          line-height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .check-subTask-detail {
          flex: 1;
          font-size: 12px;
          color: #4096ff;
          line-height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          cursor: pointer;
        }
      }
    }
  }

  .task-detail-overlay {
    background: #ffffff;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    padding: 22px;
    min-width: 240px;

    .show-phone-number,
    .hide-phone-number {
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      margin-left: 8px;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}
