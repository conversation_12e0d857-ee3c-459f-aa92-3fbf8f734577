import { dropDownListKey, dropDown<PERSON>ey } from '@/utils/constant';
import { FieldItem, FormConfig } from '@/components';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
export const tableColumns = [
  {
    title: '序号',
    dataIndex: 'rowIndex',
    align: 'center',
    width: 70,
    ellipsis: true,
  },
  {
    title: '车架号',
    dataIndex: 'serialNo',
    align: 'center',
    width: 190,
    ellipsis: true,
  },
  {
    title: '车牌号',
    dataIndex: 'name',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '供应商',
    dataIndex: 'supplierName',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '产品类型',
    dataIndex: 'productTypeName',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '设备类型',
    dataIndex: 'businessTypeName',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '箱体格口模板名称',
    dataIndex: 'boxTemplateName',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '箱体名称',
    dataIndex: 'hardwareModelName',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '箱体型号',
    dataIndex: 'hardwareModelModel',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '格口数',
    dataIndex: 'gridNum',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '车型',
    dataIndex: 'deviceTypeName',
    align: 'center',
    ellipsis: true,
    width: 220,
  },
  {
    title: '系统标签',
    dataIndex: 'tagName',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '车辆生命周期',
    dataIndex: 'hardwareStatusName',
    align: 'center',
    width: 130,
    ellipsis: true,
  },
  {
    title: '标定检测状态',
    dataIndex: 'checkStatusName',
    align: 'center',
    width: 160,
    ellipsis: true,
  },
  {
    title: '安卓设备',
    dataIndex: 'androidDeviceId',
    align: 'center',
    width: 160,
    ellipsis: true,
  },
  {
    title: '操作人',
    dataIndex: 'modifyUser',
    align: 'center',
    width: 140,
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    align: 'center',
    width: 180,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    align: 'center',
    width: 360,
    ellipsis: true,
    fixed: 'right',
  },
];
const fieldsConfig: FieldItem[] = [
  {
    fieldName: 'serialNo',
    label: '车架号',
    placeholder: '请输入车架号',
    type: 'input',
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
  {
    fieldName: 'name',
    label: '车牌号',
    placeholder: '请输入车牌号',
    type: 'input',
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
  {
    fieldName: 'supplier',
    label: '供应商',
    placeholder: '请选择供应商',
    type: 'select',
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
    specialFetch: 'commonDown',
    dropDownKey: dropDownKey.SUPPLIER,
    dropDownListKey: dropDownListKey.SUPPLIER,
  },
  {
    fieldName: 'deviceTypeBaseId',
    label: '车型',
    placeholder: '请选择车型名称',
    type: 'select',
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
    specialFetch: 'commonDown',
    dropDownKey: dropDownKey.VEHICLE_TYPE,
    dropDownListKey: dropDownListKey.VEHICLE_TYPE,
  },
  {
    fieldName: 'productType',
    label: '产品类型',
    placeholder: '请选择产品类型',
    type: 'select',
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
    specialFetch: 'commonDown',
    dropDownKey: dropDownKey.PRODUCT_TYPE,
    dropDownListKey: dropDownListKey.PRODUCT_TYPE,
  },
  {
    fieldName: 'businessType',
    label: '设备类型',
    placeholder: '请选择设备类型',
    type: 'select',
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
    specialFetch: 'commonDown',
    dropDownKey: dropDownKey.VEHICLE_BUSINESS_TYPE,
    dropDownListKey: dropDownListKey.VEHICLE_BUSINESS_TYPE,
  },
  {
    fieldName: 'androidDeviceId',
    label: '安卓设备',
    placeholder: '请输入安卓设备编号',
    type: 'input',
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
  {
    fieldName: 'tagList',
    label: '系统标签',
    placeholder: '请选择系统标签',
    type: 'select',
    multiple: true,
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
    specialFetch: 'commonDown',
    dropDownKey: dropDownKey.TAG_LIST,
    dropDownListKey: dropDownListKey.TAG_LIST,
  },
  {
    label: '硬件信息',
    labelCol: { span: 3 },
    wrapperCol: { span: 21 },
    xxl: 12,
    xl: 16,
    lg: 20,
    childrenList: ['hardwareTypeId', 'hardwareModelId', 'hardwareNumber'],
  },
  {
    fieldName: 'hardwareTypeId',
    type: 'select',
    placeholder: '请选择硬件类型',
    width: '30%',
    marginLeft: 0,
    marginRight: 0,
    isChild: true,
    specialFetch: 'commonDown',
    dropDownKey: dropDownKey.HARDWARE_TYPE,
    dropDownListKey: dropDownListKey.HARDWARE_TYPE,
  },
  {
    fieldName: 'hardwareModelId',
    placeholder: '请选择硬件型号',
    type: 'select',
    width: '30%',
    marginLeft: 0,
    marginRight: 0,
    isChild: true,
  },
  {
    fieldName: 'hardwareNumber',
    placeholder: '请输入硬件序列号',
    type: 'input',
    width: '40%',
    marginLeft: 0,
    marginRight: 0,
    isChild: true,
  },
];

export const searchConfig: FormConfig = {
  fields: fieldsConfig,
  linkRules: {
    hardwareTypeId: [
      {
        linkFieldName: 'hardwareModelId',
        rule: 'clear',
      },
      {
        linkFieldName: 'hardwareModelId',
        rule: 'fetchData',
        fetchFunc: async (val) => {
          if (!val) {
            return [];
          }
          const res = await request({
            method: 'GET',
            path: '/k2/management/hardware_type/get_hardware_model_of_type',
            urlParams: { hardwareTypeId: val.value },
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item) => {
              return {
                label: item.hardwareModelName,
                value: item.hardwareModelId,
              };
            });
          } else {
            return [];
          }
        },
      },
      {
        linkFieldName: 'hardwareNumber',
        rule: 'clear',
      },
    ],
  },
};

export const vehicleTypeTableColumn: any[] = [
  {
    title: '硬件类型名称',
    dataIndex: 'hardwareTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '硬件型号名称',
    dataIndex: 'hardwareModelName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '硬件型号',
    dataIndex: 'hardwareModelModel',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '数量',
    dataIndex: 'useNumber',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '序列号',
    dataIndex: 'hardwareNumber',
    align: 'center',
    ellipsis: true,
  },
];

export const sensorTableColumn: any[] = [
  {
    title: '硬件类型名称',
    dataIndex: 'hardwareTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '用途',
    dataIndex: 'hardwareTypeUsageName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '硬件型号名称',
    dataIndex: 'hardwareModelName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '硬件型号',
    dataIndex: 'hardwareModelModel',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '数量',
    dataIndex: 'useNumber',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '序列号',
    dataIndex: 'hardwareNumber',
    align: 'center',
    ellipsis: true,
  },
];
