.multi-select-wrapper {
  // width: 30%;
  // max-width: 242px;
  // margin-right: 16px;
  width: 100%;
  // border: 1px solid red;
  // > span {
  //   font-size: 14px;
  //   font-family: PingFangSC;
  //   font-weight: normal;
  //   color: black;
  //   margin-right: 8px;
  // }
  .#{$ant-prefix}-select {
    // width: calc(100% - 64px);
  }
  .checkbox-wrapper {
    padding: 5px;
    .#{$ant-prefix}-checkbox,
    .#{$ant-prefix}-checkbox-checked {
      margin-right: 4px;
    }
  }
}

.#{$ant-prefix}-select-item-option-selected {
  .#{$ant-prefix}-select-item-option-state {
    display: none;
  }
}
.#{$ant-prefix}-select-tree {
  // background: rgba(27, 32, 56, 1) !important;
}
.#{$ant-prefix}-select-tree .#{$ant-prefix}-select-tree-treenode {
  padding: 5px !important;
}
.#{$ant-prefix}-select-tree-switcher-noop {
  display: none;
}
.#{$ant-prefix}-select-tree-checkbox {
  .#{$ant-prefix}-select-tree-checkbox-inner {
    // background-color: #03040d;
  }
}
.#{$ant-prefix}-select-tree-checkbox-checked {
  .#{$ant-prefix}-select-tree-checkbox-inner {
    background-color: #0d85ff;
  }
}
