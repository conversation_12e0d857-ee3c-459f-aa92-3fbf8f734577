import { CommonTableRequest } from '@/types';
import { ChargeType } from '@/utils/constant';
import { ProductType } from '@/utils/enum';

/** 站点基础信息 */
export interface BasicStationInfo {
  /** 站点基础信息表id */
  stationBaseId: number;
  /** 所在城市 */
  cityId: number;
  /** 所在城市名称 */
  cityName: string;
  /** 所在省份 */
  stateId: number;
  /** 所在省份名称 */
  stateName: string;
  /** 所在国家/大区 */
  countryId: number;
  /** 所在国家/大区名称 */
  countryName: string;
  /** 站点名称 */
  name: string;
  /** 站点用途枚举 */
  useCase: string;
  /** 站点用途名称 */
  useCaseName: string;
  /** 运营方类型 (自营SELF / 三方THIRD) */
  type: string;
  /** 运营方类型名称 (自营SELF / 三方THIRD) */
  typeName: string;
  /** 站点类型枚举 (无人车站点vehicle / 机器人站点robot) */
  productType: ProductType; //
  /** 站点类型名称 (无人车站点vehicle / 机器人站点robot) */
  productTypeName: string;
  /** 站点负责人userName */
  personName: string;
  /** 站点负责人中文名 */
  personChineseName: string;
  /** 站点负责人erp */
  personErp: string;
  /** 站点负责人联系方式 */
  contact: string;
  /** 纬度 */
  lat: number;
  /** 经度 */
  lon: number;
  /** 站点详细地址 */
  address: string;
  /** 绑定设备数 */
  deviceCount: number;
  /** 站点状态 */
  enable: number;
  /** 业务编号*/
  number: string;
}

export interface StationRequest extends CommonTableRequest {
  searchForm: {
    /** 所在省份 */
    stateId?: number | null;
    /** 所在城市 */
    cityId?: number | null;
    /** 无人车站点表id */
    stationId?: number | null;
    /** 车牌号 */
    deviceName?: string | null;
    /** 车架号 */
    serialNo?: string | null;
    /** 站点用途枚举 */
    useCase?: string | null;
    /** 站点类型枚举 (无人车站点vehicle / 机器人站点robot) */
    productType?: string | null;
    /** 运营方类型 */
    type?: string | null;
    /** 站点状态 */
    enable?: number | null;
  };
}

export interface StationResponse
  extends Omit<
    BasicStationInfo,
    'cityId' | 'personName' | 'contact' | 'lat' | 'lon' | 'address'
  > {
  /** 站点状态名称 */
  enableName: string;
  /** 停靠点数量 */
  stopCount: number; // 停靠点数量
}

export interface NewStationInfo
  extends Omit<
    BasicStationInfo,
    | 'stationBaseId'
    | 'useCaseName'
    | 'typeName'
    | 'productTypeName'
    | 'deviceCount'
    | 'cityName'
    | 'stateId'
    | 'stateName'
    | 'countryId'
    | 'countryName'
    | 'enable'
  > {
  /** 青龙站点编号 (无人车站点必传) */
  number?: string;
  /** 最小接单时长 (无人车站点必传) */
  minDropOffTime?: number;
  deploymentPlanInfo: any;
}

export interface StationBusinessInfo {
  /** 站点基础信息表id */
  stationBaseId: number;
  /** 无人车站点表id */
  stationId: number;
  /** 青龙站点编号 (无人车站点必传) */
  number: string;
  /** 最小接单时长 (无人车站点必传) */
  minDropOffTime: string;
  /** 自动回充电量 */
  autoChargeLimit: number;
  /** 可接任务电量 */
  missionChargeLimit: number;
  /** 强制回充电量 */
  forceChargeLimit: number;
  /** 车端亮灯提醒设置 */
  reminderTime: number;
  /** AMR充电方式 */
  chargeType: ChargeType;
  /** 业务接口配置 */
  groupBusinessList: {
    name: string; // 业务名称
    code: string; // 业务标识
    shelfTypeList: {
      // 上装类型列表
      shelfTypeName: string;
      shelfTypeId: string | number;
    }[];
  }[] | null;
}

export interface StationVehicleRequest extends CommonTableRequest {
  searchForm: {
    stationBaseId: number;
    deviceName?: string | null;
    serialNo?: string | null;
    productType?: string | null;
    businessType?: string | null;
    isRequire?: number | null;
    deviceTypeId?: number | null;
    isVirtual?: number | null;
  };
}
export interface StationVehicleResponse
  extends Omit<
    StationVehicleDetail,
    | 'boxTemplateId'
    | 'boxTemplateName'
    | 'boxId'
    | 'gridList'
    | 'linkStopList'
    | 'jumpStationStopList'
  > {
  /** 设备归属方 */
  ownerUseCase: string;
  /** 设备归属方名称 */
  ownerUseCaseName: string;
  /** 设备阶段 */
  vehicleStage: string;
  /** 设备阶段名称 */
  vehicleStageName: string;
  /** 设备生命周期 */
  hardwareStatus: string;
  /** 设备生命周期名称 */
  hardwareStatusName: string;
  /** 推流模式 */
  videoMode: number;
  /** 推流模式名称 */
  videoModeName: string;
  /** 是否在维修 */
  isRequire: number;
  /** 是否在维修名称 */
  isRequireName: string;
  /** 是否是虚拟车 */
  isVirtual: number;
  /** 是否是虚拟车名称 */
  isVirtualName: string;
  /** 绑定的本站停靠点数目 */
  linkLocalStationStopCount: number;
  /** 绑定的跨站停靠点数目 */
  linkJumpStationStopCount: number;
  /**是否停用启用 */
  enable: number;
  /**停用启用名称*/
  enableName: string;
  /** 任务模式 */
  workMode: string;
  /** 任务模式名称 */
  workModeName: string;
}
export interface GridType {
  /** 格口类型 */
  type: string;
  /** 格口类型名称 */
  typeName: string;
  /** 长 */
  length: number;
  /** 宽 */
  width: number;
  /** 高 */
  height: number;
}

export interface VehicleGrid extends GridType {
  /** 格口号 */
  gridNo: number;
  /** 体积 */
  volume: number;
  /** 托盘信息 */
  pallet: string;
}

export interface LinkStop {
  /** 停靠点类型 */
  stopType: string;
  /** 停靠点类型名称 */
  stopTypeName: string;
  /** 停靠点列表 */
  stopList: {
    /** 停靠点编号 */
    id: number;
    /** 停靠点名称 */
    name: string;
    /** 是否绑定 */
    isLinked: number;
    /** 等待时长 */
    waitingTime: number;
  }[];
}

export interface JumpStationStop extends LinkStop {
  /** 排序值 */
  sort: number;
}

export interface StationVehicleDetail {
  /** 所在城市 */
  cityId: number;
  /** 站点基础信息表id */
  stationBaseId: number;
  /** 站点名称 */
  stationName: string;
  /** 设备基础信息表id */
  deviceBaseId: number;
  /** 车牌号 */
  deviceName: string;
  /** 车架号 */
  serialNo: string;
  /** 产品类型 */
  productType: string;
  /** 产品类型名称 */
  productTypeName: string;
  /** 设备类型基础信息表id */
  deviceTypeId: number;
  /** 车型名称 */
  devceTypeName: string;
  /** 设备类型 */
  businessType: string;
  /** 设备类型名称 */
  businessTypeName: string;
  /** 货箱模板id */
  boxTemplateId: number;
  /** 货箱模板名称 */
  boxTemplateName: string;
  /** 货箱id */
  boxId: number;
  /** 格口信息列表 */
  gridList: VehicleGrid[];
  /** 车辆在青龙系统编号 */
  vCode: string;
  /** 绑定的本站停靠点列表 */
  linkStopList: LinkStop[];
  /** 绑定的跨站停靠点列表 */
  jumpStationStopList: JumpStationStop[];
}

export interface StationVehicleBoxDetail {
  /** 设备基础信息表id */
  deviceBaseId: number;
  /** 车牌号 */
  deviceName: string;
  /** 车架号 */
  serialNo: string;
  /** 产品类型 */
  productType: string;
  /** 产品类型名称 */
  productTypeName: string;
  /** 货箱模板id */
  boxTemplateId: number;
  /** 货箱模板名称 */
  boxTemplateName: string;
  /** 货箱硬件型号id */
  hardwareModelId: number;
  /** 货箱硬件型号名称 */
  hardwareModelModel: string;
  /** 驱动类型 */
  driverType: string;
  /** 串口号 */
  deviceId: string;
  /** 波特率 */
  baudRate: number;
  /** 左侧列数 */
  leftBoxColumnNum: number;
  /** 右侧列数 */
  rightBoxColumnNum: number;
  /** 格口信息列表 */
  gridList: BoxGrid[];
  /** 格口型号列表 */
  gridSpecificationList: GridType[];
}

export interface BoxGrid extends GridType {
  /** 格口id */
  id: number;
  /** 格口号 */
  gridNo: number;
  /** 板卡号 */
  boardNo: number;
  /** 方位 */
  side: string;
  /** 锁号 */
  lockNo: number;
  /** 状态 */
  enable: number;
  /** 状态名称 */
  enableName: string;
  /** 托盘信息列表 */
  palletList: {
    /** 编号 */
    id: number;
    /** 名称 */
    name: string;
  }[];
}

/** 货箱模板 */
export interface BoxTemplate {
  /** 货箱模板id */
  id: number;
  /** 货箱模板名称 */
  name: string;
  /** 产品类型 */
  productType: string;
  /** 产品类型名称 */
  productTypeName: string;
  /** 货箱硬件型号id */
  hardwareModelId: number;
  /** 货箱硬件型号名称 */
  hardwareModelModel: string;
  /** 驱动类型 */
  driverType: string;
  /** 串口号 */
  deviceId: string;
  /** 波特率 */
  baudRate: number;
  /** 左侧列数 */
  leftBoxColumnNum: number;
  /** 右侧列数 */
  rightBoxColumnNum: number;
  /** 货箱模板状态 */
  enable: number;
  /** 格口信息列表 */
  gridList: BoxGrid[];
  /** 格口型号列表 */
  gridSpecificationList: GridType[];
}

/** 停车点信息 */
export interface ParkingSpot {
  /** 停车点id */
  id?: number;
  /** 停车点名称 */
  name: string;
  /** 纬度 */
  latitude: number;
  /** 经度 */
  longitude: number;
  /** 朝向 */
  heading: number;
  /** 级别 */
  level: number;
}

/** 被接驳人信息 */
export interface StopCollectionUser {
  /** 姓名 */
  name: string;
  /** 手机号 */
  contact: string;
  /** 是否默认 */
  isDefault: number;
  /** 是否默认名称 */
  isDefaultName?: string;
  /** 状态 */
  enable: number;
  /** 状态名称 */
  enableName?: string;
}

/** 代收人信息 */
export interface StopConnectionUser extends StopCollectionUser {}

/** 停靠点详情信息 */
export interface BasicStopInfo {
  /** 停靠点id */
  id: number;
  /** 停靠点编号 */
  number: string;
  /** 停靠点名称 */
  name: string;
  /** 停靠点类型 */
  type: string;
  /** 停靠点类型名称 */
  typeName: string;
  /** 停靠点详细地址 */
  addressName: string;
  /** 站点基础信息表id */
  stationBaseId: number;
  /** 站点名称 */
  stationName: string;
  /** 停靠时长 */
  waitingTime: number;
  /** 关键词列表 */
  keyWordList?: string[];
  /** 停车点信息列表 */
  parkingSpotList: ParkingSpot[];
  /** 被接驳人信息列表 */
  stopCollectionUserList?: StopCollectionUser[];
  /** 代收人信息列表 */
  stopConnectionUserList?: StopConnectionUser[];
  /** 停靠点状态 */
  enable: number;
  /** 停靠点状态名称 */
  enableName: string;
}

/** 无人车站点下的停靠点请求 */
export interface StopRequest extends CommonTableRequest {
  searchForm: {
    /** 站点基础信息表id */
    stationBaseId: number;
    /** 停靠点名称 */
    stopName?: string | null;
    /** 停靠点类型 */
    stopType?: string | null;
    /** 停靠点状态 */
    enable?: number | null;
  };
}
/** 无人车站点下的停靠点响应 */
export interface StopResponse
  extends Omit<
    BasicStopInfo,
    | 'addressName'
    | 'keyWordList'
    | 'parkingSpotList'
    | 'stopCollectionUserList'
    | 'stopConnectionUserList'
  > {}

/** 新建停靠点 */
export interface NewStopInfo
  extends Omit<
    BasicStopInfo,
    'id' | 'number' | 'typeName' | 'stationName' | 'enable' | 'enableName'
  > {}

/** 编辑停靠点 */
export interface EditStopInfo
  extends Omit<
    BasicStopInfo,
    'number' | 'typeName' | 'stationName' | 'enable' | 'enableName'
  > {}
