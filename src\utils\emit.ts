/* eslint-disable require-jsdoc */
class Emitter {
  map: any = {};
  listenerConfigsForEvent(event: string) {
    let listeners = this.map[event];
    if (!listeners) {
      listeners = [];
      this.map[event] = listeners;
    }
    return listeners;
  }
  on(event: string, listener: Function) {
    const listeners = this.listenerConfigsForEvent(event);
    if (listeners.indexOf(listener) === -1) {
      listeners.push({
        listener,
      });
    }
  }
  off(event: string, listener: Function) {
    const listeners = this.map[event];
    let findIndex = -1;
    const found = listeners.some((item: any, index: number) => {
      if (item.listener === listener) {
        findIndex = index;
        return true;
      }
      return false;
    });
    if (found) {
      listeners.splice(findIndex, 1);
    }
  }
  emit(event: string | number, args: any) {
    if (this.map[event]) {
      this.map[event].forEach((cb: any) => {
        cb.listener(args);
      });
    }
  }
}

const emitter = new Emitter();
export function addGlobalEventListener(event: string, cb: Function) {
  emitter.on(event, cb);
}

export function sendGlobalEvent(event: string, args?: any) {
  emitter.emit(event, args);
}

export function removeGlobalEventListener(event: string, cb: Function) {
  emitter.off(event, cb);
}
