import React, { useState, useEffect, useRef, ReactNode } from 'react';
import { Form, Row, Col, Select, Table } from 'antd';
import { PackageButton, ButtonType } from '@/components/PackageButton';
import {
  changeRelationObj,
  clearRelationObj,
} from '@/redux/reducer/expandTableRelation';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/store';
import { CommonForm, FormConfig } from '@/components';
import { AnyFunc } from '@/global';
interface AllInfoData {
  key: string; // 表格的唯一标识
  code?: string | number; // 每条数据的id
  type: string; // 该条数据的类型
  children: AllInfoData[] | null;
}
interface Props {
  bindInfoList: any[];
  searchConfig: FormConfig;
  initSearchformValue: any;
  tableColumns: any[];
  hideSelectAll?: boolean;
  checkStrictly?: boolean;
  initialExpandLevel?: number;
  renderData?: any;
  separator?: string;
  tip?: string;
  fetchAllInfoList: (values: any) => Promise<AllInfoData[]>;
  getSelectValues: (values: any) => any;
  onFieldItemFocus?: AnyFunc;
}

const ExpandTable = (props: Props) => {
  const {
    bindInfoList,
    renderData,
    searchConfig,
    initSearchformValue,
    tableColumns,
    initialExpandLevel = 1,
    hideSelectAll = false,
    checkStrictly = false,
    separator = '-',
    tip,
    fetchAllInfoList,
    getSelectValues,
    onFieldItemFocus,
  } = props;

  const dispatch = useDispatch();
  const searchFormInstance = useRef<any>(null);
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  const [allInfoList, setAllInfoList] = useState<any[]>([]);
  const [searchformValue, setSearchformValue] =
    useState<any>(initSearchformValue);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [expandedRowKeys, setExpandedRowKeys] = useState<any[]>([]);
  const hisRelationObj = useSelector(
    (state: RootState) => state.expandTableRelation,
  );

  const rowSelection = {
    hideSelectAll: hideSelectAll,
    columnWidth: 15,
    checkStrictly: checkStrictly,
    selectedRowKeys: selectedRowKeys,
    onSelectAll: (selected: any, selectedRows: any, changeRows: any) => {
      if (selected) {
        setSelectedRowKeys(selectedRows.map((item: any) => item.key));
      } else {
        setSelectedRowKeys([]);
      }
    },
    onSelect: (record: any, selected: boolean, selectedRows: any) => {
      const curSelectedKeys = handleSelectedRow[
        `${selected ? 'selected' : 'unSelected'}&${
          checkStrictly ? 'checkStrictly' : 'notCheckStrictly'
        }`
      ](record, selectedRowKeys.slice());
      setSelectedRowKeys([...curSelectedKeys]);
    },
  };
  useEffect(() => {
    processData(searchformValue, bindInfoList, true);
    if (bindInfoList?.length >= 0) {
      setSelectedRowKeys(bindInfoList);
    }
  }, [JSON.stringify(bindInfoList), renderData]);

  useEffect(() => {
    return () => {
      dispatch(clearRelationObj(null));
    };
  }, []);

  useEffect(() => {
    const selectedKeysObj: any = {};
    selectedRowKeys.forEach((key: any, index: any) => {
      if (!hisRelationObj.relationObj || !hisRelationObj.relationObj.get(key)) {
        return;
      }
      const arr = key.split(separator);
      const keyList = selectedKeysObj[arr[0]];
      if (keyList) {
        keyList.push(arr[1]);
      } else {
        selectedKeysObj[arr[0]] = [arr[1]];
      }
    });
    getSelectValues(selectedKeysObj);
  }, [selectedRowKeys, hisRelationObj.relationObj]);

  const handleSelectedRow = {
    'selected&checkStrictly': (currentRow: any, preSelectedKeys: any[]) => {
      preSelectedKeys.push(currentRow.key);
      return preSelectedKeys;
    },
    'selected&notCheckStrictly': (currentRow: any, preSelectedKeys: any[]) => {
      if (!currentRow.children) {
        preSelectedKeys.push(currentRow.key);
      } else {
        const allChildren = findChildrenList(currentRow.children, [
          currentRow.key,
        ]);
        preSelectedKeys = preSelectedKeys.concat(allChildren);
      }
      return preSelectedKeys;
    },
    'unSelected&checkStrictly': (currentRow: any, preSelectedKeys: any[]) => {
      const keyIndex: number = preSelectedKeys.indexOf(currentRow.key);
      preSelectedKeys.splice(keyIndex, 1);
      return preSelectedKeys;
    },
    'unSelected&notCheckStrictly': (
      currentRow: any,
      preSelectedKeys: any[],
    ) => {
      let needToDelList: any = [];
      if (currentRow.children) {
        needToDelList = findChildrenList(currentRow.children, [currentRow.key]);
      } else {
        const parents = hisRelationObj.relationObj?.get(currentRow.key) ?? [];
        needToDelList = parents.concat([currentRow.key]);
      }
      return preSelectedKeys.filter((v: any) => {
        return needToDelList.indexOf(v) == -1;
      });
    },
  };

  const findChildrenList = (childrenList: any[], arr: any) => {
    for (const item of childrenList) {
      arr.push(item.key);
      if (!item.children) {
        continue;
      }
      findChildrenList(item.children, arr);
    }
    return arr;
  };

  const formateParentRelationMap = (
    dataList: any,
    parentNodeList: any[],
    relationMap: any,
  ) => {
    for (const node of dataList) {
      relationMap.set(node.key, parentNodeList);
      if (!node.children) {
        continue;
      }
      formateParentRelationMap(
        node.children,
        parentNodeList.concat(node.key),
        relationMap,
      );
    }
    return relationMap;
  };

  // 处理展开项
  const handleExpand = (needExpandDataList: any[], relationMap: any) => {
    if (needExpandDataList.length <= 0) {
      if (initialExpandLevel === 1) {
        return;
      }
      let expandLevelList: any[] = [];
      relationMap?.forEach((list: any) => {
        if (list.length === initialExpandLevel - 1) {
          expandLevelList = expandLevelList.concat([...list]);
        }
      });
      setExpandedRowKeys([...new Set(expandLevelList)]);
    } else if (needExpandDataList.length > 0) {
      let parentNodes: any = [];
      needExpandDataList.forEach((item: any) => {
        const parent = relationMap?.get(item);
        parentNodes = parent
          ? parentNodes.concat(parent)
          : parentNodes.concat(item);
      });

      setExpandedRowKeys([...new Set(parentNodes)]);
    }
  };

  // 处理数据找到应该要展开的
  const processData = async (
    searchValues: any,
    needExpandDataList: any[],
    updateStore: boolean = false,
  ) => {
    setTableLoading(true);
    try {
      const dataList = await fetchAllInfoList(searchValues);
      setAllInfoList(dataList);
      if (dataList.length > 0) {
        const relationMap =
          updateStore || !hisRelationObj.relationObj
            ? formateParentRelationMap(dataList, [], new Map())
            : hisRelationObj.relationObj;
        handleExpand(needExpandDataList, relationMap);
        updateStore && dispatch(changeRelationObj(relationMap));
      }
    } catch (e) {
      console.log(e);
    } finally {
      setTableLoading(false);
    }
  };

  const onReset = () => {
    searchFormInstance.current?.setFieldsValue(initSearchformValue);
    setSearchformValue(initSearchformValue);
    processData(initSearchformValue, selectedRowKeys);
  };

  const onSearch = (values: any) => {
    processData(values, selectedRowKeys);
  };

  const onFieldFocus = (fieldName: string, allValues: any) => {
    onFieldItemFocus && onFieldItemFocus(fieldName, allValues);
  };

  return (
    <div className="expand-table">
      <div className="expand-table-searchform" style={{ marginBottom: '15px' }}>
        <Row justify="center">
          <Col span={20}>
            <CommonForm
              formConfig={searchConfig}
              layout={'horizontal'}
              onResetClick={onReset}
              onSearchClick={onSearch}
              onFieldFocus={onFieldFocus}
              formType="search"
              getFormInstance={(formRef: any) => {
                searchFormInstance.current = formRef;
              }}
            />
          </Col>
        </Row>
      </div>
      <Row justify="center">
        <Col span={20}>
          {tip && (
            <div
              style={{
                fontSize: '14px',
                color: 'rgb(170, 170, 170)',
                fontWeight: '400',
                marginBottom: '5px',
              }}
            >
              {tip}
            </div>
          )}
          <Table
            columns={tableColumns}
            loading={tableLoading}
            rowSelection={{ ...rowSelection }}
            rowKey={(record: any) => record.key}
            pagination={false}
            expandable={{
              onExpandedRowsChange: (expandedRows: any) => {
                setExpandedRowKeys(expandedRows);
              },
              expandedRowKeys: expandedRowKeys,
            }}
            dataSource={allInfoList}
            scroll={{
              y: 550,
            }}
          />
        </Col>
      </Row>
    </div>
  );
};

export default React.memo(ExpandTable);
