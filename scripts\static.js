const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const PublicPath = path.resolve(__dirname, '../build');
const CSSOutputPath = 'css';
const AssetsOutputPath = 'images';
const Envs = require('./env');
function cssLoaders() {
  return [
    {
      loader: MiniCssExtractPlugin.loader,
      options: {
        publicPath: path.relative(
          path.join(PublicPath, CSSOutputPath),
          PublicPath
        ) + '/',
      },
    },
    {
      loader: 'css-loader',
      options: {
        url: true, 
      },
    },
    {
      loader: 'postcss-loader',
    }
  ]
}

function rules() {
  return [
    {
      test: /\.css$/,
      use: cssLoaders(),
    },
    {
      test: /\.less$/,
      use: [
        ...cssLoaders(),
        "less-loader",
      ]
    },
    {
      test: /\.s[ac]ss$/i,  
      use: [
        ...cssLoaders(),
        {
          loader: 'sass-loader',
          options: {
            // 注入全局变量文件内容
            additionalData: `@import "${path.resolve(__dirname, '../src/assets/css/global-variables.scss').replace(/\\/g, '/')}";`
          }
        }
      ]
    },
    {
      test: /\.(jpe?g|png|gif|mp4|svg|woff2?|eot|ttf|otf)$/,
      use: [
        {
          loader: 'file-loader',
          options: {
            esModule: false,
            name: '[name].[hash].[ext]',
            outputPath: AssetsOutputPath,
          },
        },
      ],
      type: 'javascript/auto'
    },
  ]
}

function plugins() {
  return [
    new MiniCssExtractPlugin({
      filename: !Envs.isDevelopment ? 'css/[name]-[contenthash].min.css' : 'css/[name].css',
      ignoreOrder: true,
    })
  ]
}

module.exports = {
  rules,
  plugins,
}