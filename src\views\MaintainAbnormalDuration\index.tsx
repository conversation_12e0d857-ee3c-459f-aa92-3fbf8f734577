import React, { useEffect, useRef, useState } from 'react';
import {
  CommonForm,
  FormConfig,
  CommonTable,
  useTableData,
} from '@jd/x-coreui';
import { SearchConfig, TableColumns } from './utils/columns';
import { CommonApi, MaintainAbnormalDurationApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { useNavigate } from 'react-router-dom';
import { showModal } from '@/components';
import AbnormalDurationForm from './components/AbnormalDurationForm';
import { message, Modal } from 'antd';

const MaintainAbnormalDuration = () => {
  const fetchApi = new MaintainAbnormalDurationApi();
  const navigator = useNavigate();
  const commonApi = new CommonApi();
  const ref = useRef<any>(null);
  const updateRecordInfo = useRef<any>(null);
  const [modalShow, setModalShow] = useState<boolean>(false);
  const [searchFormConfig, setSearchFormConfig] =
    useState<FormConfig>(SearchConfig);
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );

  const initSearchCondition = {
    searchForm: {
      startTime: null,
      endTime: null,
      deviceName: null,
      stationBaseId: null,
      stationInfo: null,
      time: null,
      provinceId: null,
      cityId: null,
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValues.searchValues
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );
  const { tableData, loading, reloadTable } = useTableData(
    {
      ...searchCondition.searchForm,
      pageNum: searchCondition.pageNum,
      pageSize: searchCondition.pageSize,
    },
    fetchApi.getPageList,
  );

  useEffect(() => {
    getStationInfo();
  }, []);

  const formateColumns = () => {
    return TableColumns.map((col) => {
      switch (col.dataIndex) {
        case 'operate':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return (
                <a
                  onClick={() => {
                    getRecordInfo(record);
                  }}
                >
                  修改
                </a>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  const getStationInfo = async () => {
    const res = await commonApi.getStationDepartment({});
    if (res.code === HttpStatusCode.Success) {
      setSearchFormConfig({
        ...searchFormConfig,
        fields: searchFormConfig.fields.map((v) => {
          return v.fieldName === 'stationInfo'
            ? { ...v, options: res.data }
            : v;
        }),
      });
    }
  };

  const editAbnormalTime = async () => {
    const formVal = await ref.current?.formRef?.validateFields();
    if (formVal) {
      const list = ref.current?.list;
      const invalidData = list.filter(
        (v) => v.notOperationDuration == undefined,
      );
      if (invalidData.length > 0) {
        message.error('请输入影响运营时长');
        return;
      }
      const res = await fetchApi.editRecord({
        recordId: updateRecordInfo?.current?.recordId,
        breakdown: formVal.breakdown,
        disabledCauseKey: formVal.disabledCauseKey,
        notOperationDuration: list[0].notOperationDuration,
      });
      if (res.code === HttpStatusCode.Success) {
        message.success('提交成功');
        setModalShow(false);
        updateRecordInfo.current = null;
        reloadTable()
      } else {
        message.error(res.message);
      }
    }
  };

  const getRecordInfo = async (record: any) => {
    const res = await fetchApi.getRecordInfo({
      recordId: record.recordId,
    });
    if (res.code !== HttpStatusCode.Success) {
      message.error(res.message);
      return;
    }
    updateRecordInfo.current = {
      ...res.data,
      recordId: record.recordId,
      stationName: record.stationName,
      deviceName: record.deviceName,
    };
    setModalShow(true);
  };
  const onSearchClick = (val) => {
    let startTime: any;
    let endTime: any;
    if (val?.time && val?.time.length > 0) {
      const startMoment = val?.time[0];
      if (startMoment) {
        startTime = startMoment.format('YYYY-MM-DD HH:mm');
      }
      const endMoment = val?.time[1];
      if (endMoment) {
        endTime = endMoment.format('YYYY-MM-DD HH:mm');
      }
    }
    const data = {
      searchForm: {
        ...val,
        startTime,
        endTime,
        provinceId: val.stationInfo ? val.stationInfo[0] : null,
        stationBaseId: val.stationInfo ? val.stationInfo[2] : null,
        cityId: val.stationInfo ? val.stationInfo[1] : null,
      },
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(data);
  };
  const onResetClick = () => {
    setSearchCondition({ ...initSearchCondition });
  };

  const middleBtns: any[] = [
    {
      show: true,
      title: '录入',
      onClick: () =>
        navigator('/app/maintainAbnormalDuration/addAbnormalDuration'),
    },
  ];
  
  return (
    <>
      <CommonForm
        formConfig={searchFormConfig}
        layout={'inline'}
        defaultValue={{ ...searchCondition.searchForm, fetchVehicle: true }}
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={onResetClick}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        columns={formateColumns()}
        loading={loading}
        rowKey={'recordId'}
        middleBtns={middleBtns}
        searchCondition={searchCondition}
        onPageChange={(value: any) => {
          setSearchCondition(value);
        }}
      />
      {modalShow && (
        <Modal
          title={
            <div style={{ textAlign: 'center', width: '100%' }}>
              {`${updateRecordInfo.current?.stationName}-${updateRecordInfo.current?.deviceName}修改车辆异常时长`}
            </div>
          }
          width={'950px'}
          open={modalShow}
          onOk={() => editAbnormalTime()}
          onCancel={() => {
            setModalShow(false);
            updateRecordInfo.current = null;
          }}
        >
          <AbnormalDurationForm
            type="edit"
            tableList={[
              {
                startTime: updateRecordInfo.current?.startTime,
                endTime: updateRecordInfo.current?.endTime,
                disabledDuration: updateRecordInfo.current?.disabledDuration,
                notOperationDuration:
                  updateRecordInfo.current?.notOperationDuration,
              },
            ]}
            formVal={{
              breakdown: updateRecordInfo.current?.breakdown,
              disabledCauseKey: updateRecordInfo.current?.disabledCauseKey,
            }}
            ref={ref}
          />
        </Modal>
      )}
    </>
  );
};

export default React.memo(MaintainAbnormalDuration);
