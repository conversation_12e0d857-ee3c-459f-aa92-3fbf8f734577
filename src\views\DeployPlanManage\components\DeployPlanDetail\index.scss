.deploy-plan-detail {
  .stage-card {
    background-color: #f9fafb;
    padding: 0px 15px;
    margin-bottom: 15px;
    border-radius: 10px;
    &.processing {
      border: 1px solid #7d77eb;
    }
    .card-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .check-insurance {
        cursor: pointer;
        color: #7d77eb;
        margin-left: 10px;
      }
    }
  }
}
.toolbar-wrapper {
  padding: 0px 24px;
  color: #fff;
  font-size: 20px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 100px;
}

.toolbar-wrapper .anticon {
  padding: 12px;
  cursor: pointer;
}

.toolbar-wrapper .anticon[disabled] {
  cursor: not-allowed;
  opacity: 0.3;
}

.toolbar-wrapper .anticon:hover {
  opacity: 0.3;
}

.insurance-modal-content {
  .#{$ant-prefix}-card-head {
    background-color: #f7f7f7;
  }
}
