import { dropDownListKey, dropDown<PERSON>ey } from '@/utils/constant';
import { FieldItem, FormConfig } from '@/components';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
export const tableColumns: any[] = [
  {
    title: '厂商名称',
    dataIndex: 'factoryName',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '厂商id',
    dataIndex: 'id',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '厂商性质',
    dataIndex: 'propertyName',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '硬件类型',
    dataIndex: 'hardwareTypeList',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '联系人',
    dataIndex: 'person',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '联系方式',
    dataIndex: 'contact',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '电子邮箱',
    dataIndex: 'email',
    align: 'center',
    width: 180,
    ellipsis: true,
  },
  {
    title: '省',
    dataIndex: 'stateName',
    align: 'center',
    width: 80,
    ellipsis: true,
  },
  {
    title: '市',
    dataIndex: 'cityName',
    align: 'center',
    width: 80,
    ellipsis: true,
  },
  {
    title: '详细地址',
    dataIndex: 'address',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '厂商状态',
    dataIndex: 'enable',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '操作人',
    dataIndex: 'modifyUser',
    align: 'center',
    width: 130,
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    align: 'center',
    width: 180,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    align: 'center',
    width: 150,
    fixed: 'right',
  },
];
export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'factoryName',
      label: '厂商名称',
      placeholder: '请输入厂商名称',
      type: 'input',
    },
    {
      fieldName: 'property',
      label: '厂商性质',
      placeholder: '请选择厂商性质',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.MANUFACTORY_PROPERTY,
      dropDownListKey: dropDownListKey.MANUFACTORY_PROPERTY,
    },
    {
      fieldName: 'hardwareTypeId',
      label: '硬件类型',
      placeholder: '请选择硬件类型',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.HARDWARE_TYPE,
      dropDownListKey: dropDownListKey.HARDWARE_TYPE,
    },
    {
      fieldName: 'enable',
      label: '类型状态',
      placeholder: '请选择类型状态',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.ENABLE,
      dropDownListKey: dropDownListKey.ENABLE,
    },
  ],
};

const fieldsConfig: FieldItem[] = [
  {
    fieldName: 'factoryName',
    label: '厂商名称',
    type: 'input',
    placeholder: '请输入厂商名称',
    validatorRules: [{ required: true, message: '请输入厂商名称' }],
  },
  {
    fieldName: 'id',
    label: '厂商id',
    type: 'input',
    placeholder: '系统生成',
    disabled: true,
  },
  {
    fieldName: 'property',
    label: '厂商性质',
    type: 'radioGroup',
    validatorRules: [{ required: true, message: '请选择厂商性质' }],
  },
  {
    fieldName: 'hardwareTypeList',
    label: '硬件类型',
    type: 'checkboxGroup',
    hidden: true,
    validatorRules: [{ required: true, message: '请选择硬件类型' }],
  },
  {
    fieldName: 'person',
    label: '联系人',
    type: 'input',
    placeholder: '请输入联系人姓名',
  },
  {
    fieldName: 'contact',
    label: '联系方式',
    type: 'input',
    placeholder: '请输入手机号或座机号码',
    validatorRules: [
      { pattern: /^[1][0-9]{10}$/, message: '请输入合法的手机号' },
    ],
  },
  {
    fieldName: 'email',
    label: '邮箱',
    type: 'input',
    placeholder: '请输入合法的联系人邮箱',
    validatorRules: [{ type: 'email', message: '请输入合法的联系人邮箱' }],
  },
  {
    label: '地址',
    fieldName: 'combination',
    validatorRules: [{ required: true }],
    childrenList: ['cityInfo', 'address'],
  },
  {
    fieldName: 'cityInfo',
    placeholder: '请选择省市信息',
    type: 'cascader',
    width: '40%',
    marginLeft: 0,
    marginRight: 0,
    isChild: true,
    mapRelation: { label: 'name', value: 'id', children: 'children' },
    validatorRules: [{ required: true, message: '请选择省份城市' }],
  },
  {
    fieldName: 'address',
    type: 'input',
    placeholder: '请输入详细地址',
    width: '60%',
    marginLeft: 0,
    marginRight: 0,
    isChild: true,
    validatorRules: [{ required: true, message: '请输入详细地址' }],
  },
  {
    fieldName: 'enable',
    label: '厂商状态',
    type: 'radioGroup',
    specialFetch: 'commonDown',
    validatorRules: [{ required: true, message: '请输入厂商状态' }],
  },
];

export const editFormConfig: FormConfig = {
  fields: fieldsConfig,
  linkRules: {
    property: [
      {
        linkFieldName: 'hardwareTypeList',
        rule: 'visible',
        dependenceData: ['HARDWARE_SUPPLY'],
      },
    ],
  },
};
