import { Map, Feature, Overlay } from 'ol';
import { LineString, Point } from 'ol/geom';
import { Vector as VectorSource } from 'ol/source';
import { Vector as VectorLayer } from 'ol/layer';
import { Style, Circle as CircleStyle, Fill, Stroke, Icon } from 'ol/style';
import { Pointer as PointerInteraction } from 'ol/interaction';
import { Coordinate } from 'ol/coordinate';
import BaseEvent from 'ol/events/Event';
import { unByKey } from 'ol/Observable';
import { getLength } from 'ol/sphere';
export interface PolylineEditorOptions {
  controlPoint?: Style;
  midControlPoint?: Style;
  onChange?: (data: { length: number; controlPoints: number[][] }) => void;
}
export interface ControlPointStyles {
  normalPoint?: PointStyle;
  midPoint?: PointStyle;
  startPoint?: PointStyle;
  endPoint?: PointStyle;
}

export interface PointStyle {
  radius?: number;
  fillColor?: string;
  strokeColor?: string;
  strokeWidth?: number;
}
class PolylineEditor {
  protected map: Map;
  protected polylineLayer: VectorLayer<VectorSource> | null;
  protected polyline: LineString;
  private options: PolylineEditorOptions;
  protected editLayer: VectorLayer<VectorSource> | null;
  private interaction: PointerInteraction | null;
  private controlPoints: Feature<Point>[];
  private midPoints: Feature<Point>[];
  private startPointStyle: Style;
  private endPointStyle: Style;
  private controlPointStyle: Style;
  private midPointStyle: Style;
  private dragFeature: Feature<Point> | null;
  private isMobile: boolean;
  private startPointSelectedStyle: Style;
  private endPointSelectedStyle: Style;
  private onChange:
    | ((data: { length: number; controlPoints: number[][] }) => void)
    | undefined;

  constructor(
    map: Map,
    polylineLayer: VectorLayer<VectorSource>,
    polyline: LineString,
    options: PolylineEditorOptions = {},
  ) {
    this.map = map;
    this.polylineLayer = polylineLayer;
    this.polyline = polyline;
    this.options = options;
    this.editLayer = null;
    this.interaction = null;
    this.controlPoints = [];
    this.midPoints = [];
    this.dragFeature = null;
    this.startPointStyle = new Style({
      image: new Icon({
        src: require('@/assets/image/mapTrackPage/start-point-unselected.png'),
        scale: 0.5,
      }),
    });
    this.endPointStyle = new Style({
      image: new Icon({
        src: require('@/assets/image/mapTrackPage/end-point-unselected.png'),
        scale: 0.5,
      }),
    });
    this.startPointSelectedStyle = new Style({
      image: new Icon({
        src: require('@/assets/image/mapTrackPage/start-point-selected.png'),
        scale: 0.5,
      }),
    });
    this.endPointSelectedStyle = new Style({
      image: new Icon({
        src: require('@/assets/image/mapTrackPage/end-point-selected.png'),
        scale: 0.5,
      }),
    });
    this.controlPointStyle = new Style({
      image: new CircleStyle({
        radius: 6,
        fill: new Fill({ color: 'red' }),
        stroke: new Stroke({ color: 'white', width: 2 }),
      }),
    });
    this.onChange = options.onChange;

    this.midPointStyle = new Style({
      image: new CircleStyle({
        radius: 4,
        fill: new Fill({ color: 'blue' }),
        stroke: new Stroke({ color: 'white', width: 2 }),
      }),
    });

    if (options.controlPoint) {
      this.controlPointStyle = options.controlPoint;
    }

    if (options.midControlPoint) {
      this.midPointStyle = options.midControlPoint;
    }
    // 检测是否为移动设备
    this.isMobile = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    // 根据设备类型调整样式
    this.updateStyles();
    this.addInteraction();
  }

  setTarget(polyline: LineString): void {
    this.polyline = polyline;
    if (this.editLayer) {
      this.updatePoints();
    }
  }
  setPolylineLayer(layer: any): void {
    this.polylineLayer = layer;
    this.map.addLayer(this.polylineLayer!);
  }
  getPolylineLayer(): any {
    return this.polylineLayer;
  }
  getTarget(): LineString {
    return this.polyline;
  }

  updateStartAndEndPointStyle(array: Feature<Point>[]): void {
    this.startPointStyle = new Style({
      image: new Icon({
        src: require('@/assets/image/mapTrackPage/start-point-selected.png'),
        scale: 0.5,
      }),
    });
    this.endPointStyle = new Style({
      image: new Icon({
        src: require('@/assets/image/mapTrackPage/end-point-selected.png'),
        scale: 0.5,
      }),
    });
  }

  open(): void {
    if (!this.polyline) {
      return;
    }
    if (!this.editLayer && this.polylineLayer) {
      this.editLayer = new VectorLayer({
        source: new VectorSource(),
        style: (feature: Feature<Point>) => {
          if (feature.get('type') === 'control') {
            return this.controlPointStyle;
          } else {
            return this.midPointStyle;
          }
        },
        zIndex: 1000,
      });
      this.map.addLayer(this.polylineLayer);
      this.map.addLayer(this.editLayer);
    }
    this.updatePoints();
    if (!this.isMobile) {
      this.map.on('pointermove', this.handlePointerMove.bind(this));
    }
  }
  disableEditing(): void {
    if (this.interaction) {
      this.map.removeInteraction(this.interaction);
      this.interaction = null;
    }
    // 移除点击监听器
    unByKey(this.map.on('dblclick', this.handleDbClick.bind(this)));
    if (!this.isMobile) {
      unByKey(this.map.on('pointermove', this.handlePointerMove.bind(this)));
    }

    // 更新样式以表示不可编辑
    // this.updateNonEditableStyles();
  }
  enableEditing(): void {
    if (!this.interaction) {
      this.interaction = new PointerInteraction({
        handleDownEvent: this.handleDownEvent.bind(this),
        handleDragEvent: this.handleDragEvent.bind(this),
        handleMoveEvent: this.handleMoveEvent.bind(this),
        handleUpEvent: this.handleUpEvent.bind(this),
      });
      this.map.addInteraction(this.interaction);
      this.map.on('dblclick', this.handleDbClick.bind(this));
    }

    // 添加双击监听器

    if (!this.isMobile) {
      this.map.on('pointermove', this.handlePointerMove.bind(this));
    }

    // 更新样式以表示可编辑
    this.updateEditableStyles();
  }

  private updateEditableStyles(): void {
    // 实现更新样式的逻辑，以表示可编辑状态
    // 例如，可以更改控制点的颜色或大小
    if (this.editLayer) {
      this.editLayer.setStyle((feature) => {
        if (feature.get('type') === 'control') {
          return this.controlPointStyle;
        } else {
          return this.midPointStyle;
        }
      });
    }
  }
  protected handleMidPointMove(
    feature: Feature<Point>,
    newPosition: Coordinate,
  ): void {
    const index = feature.get('index') as number;
    const coordinates = this.polyline.getCoordinates();

    // 更新中间点的位置
    feature.getGeometry()?.setCoordinates(newPosition);

    // 在原有的控制点之间插入新的控制点
    coordinates.splice(index + 1, 0, newPosition);
    this.polyline.setCoordinates(coordinates);

    // 更新控制点和中间点
    this.updatePoints();

    // 触发变更回调
    this.onChange?.({
      length: getLength(this.polyline, {
        projection: 'EPSG:4326',
      }),
      controlPoints: coordinates,
    });
  }
  setControlPointStyles(styles: Partial<ControlPointStyles>): void {
    if (styles.normalPoint) {
      this.controlPointStyle = this.createPointStyle(styles.normalPoint);
    }
    if (styles.midPoint) {
      this.midPointStyle = this.createPointStyle(styles.midPoint);
    }
    if (styles.startPoint) {
      this.startPointStyle = this.createPointStyle(styles.startPoint);
    }
    if (styles.endPoint) {
      this.endPointStyle = this.createPointStyle(styles.endPoint);
    }

    // 更新编辑图层的样式
    if (this.editLayer) {
      this.editLayer.changed();
    }
  }

  private createPointStyle(style: PointStyle): Style {
    return new Style({
      image: new CircleStyle({
        radius: style.radius || 6,
        fill: new Fill({ color: style.fillColor || 'red' }),
        stroke: new Stroke({
          color: style.strokeColor || 'white',
          width: style.strokeWidth || 2,
        }),
      }),
    });
  }
  removeFromMap(): void {
    this.disableEditing();
    if (this.editLayer) {
      this.map.removeLayer(this.editLayer);
      this.editLayer = null;
    }
    if (this.polylineLayer) {
      this.map.removeLayer(this.polylineLayer);
      this.polylineLayer = null;
    }
  }
  removePolylineLayer(): void {
    if (this.polylineLayer) {
      this.map.removeLayer(this.polylineLayer);
      this.polylineLayer = null;
    }
  }
  private updateNonEditableStyles(): void {
    const nonEditableStyle = new Style({
      image: new CircleStyle({
        radius: 4,
        fill: new Fill({ color: 'gray' }),
        stroke: new Stroke({ color: 'darkgray', width: 1 }),
      }),
    });

    if (this.editLayer) {
      this.editLayer.setStyle(nonEditableStyle);
    }
  }
  private updateStyles(): void {
    const controlPointRadius = this.isMobile ? 8 : 6;
    const midPointRadius = this.isMobile ? 6 : 4;

    this.controlPointStyle = new Style({
      image: new CircleStyle({
        radius: controlPointRadius,
        fill: new Fill({ color: 'white' }),
        stroke: new Stroke({ color: '#1A1A1A', width: 5 }),
      }),
    });

    this.midPointStyle = new Style({
      image: new CircleStyle({
        radius: midPointRadius,
        fill: new Fill({ color: 'white' }),
        stroke: new Stroke({ color: '#1A1A1A', width: 4 }),
      }),
    });
  }
  private handlePointerMove(evt: BaseEvent): void {
    const mapEvent = evt as any;
    const feature = this.map.forEachFeatureAtPixel(
      mapEvent.pixel,
      (feature) => feature,
      {
        hitTolerance: 10, // 增加点击容差
      },
    ) as Feature<Point> | undefined;

    if (
      feature &&
      feature.get('name') !== 'marker' &&
      feature.get('id') !== 'baseLine'
    ) {
      if (feature.get('point') === 'start') {
        feature.setStyle(this.startPointSelectedStyle);
      } else if (feature.get('point') === 'end') {
        feature.setStyle(this.endPointSelectedStyle);
      }
    }

    this.map.render(); // 强制重新渲染以更新样式
  }
  protected updatePoints(): void {
    if (!this.editLayer) return;

    const source = this.editLayer.getSource();
    if (!source) return;

    source.clear();

    const coordinates = this.polyline.getCoordinates();
    this.controlPoints = coordinates.map((coord, index) => {
      const feature = new Feature({
        geometry: new Point(coord),
        type: 'control',
        index: index,
      });
      if (index === 0) {
        feature.setStyle(this.startPointStyle);
        feature.set('point', 'start');
      } else if (index === coordinates.length - 1) {
        feature.setStyle(this.endPointStyle);
        feature.set('point', 'end');
      }
      source.addFeature(feature);
      return feature;
    });

    this.createMidPoints(coordinates, source);
  }
  private addInteraction(): void {
    this.interaction = new PointerInteraction({
      handleDownEvent: this.handleDownEvent.bind(this),
      handleDragEvent: this.handleDragEvent.bind(this),
      handleMoveEvent: this.handleMoveEvent.bind(this),
      handleUpEvent: this.handleUpEvent.bind(this),
    });
    this.map.addInteraction(this.interaction);

    // 添加双击监听器
    this.map.on('dblclick', this.handleDbClick.bind(this));
  }
  private handleDbClick(evt: BaseEvent): void {
    const mapEvent = evt as any;
    const feature = this.map.forEachFeatureAtPixel(
      mapEvent.pixel,
      (feature) => feature,
    ) as Feature<Point> | undefined;
    if (feature && feature.get('type') === 'control') {
      const index = feature.get('index');
      // 检查是否为起点或终点
      if (index > 0 && index < this.controlPoints.length - 1) {
        this.removePoint(feature);
      }
    }
  }
  private handleDownEvent(evt: BaseEvent): boolean {
    const mapEvent = evt as any;
    const feature = this.map.forEachFeatureAtPixel(
      mapEvent.pixel,
      (feature) => feature,
      {
        hitTolerance: 10, // 增加点击容差
      },
    ) as Feature<Point> | undefined;
    if (
      feature &&
      (feature.get('type') === 'control' || feature.get('type') === 'mid')
    ) {
      this.dragFeature = feature;
      return true;
    }
    return false;
  }

  protected handleDragEvent(evt: BaseEvent): void {
    const mapEvent = evt as any;
    if (this.dragFeature) {
      const geometry = this.dragFeature.getGeometry();
      if (geometry) {
        geometry.setCoordinates(mapEvent.coordinate);

        if (this.dragFeature.get('type') === 'mid') {
          this.convertMidToControlPoint(this.dragFeature);
        } else {
          // 如果拖动的是控制点，只更新相邻的中间点
          this.updateAdjacentMidPoints(this.dragFeature);
        }

        this.updatePolyline();
      }
    }
  }

  private handleMoveEvent(evt: BaseEvent): void {
    const mapEvent = evt as any;
    const feature = this.map.forEachFeatureAtPixel(
      mapEvent.pixel,
      (feature) => feature,
    );
    const element = this.map.getTargetElement();
    if (element) {
      element.style.cursor = feature ? 'pointer' : '';
    }
  }

  private handleUpEvent(evt: BaseEvent): boolean {
    if (this.dragFeature) {
      this.dragFeature = null;
      this.updatePoints();
      this.updatePolylineData();
      return true;
    }
    return false;
  }
  private updatePolylineData(): void {
    if (this.onChange) {
      const length =
        getLength(this.polyline, {
          projection: 'EPSG:4326',
        }) / 1000; // 转换为公里
      const controlPoints = this.controlPoints.map((feature) => {
        const coord = feature.getGeometry()?.getCoordinates();
        return coord ? coord : [];
      });
      this.onChange({ length, controlPoints });
    }
  }

  private updatePolyline(): void {
    const coordinates = this.controlPoints
      .map((feature) => feature.getGeometry()?.getCoordinates())
      .filter((coord) => coord !== undefined) as Coordinate[];
    this.polyline.setCoordinates(coordinates);
  }
  private convertMidToControlPoint(midFeature: Feature<Point>): void {
    const index = midFeature.get('index');
    const coordinates = this.polyline.getCoordinates();
    const newCoord = midFeature.getGeometry()?.getCoordinates();

    if (newCoord) {
      coordinates.splice(index + 1, 0, newCoord);
      this.polyline.setCoordinates(coordinates);

      midFeature.set('type', 'control');
      midFeature.set('index', index + 1);

      // 更新控制点数组
      this.controlPoints.splice(index + 1, 0, midFeature);

      // 更新其他控制点的索引
      for (let i = index + 2; i < this.controlPoints.length; i++) {
        this.controlPoints[i].set('index', i);
      }

      // 从中间点数组中移除这个点
      this.midPoints.splice(index, 1);

      // 更新其他中间点的索引
      for (let i = index; i < this.midPoints.length; i++) {
        this.midPoints[i].set('index', i);
      }

      // 创建新的中间点
      this.createMidPoints(coordinates, this.editLayer?.getSource()!);
    }
  }
  private updateAdjacentMidPoints(controlFeature: Feature<Point>): void {
    const index = controlFeature.get('index');
    const coordinates = this.polyline.getCoordinates();

    // 更新前一个中间点（如果存在）
    if (index > 0) {
      const prevMid = this.midPoints[index - 1];
      const prevCoord = coordinates[index - 1];
      const currentCoord = coordinates[index];
      const midCoord: Coordinate = [
        (prevCoord[0] + currentCoord[0]) / 2,
        (prevCoord[1] + currentCoord[1]) / 2,
      ];
      prevMid.getGeometry()?.setCoordinates(midCoord);
    }

    // 更新后一个中间点（如果存在）
    if (index < coordinates.length - 1) {
      const nextMid = this.midPoints[index];
      const currentCoord = coordinates[index];
      const nextCoord = coordinates[index + 1];
      const midCoord: Coordinate = [
        (currentCoord[0] + nextCoord[0]) / 2,
        (currentCoord[1] + nextCoord[1]) / 2,
      ];
      nextMid.getGeometry()?.setCoordinates(midCoord);
    }
  }
  private createMidPoints(
    coordinates: Coordinate[],
    source?: VectorSource,
  ): void {
    // 移除旧的中间点
    this.midPoints.forEach((midPoint) => {
      source?.removeFeature(midPoint);
    });
    this.midPoints = [];

    for (let i = 0; i < coordinates.length - 1; i++) {
      const start = coordinates[i];
      const end = coordinates[i + 1];
      const mid: Coordinate = [
        (start[0] + end[0]) / 2,
        (start[1] + end[1]) / 2,
      ];
      const feature = new Feature({
        geometry: new Point(mid),
        type: 'mid',
        index: i,
      });
      source?.addFeature(feature);
      this.midPoints.push(feature);
    }
  }

  protected removePoint(feature: Feature<Point>): void {
    const index = feature.get('index');
    const coordinates = this.polyline.getCoordinates();
    if (coordinates.length > 3 && index > 0 && index < coordinates.length - 1) {
      coordinates.splice(index, 1);
      this.polyline.setCoordinates(coordinates);
      this.updatePoints();
      this.updatePolylineData();
    }
  }
}

export default PolylineEditor;
