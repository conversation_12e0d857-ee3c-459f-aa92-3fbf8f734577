import React, { useState, useEffect } from 'react';
import { RootState } from '@/redux/store';
import { useSelector, useDispatch } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { removeSearchValues } from '@/redux/reducer/searchForm';
import { Menu } from 'antd';
import { sendGlobalEvent } from '@/utils/emit';
import { MenuClstag } from '@/layouts/menuInfo';
import { customReport } from '@/utils/utils';

interface MenuItem {
  code: string;
  path: string;
  openType?: 'new' | 'iframe' | 'now';
  type: string;
}
const CommonMenu = () => {
  const location = useLocation();
  const dispatch = useDispatch();
  const navigator = useNavigate();
  const { menuData, resourceList } = useSelector(
    (state: RootState) => state.common,
  );
  const [current, setCurrent] = useState<string>('');
  const [openKeys, setOpenKeys] = useState<string[]>([]);

  const curPath =
    location.pathname === '/app/commoniframe'
      ? location.search.split('?')[1].split('=')[1]
      : location.pathname.split('/').splice(0, 3).join('/');

  useEffect(() => {
    const cb = (event) => {
      if (event.data.eventName === 'CHANGE_MENU') {
        navigator(`/app/commoniframe?iframeurl=${event.data.data}`);
        sessionStorage.setItem('iframeUrl', event.data.data);
      }
    };
    window.addEventListener('message', cb, false);
    return () => {
      window.removeEventListener('message', cb);
    };
  }, []);
  useEffect(() => {
    if (resourceList?.length > 0 && curPath) {
      const item: any = resourceList.find((val: any) => val.path === curPath);
      if (!item) return;
      setCurrent(item.code);
      const part = item?.code.split('_');
      const k = part.splice(0, part.length - 1).join('_');
      item?.code && setOpenKeys([k].concat(openKeys));
    }
  }, [curPath, resourceList]);

  const onClick = (val) => {
    dispatch(removeSearchValues(null));
    const resource: any = resourceList.find(
      (item: MenuItem) => item.code === val.key,
    );
    if (!resource) return;
    const clstag = MenuClstag.get(resource.code);
    if (clstag) {
      customReport(clstag);
    }

    switch (resource.openType) {
      case 'new':
        window.open(val.key, '_blank');
        return;

      case 'iframe':
        navigator(`/app/commoniframe?iframeurl=${val.key}`);
        sessionStorage.setItem('iframeUrl', val.key);
        sendGlobalEvent('forUpdateIframe', val.key);
        return;

      default:
        if (val.key.split('/')[1] === 'ota') {
          (window as any)._QIANKUN_SDK_?.event.emit(
            'change-microApp-menu',
            'ota',
          );
        }
        if (resource.path.includes('/')) {
          navigator(resource.path);
        }
    }
  };

  const onOpenChange = (keys) => {
    setOpenKeys(keys);
  };

  return (
    <Menu
      onClick={onClick}
      onOpenChange={onOpenChange}
      mode="inline"
      items={menuData}
      selectedKeys={[current]}
      openKeys={openKeys}
    />
  );
};

export default CommonMenu;
