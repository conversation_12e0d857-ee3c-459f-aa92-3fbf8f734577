import React, { useState, useEffect } from 'react';
import { RootState } from '@/redux/store';
import { useSelector, useDispatch } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { removeSearchValues } from '@/redux/reducer/searchForm';
import { Menu } from 'antd';
import { sendGlobalEvent } from '@/utils/emit';
import { MenuClstag } from '@/layouts/mockmenu';
import { customReport } from '@/utils/utils';

const CommonMenu = () => {
  const location = useLocation();
  const dispatch = useDispatch();
  const router = useLocation();
  const { menuData, appResourceInfoList } = useSelector(
    (state: RootState) => state.common,
  );
  const navigator = useNavigate();
  const [current, setCurrent] = useState<string>('');
  const [openKeys, setOpenKeys] = useState<string[]>([]);

  const curPath =
    location.pathname === '/app/commoniframe'
      ? location.search.split('?')[1].split('=')[1]
      : location.pathname.split('/').splice(0, 3).join('/');

  useEffect(() => {
    window.addEventListener(
      'message',
      (event) => {
        if (event.data.eventName === 'CHANGE_MENU') {
          navigator(`/app/commoniframe?iframeurl=${event.data.data}`);
          sessionStorage.setItem('iframeUrl', event.data.data);
        }
      },
      false,
    );
  }, []);
  useEffect(() => {
    if (appResourceInfoList.length > 0 && curPath) {
      setCurrent(curPath);
      const item: any = appResourceInfoList.find(
        (val: any) => val.path === curPath,
      );
      item?.resourceCode &&
        setOpenKeys([...item?.resourceCode.split(':'), ...openKeys]);
    }
  }, [curPath, appResourceInfoList]);

  const onClick = (val) => {
    dispatch(removeSearchValues(null));
    const resource: any = appResourceInfoList.find(
      (item: any) => item.path === val.key,
    );
    MenuClstag.get(resource.resourceCode) &&
      customReport(MenuClstag.get(resource.resourceCode));
    if (resource?.openType === 'new') {
      window.open(val.key, '_blank');
      return;
    }
    if (resource?.openType === 'iframe') {
      navigator(`/app/commoniframe?iframeurl=${val.key}`);
      sessionStorage.setItem('iframeUrl', val.key);
      sendGlobalEvent('forUpdateIframe', val.key);
      return;
    }
    if (val.key.split('/')[1] === 'ota') {
      (window as any)._QIANKUN_SDK_.event.emit('change-microApp-menu', 'ota');
    }
    val.key.indexOf('/') > -1 && navigator(val.key);
  };

  const onOpenChange = (keys) => {
    setOpenKeys(keys);
  };

  return (
    <Menu
      onClick={onClick}
      onOpenChange={onOpenChange}
      mode="inline"
      items={menuData}
      selectedKeys={[current]}
      openKeys={openKeys}
    />
  );
};

export default CommonMenu;
