// eslint-disable-next-line no-unused-vars
import { Cascader, Form, FormInstance, Select, Tag } from 'antd';
import React, { useEffect, useState } from 'react';
import { useCommonDropDown } from '@/utils/hooks';
import { CommonApi, StationFetchApi } from '@/fetch/business';
import './index.scss';
import { HttpStatusCode } from '@/fetch/core/constant';
import { PageType } from '../../utils/constant';
import { STATION_USECASE } from '@/utils/constant';

interface Props {
  form: FormInstance;
  origin?: any;
  noOwnerUseCase?: boolean;
  tagList: {
    id: number;
    number: string;
    typeName?: string;
  }[];
  handleDivide: (params: any) => void;
  handleStationChange?: (params: any) => void;
}
const commonFetch = new CommonApi();
const DivideForm = (props: Props) => {
  const fetchApi = new StationFetchApi();
  const { tagList, origin } = props;
  const [stationList, setStationList] = useState();
  const [tagData, setTagData] = useState<any>([]);
  const dropdownData = useCommonDropDown([
    'STATION_USE_CASE',
    'VEHICLE_OWNER_USE_CASE',
  ]);
  const makeSelectOptions = (item: []) => {
    if (item) {
      return item.map((value: any) => {
        return { label: value.name, value: value.code };
      });
    }
    return [];
  };
  const getDepartmentList = async (type: string, origin: any) => {
    let res;
    switch (origin) {
      case PageType.productBindStation:
        res = await commonFetch.getStationDepartment({
          stationUseCaseList: [STATION_USECASE.PRODUCTION_FACTORY],
        });
        break;
      case PageType.scheduleChangeStation:
        res = await commonFetch.getStationDepartment({
          stationUseCaseList: [STATION_USECASE.SCHEDULE_CENTER],
        });
        break;
      case PageType.scheduleReceiveStation:
        res = await commonFetch.getStationDepartment({
          stationUseCaseList: [STATION_USECASE.SCHEDULE_CENTER],
        });
        break;
      case PageType.scheduleDeliverStation:
        res = await commonFetch.getStationDepartment({
          stationUseCaseList: [
            STATION_USECASE.CAMPUS_OPERATION,
            STATION_USECASE.DEMONSTRATE,
            STATION_USECASE.OPEN_OPERATION,
            STATION_USECASE.OUTER_MARKER,
            STATION_USECASE.PARK_DELIVERY,
            STATION_USECASE.QA,
            STATION_USECASE.TIMELY_DELIVERY,
          ],
        });
        break;
      case PageType.usingChangeStation:
        res = await commonFetch.getStationDepartment({
          stationUseCaseList: [
            STATION_USECASE.CAMPUS_OPERATION,
            STATION_USECASE.DEMONSTRATE,
            STATION_USECASE.OPEN_OPERATION,
            STATION_USECASE.OUTER_MARKER,
            STATION_USECASE.PARK_DELIVERY,
            STATION_USECASE.QA,
            STATION_USECASE.TIMELY_DELIVERY,
          ],
        });
        break;
      case PageType.repair:
        res = await commonFetch.getStationDepartment({
          stationUseCaseList: [STATION_USECASE.FIX_CENTER],
        });
        break;
    }

    if (res.code === HttpStatusCode.Success) {
      setStationList(res.data);
    }
  };

  const onClose = (e: any, id: any) => {
    const newTag: any = [];
    tagData.map((item: any) => {
      if (item.id !== id) {
        newTag.push(item);
      }
    });
    props.handleDivide(newTag);
    setTagData(newTag);
  };
  useEffect(() => {
    setTimeout(() => {
      getDepartmentList('station', origin);
    }, 500);
  }, [origin]);

  useEffect(() => {
    setTagData(tagList);
  }, [JSON.stringify(tagList)]);

  const getVehicleOwnerUseCase = (origin) => {
    switch (origin) {
      case PageType.productBindStation:
        return 'PRODUCT';
      case PageType.scheduleChangeStation:
        return 'SCHEDULE';
      case PageType.repair:
        return 'FIX';
      case PageType.scheduleReceiveStation:
        return 'SCHEDULE';
      default:
        return null;
    }
  };

  const getStationUseCase = async (values) => {
    if (values) {
      const res = await fetchApi.fetchStationBasicInfo(Number(values[2]));
      if (res.code === HttpStatusCode.Success) {
        props.form.setFieldValue('useCase', res.data.useCase);
      }
    }
  };

  return (
    <div className="basic-info">
      <Form form={props.form} className="reform">
        <Form.Item
          label={`当前选择车辆(${tagList && tagList.length})`}
          rules={[{ required: true, message: '当前选择车辆不能为空' }]}
        >
          {tagList &&
            tagList.map((item: any) => {
              return (
                <Tag
                  key={item.id}
                  closable={tagData && tagData.length > 1 ? true : false}
                  onClose={(e) => onClose(e, item.id)}
                >
                  {item.deviceName}
                </Tag>
              );
            })}
        </Form.Item>
        <Form.Item
          name="stationInfo"
          label="站点名称"
          rules={[{ required: true, message: '站点不能为空' }]}
        >
          <Cascader
            fieldNames={{
              label: 'name',
              value: 'id',
              children: 'children',
            }}
            options={stationList}
            placeholder={'请选站点'}
            showSearch={true}
            allowClear={true}
            getPopupContainer={(triggerNode) => triggerNode.parentElement}
            showCheckedStrategy={Cascader.SHOW_CHILD}
            onChange={(values, selectOptions) => {
              getStationUseCase(values);
            }}
          />
        </Form.Item>
        <Form.Item name="useCase" label="站点用途">
          <Select
            placeholder="根据所选站点系统自动带入"
            options={makeSelectOptions(dropdownData.stationUseCaseList)}
            disabled
          />
        </Form.Item>

        <Form.Item
          label="车辆归属方"
          name="ownerUseCase"
          initialValue={getVehicleOwnerUseCase(origin)}
          rules={[{ required: true, message: '车辆归属方不能为空' }]}
        >
          <Select
            disabled={
              origin === PageType.productBindStation ||
              origin === PageType.scheduleChangeStation ||
              origin === PageType.scheduleReceiveStation ||
              origin === PageType.repair
                ? true
                : false
            }
            placeholder="请选择车辆归属方"
            showSearch={true}
            options={makeSelectOptions(dropdownData.vehicleOwnerUseCaseList)}
          />
        </Form.Item>
      </Form>
    </div>
  );
};
export default React.memo(DivideForm);
