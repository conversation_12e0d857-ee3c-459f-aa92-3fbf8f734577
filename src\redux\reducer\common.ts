import { createSlice } from '@reduxjs/toolkit';
import AllActionTypes from '../actionType';
import {
  makeFetchFailedActionType,
  makeFetchSuccessActionType,
} from '@/event/fetchActionTypeProvider';
import { HttpStatusCode } from '@/fetch/core/constant';
import { LoginState } from '@/utils/enum';
import { formatTabList } from '@/utils/utils';

const initialState = {
  menuData: [],
  hasLogin: LoginState.LOGOUT,
  realName: null,
  userName: null,
  companyName: null,
  companyNumber: null,
  resourceList: [],
  tabList: [
    {
      type: 'tab',
      resourceCode: 'vehicleProduction',
      path: 'vehicleProduction',
      openType: null,
      orderBy: 1,
    },
    {
      type: 'tab',
      resourceCode: 'vehicleSchedule',
      path: 'vehicleSchedule',
      openType: null,
      orderBy: 2,
    },
    {
      type: 'tab',
      resourceCode: 'vehicleUsing',
      path: 'vehicleUsing',
      openType: null,
      orderBy: 3,
    },
    {
      type: 'tab',
      resourceCode: 'vehicleRepairing',
      path: 'vehicleRepairing',
      openType: null,
      orderBy: 4,
    },
  ],
};

const commonState = createSlice({
  name: 'common',
  initialState,
  reducers: {
    changeUserInfo: (state, action) => {
      state.realName = action.payload.realName;
      state.userName = action.payload.userName;
      state.companyName = action.payload.companyName;
      state.companyNumber = action.payload.companyNumber;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(
        makeFetchSuccessActionType(AllActionTypes.USER_INFO),
        (state: any, action: any) => {
          const { data, code } = action.payload;
          if (code !== HttpStatusCode.Success) {
            return;
          }
          state.hasLogin = LoginState.LOGIN_SUCCESS;
          state.realName = data.realName;
          state.userName = data.userName;
          state.companyName = data.companyName;
          state.companyNumber = data.companyNumber;
          state.tabList = formatTabList(data.resourceList);
          state.resourceList = data.resourceList;
          state.menuData = data.menuData;
        },
      )
      .addCase(
        makeFetchSuccessActionType(AllActionTypes.AUTHENTICATE_LOGIN),
        (state: any, action: any) => {
          if (action.payload.code === HttpStatusCode.Success) {
            state.hasLogin = LoginState.LOGIN_SUCCESS;
          }
        },
      )
      .addCase(
        makeFetchFailedActionType(AllActionTypes.AUTHENTICATE_LOGIN),
        (state, action: any) => {
          state.hasLogin = LoginState.LOGIN_FAILED;
        },
      );
  },
});

export const { changeUserInfo } = commonState.actions;
export default commonState.reducer;
