/* eslint-disable no-unused-vars */
/* eslint-disable require-jsdoc */
// import 'url-polyfill';
// import 'whatwg-fetch';

import { Upload, Modal, Form, FormInstance, Button, message } from 'antd';
import { CloseCircleOutlined, PlusOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import { CommonApi } from '@/fetch/business';
import { file } from '@babel/types';
import { HttpStatusCode } from '@/fetch/core/constant';
const imageMaxSize = 1024 * 1024 * 5;

const UploadVehicleTypeImages = ({
  disable,
  form,
  initValues,
}: {
  disable?: boolean;
  form: FormInstance;
  initValues?: any;
}) => {
  const getBase64 = (file: any) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });
  };

  const [uploading, setUploading] = useState(false);
  const [priview, setPriview] = useState<{
    previewVisible: boolean;
    previewImage: string;
  }>({
    previewVisible: false,
    previewImage: '',
  });
  const [removeModal, setRemoveModal] = useState<{
    show: boolean;
    file: any;
  }>({
    show: false,
    file: '',
  });
  const [uploadFailer, setUploadFailer] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);

  const handleCancel = () => setPriview({ ...priview, previewVisible: false });

  const handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPriview({
      previewImage: file.url || file.preview,
      previewVisible: true,
    });
  };

  useEffect(() => {
    if (initValues) {
      setFileList([
        ...initValues.map(({ id, key, url }: any) => {

          return {
            uid: `rc-upload-${Date.now()}-${id}`,
            key: key,
            url,
            status: 'success',
          };
        }),
      ]);
    }
  }, [initValues]);

  const uploadFiles = (file: any) => {
    if (file.size && file.size > imageMaxSize) {
      message.error('文件不能大于5Mb');
      return;
    }
  };
  const customRequest = (option: any) => {
    const reader = new FileReader();
    reader.readAsDataURL(option.file);
    reader.onloadend = async (e: any) => {
      option.onSuccess();
      setUploading(true);
      const file = option.file;
      const newFile = {
        key: file.uid,
        name: file.name,
        status: 'success',
        originFileObj: file,
      };
      fileList.pop();
      setFileList([...fileList, newFile]);
    };
  };
  useEffect(() => {
    if (fileList) {
      form.setFieldsValue({
        pictureList: fileList.map((item) => {
          return item.originFileObj;
        }),
      });
    }
  }, [fileList]);

  return (
    <>
      <Form.Item name="pictureList" label="车型图片">
        <div>
          <div style={{ color: 'blue', height: 35, marginTop: 5 }}>
            上传（最多传4张）
          </div>
          <div>
            <Upload
              accept=".png,.jpeg,.jpg"
              listType="picture-card"
              fileList={fileList}
              customRequest={customRequest}
              withCredentials
              showUploadList={{
                showRemoveIcon: !disable,
              }}
              beforeUpload={(file, fileList) => {
                uploadFiles(file);
                return file;
              }}
              onPreview={handlePreview}
              onRemove={(file) => {
                setRemoveModal({ show: true, file });
                return false;
              }}
              onChange={(info: any) => {
                const file: any = info.file;
                if (file.size && file.size > imageMaxSize) {
                  return;
                }
                if (info && !info.event) {
                  setFileList(info.fileList);
                }
              }}
            >
              {fileList.length >= 4 || disable ? null : (
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>上传</div>
                </div>
              )}
            </Upload>
            <Modal
              width={'80vw'}
              visible={priview.previewVisible}
              title="预览"
              footer={null}
              onCancel={handleCancel}
            >
              <div
                style={{
                  width: '100%',
                  maxHeight: '70vh',
                  overflow: 'scroll',
                }}
              >
                <img
                  alt="example"
                  style={{ width: '100%' }}
                  src={priview.previewImage}
                />
              </div>
            </Modal>
            <Modal
              visible={removeModal.show}
              title="提示"
              closable={false}
              maskClosable={false}
              onCancel={() => {
                setRemoveModal({ show: false, file: null });
              }}
              onOk={() => {
                const list = fileList.filter(
                  (item: any) => item !== removeModal.file,
                );
                setFileList([...list]);
                setRemoveModal({ show: false, file: null });
              }}
            >
              <div>确认删除此图片吗？</div>
            </Modal>
            <Modal
              visible={uploadFailer}
              title="提示"
              closable={false}
              maskClosable={false}
              footer={
                <div>
                  <Button
                    type="primary"
                    onClick={() => {
                      setUploadFailer(false);
                      setFileList(
                        fileList.filter(
                          (item) =>
                            item.status === 'success' ||
                            item.status === 'uploading',
                        ),
                      );
                    }}
                  >
                    确定
                  </Button>
                </div>
              }
            >
              <div>上传失败，请重试</div>
            </Modal>
          </div>
        </div>
      </Form.Item>
    </>
  );
};

export default React.memo(UploadVehicleTypeImages);
