import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { CommonForm, CommonTable, FormConfig } from '@jd/x-coreui';
import { getShelfTypeList } from '@/fetch/business/integrate';
import { HttpStatusCode } from '@/fetch/core/constant';
import { showModal } from '@/components';
import { Popconfirm, message } from 'antd';
export interface BusinessListItem {
  name: string; // 业务名称
  code: string; // 业务标识
  shelfTypeList: {
    // 上装类型列表
    shelfTypeName: string;
    shelfTypeId: string | number;
  }[];
}
const GroupBusinessList = forwardRef(
  (
    props: {
      isEdit: boolean;
      businessList: any[];
      onChange?: (list: BusinessListItem[]) => void;
    },
    ref,
  ) => {
    const [businessList, setBusinessList] = useState<BusinessListItem[]>([]);
    const [shelfList, setShelfList] = useState<
      { label: string; value: number | string }[]
    >([]);
    const formatColumns = useMemo(() => {
      return [
        {
          title: '业务名称',
          dataIndex: 'name',
          align: 'center',
        },
        {
          title: '接口标识',
          dataIndex: 'code',
          align: 'center',
        },
        {
          title: '上装类型',
          dataIndex: 'shelfTypeList',
          align: 'center',
          render: (value: any, record: any) => {
            return value?.map((item) => item.shelfTypeName).join('；');
          },
        },
        props.isEdit && {
          title: '操作',
          dataIndex: 'operate',
          align: 'center',
          render: (value: any, record: any) => (
            <Popconfirm
              title="确认删除吗"
              trigger={'click'}
              onConfirm={remoteBussinessItem.bind(null, record)}
            >
              <a>删除</a>
            </Popconfirm>
          ),
        },
      ].filter(Boolean);
    }, [props.isEdit, JSON.stringify(businessList)]);

    const remoteBussinessItem = (data: any) => {
      const newList = businessList.filter((item) => item.code !== data.code);
      setBusinessList([...newList]);
      props.onChange && props.onChange(newList);
    };

    const addBussinessItem = () => {
      let formRef: any = null;
      const config: FormConfig = {
        fields: [
          {
            label: '业务名称',
            type: 'input',
            fieldName: 'name',
            maxLength: 20,
            placeholder: '请输入业务名称',
            validatorRules: [{ required: true, message: '请输入业务名称' }],
          },
          {
            label: '业务标识',
            type: 'input',
            fieldName: 'code',
            maxLength: 20,
            placeholder: '请输入业务标识',
            validatorRules: [{ required: true, message: '请输入业务标识' }],
          },
          {
            label: '上装类型',
            type: 'select',
            fieldName: 'shelfTypeList',
            multiple: true,
            options: shelfList,
            placeholder: '请选择上装类型',
            validatorRules: [{ required: true, message: '请选择上装类型' }],
          },
        ],
      };
      showModal({
        title: '新增业务',
        content: (
          <CommonForm
            formConfig={config}
            getFormInstance={(ref: any) => {
              formRef = ref;
            }}
          />
        ),
        footer: {
          showOk: true,
          okText: '确定',
          okFunc: async (cb) => {
            try {
              const value = await formRef.validateFields();
              const hasName = businessList?.some(
                (item) => item.name === value.name,
              );
              const hasCode = businessList?.some(
                (item) => item.code === value.code,
              );
              if (hasName) {
                message.error('操作失败，此业务名称已存在！');
                return;
              } else if (hasCode) {
                message.error('操作失败，此业务标识已存在！');
                return;
              }
              const data: any = {
                code: value?.code,
                name: value?.name,
                shelfTypeList: value?.shelfTypeList?.map((item: any) => ({
                  shelfTypeName: item.label,
                  shelfTypeId: item.value,
                })),
              };
              businessList.push(data);
              setBusinessList([...businessList]);
              props.onChange && props.onChange(businessList);
              cb();
            } catch (e) {}
          },
          showCancel: true,
          cancelText: '取消',
          cancelFunc: (cb) => {
            cb();
          },
        },
      });
    };

    const getBussinessList = () => {
      return businessList;
    };
    useEffect(() => {
      console.log(props.businessList);
      setBusinessList(props.businessList);
      props.onChange && props.onChange(props.businessList);
    }, [JSON.stringify(props.businessList)]);

    useEffect(() => {
      if (props.isEdit) {
        getShelfTypeList().then((res) => {
          if (res?.code === HttpStatusCode.Success) {
            const shelfList = res?.data?.map((item: any) => ({
              value: item.shelfTypeId,
              label: item.shelfTypeName,
            }));
            setShelfList(shelfList);
          }
        });
      }
    }, [props.isEdit]);

    useImperativeHandle(
      ref,
      () => {
        return {
          getBussinessList,
        };
      },
      [JSON.stringify(businessList)],
    );
    return (
      <CommonTable
        rowKey="code"
        columns={formatColumns}
        notPage={true}
        middleBtns={
          props.isEdit
            ? [
                {
                  title: '新建',
                  btnType: 'primary',
                  onClick: addBussinessItem,
                },
              ]
            : null
        }
        tableListData={{ list: businessList }}
      />
    );
  },
);

export default GroupBusinessList;
