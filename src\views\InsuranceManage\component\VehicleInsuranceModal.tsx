import React, { useEffect, useState } from 'react';
import { Modal } from 'antd';
import { VehicleInsuranceColumns } from '../utils/columns';
import { CommonTable } from '@jd/x-coreui';
import InsuranceManageApi, {
  DeviceInsuranceSearchForm,
} from '@/fetch/business/insuranceManage';
interface VehicleInsuranceModalProps {
  visible: boolean;
  onCancel: () => void;
  insuranceId: number | null;
  onOk: () => void;
}
const VehicleInsuranceModal = (props: VehicleInsuranceModalProps) => {
  const { visible, onCancel, insuranceId, onOk } = props;
  const initSearchCondition = {
    insuranceId: insuranceId,
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState(initSearchCondition);
  const [tableData, setTableData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  useEffect(() => {
    if (insuranceId) {
      setLoading(true);
      InsuranceManageApi.getDeviceInsurancePageList(
        searchCondition as DeviceInsuranceSearchForm,
      ).then((res) => {
        setTableData(res.data);
        setLoading(false);
      });
    } else {
      setTableData(null);
      setLoading(false);
    }
  }, [searchCondition]);
  useEffect(() => {
    if (visible) {
      setSearchCondition(initSearchCondition);
    }
  }, [visible]);
  return (
    <Modal
      title="车辆投保信息"
      open={visible}
      onCancel={onCancel}
      width={1000}
      destroyOnClose
      onOk={onOk}
    >
      <CommonTable
        columns={VehicleInsuranceColumns}
        searchCondition={searchCondition}
        onPageChange={(value: any) => {
          setSearchCondition(value);
        }}
        rowKey={'deviceName'}
        loading={loading}
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
      />
    </Modal>
  );
};

export default VehicleInsuranceModal;
