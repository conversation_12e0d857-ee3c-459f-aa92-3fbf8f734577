import { DropDownType, dropDownKey, dropDownList<PERSON>ey } from '@/utils/constant';
import { FormConfig } from '@/components';
export const tableColumns: any[] = [
  {
    title: '序号',
    dataIndex: 'appNumber',
    align: 'center',
    width: 80,
  },
  { title: '应用服务', dataIndex: 'appName', align: 'center', ellipsis: true },
  {
    title: '功能模块',
    dataIndex: 'moduleName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作类型',
    dataIndex: 'operationTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作描述',
    dataIndex: 'description',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作人员',
    dataIndex: 'operationUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'center',
    ellipsis: true,
  },
  { title: '是否正常', dataIndex: 'flagName', align: 'center', ellipsis: true },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 140,
    align: 'center',
    ellipsis: true,
    fixed: 'right',
  },
];

export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'time',
      label: '时间段',
      placeholder: '请选择模块',
      type: 'rangeTime',
      labelCol: { span: 3 },
      wrapperCol: { span: 21 },
      xxl: 8,
      xl: 12,
      lg: 16,
    },
    {
      fieldName: 'app',
      label: '应用服务',
      placeholder: '请选择应用服务',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.LOG_APP,
      dropDownListKey: dropDownListKey.LOG_APP,
    },
    {
      fieldName: 'module',
      label: '功能模块',
      placeholder: '请选择功能模块',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.LOG_MODULE,
      dropDownListKey: dropDownListKey.LOG_MODULE,
    },
    {
      fieldName: 'operationType',
      label: '操作类型',
      placeholder: '请选择操作类型',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.LOG_OPERATION_TYPE,
      dropDownListKey: dropDownListKey.LOG_OPERATION_TYPE,
    },
    {
      fieldName: 'flag',
      label: '是否正常',
      placeholder: '请选择是否正常',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.YES_OR_NO,
      dropDownListKey: dropDownListKey.YES_OR_NO,
    },
    {
      fieldName: 'uri',
      label: '接口',
      placeholder: '请输入接口名称',
      type: 'input',
    },
    {
      fieldName: 'requestParam',
      label: '入参',
      placeholder: '请输入入参',
      type: 'input',
    },
  ],
};
