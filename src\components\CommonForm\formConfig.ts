import { ReactNode } from 'react';
import { dropDownKey, dropDownListKey, DropDownType } from '@/utils/constant';
import { Cascader } from 'antd';
export interface FieldItem {
  // 通用属性
  type?:
    | 'input'
    | 'inputNumber'
    | 'select'
    | 'rangeTime'
    | 'cascader'
    | 'textarea'
    | 'radioGroup'
    | 'checkboxGroup'
    | 'datePicker'
    | 'customize'
    | 'dateTime'
    | 'switch'
    | 'upload'
    | 'ReactNode';
  renderFunc?: () => ReactNode;
  fieldName?: string;
  label?: ReactNode;
  placeholder?: string;
  hidden?: boolean; // 是否隐藏字段（依然会收集和校验字段）
  fileListType?: 'picture' | 'file';
  accept?: string;
  bucketName?: string;
  LOPDN?: string;
  getPreSignatureUrl?: string;
  maxFileSize?: number;
  unit?: 'MB' | 'GB' | 'KB';
  validatorRules?:
    | {
        // 校验规则
        required: boolean;
        message: string;
      }[]
    | any[];
  help?: ReactNode;
  labelCol?: { span: number };
  wrapperCol?: { span: number };
  childrenList?: string[]; // 多个表单项合并成一个
  width?: string;
  marginLeft?: number;
  marginRight?: number;
  disabled?: boolean;
  isChild?: boolean;
  xxl?: number;
  xl?: number;
  lg?: number;
  md?: number;
  specialFetch?: 'commonDown' | 'station' | 'city';
  departmentParams?: Object;
  dropDownKey?: dropDownKey;
  dropDownListKey?: dropDownListKey;

  // switch
  defaultChecked?: boolean;

  // 输入框
  maxLength?: number; // 输入框长度限制
  autoSize?: boolean | object; // textArea自适应内容高度
  showCount?: boolean;

  // 选择: 单多选、级联、单复选
  options?: any[];
  showSearch?: boolean; // 是否支持搜索
  allowClear?: boolean;
  maxTagCount?: number;
  multiple?: boolean;
  showSelectAll?: boolean;
  mapRelation?: object;
  changeOnSelect?: boolean;
  labelInValue?: boolean;
  showCheckedStrategy?: 'SHOW_PARENT' | 'SHOW_CHILD';
  min?: number;
  max?: number;
}

export interface FormConfig {
  fields: FieldItem[];
  linkRules?: {
    [fieldName: string]: {
      // 变化的表单项
      linkFieldName: string; // 表单项变化后导致哪些表单项联动变化
      rule:
        | 'fetchData'
        | 'clear'
        | 'refresh'
        | 'visible'
        | 'valueDisable'
        | 'fieldItemDisable'; // 当前元素要发生什么变化 refresh->更新 visible->是否展示 dataEcho->数据回显 valueDisable->数据不可用 fieldItemDisable->元素不可用
      dependenceData?: any[]; // 依赖元素的值变为哪个时当前元素发生变化
      disabledValue?: any[]; // rule为valueDisable时，哪个值不可用
      fetchFunc?: Function;
    }[];
  };
}
