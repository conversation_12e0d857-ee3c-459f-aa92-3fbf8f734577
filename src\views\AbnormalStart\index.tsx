import React, { useState, useEffect } from 'react';
import { CommonTable, CommonForm, TableOperateBtn } from '@/components';
import { searchConfig, tableColumns } from './utils/columns';
import { useTableData } from '@/components/CommonTable/useTableData';
import { AbnormalStartApi } from '@/fetch/business';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/store';
import { useNavigate } from 'react-router-dom';
import { ProductType, YESNO } from '@/utils/enum';
import { saveSearchValues } from '@/redux/reducer/searchForm';
import { HttpStatusCode } from '@/fetch/core/constant';
import { message, Popconfirm } from 'antd';
import { PageType } from '@/utils/EditTitle';
import { TableListType } from '@/utils/constant';

const AbnormalStart = () => {
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const fetchApi = new AbnormalStartApi();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const initSearchCondition = {
    searchForm: {
      device: null,
      module: null,
      moduleField: null,
      moduleFieldName: null,
      principalErp: null,
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValues.searchValues
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [tableData, setTableData] = useState<TableListType>({
    list: [],
    total: 0,
    pages: 0,
  });

  useEffect(() => {
    getTableList();
  }, [searchCondition]);

  const middleBtns: any[] = [
    {
      show: true,
      title: '新建',
      onClick: () => handleEdit(PageType.ADD),
    },
  ];
  const formateColumns = () => {
    return tableColumns.map((col) => {
      switch (col.dataIndex) {
        case 'rowIndex':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              const { pageSize, pageNum } = searchCondition;
              return <div>{(pageNum - 1) * pageSize + index + 1}</div>;
            },
          };
        case 'operation':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return (
                <div className="operate">
                  <TableOperateBtn
                    title="编辑"
                    handleClick={() => handleEdit(PageType.EDIT, record.number)}
                  />
                  <Popconfirm
                    placement="left"
                    title={'请确认删除模块吗？'}
                    onConfirm={async () => {
                      const res = await fetchApi.delException(record.number);
                      if (res.code === HttpStatusCode.Success) {
                        message.success(res.message);
                        getTableList();
                      } else {
                        message.error(res.message);
                      }
                    }}
                    okText="确定"
                    cancelText="取消"
                    overlayStyle={{ maxWidth: 800 }}
                  >
                    <a>删除</a>
                  </Popconfirm>
                </div>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  const getTableList = async () => {
    try {
      setLoading(true);
      const params = {
        searchForm: {
          device: searchCondition.searchForm.device?.value,
          module: searchCondition.searchForm.module?.value,
          moduleField: searchCondition.searchForm.moduleField,
          moduleFieldName: searchCondition.searchForm.moduleFieldName,
          principalErp: searchCondition.searchForm.principalErp,
        },
        pageNum: searchCondition.pageNum,
        pageSize: searchCondition.pageSize,
      };
      const res: any = await fetchApi.fetchTableList(params);
      if (res.code === HttpStatusCode.Success) {
        setTableData({
          list: res.data.list,
          total: res.data.total,
          pages: res.data.pages,
        });
      }
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (type: PageType, id?: number) => {
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: searchCondition,
      }),
    );
    navigator(
      id
        ? `/app/abnormalStart/edit?type=${type}&id=${id}`
        : `/app/abnormalStart/edit?type=${type}`,
    );
  };

  const onSearchClick = (val) => {
    const data = {
      searchForm: val,
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(data);
  };
  return (
    <>
      <CommonForm
        formConfig={searchConfig}
        defaultValue={searchCondition.searchForm}
        layout="inline"
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={() => setSearchCondition({ ...initSearchCondition })}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        columns={formateColumns()}
        loading={loading}
        rowKey={'id'}
        middleBtns={middleBtns}
        searchCondition={searchCondition}
        onPageChange={(value: any) => setSearchCondition(value)}
      />
    </>
  );
};

export default React.memo(AbnormalStart);
