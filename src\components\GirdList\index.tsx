/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import EditModuleTitle from '@/components/EditModuleTitle';
import GridTableList from '@/components/Grid/ListInfo';
import './index.scss';
import { Form, FormInstance, Col, Row, message } from 'antd';
import Config from './Config';
import './index.scss';
import CustomButton, { ButtonType } from '@/components/CustomButton';
import PreviewGrid from '@/components/Grid/PreviewGrid';
import { setGridListAction } from '@/redux/reducer/commonData';

const layout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 20 },
};
const GirdList = ({
  gridList,
  formRef,
  disabled,
  moduleTitle,
  previewClick,
  viewType,
  isShowCarImg,
}: {
  gridList: any;
  formRef: FormInstance;
  disabled?: boolean;
  moduleTitle: string;
  previewClick?: Function;
  viewType?: any;
  isShowCarImg?: boolean;
}) => {
  const dispatch = useDispatch();
  const [isShowPreBtn, setIsShowPreBtn] = useState(false);
  const [isShowImg, setIsShowImg] = useState<any>(false);
  useEffect(() => {
    setIsShowPreBtn(
      gridList && gridList.length !== 0 && viewType != 'readOnly',
    );
    formRef.setFieldsValue({
      gridList: gridList,
    });
    if (viewType == 'readOnly') {
      dispatch(setGridListAction(gridList));
    }
  }, [gridList]);

  useEffect(() => {
    setIsShowImg(isShowCarImg);
  }, [isShowCarImg]);

  return (
    <div>
      <EditModuleTitle title={moduleTitle} />
      <Config disabled={viewType == 'readOnly'}></Config>
      <Form {...layout} form={formRef}>
        <Form.Item name="gridList" label=" " colon={false}>
          <GridTableList
            viewType={viewType}
            disabled={disabled}
            initValues={gridList}
            onGridChanged={(values: any) => {
              formRef.setFieldsValue({
                gridList: values,
              });
              setIsShowPreBtn(values.length !== 0 && viewType != 'readOnly');
            }}
          />
        </Form.Item>
      </Form>
      {/* 生成效果图按钮 */}
      {isShowPreBtn ? (
        <Row>
          <Col span={20} offset={3}>
            <CustomButton
              title="生成货箱效果图"
              buttonType={ButtonType.DefaultButton}
              otherCSSProperties={{
                marginBottom: '20px',
                border: '1px solid #31C2A6',
              }}
              onSubmitClick={() => {
                if (previewClick && previewClick()) {
                  let msg = '货箱效果图已更新';
                  if (!isShowImg) {
                    setIsShowImg(true);
                    msg = '货箱效果图已生成';
                  }
                  message.success(msg);
                }
              }}
            />
          </Col>
        </Row>
      ) : null}
      {/* 效果图展示部分 */}
      {isShowImg ? (
        <Row>
          <Col span={20} offset={3}>
            <PreviewGrid></PreviewGrid>
          </Col>
        </Row>
      ) : null}
    </div>
  );
};

export default React.memo(GirdList);
