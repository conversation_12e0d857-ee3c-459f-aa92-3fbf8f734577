import React, { forwardRef } from 'react';
import { Switch } from 'antd';

interface Props {
  mapName: string;
  mapBusiness: any[];
  showBusiness: string[];
  switchDisable: boolean;
  handleChangeBusiness: (item: any, checked: boolean) => void;
}

const MapUI = forwardRef((props: Props, ref: any) => {
  const {
    mapName,
    mapBusiness,
    showBusiness,
    switchDisable,
    handleChangeBusiness,
  } = props;
  
  return (
    <div className="robot-map-container" ref={ref}>
      <p className="map-name">{mapName}</p>
      <div className="change-business">
        <p>显示业务参考系数:</p>
        {mapBusiness.map((item) => {
          return (
            <div className="business-icon" key={item.value}>
              <p>{item.label}</p>
              <Switch
                defaultChecked={showBusiness.includes(item.value)}
                disabled={switchDisable}
                onChange={(checked: boolean) =>
                  handleChangeBusiness(item, checked)
                }
              />
            </div>
          );
        })}
      </div>
    </div>
  );
});

export default React.memo(MapUI);
