import { CommonTableRequest } from '@/types';

export const RepairOrderErrorCodeMap = new Map<string, string>([
  ['A4002', '获取维修单信息失败'],
  ['A4003', '当前维修单状态不能进行此操作'],
  ['B1019', '服务端错误获取车辆详情'],
  ['FFFF', '提交失败，仅车辆生命周期"待交付、已交付、维修中"才可提交维修！'],
]);

export enum repairOrderStatus {
  TO_BE_ACCEPTED = 1, // 待受理
  ACCEPTED = 2, // 已受理
  NOT_ACCEPTED = 3, // 不受理
  REPAIRING = 4, // 维修中
  TO_BE_CONFIRMED = 5, // 待确认
  COMPLETED = 6, // 已完成
}

/** 维修单列表请求 */
export interface RepairOrderRequest extends CommonTableRequest {
  searchForm: {
    /** 维修单号 */
    number?: string | null;
    /** 车牌号 */
    deviceName?: string | null;
    /** 联系人 */
    reportErp?: string | null;
    /** 提报开始时间 */
    reportStartTime?: string | null;
    /** 提报结束时间 */
    reportEndTime?: string | null;
    /** 维修单状态 */
    status?:
      | number[]
      | {
          label?: string;
          value: number;
        }[];
    /** 是否影响运营 */
    isInfluenceOperation?: number | null;
    /** 站点基础信息表id */
    stationBaseId?: string | null;
  };
}

/** 维修单列表响应 */
export interface RepairOrderResponse {
  /** id */
  id: number;
  /** 维修单号 */
  number: string;
  /** 车辆id */
  deviceBaseId: number;
  /** 车牌号 */
  deviceName: string;
  /** 车型名称 */
  deviceTypeName: string;
  /** 报修标题 */
  requireHardwareTypeNames: string;
  /** 联系人erp */
  reportErp: string;
  /** 提报时间 */
  createTime: string;
  /** 标定检测状态 */
  checkStatus: string;
  /** 标定检测名称 */
  checkStatusName: string;
  /** 维修单状态 */
  status: number;
  /** 维修单状态名称 */
  statusName: string;
  /** 服务费用 */
  cost: string;
  /** 处理时长 */
  duration: string;
  /** 最后操作时间 */
  modifyTime: string;
  /** 最后操作人 */
  modifyUser: string;
  /** 站点基础信息表id */
  stationBaseId: number;
  /** 站点名称 */
  stationName: string;
  /** 是否影响运营 */
  isInfluenceOperation: number;
  /** 是否影响运营名称 */
  isInfluenceOperationName: string;
  /** 影响运营小时数 */
  affectOperationHours: string;
  /** 影响运营天数 */
  affectOperationDays: string;
}

/** 新建维修单 */
export interface NewRepairOrder {
  /** 涉及京me小程序，此处等价于deviceName车牌号 */
  vehicleName: string;
  /** 保修硬件类型ids */
  requireHardwareTypeIds: string;
  /** 维修标题 */
  // title: string;
  /** 维修描述 */
  description: string;
  /** 联系人erp */
  reportErp: string;
  /** 联系人手机号 */
  reportPhone: string;
  /** 联系人邮箱 */
  reportEmail: string;
  /** 提报人erp */
  erp: string;
  /** 上传的图片数据 */
  pictureList?: string[];
  /** 上传的视频数据 */
  video?: string;
}

/** 维修单详情 */
export interface RepairOrderDetail {
  /** id */
  id: number;
  /** 维修单号 */
  number: string;
  /** 车辆id */
  deviceId: number;
  /** 车牌号 */
  deviceName: string;
  /** 车架号 */
  serialNo: string;
  /** 站点位置信息 */
  stationBaseInfo: string;
  /** 报修标题 */
  requireHardwareTypeNames: string;
  /** 图片文件路径 */
  pictureList: string[];
  /** 视频文件路径 */
  video: string;
  /** 报修标题 */
  // title: string;
  /** 报修描述 */
  description: string;
  /** 联系人erp */
  reportErp: string;
  /** 联系人手机号 */
  reportPhone: string;
  /** 联系人邮箱 */
  reportEmail: string;
  /** 维修单操作记录 */
  requireLog: {
    /** id */
    id: number;
    /** 维修单号 */
    requireNumber: string;
    /** 操作类型 */
    type: string;
    /** 操作类型名称 */
    typeName: string;
    /** 备注 */
    remark: string;
    /** 最后操作时间 */
    modifyTime: string;
    /** 最后操作人 */
    modifyUser: string;
  }[];
  /** 原始车辆归属方 */
  oldOwnerUseCase: string;
  /** 原始车辆归属方名称 */
  oldOwnerUseCaseName: string;
  /** 原始车辆生命周期 */
  oldHardwareStatus: string;
  /** 原始车辆生命周期名称 */
  oldHardwareStatusName: string;
  /** 工时费 */
  laborCost: string;
  /** 服务费 */
  serviceCost: string;
  /** 其他费用 */
  otherCost: string;
  /** 其他费用说明 */
  otherCostRemark: string;
  /** 费用核销单号 */
  costVerifyNumbers: string;
  /** 是否标定检测 */
  isCheck: number;
  /** 标定检测人erp */
  checkErp: string;
  /** 不校验原因 */
  notCheckRemark: string;
  /** 是否验证自动驾驶 */
  isVerifyAutoDrive: number;
  /** 验证自动驾驶人erp */
  verifyAutoDriveErp: string;
  /** 不验证自动驾驶原因 */
  notVerifyAutoDriveRemark: string;
  /** 状态 */
  status: number;
  /** 状态名称 */
  statusName: string;
  /** 维修完成备注 */
  requireCompleteRemark: string;
  /** 维修详情列表 */
  requireHardwareModelInfoList: {
    /** id */
    id: number;
    /** 维修单号 */
    requireNumber: string;
    /** 旧硬件类型id */
    oldHardwareTypeId: number;
    /** 旧的设备类型是否属于车型 */
    isOfVehicleType: number;
    /** 旧硬件型号id */
    oldHardwareModelId: number;
    /** 旧硬件型号型号 */
    oldHardwareModelModel: string;
    /** 旧硬件型号名称 */
    oldHardwareModelName: string;
    /** 新硬件型号id */
    newHardwareModelId: number;
    /** 新硬件型号型号 */
    newHardwareModelModel: string;
    /** 新硬件型号名称 */
    newHardwareModelName: string;
    /** 新硬件型号编号 */
    newHardwareModelNumber: string;
  }[];
  /** 是否影响运营 */
  isInfluenceOperation: number;
  /** 是否影响运营名称 */
  isInfluenceOperationName: string;
}

/** 维修完成请求参数 */
export interface CompleteRepairRequest {
  /** 维修单编号 */
  number: string;
  /** 是否提交 */
  isCommit: number;
  /** 工时费 */
  laborCost?: number;
  /** 服务费 */
  serviceCost?: number;
  /** 其他费用 */
  otherCost?: number;
  /** 其他费用说明 */
  otherCostRemark?: string;
  /** 费用核销单号 */
  costVerifyNumbers?: string;
  /** 是否标定检测 */
  isCheck?: number;
  /** 标定检测人erp */
  checkErp?: string;
  /** 是否验证自动驾驶 */
  isVerifyAutoDrive?: number;
  /** 验证自动驾驶人erp */
  verifyAutoDriveErp?: string;
  /** 不验证自动驾驶原因 */
  notVerifyAutoDriveRemark?: string;
  /** 备注 */
  remark?: string;
  /** 维修硬件型号信息 */
  requireHardwareModelInfoList?: {
    /** 旧硬件型号id */
    oldHardwareModelId: number;
    /** 新硬件型号id */
    newHardwareModelId: number;
    /** 新硬件型号编号 */
    newHardwareModelNumber: string;
  }[];
}
