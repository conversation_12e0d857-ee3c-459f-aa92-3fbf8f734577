import { Row, Col, Form, Radio, Input, FormInstance, InputNumber } from 'antd';
import React, { useState, useEffect } from 'react';
import './index.scss';
import EditHardwareInfo from '../EditHardwareInfo';
const { TextArea } = Input;
interface Props {
  vehicleId: number;
  form: FormInstance;
  repairRecordInfo: any;
}
enum IsCheckKey {
  checked = 1, // 标定
  uncheck = 0, // 未标定
}
enum IsVerifyAutoDrive {
  verified = 1, // 已验证
  noVerify = 0, // 未验证
}
const RepairRecord = (props: Props) => {
  const { vehicleId, form, repairRecordInfo } = props;
  const [checkRadioKey, setCheckRadioKey] = useState(null);
  const [verifyAutoDriveRadioKey, setVerifyAutoDriveRadioKey] = useState(null);

  useEffect(() => {
    setCheckRadioKey(repairRecordInfo.isCheck);
    setVerifyAutoDriveRadioKey(repairRecordInfo.isVerifyAutoDrive);
    form.setFieldsValue(repairRecordInfo);
  }, [JSON.stringify(repairRecordInfo)]);

  return (
    <div className="repair-record">
      <p className="module-title">{'维修记录'}</p>
      <Row>
        <Col span={23}>
          <Row>
            <Form form={form} labelCol={{ span: 3 }}>
              <Form.Item
                label="维修结果备注"
                rules={[{ required: true, message: '请输入维修结果备注' }]}
                name="remark"
              >
                <TextArea
                  placeholder="请输入维修结果备注"
                  autoSize={{ minRows: 3, maxRows: 3 }}
                  maxLength={200}
                />
              </Form.Item>
              <Form.Item
                name="laborCost"
                label="维修工时费(元)"
                rules={[{ required: true, message: '请输入维修工时费' }]}
                wrapperCol={{ span: 12 }}
              >
                <InputNumber
                  min={'0'}
                  placeholder="请输入费用"
                  max={'999999999999999.99'}
                  step={'0.01'}
                  precision={2}
                  style={{ width: '100%' }}
                />
              </Form.Item>
              <Form.Item
                label="上门服务费(元)"
                name="serviceCost"
                rules={[{ required: true, message: '请输入上门服务费' }]}
                wrapperCol={{ span: 12 }}
              >
                <InputNumber
                  min={'0'}
                  placeholder="请输入费用"
                  max={'999999999999999.99'}
                  step={'0.01'}
                  precision={2}
                  style={{ width: '100%' }}
                />
              </Form.Item>
              <Form.Item
                label="其他费用(元)"
                name="otherCost"
                wrapperCol={{ span: 12 }}
              >
                <InputNumber
                  min={'0'}
                  placeholder="请输入费用"
                  max={'999999999999999.99'}
                  step={'0.01'}
                  precision={2}
                  style={{ width: '100%' }}
                />
              </Form.Item>
              <Form.Item
                label="其他费用说明"
                name="otherCostRemark"
                wrapperCol={{ span: 12 }}
              >
                <Input
                  placeholder="请输入发生费用原因，最多输入50位"
                  maxLength={50}
                />
              </Form.Item>
              <Form.Item
                label="费用核销单号"
                name="costVerifyNumbers"
                wrapperCol={{ span: 12 }}
              >
                <Input
                  placeholder="请输入第三方报销单号，多个用英文“;”隔开"
                  maxLength={50}
                />
              </Form.Item>
              <Form.Item
                label="是否标定"
                name="isCheck"
                rules={[{ required: true, message: '请选择是否标定' }]}
              >
                <Radio.Group
                  onChange={(e) => {
                    setCheckRadioKey(e.target.value);
                    form.resetFields(['checkErp']);
                  }}
                >
                  <Radio value={IsCheckKey.checked} className="yes">
                    是
                  </Radio>
                  <Radio value={IsCheckKey.uncheck} className="no">
                    否
                  </Radio>
                </Radio.Group>
              </Form.Item>
              {checkRadioKey === IsCheckKey.checked && (
                <Form.Item
                  label="标定人"
                  name="checkErp"
                  rules={[{ required: true, message: '请输入姓名或erp' }]}
                  wrapperCol={{ span: 12 }}
                >
                  <Input maxLength={20} placeholder="请输入姓名或erp" />
                </Form.Item>
              )}
              <Form.Item
                label="验证自动驾驶"
                name="isVerifyAutoDrive"
                rules={[
                  { required: true, message: '请选择是否进行自动驾驶验证' },
                ]}
              >
                <Radio.Group
                  onChange={(e) => {
                    setVerifyAutoDriveRadioKey(e.target.value);
                    form.resetFields([
                      'verifyAutoDriveErp',
                      'notVerifyAutoDriveRemark',
                    ]);
                  }}
                >
                  <Radio
                    value={IsVerifyAutoDrive.verified}
                    className="verified"
                  >
                    已验证
                  </Radio>
                  <Radio
                    value={IsVerifyAutoDrive.noVerify}
                    className="noVerify"
                  >
                    未验证
                  </Radio>
                </Radio.Group>
              </Form.Item>
              {verifyAutoDriveRadioKey === IsVerifyAutoDrive.verified && (
                <Form.Item
                  label="验证人"
                  name="verifyAutoDriveErp"
                  rules={[{ required: true, message: '请输入姓名或erp' }]}
                  wrapperCol={{ span: 12 }}
                >
                  <Input maxLength={20} placeholder="请输入姓名或erp" />
                </Form.Item>
              )}
              {verifyAutoDriveRadioKey === IsVerifyAutoDrive.noVerify && (
                <Form.Item
                  label="未验证原因"
                  name="notVerifyAutoDriveRemark"
                  rules={[{ required: true, message: '请输入未验证原因' }]}
                  wrapperCol={{ span: 12 }}
                >
                  <Input maxLength={50} placeholder="请输入未验证原因" />
                </Form.Item>
              )}
              <Row style={{ marginLeft: '55px', marginBottom: '10px' }}>
                {'维修硬件信息记录（如未更换可不填写）'}
              </Row>
              <Form.Item label="" name="requireHardwareModelInfoList">
                <Row>
                  <Col push={1}>
                    <EditHardwareInfo
                      form={form}
                      vehicleId={vehicleId}
                      hardwareInfoList={repairRecordInfo.hardwareModelInfoList}
                    />
                  </Col>
                </Row>
              </Form.Item>

              <div
                style={{
                  color: '#999',
                  marginBottom: '10px',
                  paddingLeft: '25px',
                }}
              >
                注：“硬件名称”仅可带出该车的硬件信息，如果没有对应硬件名称，请在【车辆信息管理】更新车辆信息。
              </div>
            </Form>
          </Row>
        </Col>
      </Row>
    </div>
  );
};

export default React.memo(RepairRecord);
