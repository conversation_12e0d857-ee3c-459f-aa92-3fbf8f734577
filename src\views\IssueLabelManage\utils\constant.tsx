import { getBugCategoryDownList } from '@/fetch/business';
import { request } from '@/fetch/core';
import { HttpStatusCode } from '@/fetch/core/constant';
import { FormConfig, CommonTable } from '@jd/x-coreui';
import React from 'react';
export enum YESNO {
  YES = 1,
  NO = 0,
}

export enum TestMethod {
  ONLY_SIMULATION = 'ONLY_SIMULATION',
  ONLY_REAL_VEHICLE = 'ONLY_REAL_VEHICLE',
  SIMULATION_REAL_VEHICLE = 'SIMULATION_REAL_VEHICLE',
}

export const TestMethodOptions = [
  {
    label: '仅仿真',
    value: TestMethod.ONLY_SIMULATION,
  },
  {
    label: '仅实车',
    value: TestMethod.ONLY_REAL_VEHICLE,
  },
  {
    label: '仿真和实车均测试',
    value: TestMethod.SIMULATION_REAL_VEHICLE,
  },
];
export const YES_NO_OPTIONS = [
  {
    label: '是',
    value: YESNO.YES,
  },
  {
    label: '否',
    value: YESNO.NO,
  },
];

export const TestResultOptions = [
  {
    label: '待测试',
    value: 3,
  },
  {
    label: '长期测试',
    value: 2,
  },
  {
    label: '测试通过',
    value: 0,
  },
  {
    label: '测试不通过(保留)',
    value: 1,
  },
  {
    label: '测试不通过(已回滚)',
    value: 7,
  },
];

export const MrInfo: FormConfig = {
  fields: [
    {
      label: 'MR_ID',
      fieldName: 'mrId',
      type: 'input',
      disabled: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    },
    {
      label: 'MR合入时间',
      fieldName: 'mrUpdateTime',
      type: 'input',
      disabled: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    },
    {
      label: '是否提测',
      fieldName: 'isSubmitTest',
      type: 'select',
      options: YES_NO_OPTIONS,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      labelInValue: false,
    },
    {
      label: '不提测原因',
      fieldName: 'noTestReason',
      type: 'input',
      hidden: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    },
    {
      label: 'fix_jira',
      fieldName: 'fixJiraKeyList',
      type: 'ReactNode',
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      renderFunc: (fieldItem: any, value: any) => {
        return value?.map((item: any) => (
          <a target="_blank" style={{ marginRight: '6px' }} href={item.jiraUrl}>
            {item.jiraKey}
          </a>
        ));
      },
    },
    {
      label: '提测内容',
      fieldName: 'description',
      type: 'textarea',
      autoSize: { minRows: 1, maxRows: 3 },
      placeholder: '请填写提测内容',
      maxLength: 500,
      showCount: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    },
    {
      label: '模块',
      fieldName: 'module',
      type: 'textarea',
      autoSize: { minRows: 1, maxRows: 3 },
      placeholder: '请输入模块',
      maxLength: 500,
      showCount: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    },
    {
      label: '软件/模块依赖',
      fieldName: 'dependency',
      placeholder: '请输入软件/模块依赖',
      type: 'textarea',
      autoSize: { minRows: 1, maxRows: 3 },
      maxLength: 500,
      showCount: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    },
    {
      label: '是否有依赖MR',
      fieldName: 'isDependOnMr',
      type: 'select',
      options: YES_NO_OPTIONS,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      labelInValue: false,
    },
    {
      label: '依赖的MR_ID',
      fieldName: 'dependencyMrId',
      type: 'textarea',
      autoSize: { minRows: 1, maxRows: 3 },
      maxLength: 500,
      showCount: true,
      placeholder: '请输入依赖的MR_ID,多个MR_ID用英文;隔开',
      hidden: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    },
    {
      label: '是否有依赖conf',
      fieldName: 'isDependOnConf',
      type: 'select',
      options: YES_NO_OPTIONS,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      labelInValue: false,
    },
    {
      label: '依赖的conf文件名',
      fieldName: 'dependencyConfName',
      type: 'textarea',
      autoSize: { minRows: 1, maxRows: 3 },
      maxLength: 500,
      showCount: true,
      placeholder: '请输入依赖的conf文件名,多个文件名用英文;隔开',
      hidden: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    },
    {
      label: '期望结果',
      fieldName: 'expectedOutcome',
      type: 'textarea',
      autoSize: { minRows: 1, maxRows: 3 },
      placeholder: '请输入期望结果',
      maxLength: 500,
      showCount: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    },
    {
      label: '研发人员',
      fieldName: 'developer',
      type: 'input',
      disabled: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    },
    {
      label: 'coding测试审核人',
      fieldName: 'testReviewer',
      type: 'input',
      disabled: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    },
  ],
  linkRules: {
    isSubmitTest: [
      {
        linkFieldName: 'noTestReason',
        rule: 'visible',
        dependenceData: [YESNO.NO],
      },
    ],
    isDependOnMr: [
      {
        linkFieldName: 'dependencyMrId',
        rule: 'visible',
        dependenceData: [YESNO.YES],
      },
    ],
    isDependOnConf: [
      {
        linkFieldName: 'dependencyConfName',
        rule: 'visible',
        dependenceData: [YESNO.YES],
      },
    ],
  },
};

export const TestTask: FormConfig = {
  fields: [
    {
      label: '是否为专项测试',
      fieldName: 'isSpecificTest',
      type: 'radioGroup',
      options: YES_NO_OPTIONS,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      label: '专项名称',
      fieldName: 'specificTestName',
      type: 'textarea',
      autoSize: { minRows: 1, maxRows: 1 },
      maxLength: 50,
      showCount: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      placeholder: '请输入项目名称',
      hidden: true,
      validatorRules: [
        {
          required: true,
          message: '请输入项目名称',
        },
      ],
    },
    {
      label: '测试方式',
      fieldName: 'testMethod',
      type: 'radioGroup',
      options: TestMethodOptions,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      label: '仿真测试人员',
      fieldName: 'simulationTester',
      type: 'textarea',
      autoSize: { minRows: 1, maxRows: 1 },
      maxLength: 50,
      showCount: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      placeholder: '请输入姓名',
      hidden: true,
      validatorRules: [
        {
          required: true,
          message: '请输入姓名',
        },
      ],
    },
    {
      label: '实车测试人员',
      fieldName: 'realCarTester',
      type: 'textarea',
      autoSize: { minRows: 1, maxRows: 1 },
      maxLength: 50,
      showCount: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      placeholder: '请输入姓名',
      hidden: true,
      validatorRules: [
        {
          required: true,
          message: '请输入姓名',
        },
      ],
    },
  ],
  linkRules: {
    isSpecificTest: [
      {
        linkFieldName: 'specificTestName',
        rule: 'visible',
        dependenceData: [YESNO.YES],
      },
    ],
    testMethod: [
      {
        linkFieldName: 'simulationTester',
        rule: 'visible',
        dependenceData: [
          TestMethod.ONLY_SIMULATION,
          TestMethod.SIMULATION_REAL_VEHICLE,
        ],
      },
      {
        linkFieldName: 'realCarTester',
        rule: 'visible',
        dependenceData: [
          TestMethod.ONLY_REAL_VEHICLE,
          TestMethod.SIMULATION_REAL_VEHICLE,
        ],
      },
    ],
  },
};

export const TestPlan: FormConfig = {
  fields: [
    {
      label: '操作步骤',
      fieldName: 'operationStep',
      type: 'textarea',
      autoSize: { minRows: 3, maxRows: 5 },
      placeholder: '请输入测试用例内容、操作步骤',
      maxLength: 500,
      showCount: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    },
    {
      label: '上传图片（0/5）',
      fieldName: 'imageList',
      type: 'upload',
      fileListType: 'picture',
      accept: 'images/*',
      LOPDN: process.env.JDX_APP_REQUEST_HEADER!,
      bucketName: 'rover-operation',
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      max: 5,
      getPreSignatureUrl:
        location.protocol +
        '//' +
        process.env.JDX_APP_CLOUD_FETCH_DOMAIN +
        '/infrastructure/oss/getPreUrl',
    },
    {
      label: '上传文件（0/5）',
      fieldName: 'fileList',
      type: 'upload',
      fileListType: 'file',
      accept: '.zip',
      LOPDN: process.env.JDX_APP_REQUEST_HEADER!,
      bucketName: 'rover-operation',
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      max: 5,
      getPreSignatureUrl:
        location.protocol +
        '//' +
        process.env.JDX_APP_CLOUD_FETCH_DOMAIN +
        '/infrastructure/oss/getPreUrl',
    },
    {
      label: 'fix_缺陷列表',
      fieldName: 'fixBugDetailList',
      type: 'ReactNode',
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      renderFunc: (fieldItem: any, value) => {
        return (
          <CommonTable
            tableListData={{ list: value }}
            columns={[
              {
                title: 'FIX_缺陷',
                dataIndex: 'bugCode',
                align: 'center',
              },
              {
                title: '是否可复现',
                dataIndex: 'isReproducibleName',
                align: 'center',
              },
              {
                title: '不可复现原因',
                dataIndex: 'nonReproducibleReason',
                align: 'center',
              },
              {
                title: '是否可召回',
                dataIndex: 'isRecallableName',
                align: 'center',
              },
              {
                title: '不可召回原因',
                dataIndex: 'nonRecallableReason',
                align: 'center',
              },
            ]}
            rowKey={'bugCode'}
            loading={false}
          />
        );
      },
    },
    {
      label: '测试结果',
      fieldName: 'testResult',
      type: 'select',
      options: TestResultOptions,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      labelInValue: false,
      validatorRules: [
        {
          required: true,
        },
      ],
    },
    {
      label: '关联MR_ID',
      fieldName: 'associationMrIds',
      type: 'input',
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      labelInValue: false,
      hidden: true,
      placeholder: '请输入MR_ID，多个用英文分号";"隔开',
      validatorRules: [
        {
          required: true,
          message: '请输入MR_ID，多个用英文分号";"隔开',
        },
      ],
    },
    {
      label: 'MR状态',
      fieldName: 'mrStatus',
      type: 'select',
      options: [
        {
          label: '待发版',
          value: 'TO_BE_RELEASE',
        },
        {
          label: '已发版',
          value: 'RELEASED',
        },
      ],
      placeholder: '请选择',
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      labelInValue: false,
      validatorRules: [
        {
          required: true,
          message: '请选择',
        },
      ],
    },
    {
      label: 'Rover版本号',
      fieldName: 'roverVersion',
      type: 'select',
      placeholder: '请选择',
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      labelInValue: false,
      hidden: true,
      validatorRules: [
        {
          required: true,
          message: '请选择',
        },
      ],
    },
    {
      label: '发版周：W',
      fieldName: 'releaseWeek',
      type: 'input',
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      labelInValue: false,
      placeholder: '请输入发版周数，仅能填写数字',
      validatorRules: [
        {
          required: true,
          message: '请输入发版周数，仅能填写数字',
        },
      ],
    },
    {
      label: '备注',
      fieldName: 'remark',
      type: 'textarea',
      autoSize: { minRows: 3, maxRows: 5 },
      maxLength: 500,
      showCount: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      labelInValue: false,
      placeholder: '请输入备注内容',
    },
  ],
  linkRules: {
    testResult: [
      {
        linkFieldName: 'associationMrIds',
        rule: 'visible',
        dependenceData: [7],
      },
    ],
    mrStatus: [
      {
        linkFieldName: 'roverVersion',
        rule: 'fetchData',
        fetchFunc: async (val: any, formRef: any) => {
          if (val != 'RELEASED') {
            return [];
          }
          const res = await request({
            path: '/ota/web/application_version_get_list',
            method: 'POST',
            body: {
              appName: 'rover',
              enable: 1,
            },
          });
          if (res.code === HttpStatusCode.Success) {
            return res?.data?.map((item: any) => ({
              label: item.version,
              value: item.versionNumber,
            }));
          }
        },
      },
      {
        linkFieldName: 'roverVersion',
        rule: 'visible',
        dependenceData: ['RELEASED'],
      },
    ],
  },
};

export const getFormConfig = (type: 'ADD' | 'EDIT'): FormConfig => {
  return {
    fields: [
      {
        label: '问题标签名称',
        fieldName: 'bugLabelName',
        type: 'input',
        maxLength: 20,
        placeholder: '请输入标签名称',
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
        labelInValue: false,
        validatorRules: [
          {
            required: true,
            message: '请输入标签名称',
          },
        ],
      },
      {
        label: '对应模块',
        fieldName: 'moduleCode',
        type: 'select',
        placeholder: '请选择模块',
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
        disabled: type === 'EDIT',
        validatorRules: [
          {
            required: true,
            message: '请选择模块',
          },
        ],
      },
      {
        label: '对应问题分类',
        fieldName: 'bugCategoryId',
        type: 'select',
        placeholder: '请选择分类名称，支持关键字联想全称',
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
        disabled: type === 'EDIT',
        labelInValue: false,
        showSearch:true,
        validatorRules: [
          {
            required: true,
            message: '请选择分类名称，支持关键字联想全称',
          },
        ],
      },
    ],
    linkRules: {
      moduleCode: [
        {
          linkFieldName: 'bugCategoryId',
          rule: 'fetchData',
          fetchFunc: async (val: any, commonFormRef: any) => {
            const value = commonFormRef.getFieldValue('moduleCode');
            const res = await getBugCategoryDownList(value?.key);
            if (res?.code === HttpStatusCode.Success) {
              return res?.data?.map((item) => ({
                label: item.bugCategoryName,
                value: item.bugCategoryId,
              }));
            }
          },
        },
      ],
    },
  };
};
