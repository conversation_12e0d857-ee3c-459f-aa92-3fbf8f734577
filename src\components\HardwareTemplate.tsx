import React, { useState, useEffect } from 'react';
import {
  Form,
  Radio,
  message,
  Row,
  Col,
  Select,
  Input,
  FormInstance,
} from 'antd';
import { VehicleTypeApi, HardwareTypeApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { ProductType } from '@/utils/enum';

const HardwareTemplate = ({
  type,
  productType,
  formRef,
  disabled,
  initialVal,
}: {
  type: 'device_type' | 'device';
  productType: ProductType;
  disabled?: boolean;
  formRef: FormInstance;
  initialVal: any[];
}) => {
  const fetchApi = new VehicleTypeApi();
  const hardwareTypeApi = new HardwareTypeApi();
  const [curOptions, setCurOptions] = useState<
    { value: number | string; label: string }[]
  >([]);
  const [hardwareDetail, setHardwareDetail] = useState<{
    [propName: string]: any;
  }>({});

  useEffect(() => {
    if (initialVal?.length > 0) {
      initialVal.forEach((item) => {
        formRef.setFieldsValue({
          [`hardwareModel_${item.hardwareTypeId}`]: {
            value: item.hardwareModelId,
            label: item.hardwareModelName,
          },
          [`${item.hardwareTypeId}Modal`]: item.hardwareModelModel,
          [`${item.hardwareTypeId}_number`]: item.hardwareNumber,
        });
      });
    }
  }, [initialVal]);

  useEffect(() => {
    if (type && productType) {
      fetchApi.getDeviceTemplate({ type, productType }).then((res) => {
        if (res.code === HttpStatusCode.Success) {
          setHardwareDetail(res.data);
        }
      });
    }
  }, [type, productType]);

  const getHardwareType = async (typeId) => {
    const res = await hardwareTypeApi.getHardwareModel(typeId);
    if (res.code === HttpStatusCode.Success) {
      const list = res.data.map((v) => {
        return {
          value: v.hardwareModelId,
          label: v.hardwareModelName,
          key: v.hardwareModelModel,
        };
      });
      setCurOptions([...list]);
    }
  };

  return (
    <>
      {hardwareDetail?.deviceHardwareDetailList?.map((item) => {
        return (
          <Form.Item
            key={item.hardwareTypeId}
            label={item.hardwareTypeName}
            style={{ marginBottom: 0 }}
            required={item.isNecessary}
          >
            <Form.Item
              name={`hardwareModel_${item.hardwareTypeId}`}
              style={{
                display: 'inline-block',
                width: '49%',
              }}
              rules={[
                {
                  required: item.isNecessary,
                  message: `请选择${item.hardwareTypeName}`,
                },
              ]}
            >
              <Select
                disabled={disabled}
                placeholder={`请选择${item.hardwareTypeName}`}
                options={curOptions}
                labelInValue
                allowClear
                onFocus={() => getHardwareType(item.hardwareTypeId)}
                onBlur={() => setCurOptions([])}
                onChange={(value) => {
                  if (type === 'device_type') {
                    formRef.setFieldsValue({
                      [`${item.hardwareTypeId}Modal`]: value.key,
                    });
                  }
                }}
              />
            </Form.Item>
            {type === 'device_type' && (
              <Form.Item
                name={`${item.hardwareTypeId}Modal`}
                style={{
                  display: 'inline-block',
                  width: '49%',
                  marginLeft: '10px',
                }}
              >
                <Input
                  disabled
                  placeholder={`${item.hardwareTypeName}型号(系统自动带入)`}
                />
              </Form.Item>
            )}
            {type === 'device' && (
              <Form.Item
                name={`${item.hardwareTypeId}_number`}
                style={{
                  display: 'inline-block',
                  width: '49%',
                  marginLeft: '10px',
                }}
                rules={[
                  {
                    pattern:
                      /^[^\u4e00-\u9fa5|\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5]*$/g,
                    message: '请输入正确序列号',
                  },
                ]}
              >
                <Input
                  placeholder={`请输入${item.hardwareTypeName}序列号`}
                  allowClear
                  disabled={disabled}
                />
              </Form.Item>
            )}
          </Form.Item>
        );
      })}
    </>
  );
};
export default React.memo(HardwareTemplate);
