/* eslint-disable no-unused-vars */
import { Button, Form, message, Modal } from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import BreadCrumb from '@/components/BreadCrumb';
import FormTitle from '@/components/FormTitle';
import { formatLocation } from '@/utils/utils';
import GirdInfo from '../GirdInfo';
import './index.scss';
import GirdList from '@/components/GirdList';
import {
  setGridPositionValueAction,
  setGridSizeListAction,
} from '@/redux/reducer/commonData';
import { validationGrid } from '@/utils/grid';
import { HttpStatusCode } from '@/fetch/core/constant';
import { StationFetchApi } from '@/fetch/business';
const girdInfoInit: any = {
  id: 0,
  name: '',
  model: '',
  serialNo: '',
  driverType: '',
  deviceId: '',
  baudRate: undefined,
  boxTemplateId: -1,
};
let gridCofigArr: any[] = [];
let gridPositionValue: any = {};
const GridEdit = () => {
  const fetchApi = new StationFetchApi();
  const dispatch = useDispatch();
  const locationMap = formatLocation(window.location.search);
  const vehicleId = Number(locationMap.id);
  const [gridDetail, setGridDetail] = useState<{
    gridInfo: any;
    gridList: any[];
  }>({
    gridInfo: girdInfoInit,
    gridList: [],
  });
  const gridCrumbItems = [
    {
      title: '站点详情',
      route: `/app/stationManagement/detail?stationBaseId=${locationMap.stationBaseId}`,
    },
    {
      title: '车辆配置',
      route: '',
    },
    {
      title: '格口调整',
      route: '',
    },
  ];
  const [boxTemplateId, setBoxTemplateId] = useState<any>(null);
  const [formRef] = Form.useForm();
  const [isShowCarImg, setIsShowCarImg] = useState<boolean>(false);
  useEffect(() => {
    fetchDetail();
    return () => {
      dispatch(setGridPositionValueAction([]));
    };
  }, []);

  useEffect(() => {
    if (!boxTemplateId) return;
    onBoxTemplatChanged(boxTemplateId);
  }, [boxTemplateId]);
  useEffect(() => {
    if (gridDetail.gridList && gridDetail.gridList.length > 0) {
      const valid = validationGrid(
        gridDetail.gridList,
        gridPositionValue,
        gridCofigArr,
      );
      setIsShowCarImg(valid.isValid);
      if (!valid.isValid) {
        message.warning(valid.msg);
      }
    }
  }, [gridDetail && gridDetail.gridList]);
  const fetchDetail = async () => {
    try {
      const resp: any = await fetchApi.getStationVehicleBoxDetail(vehicleId);
      if (resp.code === HttpStatusCode.Success && resp.data) {
        const basic = {
          id: resp.data.id,
          name: resp.data.boxTemplateName,
          model: resp.data.model,
          serialNo: resp.data.serialNo,
          driverType: resp.data.driverType,
          deviceId: resp.data.deviceId,
          baudRate: resp.data.baudRate,
          boxTemplateId: resp.data.boxTemplateId,
          leftBoxColumnNum: resp.data.leftBoxColumnNum,
          rightBoxColumnNum: resp.data.rightBoxColumnNum,
          gridSpecificationList: resp.data.gridSpecificationList,
        };
        gridCofigArr =
          basic.gridSpecificationList &&
          basic.gridSpecificationList.map((item: any) => {
            return {
              ...item,
              sizeStr: `${item.width} × ${item.length} × ${item.height}`,
            };
          });
        gridPositionValue = {
          leftBoxColumnNum: basic.leftBoxColumnNum,
          rightBoxColumnNum: basic.rightBoxColumnNum,
        };
        const list =
          resp.data.gridList &&
          resp.data.gridList.map((item: any) => {
            return {
              ...item,
              sizeStr: `${item.width} × ${item.length} × ${item.height}`,
            };
          });
        setGridDetail({
          gridInfo: basic,
          gridList: list,
        });
        dispatch(
          setGridPositionValueAction({
            leftBoxColumnNum: basic.leftBoxColumnNum,
            rightBoxColumnNum: basic.rightBoxColumnNum,
          }),
        );
        dispatch(setGridSizeListAction(gridCofigArr));
      }
    } catch (e) {
      console.log(e);
    }
  };

  const goBack = () => {
    window.history.go(-1);
  };

  const onBoxTemplatChanged = async (id: number) => {
    const resp: any = await fetchApi.getBoxTemplateDetail(id);
    if (resp.code === HttpStatusCode.Success && resp.data) {
      const basic = {
        name: resp.data.name,
        model: resp.data.hardwareModelModel,
        serialNo: resp.data.serialNo,
        driverType: resp.data.driverType,
        deviceId: resp.data.deviceId,
        baudRate: resp.data.baudRate,
        boxTemplateId: resp.data.id,
        leftBoxColumnNum: resp.data.leftBoxColumnNum,
        rightBoxColumnNum: resp.data.rightBoxColumnNum,
        gridSpecificationList: resp.data.gridSpecificationList || [],
      };
      gridCofigArr = basic.gridSpecificationList;
      gridPositionValue = {
        leftBoxColumnNum: basic.leftBoxColumnNum,
        rightBoxColumnNum: basic.rightBoxColumnNum,
      };
      setGridDetail({
        gridInfo: basic,
        gridList: resp.data.gridList?.map((item: any) => ({
          ...item,
          statusId: `${item.enable}`,
        })),
      });
      dispatch(
        setGridPositionValueAction({
          leftBoxColumnNum: basic.leftBoxColumnNum,
          rightBoxColumnNum: basic.rightBoxColumnNum,
        }),
      );
      dispatch(setGridSizeListAction(gridCofigArr));
    }
  };

  const handleGirdList = () => {
    const editGridList: any[] = formRef.getFieldValue('gridList');
    const gird: any[] = [];
    editGridList.forEach((item: any) => {
      const obj = {
        boardNo: item.boardNo,
        gridNo: item.gridNo,
        height: item.height,
        length: item.length,
        lockNo: item.lockNo,
        side: item.side,
        statusId: item.enable,
        // statusId: item.statusId || `${item.enable || 1}`,
        width: item.width,
        id: item.newAdd ? null : item.id,
        palletList: item.palletList,
      };
      gird.push(obj);
    });
    return gird;
  };
  const submit = async () => {
    try {
      const params = {
        gridList: handleGirdList(),
        deviceBaseId: parseInt(`${vehicleId}`),
        boxTemplateId: gridDetail.gridInfo.boxTemplateId,
      };
      const res: any = await fetchApi.editStationVehicleBox(params);
      if (res.code === HttpStatusCode.Success) {
        message.success(res.message);
        window.history.go(-1);
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    }
  };
  return (
    <div style={{ padding: '10px' }}>
      <BreadCrumb items={gridCrumbItems} />
      <div className="manage-edit">
        <FormTitle title={`格口调整`} />
        <GirdInfo
          formRef={formRef}
          girdInfo={gridDetail.gridInfo}
          onTemplateChanged={(templateId: number) => {
            setBoxTemplateId(templateId);
          }}
        />
        <GirdList
          gridList={gridDetail.gridList}
          formRef={formRef}
          moduleTitle="格口信息"
          viewType="readOnly"
          isShowCarImg={isShowCarImg}
        />
        <div className="grid-btn">
          <Button type="primary" className="active" onClick={submit}>
            保存
          </Button>
          <Button className="" onClick={goBack}>
            取消
          </Button>
        </div>
      </div>
    </div>
  );
};
export default GridEdit;
