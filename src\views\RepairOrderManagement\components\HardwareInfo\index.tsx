import { Row, Col, Table, Descriptions } from 'antd';
import React, { useState, useEffect } from 'react';
import { hardwareInfoTableColums } from '../../utils/column';
import './index.scss';

interface Iconfig {
  [key: string]: any;
}
interface IpriceInfo {
  [key: string]: any;
}
interface Props {
  hardwareInfo: any;
  priceInfo: any;
}
const HardwareInfo = (props: Props) => {
  const { hardwareInfo, priceInfo } = props;
  const [config, setConfig] = useState<Iconfig>({
    laborCost: '维修工时费(元)',
    serviceCost: '上门服务费(元)',
    otherCost: '其他费用(元)',
    costVerifyNumbers: '费用核销单号',
    otherCostRemark: '其他费用说明',
  });
  const formatColumns = () => {
    return hardwareInfoTableColums.map((col: any) => {
      switch (col.dataIndex) {
        case 'oldHardwareModelName':
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'oldHardwareModelModel':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'newHardwareModelName':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'newHardwareModelModel':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'newHardwareModelNumber':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        default:
          return col;
      }
      return col;
    });
  };
  return (
    <Col span={20} push={2}>
      <div className="hardware-info">
        <Row>
          <span>维修硬件信息记录（如未更换可不填写）</span>
        </Row>
        <Row>
          <Table
            rowKey={(reocrd) => reocrd.id}
            bordered
            columns={formatColumns()}
            dataSource={props.hardwareInfo}
            pagination={false}
          ></Table>
        </Row>
        <div className="price-info">
          <Descriptions bordered column={2}>
            {Object.keys(priceInfo).map((item: string) => {
              switch (item) {
                case 'otherPriceDescription':
                  return (
                    <Descriptions.Item key={item} label={config[item]} span={2}>
                      {priceInfo[item] || '-'}
                    </Descriptions.Item>
                  );
                  break;
                default:
                  return (
                    <Descriptions.Item key={item} label={config[item]}>
                      {priceInfo[item] || '-'}
                    </Descriptions.Item>
                  );
              }
            })}
          </Descriptions>
        </div>
      </div>
    </Col>
  );
};

export default React.memo(HardwareInfo);
