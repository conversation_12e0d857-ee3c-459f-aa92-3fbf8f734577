import React, { ReactNode } from 'react';
import { Table, Transfer } from 'antd';
import type { ColumnsType, TableRowSelection } from 'antd/es/table/interface';
import type { TransferItem, TransferProps } from 'antd/es/transfer';
import difference from 'lodash/difference';
import './index.scss';

interface TableTransferProps extends TransferProps<TransferItem> {
  dataSource: any[];
  rightColumns: any[];
  leftColumns: any[];
}
interface Props {
  leftColumns: any[]; // 左边列表每列是啥
  rightColumns: any[]; // 右边列表每列是啥
  onSelectChange: Function; // 穿梭框中选中项变化
  titles?: ReactNode[]; // 穿梭框中左右表格表头
  dataSource: any[];
  targetKeys: any[];
  showSearch: boolean; // 是否展示搜索框
  searchColumn?: string; // 指定哪一列作为搜索依据
  showSelectAll: boolean; // 穿梭框左上角是否有全选框
  tableSelectAll: boolean; // 穿梭框表格是否支持全选
  scrollY: number; // 穿梭框表格滚动高度
  leftTableLoading?: boolean; // 穿梭框左侧表格加载中状态
  rowKey: string;
}
const TransferWithTable = (props: Props) => {
  const {
    rowKey,
    leftColumns,
    rightColumns,
    onSelectChange,
    titles,
    dataSource,
    targetKeys,
    showSearch,
    searchColumn,
    showSelectAll,
    tableSelectAll,
    scrollY,
    leftTableLoading,
  } = props;

  const TableTransfer = ({
    rightColumns,
    leftColumns,
    ...restProps
  }: TableTransferProps) => (
    <Transfer
      {...restProps}
      rowKey={(record) => record[rowKey]}
      listStyle={{
        width: 400,
        height: 600,
      }}
    >
      {({
        direction,
        filteredItems,
        onItemSelect,
        onItemSelectAll,
        selectedKeys: listSelectedKeys,
      }) => {
        const columns = direction === 'left' ? leftColumns : rightColumns;
        const rowSelection: TableRowSelection<TransferItem> = {
          columnWidth: '50px',
          onSelect({ key }, selected) {
            onItemSelect(key!, selected);
          },
          onSelectAll(selected, selectedRows) {
            if (tableSelectAll) {
              const treeSelectedKeys = selectedRows
                .filter((item) => !item.disabled)
                .map(({ key }) => key);
              const diffKeys = selected
                ? difference(treeSelectedKeys, listSelectedKeys)
                : difference(listSelectedKeys, treeSelectedKeys);
              onItemSelectAll(diffKeys as string[], selected);
            }
          },
          selectedRowKeys: listSelectedKeys,
        };

        return (
          <div className="transfer-content">
            <Table
              rowKey={(record: any) => record[rowKey]}
              rowSelection={rowSelection}
              columns={columns}
              dataSource={filteredItems}
              size="small"
              pagination={false}
              bordered
              scroll={{
                y: scrollY,
              }}
              loading={leftTableLoading ? leftTableLoading : false}
            />
            <div className="selected-count">
              {`共${filteredItems.length}条，已选${listSelectedKeys.length}条`}
            </div>
          </div>
        );
      }}
    </Transfer>
  );
  return (
    <>
      <TableTransfer
        titles={titles}
        dataSource={dataSource}
        targetKeys={targetKeys}
        showSearch={true}
        onChange={(nextTargetKeys: any[]) => onSelectChange(nextTargetKeys)}
        filterOption={(inputValue: any, item: any) =>
          item[searchColumn!]!.indexOf(inputValue) !== -1
        }
        leftColumns={leftColumns}
        rightColumns={rightColumns}
        showSelectAll={false}
      />
    </>
  );
};

export default React.memo(TransferWithTable);
