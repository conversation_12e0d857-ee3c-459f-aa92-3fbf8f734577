import React, { useState, useEffect } from 'react';
import { CommonTable, TableOperateBtn, CommonForm } from '@/components';
import { searchConfig, tableColumns } from './utils/columns';
import { useTableData } from '@/components/CommonTable/useTableData';
import { DeviceInfoApi } from '@/fetch/business';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/store';
import { useNavigate } from 'react-router-dom';
import { ProductType, YESNO } from '@/utils/enum';
import { saveSearchValues } from '@/redux/reducer/searchForm';
import { HttpStatusCode } from '@/fetch/core/constant';
import { PageType } from '@/utils/EditTitle';
import { Space, Modal, message, Button, Row, Col } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import UploadHistoryModal from './component/UploadHistoryModal';
import UploadModal from './component/UploadModal';
import { SUPPLIER, TableListType } from '@/utils/constant';
const { confirm } = Modal;
const DeviceInfo = () => {
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const fetchApi = new DeviceInfoApi();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const initSearchCondition = {
    searchForm: {
      name: null,
      serialNo: null,
      deviceTypeBaseId: null,
      businessType: null,
      productType: null,
      hardwareModelId: null,
      hardwareTypeId: null,
      hardwareNumber: null,
      androidDeviceId: null,
      supplier: null,
      tagList: [],
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValues.searchValues
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [tableData, setTableData] = useState<TableListType>({
    list: [],
    total: 0,
    pages: 0,
  });
  useEffect(() => {
    fetchTableData();
  }, [searchCondition]);

  const [showUploadModal, setShowUploadModal] = useState<{
    visible: boolean;
    module: 'addVehicle' | 'addCardNo';
  }>({
    visible: false,
    module: 'addVehicle',
  });

  const [showUploadHistoryModal, setShowUploadHistoryModal] = useState(false);
  const [isSecretKeyModalVisible, setIsSecretKeyModalVisible] = useState(false);
  const [editItem, setEditItem] = useState<any>();
  const [apiKey, setApiKey] = useState<boolean>(false);
  const middleBtns: any[] = [
    {
      show: true,
      title: '新建',
      key: 'vehicleTypeAdd',
      onClick: () => handleEdit(PageType.ADD, ProductType.VEHICLE),
    },
    {
      show: true,
      title: '批量新增车辆',
      onClick: () => {
        setShowUploadModal({
          visible: true,
          module: 'addVehicle',
        });
      },
    },
    {
      show: true,
      title: '导入更多设备号',
      onClick: () => {
        setShowUploadModal({
          visible: true,
          module: 'addCardNo',
        });
      },
    },
    {
      show: true,
      title: '导入日志',
      btnType: 'default',
      onClick: () => setShowUploadHistoryModal(true),
    },
  ];

  const fetchTableData = async () => {
    if (
      searchCondition.searchForm.hardwareTypeId &&
      !searchCondition.searchForm.hardwareModelId
    ) {
      message.error('请选择硬件型号');
      return;
    }
    try {
      setLoading(true);
      const params = {
        searchForm: {
          ...searchCondition.searchForm,
          deviceTypeBaseId: searchCondition.searchForm.deviceTypeBaseId?.value,
          businessType: searchCondition.searchForm.businessType?.value,
          productType: searchCondition.searchForm.productType?.value,
          hardwareModelId: searchCondition.searchForm.hardwareModelId?.value,
          tagList: searchCondition.searchForm.tagList?.map((v) => v.value),
          hardwareTypeId: null,
          supplier: searchCondition.searchForm.supplier?.value,
        },
        pageNum: searchCondition.pageNum,
        pageSize: searchCondition.pageSize,
      };
      const res: any = await fetchApi.fetchTableList(params);
      if (res.code === HttpStatusCode.Success) {
        setTableData({
          list: res.data.list,
          total: res.data.total,
          pages: res.data.pages,
        });
      }
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };
  const formateColumns = () => {
    return tableColumns.map((col) => {
      switch (col.dataIndex) {
        case 'rowIndex':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              const { pageSize, pageNum } = searchCondition;
              return <div>{(pageNum - 1) * pageSize + index + 1}</div>;
            },
          };
        case 'enableName':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              const enableStyle =
                record.enable === YESNO.YES ? 'enable' : 'unenable';
              return <div className={enableStyle}>{record.enableName}</div>;
            },
          };
        case 'operation':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              const { enable, id, productType } = record;
              return (
                <div className="operate">
                  <TableOperateBtn
                    title="编辑"
                    handleClick={() =>
                      handleEdit(
                        PageType.EDIT,
                        productType,
                        id,
                        record.supplier,
                      )
                    }
                  />
                  <TableOperateBtn
                    title="查看"
                    handleClick={() =>
                      handleEdit(
                        PageType.READONLY,
                        productType,
                        id,
                        record.supplier,
                      )
                    }
                  />

                  {record.androidDeviceId && record.androidDeviceId !== '-' ? (
                    <TableOperateBtn
                      title="解绑安卓设备"
                      handleClick={() => unbindClick(record.name)}
                    />
                  ) : null}
                  <TableOperateBtn
                    title="查看密钥"
                    handleClick={() => {
                      setEditItem(record);
                      getVehicleApiKey(record.id);
                      setIsSecretKeyModalVisible(true);
                    }}
                  />
                  {record.supplier === SUPPLIER.JD ? (
                    <TableOperateBtn
                      title={'查看车辆配置'}
                      handleClick={() => {
                        sessionStorage.removeItem('iframeUrl');
                        navigator(
                          `/ota/vehicleConfigAndRelease?vehicleName=${record.name}`,
                        );
                      }}
                    />
                  ) : null}
                </div>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  const unbindClick = (name: string) => {
    confirm({
      title: '确定解绑当前安卓设备吗?',
      icon: <ExclamationCircleOutlined />,
      okText: '解绑',
      cancelText: '取消',
      onOk() {
        unbindAndroidDevice(name);
      },
    });
  };

  const unbindAndroidDevice = async (license: string) => {
    const response: any = await fetchApi.unbindAndroidDeviceService(license);
    if (response.code === HttpStatusCode.Success) {
      message.success(response.message);
      fetchTableData();
    } else {
      message.error(response.message);
    }
  };

  const getVehicleApiKey = async (id: number) => {
    const response: any = await fetchApi.getApiKey(id);
    if (response.code === HttpStatusCode.Success) {
      setApiKey(response.data.apikey);
    } else {
      message.error(response.message);
    }
  };

  const handleEdit = (
    type: PageType,
    productType?: ProductType,
    id?: number,
    supplier?: SUPPLIER,
  ) => {
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: searchCondition,
      }),
    );
    navigator(
      id
        ? `/app/deviceInfo/edit?type=${type}&id=${id}&productType=${productType}&supplier=${supplier}`
        : `/app/deviceInfo/edit?type=${type}&productType=${productType}`,
    );
  };

  const onSearchClick = (val) => {
    const data = {
      searchForm: val,
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(data);
  };

  const cancelSecretKeyModalClick = () => {
    setIsSecretKeyModalVisible(false);
  };

  return (
    <>
      <CommonForm
        formConfig={searchConfig}
        defaultValue={searchCondition.searchForm}
        layout="inline"
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={() => setSearchCondition({ ...initSearchCondition })}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        columns={formateColumns()}
        loading={loading}
        rowKey={'id'}
        middleBtns={middleBtns}
        searchCondition={searchCondition}
        onPageChange={(value: any) => setSearchCondition(value)}
      />

      {showUploadModal.visible && (
        <UploadModal
          module={showUploadModal.module}
          visable={showUploadModal.visible}
          onComplete={(result: boolean) => {
            if (result) {
              // setShouldReload(shouldReload + 1);
            }
            setShowUploadModal({
              ...showUploadModal,
              visible: false,
            });
          }}
        />
      )}
      {showUploadHistoryModal && (
        <UploadHistoryModal
          visable={showUploadHistoryModal}
          onClose={() => {
            setShowUploadHistoryModal(false);
          }}
        />
      )}
      <Modal
        title={`查看密钥 -【${editItem && editItem.name}】`}
        visible={isSecretKeyModalVisible}
        onCancel={cancelSecretKeyModalClick}
        width={600}
        footer={[
          <Button key="back" onClick={cancelSecretKeyModalClick}>
            取消
          </Button>,
        ]}
      >
        <Row>
          <Col span={24}>{apiKey}</Col>
        </Row>
      </Modal>
    </>
  );
};

export default DeviceInfo;
