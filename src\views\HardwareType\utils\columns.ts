import { dropDownListKey, dropDownKey } from '@/utils/constant';
import { FieldItem, FormConfig } from '@/components';
export const tableColumns = [
  {
    title: '硬件类型名称',
    dataIndex: 'name',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '类型ID',
    dataIndex: 'id',
    align: 'center',
    width: 50,
    ellipsis: true,
  },
  {
    title: '是否属于传感器',
    dataIndex: 'isSensorName',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '是否属于箱体',
    dataIndex: 'isBoxName',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '类型状态',
    dataIndex: 'enableName',
    align: 'center',
    width: 60,
    ellipsis: true,
  },
  {
    title: '操作人',
    dataIndex: 'modifyUser',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
];
export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'name',
      label: '类型名称',
      placeholder: '请输入硬件类型名称',
      type: 'input',
    },
    {
      fieldName: 'isSensor',
      label: '是否属于传感器',
      placeholder: '请选择是否属于传感器',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.YES_OR_NO,
      dropDownListKey: dropDownListKey.YES_OR_NO,
    },
    {
      fieldName: 'isBox',
      label: '是否属于箱体',
      placeholder: '请选择是否属于箱体',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.YES_OR_NO,
      dropDownListKey: dropDownListKey.YES_OR_NO,
    },
    {
      fieldName: 'enable',
      label: '类型状态',
      placeholder: '请选择类型状态',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.ENABLE,
      dropDownListKey: dropDownListKey.ENABLE,
    },
  ],
};
