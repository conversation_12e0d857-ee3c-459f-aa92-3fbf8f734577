/* eslint-disable require-jsdoc */
/* eslint-disable no-unused-vars */
import { Form, FormInstance } from 'antd';
import React, { useEffect, useState } from 'react';
import EditModuleTitle from '@/components/EditModuleTitle';
import './index.scss';
import ParkingEdit from '../EditParking';
import _ from 'lodash';
const win = window as any;
const { rad, deg } = win;
const layout = {
  labelCol: {
    span: 4,
  },
};

const LocationInfo = ({
  form,
  editInfo, // 点编辑进来后拿到的表单信息，若从新建进来则为undefined
  department, // 城市和站点名称
  onValueChanged,
}: {
  form: FormInstance;
  editInfo?: any;
  department: any;
  onValueChanged: Function;
}) => {
  const [parkingList, setParkingList] = useState<any[]>([]);
  const makeParkingSpotList = (list: any[]) => {
    if (!list) return list;
    return list.map((item: any) => {
      return {
        ...item,
        heading: parseFloat(Number(deg(item.heading)).toFixed(8)),
      };
    });
  };

  useEffect(() => {
    if (editInfo) {
      setParkingList(makeParkingSpotList(editInfo?.parkingSpotList));
    }
  }, [editInfo]);
  return (
    <Form form={form} {...layout}>
      <Form.Item>
        <EditModuleTitle title={'位置信息'} />
      </Form.Item>
      <ParkingEdit
        form={form}
        initValue={parkingList}
        department={department}
      />
    </Form>
  );
};

export default React.memo(LocationInfo);
