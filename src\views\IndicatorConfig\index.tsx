import React, { useEffect, useRef, useState } from 'react';
import { CommonForm, FormConfig } from '@/components';
import IndicatorFetch from '../IndicatorConfig/utils/fetchApi';
import { ColumnType } from 'antd/lib/table';
import { indicatorColumns, DataType } from './utils/columns';
import { Input, message, Table } from 'antd';
import { HttpStatusCode } from '@/fetch/core/constant';
import { useNavigate, useSearchParams } from 'react-router-dom';
const fetchApi = new IndicatorFetch();
const IndicatorConfig = () => {
  const navigator = useNavigate();
  const [search] = useSearchParams();
  const [layerList, setLayerList] = useState<any>([]);
  const [tableData, setTableData] = useState<any>([]);
  const [editingKey, setEditingKey] = useState<string>();
  const editDataRef = useRef<any>(new Map());
  const initSearchCondition = {
    searchForm: {
      indicatorLayerNumber: search.get('layerNumber') ?? null,
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] =
    useState<SearchCondition>(initSearchCondition);
  const formConfig: FormConfig = {
    fields: [
      {
        fieldName: 'indicatorLayerNumber',
        type: 'select',
        label: '所属指标层',
        placeholder: '请选择',
        options: layerList,
        labelInValue: false,
      },
    ],
  };
  useEffect(() => {
    getLayerList();
  }, []);
  useEffect(() => {
    getIndicatorTable();
  }, [searchCondition]);
  const getLayerList = async () => {
    try {
      const res = await fetchApi.fetchIndicatorLayerList();
      if (res.code === HttpStatusCode.Success) {
        if (res.data) {
          setLayerList(
            res.data.map((item) => {
              return {
                label: item.layerName,
                value: item.layerNumber,
              };
            }),
          );
        }
      }
    } catch (err) {
      console.error(err);
    }
  };
  const getIndicatorTable = async () => {
    try {
      const res = await fetchApi.fetchIndicatorList(searchCondition.searchForm);
      if (res.code === HttpStatusCode.Success) {
        let temp: any = [];
        res.data &&
          res.data.forEach((indicatorLayer) => {
            indicatorLayer.indicatorInfoList &&
              indicatorLayer.indicatorInfoList.forEach((indicatorInfo) => {
                temp.push({
                  layerNumber: indicatorLayer.layerNumber,
                  layerName: indicatorLayer.layerName,
                  indicatorNumber: indicatorInfo.indicatorNumber,
                  indicatorName: indicatorInfo.indicatorName,
                  priority: indicatorInfo.priority,
                  modifyUser: indicatorLayer.modifyUser,
                  modifyTime: indicatorLayer.modifyTime,
                });
              });
          });
        setTableData(temp);
        editDataRef.current = new Map();
        temp.forEach((item) => {
          editDataRef.current.set(item.indicatorNumber, {
            layerNumber: item.layerNumber,
            priority: item.priority,
          });
        });
      }
    } catch (error) {
      console.error(error);
    }
  };
  const onSearchClick = (val) => {
    const data = {
      searchForm: val,
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(data);
  };
  const mergedCells = (record: DataType, index: number) => {
    const layerSet = new Set();
    const layerMap = new Map();
    tableData.forEach((row: any, idx: number) => {
      layerSet.add(row.layerName);
    });
    layerSet.forEach((name) => {
      const idx = tableData.findIndex((item) => {
        return item.layerName === name;
      });
      if (idx !== -1) {
        layerMap.set(name, idx);
      }
    });
    if (index === layerMap.get(record.layerName)) {
      let count = 0;
      tableData.forEach((row) => {
        if (row.layerName === record.layerName) {
          count++;
        }
      });
      return {
        rowSpan: count,
      };
    } else {
      return {
        rowSpan: 0,
      };
    }
  };
  const saveIndicatorPriority = async (layerNumber: string) => {
    try {
      const indicatorInfoEdit: any = {
        layerNumber: layerNumber,
        indicatorInfoList: [],
      };
      editDataRef.current.forEach((value, key) => {
        if (typeof value.priority !== 'number') {
          message.error('权重不能为空！');
          throw new Error('权重不能为空！');
        } else {
          layerNumber === value.layerNumber &&
            indicatorInfoEdit.indicatorInfoList.push({
              indicatorNumber: key,
              priority: value.priority,
            });
        }
      });
      const res = await fetchApi.editIndicatorList(indicatorInfoEdit);
      if (res.code === HttpStatusCode.Success) {
        message.success('保存成功！');
        setEditingKey('');
        getIndicatorTable();
      } else {
        message.error(res.message);
      }
    } catch (err) {
      console.error(err);
    }
  };
  const formatColumns = () => {
    return indicatorColumns.map((col: ColumnType<DataType>) => {
      switch (col.dataIndex) {
        case 'layerName':
        case 'modifyUser':
        case 'modifyTime':
          return {
            ...col,
            onCell: (record, index) => {
              return mergedCells(record, index as number);
            },
          };
        case 'priority':
          return {
            ...col,
            render: (text, record) => {
              if (editingKey === record.layerNumber) {
                return (
                  <Input
                    type="number"
                    defaultValue={text}
                    onBlur={(e) => {
                      if (e.target.value) {
                        editDataRef.current.set(record.indicatorNumber, {
                          layerNumber: record.layerNumber,
                          priority: Number(
                            Number(
                              e.target.value.replace(
                                /[^\d{1,}\.\d{1,}|\d{1,}]/g,
                                '',
                              ),
                            ).toFixed(3),
                          ),
                        });
                      } else {
                        editDataRef.current.set(record.indicatorNumber, {
                          layerNumber: record.layerNumber,
                          priority: null,
                        });
                        message.error('请输入数字！');
                      }
                    }}
                  ></Input>
                );
              } else {
                return <>{text}</>;
              }
            },
          };
        case 'operations':
          return {
            ...col,
            onCell: (record, index) => {
              return mergedCells(record, index as number);
            },
            render: (text, record) => {
              if (editingKey === record.layerNumber) {
                return (
                  <a
                    onClick={() => {
                      saveIndicatorPriority(record.layerNumber);
                    }}
                  >
                    保存
                  </a>
                );
              } else {
                return editingKey && editingKey !== record.layerNumber ? (
                  <a style={{ color: 'grey', cursor: 'not-allowed' }}>编辑</a>
                ) : (
                  <a
                    onClick={() => {
                      setEditingKey(record.layerNumber);
                    }}
                  >
                    编辑
                  </a>
                );
              }
            },
          };
        default:
          return col;
      }
    });
  };
  return (
    <>
      <CommonForm
        formConfig={formConfig}
        defaultValue={searchCondition.searchForm}
        layout="inline"
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={() => {
          setSearchCondition({
            searchForm: {
              indicatorLayerNumber: null,
            },
            pageNum: 1,
            pageSize: 10,
          });
          navigator('/app/indicatorConfig');
        }}
      />
      <Table
        bordered
        pagination={false}
        rowKey="indicatorNumber"
        scroll={{
          y: 700,
        }}
        dataSource={tableData}
        columns={formatColumns()}
      ></Table>
    </>
  );
};
export default React.memo(IndicatorConfig);
