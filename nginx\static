log_format  style_app   '$remote_addr - $remote_user [$time_local] "$http_x_forwarded_for" "$http_j_forwarded_for" '
                                '"$request" $status $bytes_sent '
                                '"$http_referer" "$http_user_agent" '
                                '"$gzip_ratio" '
                                '$request_time';

add_header 'Access-Control-Allow-Origin' *;
add_header 'Access-Control-Allow-Methods' *;
add_header 'Access-Control-Allow-Headers' *;
server {
    listen           80;
    listen 443 ssl; 
    server_name     *.jdl.cn;
    access_log      /export/servers/nginx/logs/app_access.log style_app;
    error_log       /export/servers/nginx/logs/app_error.log error;
 
    root /export/App/;
    error_page 302 = http://www.jd.com/error2.aspx;
    index index.html;
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|eot|woff|woff2|ttf|html)$ {
      add_header Cache-Control "no-cache, no-store, must-revalidate";
      add_header Pragma "no-cache";
      add_header Expires 0;
      add_header 'Access-Control-Allow-Origin' *;
      try_files $uri $uri/ =404;
   }
    location /qrcode-api/ {
      proxy_pass https://qr.m.jd.com/;
      proxy_set_header Host "qr.m.jd.com";
      proxy_set_header Referer $http_referer;
      proxy_cookie_path / "/; Secure; SameSite=None";
      proxy_cookie_domain .jd.com .jdl.cn;
      proxy_set_header Cookie $http_cookie;
    }

    location /xupload/ {
      proxy_pass https://webterminal.s3.cn-north-1.jdcloud-oss.com/;
    }
    
    location / {
        add_header 'Access-Control-Allow-Origin' *;
        if ( $request_uri ~* ^.+.(js|css|jpg|png|gif|tif|dpg|jpeg|eot|svg|ttf|woff|json|mp4|rmvb|rm|wmv|avi|3gp)$ ){
          add_header 'Cache-Control' max-age=7776000;
          add_header 'Access-Control-Allow-Origin' *;
        }
        try_files $uri /index.html;
    }

    
}
