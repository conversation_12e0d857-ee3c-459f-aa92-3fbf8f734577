import { TaskStatusEnum } from '@/utils/constant';
import { MapLayerEnum } from './enum';
export enum AnchorLayerType {
  MOVE_ROUND = 'VOT_TNTA',
  ROAD_CLOSED = 'VOT_ROAD_CLOSURE',
  NO_PARKING = 'VOT_NO_PARKING',
  FOCUS_AREAS = 'ATTENTION_REGION_WARN',
  ATTRIBUTE_ANNOTATION = 'ATTRIBUTE_ANNOTATION',
  SPEED_LIMIT = 'SPEED_LIMIT',
}
export const AnchorLayerText = new Map([
  [AnchorLayerType.MOVE_ROUND, '绕行障碍物'],
  [AnchorLayerType.ROAD_CLOSED, '封路障碍物'],
  [AnchorLayerType.NO_PARKING, '小区禁停区'],
  [AnchorLayerType.FOCUS_AREAS, '重点关注区'],
  [AnchorLayerType.ATTRIBUTE_ANNOTATION, '属性标注区'],
  [AnchorLayerType.SPEED_LIMIT, '道路限速区'],
]);

export const OtherTileLayer = new Map<MapLayerEnum, any>([
  [
    MapLayerEnum.OPEN_AREA,
    {
      layerId: MapLayerEnum.OPEN_AREA,
      url: `https://${process.env.JDX_APP_MAP_EDITOR}/geoserver/${process.env.JDX_APP_MAP_DATA}/wms`,
      params: {
        FORMAT: 'image/png',
        TILED: true,
        LAYERS: `${process.env.JDX_APP_MAP_DATA}:map_free_area_gcj02`,
        STYLES: 'open-area',
      },
    },
  ],
  [
    MapLayerEnum.LINE_BOUNDARY,
    {
      layerId: MapLayerEnum.LINE_BOUNDARY,
      url: `https://${process.env.JDX_APP_MAP_EDITOR}/geoserver/${process.env.JDX_APP_MAP_DATA}/wms`,
      params: {
        FORMAT: 'image/png',
        TILED: true,
        LAYERS: `${process.env.JDX_APP_MAP_DATA}:map_boundary_gcj02`,
        STYLES: 'line-boundary',
      },
    },
  ],
  [
    MapLayerEnum.GATE_LAYER,
    {
      layerId: MapLayerEnum.GATE_LAYER,
      url: `https://${process.env.JDX_APP_MAP_EDITOR}/geoserver/${process.env.JDX_APP_MAP_DATA}/wms`,
      params: {
        FORMAT: 'image/png',
        TILED: true,
        LAYERS: `${process.env.JDX_APP_MAP_DATA}:map_gate_gcj02`,
        STYLES: 'line-gate',
      },
    },
  ],
]);
export const initTileLayer = [
  {
    layerId: MapLayerEnum.OPEN_AREA,
    url: `https://${process.env.JDX_APP_MAP_EDITOR}/geoserver/${process.env.JDX_APP_MAP_DATA}/wms`,
    params: {
      FORMAT: 'image/png',
      TILED: true,
      LAYERS: `${process.env.JDX_APP_MAP_DATA}:map_free_area_gcj02`,
      STYLES: 'open-area',
    },
  },

  {
    layerId: MapLayerEnum.LINE_BOUNDARY,
    url: `https://${process.env.JDX_APP_MAP_EDITOR}/geoserver/${process.env.JDX_APP_MAP_DATA}/wms`,
    params: {
      FORMAT: 'image/png',
      TILED: true,
      LAYERS: `${process.env.JDX_APP_MAP_DATA}:map_boundary_gcj02`,
      STYLES: 'line-boundary',
    },
  },
  {
    layerId: MapLayerEnum.GATE_LAYER,
    url: `https://${process.env.JDX_APP_MAP_EDITOR}/geoserver/${process.env.JDX_APP_MAP_DATA}/wms`,
    params: {
      FORMAT: 'image/png',
      TILED: true,
      LAYERS: `${process.env.JDX_APP_MAP_DATA}:map_gate_gcj02`,
      STYLES: 'line-gate',
    },
  },
  {
    layerId: MapLayerEnum.INTERSECTION,
    url: `https://${process.env.JDX_APP_MAP_EDITOR}/geoserver/${process.env.JDX_APP_MAP_DATA}/wms`,
    params: {
      FORMAT: 'image/png',
      TILED: true,
      LAYERS: `${process.env.JDX_APP_MAP_DATA}:map_intersection_gcj02`,
      STYLES: 'intersection',
    },
  },
  {
    layerId: MapLayerEnum.CENTER_LINE,
    url: `https://${process.env.JDX_APP_MAP_EDITOR}/geoserver/${process.env.JDX_APP_MAP_DATA}/wms`,
    params: {
      FORMAT: 'image/png',
      TILED: true,
      LAYERS: `${process.env.JDX_APP_MAP_DATA}:map_lane_gcj02`,
      STYLES: 'line-center',
    },
  },
  {
    layerId: MapLayerEnum.STOP_LINE,
    url: `https://${process.env.JDX_APP_MAP_EDITOR}/geoserver/${process.env.JDX_APP_MAP_DATA}/wms`,
    params: {
      FORMAT: 'image/png',
      TILED: true,
      LAYERS: `${process.env.JDX_APP_MAP_DATA}:map_stop_line_gcj02`,
      STYLES: 'line-red',
    },
  },
];

// 获取状态颜色
export const getStatusColor = (status: string) => {
  switch (status) {
    case TaskStatusEnum.PENDING_CLAIM:
      return '#FF7700';
    case TaskStatusEnum.COLLECTING:
      return '#11D6D0';
    case TaskStatusEnum.COPY_COMPLETED:
      return '#18B312';
    case TaskStatusEnum.UNDER_EXPLORATION:
      return '#979797';
    default:
      return '#999999';
  }
};

export enum ENABLE_ENUM {
  ENABLE = 1,
  DISABLE = 0,
}
