/* eslint-disable no-unused-vars */
import { Form, Input, Select } from 'antd';
import { FormInstance } from 'antd/lib/form/Form';
import React, { useEffect, useState } from 'react';
import EditModuleTitle from '@/components/EditModuleTitle';
import './index.scss';
import { useCommonDropDown } from '@/utils/hooks';
const layout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 21 },
};
interface Props {
  girdInfo: any;
  formRef: FormInstance;
  onTemplateChanged: (templateId: number) => void;
}

const GirdInfo = (props: Props) => {
  const { girdInfo, formRef, onTemplateChanged } = props;
  const initialValues = { ...girdInfo };
  const dropDownData = useCommonDropDown(['BOX_TEMPLATE_NAME']);
  const [editConfiger, setEditConfiger] = useState<{
    boxTemplateNameList: any[];
  }>({ boxTemplateNameList: [] }); //
  useEffect(() => {
    girdInfo && girdInfo;
    formRef.setFieldsValue({
      ...girdInfo,
    });
    fetchOperatorList();
  }, [JSON.stringify(girdInfo)]);

  const fetchOperatorList = async () => {
    const enableArr: any = dropDownData.boxTemplateNameList
      ? dropDownData.boxTemplateNameList.filter((item: any) => {
          return item.enable;
        })
      : [];
    setEditConfiger({ boxTemplateNameList: enableArr });
  };

  return (
    <div className="gird-info">
      <EditModuleTitle title={'货箱信息'} />
      <Form
        {...layout}
        form={formRef}
        initialValues={initialValues}
        className="reform"
        autoComplete="off"
      >
        <Form.Item label="模板名称" name="name">
          <Select
            showSearch
            placeholder="系统自动带入"
            options={
              editConfiger.boxTemplateNameList?.map(
                (e: { code: any; name: string; parent: any }) => ({
                  label: e.name,
                  value: e.name,
                }),
              ) || []
            }
            onChange={async (value: any) => {
              const ele = editConfiger.boxTemplateNameList.find(
                (item) => item.name === value,
              );
              if (ele) onTemplateChanged && onTemplateChanged(ele.code);
            }}
          />
        </Form.Item>
        <Form.Item label="货箱型号" name="model">
          <Input placeholder="系统自动带入" disabled />
        </Form.Item>
        <Form.Item name="driverType" label="驱动类型">
          <Input disabled />
        </Form.Item>
        <Form.Item label="波特率" name="baudRate">
          <Input placeholder="系统自动带入" disabled />
        </Form.Item>
        <Form.Item label="串口号" name="deviceId">
          <Input placeholder="系统自动带入" disabled />
        </Form.Item>
        <Form.Item label="左侧货箱列数" name="leftBoxColumnNum">
          <Input placeholder="系统自动带入" disabled />
        </Form.Item>
        <Form.Item label="右侧货箱列数" name="rightBoxColumnNum">
          <Input placeholder="系统自动带入" disabled />
        </Form.Item>
      </Form>
    </div>
  );
};
export default React.memo(GirdInfo);
