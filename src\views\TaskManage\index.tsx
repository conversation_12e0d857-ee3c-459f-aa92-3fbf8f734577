import React, { useState, useEffect } from 'react';
import { CommonTable, TableOperateBtn, CommonForm } from '@/components';
import { tableColumns, SearchConfig } from './constant';
import { useTableData } from '@/components/CommonTable/useTableData';
import { RobotMapApi, TaskManageApi } from '@/fetch/business';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { useNavigate } from 'react-router-dom';
import { HttpStatusCode } from '@/fetch/core/constant';
const robotMapApi = new RobotMapApi();
const fetchApi = new TaskManageApi();
const TaskManage = () => {
  const navigator = useNavigate();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const initSearchCondition = {
    searchForm: {
      deviceName: null,
      orderNo: null,
      stationBaseId: null,
      taskStatus: [],
      taskType: null,
      rangeTime: null,
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValues.searchValues
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );
  const [stationList, setStationList] = useState<
    [{ lable: string; value: any }] | []
  >([]);

  const { tableData, loading } = useTableData(
    {
      searchForm: {
        ...searchCondition.searchForm,
        stationBaseId: searchCondition.searchForm.stationBaseId?.value,
        taskStatus: searchCondition.searchForm.taskStatus?.map(
          (item) => item.value,
        ),
        taskType: searchCondition.searchForm.taskType?.value,
      },
      pageNum: searchCondition.pageNum,
      pageSize: searchCondition.pageSize,
    },
    fetchApi.fetchTableList,
  );
  useEffect(() => {
    robotMapApi.fetchStationList().then((res) => {
      if (res.code === HttpStatusCode.Success) {
        setStationList(
          res.data?.map((val) => ({
            label: val.name,
            value: val.groupNo,
          })),
        );
      }
    });
  }, []);

  const onSearchClick = (val) => {
    const { rangeTime, ...other } = val;
    const data = {
      ...searchCondition,
      searchForm: {
        ...other,
        startTime:
          rangeTime &&
          rangeTime[0] &&
          rangeTime[0].format('YYYY-MM-DD HH:mm:ss'),
        endTime:
          rangeTime &&
          rangeTime[1] &&
          rangeTime[1].format('YYYY-MM-DD HH:mm:ss'),
      },
    };
    setSearchCondition(data);
  };

  const formatColumns = () => {
    return tableColumns.map((col) => {
      switch (col.dataIndex) {
        case 'operation':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return (
                <div className="operate">
                  <TableOperateBtn
                    title="任务日志"
                    handleClick={() => {
                      navigator(
                        '/app/taskManage/detail?taskId=' + record.taskId,
                      );
                    }}
                  />
                </div>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  return (
    <>
      <CommonForm
        formConfig={SearchConfig}
        optionsList={{
          stationBaseId: stationList,
        }}
        defaultValue={searchCondition.searchForm}
        layout="inline"
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={() => {
          setSearchCondition({ ...initSearchCondition })
        }}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        columns={formatColumns()}
        loading={loading}
        rowKey={'taskId'}
        searchCondition={searchCondition}
        onPageChange={(value: any) => setSearchCondition(value)}
      />
    </>
  );
};

export default TaskManage;
