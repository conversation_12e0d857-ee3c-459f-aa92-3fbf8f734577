import React, { useMemo, useRef, useState } from 'react';
import { Select, Spin } from 'antd';
import type { SelectProps } from 'antd/es/select';
import debounce from 'lodash/debounce';
import { AnyFunc } from '@/global';
import { fetchPoiList, LocationType, Poi } from '../../utils/dataSearch';
import './index.scss';

export interface DebounceSelectProps<ValueType = any>
  extends Omit<SelectProps<ValueType | ValueType[]>, 'options' | 'children'> {
  fetchOptions: (search: string) => Promise<ValueType[]>;
  debounceTimeout?: number;
  placeholder?: string;
}

function DebounceSelect<
  ValueType extends {
    key?: string;
    label: React.ReactNode;
    value: string | number;
    placeholder?: string;
    defaultValue?: string | undefined;
  } = any,
>({
  fetchOptions,
  debounceTimeout = 1000,
  ...props
}: DebounceSelectProps<ValueType>) {
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState<ValueType[]>([]);
  const fetchRef = useRef(0);

  const debounceFetcher = useMemo(() => {
    const loadOptions = (value: string) => {
      fetchRef.current += 1;
      const fetchId = fetchRef.current;
      setOptions([]);
      setFetching(true);

      fetchOptions(value).then((newOptions) => {
        if (fetchId !== fetchRef.current) {
          return;
        }

        setOptions(newOptions);
        setFetching(false);
      });
    };

    return debounce(loadOptions, debounceTimeout);
  }, [fetchOptions, debounceTimeout]);

  return (
    <Select
      style={{ width: '100%' }}
      showSearch
      labelInValue
      filterOption={false}
      // allowClear
      onSearch={debounceFetcher}
      notFoundContent={fetching ? <Spin size="small" /> : null}
      {...props}
      options={options}
      optionRender={(option: any, info: { index: number }) => {
        const { data } = option;
        return (
          <div className="location-search-select-option-label">
            <div className="title">{data.label}</div>
            <div className="address">{data.address}</div>
          </div>
        );
      }}
    />
  );
}

const DebounceAsyncSelect = (props: {
  onChange: AnyFunc;
  placeholder?: string;
  defaultValue?: string;
  locationType?: LocationType;
}) => {
  const [value, setValue] = useState<Poi[]>([]);
  return (
    <DebounceSelect
      value={value}
      placeholder={props?.placeholder}
      fetchOptions={(search) => fetchPoiList(search, props?.locationType)}
      onChange={(newValue) => {
        setValue(newValue as Poi[]);
        props?.onChange(newValue);
      }}
    />
  );
};

export default DebounceAsyncSelect;
