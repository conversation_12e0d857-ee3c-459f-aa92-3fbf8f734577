import { request } from '../core';

export class TaskManageApi {
  fetchDomain = `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}`;
  // 分页查询数据列表
  fetchTableList = ({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/integrate/task/web/get_task_info_page_list`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: { ...searchForm, pageNum, pageSize },
    };
    return request(options);
  };

  // 根据id获取任务详情
  getTaskDetail = (taskId: string) => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/integrate/task/web/get_task_detail_info_by_id`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: {
        taskId,
      },
    };
    return request(options);
  };

  // 获取任务日志详情列表
  fetchTaskLog = ({
    pageNum,
    pageSize,
    taskId,
  }: {
    pageNum: number;
    pageSize: number;
    taskId: string;
  }) => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/integrate/task/web/get_task_log_info_page_list`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: {
        taskId,
        pageNum,
        pageSize,
      },
    };
    return request(options);
  };

  // 下发终止任务的指令
  stopTask(taskId: string) {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/k2/management/warehouse_task/stop_task`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: {
        taskId,
      },
    };
    return request(options);
  }
}
