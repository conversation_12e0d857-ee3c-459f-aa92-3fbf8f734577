
import React from 'react';
const TriangularContainer = ({
  children,
  tranguleOffset = 20,
  customStyle = { backgroundColor: "#f1f1f1" }
}: {
  children: any,
  tranguleOffset?: number | string,
  customStyle?: React.CSSProperties
}) => {

  const styles: React.CSSProperties = {
    marginLeft: tranguleOffset, width: 0, height: 0, borderLeft: "10px solid transparent",
    borderRight: "10px solid transparent", borderBottom: `15px solid ${customStyle?.backgroundColor}`
  }

  return <>
    <div style={styles} />
    <div style={customStyle}>
      {children}
    </div>
  </>
}

export default React.memo(TriangularContainer);