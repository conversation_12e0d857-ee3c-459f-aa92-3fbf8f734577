import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import DescriptionTable from '../DescriptionTable';
import { Tooltip, Modal, Watermark } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import './index.scss';
import CustomButton, { ButtonType } from '@/components/CustomButton';
import { HttpStatusCode } from '@/fetch/core/constant';
import { OrderManageFetchApi } from '../../fetch';
import { RootState } from '@/redux/store';

const DeliveryOrderDetail = (props: any) => {
  const fetchApi = new OrderManageFetchApi();
  const { visible, id, onCancel } = props;
  const erp: any = useSelector((state: RootState) => state.common).userName;
  const [orderInfo, setOrderInfo] = useState<any>();
  const [scheduleInfo, setScheduleInfo] = useState<any>();
  const [deliveryInfo, setDeliveryInfo] = useState<any>();
  const orderInfoColumn: any[] = [
    { label: '订单编号', key: 'originalOrderId', span: 1 },
    {
      key: 'orderId',
      span: 1,
      label: (
        <div className="packageNo">
          <p>包裹号</p>
          <Tooltip
            placement="right"
            title={'包含“单号-包裹号-包裹数-配送次数”。'}
            overlayStyle={{ maxWidth: 600 }}
            autoAdjustOverflow={false}
          >
            <QuestionCircleOutlined className="question" />
          </Tooltip>
        </div>
      ),
    },
    { label: '订单来源', key: 'orderSourceName', span: 1 },
    { label: '收货人姓名', key: 'name', span: 1 },
    { label: '收货人手机号', key: 'contact', span: 1 },
    { label: '订单状态', key: 'orderStatusName', span: 1 },
    { label: '装载人手机号', key: 'loadUserContact', span: 1 },
    { label: '装载格口号', key: 'gridNo', span: 1 },
    { label: '停靠点名称', key: 'address', span: 1 },
    { label: '配送方式', key: 'deliveryModeName', span: 1 },
    { label: '投递方式', key: 'loadMethodName', span: 1 },
    { label: '站转站原因', key: 'stationTransferReason', span: 1 },
    { label: '取件人姓名', key: 'collectionUserName', span: 1 },
    { label: '取件人手机号', key: 'collectionUserContact', span: 1 },
    { label: '取件码', key: 'verifyCode', span: 1 },
  ];
  const scheduleColumn: any[] = [
    { label: '调度单号', key: 'scheduleName', span: 1 },
    { label: '车牌号', key: 'deviceName', span: 1 },
    { label: '所属站点', key: 'stationName', span: 1 },
  ];
  const deliveryColumn: any[] = [
    { label: '调度生成时间', key: 'scheduleCreateTime', span: 1 },
    { label: '订单装载时间', key: 'createTime', span: 1 },
    { label: '调度出发时间', key: 'scheduleStartTime', span: 1 },
    { label: '到达停靠点时间', key: 'arrivalTime', span: 1 },
    {
      key: 'gridOpenTime',
      span: 1,
      label: (
        <div className="packageNo">
          <p>开箱取件时间</p>
          <Tooltip
            placement="right"
            title={'实际货箱格口打开时间'}
            overlayStyle={{ maxWidth: 600 }}
            autoAdjustOverflow={false}
          >
            <QuestionCircleOutlined className="question" />
          </Tooltip>
        </div>
      ),
    },
    {
      key: 'pickupTime',
      span: 1,
      label: (
        <div className="packageNo">
          <p>取件上报时间</p>
          <Tooltip
            placement="right"
            title={'系统之间数据对接或更新的取件时间'}
            overlayStyle={{ maxWidth: 600 }}
            autoAdjustOverflow={false}
          >
            <QuestionCircleOutlined className="question" />
          </Tooltip>
        </div>
      ),
    },
    { label: '调度结束时间', key: 'scheduleEndTime', span: 1 },
  ];

  useEffect(() => {
    getOrderDetail(id);
  }, [id]);

  const getOrderDetail = async (id: any) => {
    const res: any = await fetchApi.fetchOrderDetail(id);
    if (res && res.code === HttpStatusCode.Success) {
      setOrderInfo({
        originalOrderId: res.data.originalOrderId,
        orderId: res.data.orderId,
        orderSourceName: res.data.orderSourceName,
        name: res.data.name,
        contact: res.data.contact,
        orderStatusName: res.data.orderStatusName,
        loadUserContact: res.data.loadUserContact,
        gridNo: res.data.gridNo,
        address: res.data.address,
        deliveryModeName: res.data.deliveryModeName,
        loadMethodName: res.data.loadMethodName,
        stationTransferReason: res.data.stationTransferReason,
        collectionUserName: res.data.collectionUserName,
        collectionUserContact: res.data.collectionUserContact,
        verifyCode: res.data.verifyCode,
        contactSecret: true,
        loadUserContactSecret: true,
        collectionUserContactSecret: true,
      });
      setScheduleInfo({
        scheduleName: res.data.scheduleName,
        deviceName: res.data.deviceName,
        stationName: res.data.stationName,
      });
      setDeliveryInfo({
        scheduleStartTime: res.data.scheduleStartTime,
        createTime: res.data.createTime,
        deliveryStartTime: res.data.deliveryStartTime,
        arrivalTime: res.data.arrivalTime,
        pickupTime: res.data.pickupTime,
        gridOpenTime: res.data.gridOpenTime,
        scheduleCreateTime: res.data.scheduleCreateTime,
        scheduleEndTime: res.data.scheduleEndTime,
      });
    }
  };

  return (
    <div className="order-detail">
      <Modal
        visible={visible}
        width={'90%'}
        title={'配送详情'}
        footer={
          <CustomButton
            title="关闭"
            onSubmitClick={onCancel}
            buttonType={ButtonType.DefaultButton}

          />
        }
        onCancel={onCancel}
      >
         <Watermark
          content={erp}
          font={{ color: 'rgba(0,0,0,.07)' }}
          gap={[50, 90]}
        >
        {orderInfo && (
          <DescriptionTable
            key={'order'}
            tableColumns={orderInfoColumn}
            title="订单信息"
            info={orderInfo}
            column={3}
            id={id}
          />
        )}
        {scheduleInfo && (
          <DescriptionTable
            key={'schedule'}
            tableColumns={scheduleColumn}
            title="调度单信息"
            info={scheduleInfo}
            column={3}
            id={id}
          />
        )}
        {deliveryInfo && (
          <DescriptionTable
            key={'delivery'}
            tableColumns={deliveryColumn}
            title="配送信息跟踪"
            info={deliveryInfo}
            column={1}
            id={id}
          />
        )}
        </Watermark>
      </Modal>
    </div>
  );
};

export default React.memo(DeliveryOrderDetail);
