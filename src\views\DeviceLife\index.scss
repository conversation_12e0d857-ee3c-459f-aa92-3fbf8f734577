.vehicle-management {
  display: flex;
  flex-direction: column;
  padding: 10px;

  .search-form {
    // padding: 15px 15px 5px;
    background-color: white;

    .form-button {
      margin-top: -25px;
      text-align: center;

      .submit-btn {
        background: rgba(49, 194, 166, 1);
        border: none;
        margin-right: 30px;
      }
    }
  }

  .card-container {
    margin-top: 10px;

    p {
      margin-top: 0px;
    }
  }

  .card-container .card-container > .#{$ant-prefix}-tabs-card .#{$ant-prefix}-tabs-content {
    // height: 120px;
    // margin-top: -16px;
  }

  .card-container > .#{$ant-prefix}-tabs-card .#{$ant-prefix}-tabs-content > .#{$ant-prefix}-tabs-tabpane {
    // padding: 16px;
    background: #fff;
  }

  .card-container > .#{$ant-prefix}-tabs-card > .#{$ant-prefix}-tabs-nav::before {
    display: none;
  }

  .card-container > .#{$ant-prefix}-tabs-card .#{$ant-prefix}-tabs-tab,
  [data-theme="compact"] .card-container > .#{$ant-prefix}-tabs-card .#{$ant-prefix}-tabs-tab {
    background: transparent;
    border-color: transparent;
  }

  .card-container > .#{$ant-prefix}-tabs-card .#{$ant-prefix}-tabs-tab-active,
  [data-theme="compact"] .card-container > .#{$ant-prefix}-tabs-card .#{$ant-prefix}-tabs-tab-active {
    background: #fff;
    border-color: #fff;
  }

  #components-tabs-demo-card-top .code-box-demo {
    padding: 24px;
    overflow: hidden;
    background: #f5f5f5;
  }

  [data-theme="compact"] .card-container > .#{$ant-prefix}-tabs-card .#{$ant-prefix}-tabs-content {
    // height: 120px;
    // margin-top: -8px;
  }

  [data-theme="dark"] .card-container > .#{$ant-prefix}-tabs-card .#{$ant-prefix}-tabs-tab {
    background: transparent;
    border-color: transparent;
  }

  [data-theme="dark"] #components-tabs-demo-card-top .code-box-demo {
    background: #000;
  }

  [data-theme="dark"] .card-container > .#{$ant-prefix}-tabs-card .#{$ant-prefix}-tabs-content > .#{$ant-prefix}-tabs-tabpane {
    background: #141414;
  }

  [data-theme="dark"] .card-container > .#{$ant-prefix}-tabs-card .#{$ant-prefix}-tabs-tab-active {
    background: #141414;
    border-color: #141414;
  }

  .manage-table {
    min-height: 120px;
    height: 100%;
    margin-top: -16px;
    background: #fff;

    .head-time {
      display: flex;
      justify-content: left;
      margin-bottom: 10px;

      span {
        display: block;
        margin-right: 20px;
        font-size: 14px;
      }
    }

    .status-stop {
      color: #d9001b;
      margin-left: 0 !important;
    }

    .status-active {
      color: #1abc9c;
    }

    .operate-btn {
      text-align: center;
      display: flex;
      justify-content: center;

      a {
        display: block;
        margin-right: 7px;
        margin-left: 7px;
        font-size: 13px;
      }

      .active {
        color: #1abc9c;
      }
    }
  }
}
