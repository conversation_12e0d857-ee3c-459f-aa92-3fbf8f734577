import React, { useEffect, useRef, useState } from 'react';
import { buildURL, parseResponseData } from '@/fetch/core/util';
import { Select, Spin } from 'antd';
import './index.scss';

const LocationSearch = ({
  changeSelect,
  labelInfo,
  parentOptions,
}: {
  changeSelect: any;
  labelInfo?: { label: string; lat: string; lon: string; index: number };
  parentOptions?: any[];
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [options, setOptions] = useState<any[]>(parentOptions || []);
  const fetchIdRef = useRef(0);
  const [selectedInfo, setSelectedInfo] = useState<any>();

  useEffect(() => {
    setSelectedInfo(
      labelInfo
        ? {
            label: labelInfo?.label,
            value: `${labelInfo?.lat}-${labelInfo?.lon}:${labelInfo?.index}`,
          }
        : null,
    );
  }, [JSON.stringify(labelInfo)]);
  useEffect(() => {
    setOptions(parentOptions || []);
  }, [parentOptions]);

  const debounce = (func, wait) => {
    let timeout;
    return (...args) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  };

  const fetchPoiList = (searchText: string): Promise<any[]> => {
    return new Promise((resolve, reject) => {
      const win = window as any;
      win.$.ajax({
        type: 'get',
        url: buildURL({
          absoluteURL: 'https://apis.map.qq.com/ws/place/v1/suggestion',
          urlParams: {
            key: 'KRBBZ-VJEWS-Q3IOZ-67LGG-RVU2E-UEF6X',
            keyword: encodeURI(searchText),
            output: 'jsonp',
          },
        }),
        dataType: 'jsonp',
        success: (res: any) => {
          if (res.status == 0) {
            resolve(res?.data ?? []);
          }
        },
      });
    });
  };

  const handleSearch = debounce(async (value) => {
    if (!value) {
      setOptions([]);
      return;
    }
    setLoading(true);
    const fetchId = ++fetchIdRef.current;
    const res = await fetchPoiList(value);
    if (fetchId === fetchIdRef.current) {
      const newOptions = res.map((item, index) => ({
        value: `${item.location.lat}-${item.location.lng}:${index}`,
        label: item.title,
        address: item.address,
      }));
      setOptions(newOptions);
      setLoading(false);
    }
  }, 300);

  const transformLocationGcj02towgs84 = (location: {
    lon: any;
    lat: any;
  }): { lon: number; lat: number } => {
    const win = window as any;
    const formatedLoaction = win
      .coordtransform()
      .gcj02towgs84(parseFloat(location.lon), parseFloat(location.lat));
    return { lon: formatedLoaction[0], lat: formatedLoaction[1] };
  };

  return (
    <Select
      className="location-search-select"
      style={{ width: '100%' }}
      showSearch
      labelInValue
      value={selectedInfo}
      filterOption={false}
      notFoundContent={loading ? <Spin size="small" /> : null}
      options={options}
      popupMatchSelectWidth={false}
      placeholder="搜索地理位置"
      optionRender={(option: any, info: { index: number }) => {
        const { data } = option;
        return (
          <div className="location-search-select-option-label">
            <div className="title">{data.label}</div>
            <div className="address">{data.address}</div>
          </div>
        );
      }}
      onSearch={(value) => {
        handleSearch(value);
      }}
      onChange={(value) => {
        setSelectedInfo(value);
        const [lat, lon] = value?.value
          ? value.value.split(':')[0].split('-')
          : [null, null];
        // changeSelect &&
        //   changeSelect({
        //     ...transformLocationGcj02towgs84({ lon: lon, lat: lat }),
        //     label: value.label,
        //   });
        changeSelect &&
          changeSelect({
            lat: lat,
            lon: lon,
            label: value.label,
            index: value.value.split(':')[1],
          });
      }}
    />
  );
};

export default React.memo(LocationSearch);
