import { YESNO } from '@/utils/enum';
import { request } from '../core';

export class IssueCateGoryManageApi {
  fetchDomain = `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}`;
  // 分页查询列表
  fetchTableList({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: any;
  }) {
 
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/bugCategory/getPageList',
      body: {
        pageNum,
        pageSize,
        ...searchForm,
      },
      newGeteway: true,
    };
    return request(options);
  }


  getModuleList() {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/bugCategory/getModuleList',
      newGeteway: true,
    };
    return request(options);
  }
  //  新建
  addCateGoryInfo(requestBody: Object) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/bugCategory/add',
      body: requestBody,
      newGeteway: true,
    };
    return request(options);
  }

  //  编辑
  editCateGoryInfo(requestBody: Object) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/bugCategory/edit',
      body: requestBody,
      newGeteway: true,
    };
    return request(options);
  }
  // 获取详情
  getCateGoryInfoById(requestBody: Object) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/bugCategory/getDetail',
      body: requestBody,
      newGeteway: true,
    };
    return request(options);
  }

  // 删除问题分类
  deleteCateGoryInfo(requestBody: Object) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/bugCategory/delete',
      body: requestBody,
      newGeteway: true,
    };
    return request(options);
  }
}
