// 列表页面可选页数
import { YESNO, ProductType } from './enum';
export const pageSizeOptions: string[] = ['10', '20', '30', '40', '100'];
export const DEFAULT_PAGE = {
  pageNum: 1,
  pageSize: 10,
};

export interface TableListType {
  list: any[];
  total: number;
  pages: number;
}

export const EnableDropDownList = [
  {
    label: '停用',
    value: 0,
  },
  {
    label: '启用',
    value: 1,
  },
];

export const ProductDropDownList = [
  {
    label: '无人车',
    value: ProductType.VEHICLE,
  },
  {
    label: '机器人',
    value: ProductType.ROBOT,
  },
  {
    label: '多合一',
    value: ProductType.INTEGRATE,
  },
];

export enum DropDownType {
  INPUT = 'INPUT',
  SELECT = 'SELECT',
  DATEPICKER = 'DATEPICKER',
  MULTIPLESELECT = 'MULTIPLESELECT',
}

export enum dropDownKey {
  ENABLE = 'ENABLE',
  YES_OR_NO = 'YES_OR_NO',
  PRODUCT_TYPE = 'PRODUCT_TYPE', // 所属产品
  SUPPLIER = 'SUPPLIER', // 供应商
  HARDWARE_TYPE = 'HARDWARE_TYPE', // 硬件类型
  PRODUCT_MANUFACTORY = 'PRODUCT_MANUFACTORY', // 生产厂商
  MANUFACTORY_PROPERTY = 'MANUFACTORY_PROPERTY', // 厂商性质
  SENSOR_SCHEME_NAME = 'SENSOR_SCHEME_NAME', // 传感器方案名称
  BOX_DRIVER_TYPE = 'BOX_DRIVER_TYPE', // 货箱驱动类型
  SERIAL_PORT_NUMBER = 'SERIAL_PORT_NUMBER', // 货箱串口号
  GRID_POSITION = 'GRID_POSITION', // 格口方位
  ROBOT_GRID_TYPE = 'ROBOT_GRID_TYPE', // 机器人格口型号
  TAG_LIST = 'TAG_LIST', // 系统标签
  TELECOM_OPERATOR = 'TELECOM_OPERATOR', // 运营商
  MESSAGE_MODULE = 'MESSAGE_MODULE', // 对照模块
  JIRA_TEST_RESULT = 'JIRA_TEST_RESULT', // jira测试结果
  SHADOW_EVENT_SOURCE = 'SHADOW_EVENT_SOURCE', // 影子事件来源
  EVENT_GROUP = 'EVENT_GROUP', // 影子事件类别
  LOG_APP = 'LOG_APP', // 应用服务
  LOG_OPERATION_TYPE = 'LOG_OPERATION_TYPE', // 日志操作类型
  LOG_MODULE = 'LOG_MODULE', // 日志模块
  RESOURCE_APP = 'RESOURCE_APP', // 资源应用方
  RESOURCE_TYPE = 'RESOURCE_TYPE', // 资源类型

  VEHICLE_TYPE = 'VEHICLE_TYPE', // 车型类型
  VEHICLE_HARDWARE_STATUS = 'VEHICLE_HARDWARE_STATUS', // 车辆生命周期
  VEHICLE_CHECK_STATUS = 'VEHICLE_CHECK_STATUS', // 标定检测状态
  VEHICLE_OWNER_USE_CASE = 'VEHICLE_OWNER_USE_CASE', // 车辆归属方
  VEHICLE_BUSINESS_TYPE = 'VEHICLE_BUSINESS_TYPE', // 车辆类型
  DEFAULT_STATUS = 'DEFAULT_STATUS', // 存在未完成维修单

  // 站点管理
  STATION_USECASE = 'STATION_USE_CASE', // 站点用途
  STATION_STATUS = 'STATION_STATUS', // 站点状态

  // 停靠点管理
  STOP_TYPE = 'STOP_TYPE_NEW', // 点位类型
  STOP_STATUS = 'STOP_STATUS', // 点位状态

  // 代收点管理
  STOP_COLLECTION_STATUS = 'STOP_COLLECTION_STATUS', // 代收点状态

  // 开机启动异常管理
  VEHICLE_EXCEPTION_DEVICE = 'VEHICLE_EXCEPTION_DEVICE', // 车辆异常设备
  VEHICLE_EXCEPTION_MODULE = 'VEHICLE_EXCEPTION_MODULE', // 车辆异常模块

  // 多合一
  WAREHOUSE_TASK_TYPE = 'WAREHOUSE_TASK_TYPE', // 多合一任务类型
  WAREHOUSE_TASK_STATUS = 'WAREHOUSE_TASK_STATUS', // 多合一任务状态
  WAREHOUSE_POINT_TYPE = 'WAREHOUSE_POINT_TYPE',
}

export enum dropDownListKey {
  ENABLE = 'enableList',
  YES_OR_NO = 'yesOrNoList',
  PRODUCT_TYPE = 'productTypeList',
  SUPPLIER = 'supplierList', // 供应商
  HARDWARE_TYPE = 'hardwareTypeList', // 硬件类型
  PRODUCT_MANUFACTORY = 'productManufactoryList', // 生产厂商
  MANUFACTORY_PROPERTY = 'manufactoryPropertyList', // 厂商性质
  SENSOR_SCHEME_NAME = 'sensorSchemeNameList', // 传感器方案名称
  BOX_DRIVER_TYPE = 'boxDriverTypeList',
  SERIAL_PORT_NUMBER = 'serialPortNumberList',
  GRID_POSITION = 'gridPositionList', // 格口方位
  ROBOT_GRID_TYPE = 'robotGridTypeList', // 机器人格口型号
  TAG_LIST = 'tagList', // 系统标签
  TELECOM_OPERATOR = 'telecomOperatorList', // 运营商
  MESSAGE_MODULE = 'messageModuleList', // 对照模块
  JIRA_TEST_RESULT = 'jiraTestResultList', // jira测试结果
  SHADOW_EVENT_SOURCE = 'shadowEventSourceList', // 影子事件来源
  EVENT_GROUP = 'eventGroupList', // 影子事件类别
  LOG_APP = 'logAppList', // 应用服务
  LOG_OPERATION_TYPE = 'logOperationTypeList', // 日志操作类型
  LOG_MODULE = 'logModuleList', // 日志模块
  RESOURCE_APP = 'resourceAppList', // 资源应用方
  RESOURCE_TYPE = 'resourceTypeList', // 资源类型

  VEHICLE_TYPE = 'deviceTypeBaseInfoList', // 车型类型
  VEHICLE_HARDWARE_STATUS = 'vehicleHardwareStatusList', // 车辆生命周期
  VEHICLE_CHECK_STATUS = 'vehicleCheckStatusList', // 标定检测状态
  VEHICLE_OWNER_USE_CASE = 'vehicleOwnerUseCaseList', // 车辆归属方
  VEHICLE_BUSINESS_TYPE = 'vehicleBusinessTypeList', // 车辆类型
  DEFAULT_STATUS = 'defaultStatusList', // 存在未完成维修单

  // 站点管理
  STATION_USECASE = 'stationUseCaseList', // 站点用途
  STATION_STATUS = 'stationStatusList', // 站点状态

  // 停靠点管理
  STOP_TYPE = 'stopTypeNewList', // 点位类型
  STOP_STATUS = 'stopStatusList', // 点位状态

  // 代收点管理
  STOP_COLLECTION_STATUS = 'stopCollectionStatusList', // 代收点状态

  // 开机启动异常管理
  VEHICLE_EXCEPTION_DEVICE = 'vehicleExceptionDeviceList', // 车辆异常设备
  VEHICLE_EXCEPTION_MODULE = 'vehicleExceptionModuleList', // 车辆异常模块

  COCKPIT_TYPE = 'cockpitTypeList', //

  // 多合一
  WAREHOUSE_TASK_TYPE = 'WarehouseTaskTypeList', // 多合一任务类型
  WAREHOUSE_TASK_STATUS = 'WarehouseTaskStatusList', // 多合一任务状态
  WAREHOUSE_POINT_TYPE = 'warehousePointTypeList',
}

// 订阅事件
export enum GlobalEventName {
  EVENT_ADD_CONFIG = 'addGridConfig',
  EVENT_DELETE_CONFIG = 'deleteGridConfig',
  EVENT_UPDATE_CONFIG = 'updateGridConfig',
  EVENT_VALID_CONFIG = 'validGridConfig',
}

export enum SUPPLIER {
  JD = 'JD',
  NEOLIX = 'NEOLIX',
  RINO = 'RINO',
}

export enum STATION_USECASE {
  'PRODUCTION_FACTORY' = 'PRODUCTION_FACTORY',
  'OPEN_OPERATION' = 'OPEN_OPERATION',
  'CAMPUS_OPERATION' = 'CAMPUS_OPERATION',
  'QA' = 'QA',
  'FIX_CENTER' = 'FIX_CENTER',
  'SCHEDULE_CENTER' = 'SCHEDULE_CENTER',
  'OUTER_MARKER' = 'OUTER_MARKER',
  'TIMELY_DELIVERY' = 'TIMELY_DELIVERY',
  'DEMONSTRATE' = 'DEMONSTRATE',
  'PARK_DELIVERY' = 'PARK_DELIVERY',
}
export enum BindCrossStopPageType {
  SINGLE = 'SINGLE', // 单车
  BATCH = 'BATCH', // 多车
}
export enum BindStoppointTab {
  BINDCURRENTSTOPPOINT = 'BINDCURRENTSTOPPOINT',
  BINDCROSSSTOPPOINT = 'BINDCROSSSTOPPOINT',
}

export enum StopPointType {
  HOME = 'homeList',
  PICKUP = 'pickList',
  LOAD = 'loadList',
  UNLOAD = 'unloadList',
  VENDING = 'vendingList',
  STRADDLE = 'straddleList',
  COLLECT = 'collectList',
}

export const StopPointKeyMap = new Map([
  ['homeList', 'HOME'],
  ['pickList', 'PICKUP'],
]);
export enum LayerIdEnum {
  TIANMAP_LAYER = 'TIANMAP_LAYER',
  TDTCVA_LAYER = 'TDTCVA_LAYER',
  WSM_LAYER = 'WSM_LAYER',
  ANCHOR_LAYER = 'ANCHOR_LAYER',
  SEARCHPOINT_LAYER = 'SEARCHPOINT_LAYER',
  INTER_LAYER = 'INTER_LAYER',
  BOUNDARY_LAYER = 'BOUNDARY_LAYER',
  STOPLINE_LAYER = 'STOPLINE_LAYER',
  GATE_LAYER = 'GATE_LAYER',
  OPENAREA_LAYER = 'OPENAREA_LAYER',
  PLANNED_ROUTE_LAYER = 'PLANNED_ROUTE_LAYER',
}

export const returnTileLayers = () => {
  return [
    {
      layerId: LayerIdEnum.INTER_LAYER,
      url: `https://${process.env.JDX_APP_MAP_EDITOR}/geoserver/${process.env.JDX_APP_MAP_DATA}/wms`,
      params: {
        FORMAT: 'image/png',
        TILED: true,
        LAYERS: `${process.env.JDX_APP_MAP_DATA}:map_intersection`,
        STYLES: 'intersection',
      },
    },
    {
      layerId: LayerIdEnum.WSM_LAYER,
      url: `https://${process.env.JDX_APP_MAP_EDITOR}/geoserver/${process.env.JDX_APP_MAP_DATA}/wms`,
      params: {
        FORMAT: 'image/png',
        TILED: true,
        LAYERS: `${process.env.JDX_APP_MAP_DATA}:map_lane`,
        STYLES: 'line-center',
      },
    },
    {
      layerId: LayerIdEnum.OPENAREA_LAYER,
      url: `https://${process.env.JDX_APP_MAP_EDITOR}/geoserver/${process.env.JDX_APP_MAP_DATA}/wms`,
      params: {
        FORMAT: 'image/png',
        TILED: true,
        LAYERS: `${process.env.JDX_APP_MAP_DATA}:map_free_area`,
        STYLES: 'open-area',
      },
    },
    {
      layerId: LayerIdEnum.BOUNDARY_LAYER,
      url: `https://${process.env.JDX_APP_MAP_EDITOR}/geoserver/${process.env.JDX_APP_MAP_DATA}/wms`,
      params: {
        FORMAT: 'image/png',
        TILED: true,
        LAYERS: `${process.env.JDX_APP_MAP_DATA}:map_boundary`,
        STYLES: 'line-boundary',
      },
    },
    {
      layerId: LayerIdEnum.STOPLINE_LAYER,
      url: `https://${process.env.JDX_APP_MAP_EDITOR}/geoserver/${process.env.JDX_APP_MAP_DATA}/wms`,
      params: {
        FORMAT: 'image/png',
        TILED: true,
        LAYERS: `${process.env.JDX_APP_MAP_DATA}:map_stop_line`,
        STYLES: 'line-red',
      },
    },
    {
      layerId: LayerIdEnum.GATE_LAYER,
      url: `https://${process.env.JDX_APP_MAP_EDITOR}/geoserver/${process.env.JDX_APP_MAP_DATA}/wms`,
      params: {
        FORMAT: 'image/png',
        TILED: true,
        LAYERS: `${process.env.JDX_APP_MAP_DATA}:map_gate`,
        STYLES: 'line-gate',
      },
    },
  ];
};

export enum InsuredStatus {
  PENDING = 'PENDING', // 待生效
  EFFECTIVE = 'EFFECTIVE', // 已生效
  EXPIRED = 'INACTIVE', // 已失效
}

export enum DeviceInsuredStatus {
  NORMAL = 'NORMAL', // 正常
  LAPSED = 'LAPSED', // 退保
  INACTIVE = 'INACTIVE', // 失效
}

export enum TaskStatusEnum {
  UNDER_EXPLORATION = 'UNDER_EXPLORATION',
  PENDING_CLAIM = 'PENDING_CLAIM',
  COLLECTING = 'COLLECTING',
  PENDING_COPY = 'PENDING_COPY',
  COPYING = 'COPYING',
  COPY_COMPLETED = 'COPY_COMPLETED',
  MAP_IN_PROGRESS = 'MAP_IN_PROGRESS',
  MAP_RELEASE = 'MAP_RELEASE',
  TASK_CLOSED = 'TASK_CLOSED',
}

export const TaskStatusTextMap = new Map([
  [TaskStatusEnum.UNDER_EXPLORATION, '勘查中'],
  [TaskStatusEnum.PENDING_CLAIM, '待认领'],
  [TaskStatusEnum.COLLECTING, '采集中'],
  [TaskStatusEnum.PENDING_COPY, '待拷贝'],
  [TaskStatusEnum.COPYING, '拷贝中'],
  [TaskStatusEnum.COPY_COMPLETED, '拷贝完成'],
  [TaskStatusEnum.MAP_IN_PROGRESS, '地图制作中'],
  [TaskStatusEnum.MAP_RELEASE, '地图上线'],
  [TaskStatusEnum.TASK_CLOSED, '已完成'],
]);

export enum TASK_ROUTE_COLOR {
  BLUE = '#3C6EF0',
  GREEN = '#12B35D',
  YELLOW = '#FF7700',
}
export const TASK_ROUTE_COLOR_STRING = {
  [TASK_ROUTE_COLOR.BLUE]: 'BLUE',
  [TASK_ROUTE_COLOR.GREEN]: 'GREEN',
  [TASK_ROUTE_COLOR.YELLOW]: 'YELLOW',
};
export const TASK_ROUTE_HIGHLIGHT_COLOR = {
  [TASK_ROUTE_COLOR.BLUE]: '#FF1037',
  [TASK_ROUTE_COLOR.GREEN]: '#FF1037',
  [TASK_ROUTE_COLOR.YELLOW]: '#FF1037',
};
export enum PRECISE_TYPE {
  STANDARD_PRECISION = 'STANDARD_PRECISION', // 标准精度
  HIGH_PRECISION = 'HIGH_PRECISION', // 高精度
}

export enum ROUTE_PLAN_TYPE {
  MOTOR_VEHICLE = 'MOTOR_VEHICLE', // 机动车
  NON_MOTOR_VEHICLE = 'NON_MOTOR_VEHICLE', // 非机动车
}

export enum ELEMENT_TYPE {
  STATION = 'STATION', // 站点
  STOP = 'STOP', // 停靠点
  VEHICLE = 'VEHICLE', // 车辆
  MAP_COLLECTION_TASK = 'MAP_COLLECTION_TASK', // 地图采集任务
}

export enum SearchType {
  LOCATION = 'location', // 位置
  ELEMENT = 'element', // 元素
}

export enum ChargeType {
  CHARGE = 'charge',
  SWAP = 'swap',
}

export const ChargeTypeText: any = {
  [ChargeType.CHARGE]: '直充',
  [ChargeType.SWAP]: '换电',
};

export enum WorkMode {
  SYS = 'sys',
  SA = 'sa',
}


export enum OccupyStatus {
  EMPTY = 'empty',
  BIND_CONTAINER = 'bind_container',
  BIND_ROBOT = 'bind_robot',
}

export const OccupyStatusName = {
  [OccupyStatus.EMPTY]: '未绑定容器',
  [OccupyStatus.BIND_CONTAINER]: '未占用',
  [OccupyStatus.BIND_ROBOT]: '已占用',
};
