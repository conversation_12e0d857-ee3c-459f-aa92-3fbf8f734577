import { Spin } from 'antd'
import React from 'react';
import './index.scss';
const Loading = ({fetching, tips} : {fetching: boolean, tips?: string}) => {
  if (fetching) {
    return (
      <div className="loading_container">
        <div className="loading">
          <Spin
            tip={tips ?? "加载中..."}
            size="large"
            spinning={fetching}
          />
        </div>
      </div>
    );
  } else {
    return <div></div>
  }
}

export default React.memo(Loading);