import {
  CompleteRepairRequest,
  NewRepairOrder,
  RepairOrderRequest,
} from '@/types';
import { request } from '../core';
import { Method } from '../core/constant';
export class RepairOrderFetchApi {
  // 维修单列表查询方法
  public async getRepairOrderTable(searchOptions: RepairOrderRequest) {
    const { pageNum, pageSize, searchForm } = searchOptions;
    const requestOptions: RequestOptions = {
      path: '/k2/management/require/require_info_get_page_list',
      method: Method.POST,
      urlParams: {
        pageNum,
        pageSize,
      },
      body: searchForm,
    };
    return request(requestOptions);
  }

  // 获取维修单详情
  public async getRepairOrderDetail(requireNumber: string) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/require/require_info_get_detail',
      method: Method.GET,
      urlParams: { requireNumber },
    };
    return request(requestOptions);
  }

  // 处理维修单
  public async handleRepairOrder({
    number,
    isHandle,
    requireHardwareTypeIds,
    remark,
  }: {
    number: string;
    isHandle: number;
    requireHardwareTypeIds?: any[];
    remark?: string;
  }) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/require/handle_require',
      method: Method.PUT,
      body: {
        number,
        isHandle,
        requireHardwareTypeIds,
        remark,
      },
    };
    return request(requestOptions);
  }

  // 开始维修
  public async beginRepair(requireNumber: string) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/require/begin_require',
      method: Method.PUT,
      urlParams: {
        requireNumber,
      },
    };
    return request(requestOptions);
  }

  // 维修完成
  public async completeRepair(completeRepairRequest: CompleteRepairRequest) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/require/complete_require',
      method: Method.POST,
      body: completeRepairRequest,
    };
    return request(requestOptions);
  }

  // 维修确认
  public async checkRepair({
    number,
    remark,
  }: {
    number: string;
    remark?: string;
  }) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/require/check_require',
      method: Method.PUT,
      body: {
        number,
        remark,
      },
    };
    return request(requestOptions);
  }

  // 获取车辆硬件型号下拉列表
  fetchVehicleHardwareModel(deviceBaseId: number) {
    const options: RequestOptions = {
      method: 'GET',
      path: `/k2/management/device/get_hardware_model_of_device?deviceBaseId=${deviceBaseId}`,
    };
    return request(options);
  }

  // 查询某硬件类型下硬件型号列表
  fetchHardwareModel(hardwareTypeId: number) {
    const options: RequestOptions = {
      method: 'GET',
      path: `/k2/management/hardware_type/get_hardware_model_of_type?hardwareTypeId=${hardwareTypeId}`,
    };
    return request(options);
  }

  getHardwareType() {
    const requestOptions: RequestOptions = {
      path: '/k2/management/common/common_get_down_list',
      method: Method.POST,
      body: {
        keyList: ['HARDWARE_TYPE'],
      },
    };
    return request(requestOptions);
  }
}
