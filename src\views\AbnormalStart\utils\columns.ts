import {
  dropDownListKey,
  dropDown<PERSON>ey,
  EnableDropDownList,
} from '@/utils/constant';
import { FormConfig } from '@/components';
export const tableColumns: any[] = [
  {
    title: '序号',
    dataIndex: 'rowIndex',
    align: 'center',
    width: 40,
  },
  {
    title: '编号',
    dataIndex: 'number',
    align: 'center',
    width: 40,
    ellipsis: true,
  },
  {
    title: '设备',
    dataIndex: 'deviceName',
    align: 'center',
    width: 50,
    ellipsis: true,
  },
  {
    title: '模块所属',
    dataIndex: 'moduleName',
    align: 'center',
    width: 55,
    ellipsis: true,
  },
  {
    title: '模块字段名',
    dataIndex: 'moduleField',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '模块名称',
    dataIndex: 'moduleFieldName',
    width: 120,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '常规耗时(s)',
    dataIndex: 'normalTime',
    align: 'center',
    width: 60,
    ellipsis: true,
  },
  {
    title: '超时阈值(s)',
    dataIndex: 'overtimeThreshold',
    align: 'center',
    width: 60,
    ellipsis: true,
  },
  {
    title: '责任人ERP',
    dataIndex: 'principalErps',
    align: 'center',
    width: 70,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 55,
    align: 'center',
    fixed: 'right',
  },
];

export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'device',
      label: '设备',
      placeholder: '请选择设备',
      type: 'select',
      showSearch: true,
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.VEHICLE_EXCEPTION_DEVICE,
      dropDownListKey: dropDownListKey.VEHICLE_EXCEPTION_DEVICE,
    },
    {
      fieldName: 'module',
      label: '模块所属',
      placeholder: '请选择模块所属',
      type: 'select',
      showSearch: true,
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.VEHICLE_EXCEPTION_MODULE,
      dropDownListKey: dropDownListKey.VEHICLE_EXCEPTION_MODULE,
    },
    {
      fieldName: 'moduleField',
      label: '模块字段名',
      placeholder: '请输入模块字段名',
      type: 'input',
    },
    {
      fieldName: 'moduleFieldName',
      label: '模块名称',
      placeholder: '请输入模块名称',
      type: 'input',
    },
    {
      fieldName: 'principalErp',
      label: '责任人ERP',
      placeholder: '请输入责任人ERP',
      type: 'input',
    },
  ],
};

export const editFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'module',
      label: '模块',
      placeholder: '请选择模块名称',
      type: 'select',
    },
    {
      fieldName: 'code',
      label: '编码',
      placeholder: '请输入编码名称',
      type: 'input',
    },
    {
      fieldName: 'message',
      label: '消息内容',
      placeholder: '请输入消息内容',
      type: 'input',
    },
    {
      fieldName: 'transform',
      label: '转换消息',
      placeholder: '请输入转换消息',
      type: 'input',
    },
    {
      fieldName: 'enable',
      label: '状态',
      placeholder: '请选择状态',
      type: 'radioGroup',
      options: EnableDropDownList,
    },
  ],
};
