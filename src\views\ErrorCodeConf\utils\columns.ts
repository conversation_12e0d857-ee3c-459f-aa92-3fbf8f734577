import { dropDownListKey, dropDown<PERSON>ey } from '@/utils/constant';
import { FieldItem, FormConfig } from '@/components';
export const tableColumns: any[] = [
  {
    title: '序号',
    dataIndex: 'order',
    width: 100,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '错误码',
    dataIndex: 'errorCode',
    width: 150,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '错误信息',
    dataIndex: 'errorMessage',
    width: 180,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '翻译信息',
    dataIndex: 'errorTranslate',
    width: 200,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作人',
    dataIndex: 'modifyUser',
    width: 120,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    width: 180,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 140,
    align: 'center',
    ellipsis: true,
    fixed: 'right',
  },
];

export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'errorCode',
      label: '错误码',
      placeholder: '请输入错误码',
      type: 'input',
    },
    {
      fieldName: 'errorMessage',
      label: '错误信息',
      placeholder: '请输入错误信息',
      type: 'input',
    },
    {
      fieldName: 'errorTranslate',
      label: '翻译信息',
      placeholder: '请输入翻译信息',
      type: 'input',
    },
    {
      fieldName: 'modifyUser',
      label: '编辑人',
      placeholder: '请输入编辑人',
      type: 'input',
    },
  ],
};

export const editFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'errorCode',
      label: '错误码',
      placeholder: '请输入错误码',
      type: 'input',
      maxLength: 60,
      validatorRules: [{ required: true, message: '请选择模块名称' }],
    },
    {
      fieldName: 'errorMessage',
      label: '错误信息',
      placeholder: '请输入错误信息',
      type: 'input',
      maxLength: 500,
      validatorRules: [{ required: true, message: '请输入编码名称' }],
    },
    {
      fieldName: 'errorTranslate',
      label: '翻译信息',
      placeholder: '请输入翻译信息',
      type: 'input',
      maxLength: 500,
      validatorRules: [{ required: true, message: '请输入消息内容' }],
    },
  ],
};
