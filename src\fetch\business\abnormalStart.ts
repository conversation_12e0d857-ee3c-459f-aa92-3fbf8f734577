import { YESNO } from '@/utils/enum';
import { request } from '../core';
import { PageType } from '@/utils/enum';

export class AbnormalStartApi {
  // 分页查询数据列表
  fetchTableList({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/vehicle_exception/vehicle_exception_get_page_list',
      urlParams: {
        pageNum,
        pageSize,
      },
      body: searchForm,
    };
    return request(options);
  }
  //  新增编辑
  submitEditInfo({
    type,
    requestBody,
  }: {
    type: PageType.EDIT | PageType.ADD;
    requestBody: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path:
        type === PageType.EDIT
          ? '/k2/management/vehicle_exception/edit_vehicle_exception'
          : '/k2/management/vehicle_exception/add_vehicle_exception',
      body: requestBody,
    };
    return request(options);
  }
  // 获取详情
  fetchDetail(id: number | string) {
    const options: RequestOptions = {
      method: 'GET',
      path: '/k2/management/vehicle_exception/get_vehicle_exception_detail',
      urlParams: { number: id },
    };
    return request(options);
  }
  // 删除开机异常模块
  delException(number: number) {
    const options: RequestOptions = {
      method: 'GET',
      path: '/k2/management/vehicle_exception/delete_vehicle_exception',
      urlParams: { number },
    };
    return request(options);
  }
}
