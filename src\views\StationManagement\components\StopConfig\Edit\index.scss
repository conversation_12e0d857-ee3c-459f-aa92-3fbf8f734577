@import "../../../../../assets/css/index.scss";

.stoppoint-edit {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 10px;

  .bread-crub {
    background-color: alpha($color: #000000);
    padding: 10px 0;
  }

  .content {
    padding-top: 30px;
    background-color: white;
    display: flex;
    flex-direction: column;

    .title {
      display: flex;
      justify-content: center;
      font-size: 20px;
      color: $text-normal-color;
      font-weight: $text-font-bold;
    }

    .module-container {
      margin-top: 30px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      .center {
        flex: 1;
        margin-top: 80px;
        display: flex;
        flex-direction: column;

        .form-item {
          margin-top: 10px;
        }

        .tips-label {
          margin-left: 120px;
          font-size: 14px;
          color: #aaa;
          font-weight: 400;
        }
      }
    }
  }

  .btn {
    width: 87px;
    height: 40px;
    border-radius: 4px;
    color: white;
    background: $color-blue;
  }

  .function {
    font-size: 13px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin: 30px 0;

    .cancel {
      margin-left: 30px;
      color: $text-normal-color;
      background: white;
      border-color: $text-normal-color;
    }
  }
}

.select-detail-address{
  .head{
    .ant-select {
      flex: 1;
      margin-right: 40px;
    }
  }
}