import { DeviceBatchRequest, DeviceRequest } from '@/types/deviceLife';
import { request } from '../core';
import { Method } from '../core/constant';

export class DeviceLifeApi {
  public async getCompanyList(companyTypeList?: string[]) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/company/get_company_list',
      method: Method.POST,
      body: { companyTypeList: companyTypeList ?? null },
    };
    return request(requestOptions);
  }

  public async getDeviceTypeList() {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_type/get_device_type_list',
      method: Method.GET,
    };
    return request(requestOptions);
  }
  // 车辆生产Tab
  // 6.分页查询车辆生产列表
  public async fetchDeviceProduceTable(searchOptions: DeviceRequest) {
    const { pageNum, pageSize, searchForm } = searchOptions;
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/device_get_product_page_list',
      method: Method.POST,
      urlParams: {
        pageNum,
        pageSize,
      },
      body: { ...searchForm },
    };
    return request(requestOptions);
  }

  // 7.验收/批量验收
  public async deviceCheckAccept(deviceBaseIdList: number[]) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/device_check_accept',
      method: Method.PUT,
      body: { deviceBaseIdList: deviceBaseIdList },
    };
    return request(requestOptions);
  }

  // 8.出厂/批量出厂
  public async deviceLeaveFactory(batchRequest: DeviceBatchRequest) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/device_leave_factory_batch',
      method: Method.PUT,
      body: { deviceNameList: batchRequest.deviceNameList },
    };
    return request(requestOptions);
  }

  // 9.绑站/批量绑站
  public async deviceBindStation(batchRequest: DeviceBatchRequest) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/device_bind_station',
      method: Method.PUT,
      body: {
        ...batchRequest,
      },
    };
    return request(requestOptions);
  }

  // 11. 查看标定结果
  public async fetchDeviceCheckResultTable({
    deviceBaseId,
    pageNum,
    pageSize,
  }: {
    deviceBaseId: number;
    pageNum: number;
    pageSize: number;
  }) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/device_check_result_get_list',
      method: Method.POST,
      urlParams: {
        pageNum: pageNum,
        pageSize: pageSize,
      },
      body: {
        deviceBaseId: deviceBaseId,
      },
    };
    return request(requestOptions);
  }

  // 12.下载无人车标定图片
  public async deviceCheckResultDownload(otaToolResultRecordId: number) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/device_check_result_download',
      method: Method.GET,
      urlParams: { otaToolResultRecordId: otaToolResultRecordId },
    };
    return request(requestOptions);
  }

  // 13.删除车辆
  public async deleteDevice(deviceName: string) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/delete_device',
      method: Method.POST,
      urlParams: {
        deviceName: deviceName,
      },
    };
    return request(requestOptions);
  }

  // 14.判断车端是否存在调度
  public async checkDeviceSchedule(deviceNameList: string[]) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/check_device_schedule',
      method: Method.POST,
      body: {
        deviceNameList: deviceNameList,
      },
    };
    return request(requestOptions);
  }

  // 车辆调度页面
  // 15.分页查询车辆调度页面
  public async fetchDevicePoolTable(searchOptions: DeviceRequest) {
    const { pageNum, pageSize, searchForm } = searchOptions;
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/device_get_pool_page_list',
      method: Method.POST,
      urlParams: {
        pageNum,
        pageSize,
      },
      body: { ...searchForm },
    };
    return request(requestOptions);
  }

  // 16.接收/批量接受
  public async deviceReceive(batchRequest: DeviceBatchRequest) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/device_receive_batch',
      method: Method.PUT,
      body: {
        ...batchRequest,
      },
    };
    return request(requestOptions);
  }

  // 17.转站/批量转站
  public async deviceChangeStation(batchRequest: DeviceBatchRequest) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/device_change_station',
      method: Method.PUT,
      body: {
        ...batchRequest,
      },
    };
    return request(requestOptions);
  }

  // 18.交付/批量交付
  public async deviceDistribute(batchRequest: DeviceBatchRequest) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/device_distribute',
      method: Method.PUT,
      body: {
        ...batchRequest,
      },
    };
    return request(requestOptions);
  }

  // 19.驳回
  public async deviceRejectToProduct(deviceName: string) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/device_reject_to_product',
      method: Method.PUT,
      urlParams: {
        deviceName: deviceName,
      },
    };
    return request(requestOptions);
  }

  // 车辆交付页面
  // 20.分页查询车辆交付页面
  public async fetchDeviceUseTable(searchOptions: DeviceRequest) {
    const { pageNum, pageSize, searchForm } = searchOptions;
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/device_get_use_page_list',
      method: Method.POST,
      urlParams: {
        pageNum,
        pageSize,
      },
      body: { ...searchForm },
    };
    return request(requestOptions);
  }

  // 21.转站/批量转站
  public async bactchChangeStation(batchRequest: DeviceBatchRequest) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/batch_change_station',
      method: Method.POST,
      body: { ...batchRequest },
    };
    return request(requestOptions);
  }

  // 22.批量设置推流模式
  public async deviceChangeVideoMode({
    deviceNameList,
    videoMode,
  }: {
    deviceNameList: string[];
    videoMode: number;
  }) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/device_change_video_mode',
      method: Method.POST,
      body: {
        deviceNameList: deviceNameList,
        videoMode: videoMode,
      },
    };
    return request(requestOptions);
  }

  // 车辆维修页面
  // 23.分页查询车辆维修列表
  public async fetchDeviceRepairTable(searchOptions: DeviceRequest) {
    const { pageNum, pageSize, searchForm } = searchOptions;
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/device_require_info_get_page_list',
      method: Method.POST,
      urlParams: {
        pageNum,
        pageSize,
      },
      body: { ...searchForm },
    };
    return request(requestOptions);
  }

  // 24.转维修中心
  public async deviceToMaintain(batchRequest: DeviceBatchRequest) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_lifecycle/device_change_to_maintain_station',
      method: Method.PUT,
      body: {
        ...batchRequest,
      },
    };
    return request(requestOptions);
  }
}
