import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export declare type NavigatParams = {
  routeName: any; // 路由名称
  searchValues: any; // 搜索条件数据
};

const initialState: NavigatParams = {
  routeName: null,
  searchValues: null,
};

const searchFormState = createSlice({
  name: 'searchForm',
  initialState,
  reducers: {
    saveSearchValues(state, action: PayloadAction<NavigatParams>) {
      const { routeName, searchValues } = action.payload;
      state.routeName = routeName;
      state.searchValues = searchValues;
    },
    removeSearchValues(state, action) {
      state.routeName = null;
      state.searchValues = null;
    },
  },
});

export default searchFormState.reducer;
export const { saveSearchValues, removeSearchValues } = searchFormState.actions;
