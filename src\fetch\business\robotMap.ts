import { ProductType } from '@/utils/enum';
import { request } from '../core';

export class RobotMapApi {
  fetchDomain = `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}`;
  fetchStationList = () => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/k2/indoorMap/web/warehouse/get_warehouse_list`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: {
        productKey: ProductType.INTEGRATE,
      },
    };
    return request(options);
  };

  // 分页查询数据列表
  fetchTableList = ({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/k2/indoorMap/web/mapInfo/map_info_get_page_list`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: {
        ...searchForm,
        pageNum,
        pageSize,
        productKey: ProductType.INTEGRATE,
      },
    };
    return request(options);
  };

  fetchVersionList = ({
    pageNum,
    pageSize,
    number,
  }: {
    pageNum: number;
    pageSize: number;
    number: string;
  }) => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/k2/indoorMap/web/mapInfo/map_version_info_get_page_list`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: { mapNumber: number, pageNum, pageSize },
    };
    return request(options);
  };

  fetchMapElement = ({
    version,
    number,
  }: {
    version: string;
    number: string;
  }) => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/k2/indoorMap/web/mapInfo/get_map_element`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: {
        version,
        number,
      },
    };
    return request(options);
  };

  fetchMapInfo = (number: string, version: string) => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/k2/indoorMap/web/mapInfo/get_map_info`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: {
        number,
        version,
      },
    };
    return request(options);
  };
}
