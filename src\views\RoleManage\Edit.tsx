import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Form, Input, Select, Switch, message } from 'antd';
import lodash from 'lodash';
import { CommonEdit, CommonForm, TableOperateBtn } from '@/components';
import { formatLocation, formatOptions } from '@/utils/utils';
import { RoleManageApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';

const RoleManageEdit = () => {
  const [formRef] = Form.useForm();
  const fetchApi = new RoleManageApi();
  const { id, type } = formatLocation(window.location.search);
  const [breadCrumbItems, setBreadCrumbItems] = useState([
    { title: '角色管理', route: '' },
    { title: '新建角色', route: '' },
  ]);
  const [detailValues, setDetailValues] = useState<any>({
    enable: 1,
  });
  // clone一份详情,用于比较是否修改,未修改时点击确定不调用接口
  const [originDetailValues, setoriginDetailValues] = useState<any>();
  const [loading, setLoading] = useState(false);
  const [loadingMsg, setLoadingMsg] = useState<string>('');
  useEffect(() => {
    if (id) {
      setBreadCrumbItems([
        { title: '角色管理', route: '' },
        { title: '编辑角色', route: '' },
      ]);
      fetchApplicantDetail();
    }
  }, []);
  useEffect(() => {
    formRef.setFieldsValue({ ...detailValues });
  }, [JSON.stringify(detailValues)]);

  const fetchApplicantDetail = async () => {
    setLoading(true);
    const response: any = await fetchApi.fetchDetail(id);
    if (response.code === HttpStatusCode.Success) {
      const detail = response.data;
      setDetailValues(detail);
      setoriginDetailValues(lodash.cloneDeep(detail));
    } else {
      message.error(response.message);
    }
    setLoading(false);
  };
  const confirmClick = () => {
    formRef.validateFields().then(async (formValue) => {
      if (id && mapCompar(formValue)) {
        window.history.back();
        return;
      }
      setLoadingMsg('保存中...');
      setLoading(true);
      const reqParams = { ...formValue };
      if (id) {
        reqParams.number = id;
      }
      const response = await fetchApi.submitEditInfo({
        type,
        requestBody: reqParams,
      });

      if (response.code === HttpStatusCode.Success) {
        message.success(response.message);
        window.history.back();
      } else {
        message.error(response.message);
      }
    });
  };
  const mapCompar = (formValue: any) => {
    // JSON.stringify 的第二个可选参数,过滤一些不必须要的属性
    const originDataStr = JSON.stringify(originDetailValues, (k, v) => {
      // 注意：第一次 k 是 undefined，v 是原对象
      if (!k || Object.prototype.hasOwnProperty.call(formValue, k)) {
        return v;
      }
    });
    const newDataStr = JSON.stringify(formValue);
    return originDataStr === newDataStr;
  };
  return (
    <CommonEdit
      title={id ? '编辑角色' : '新建角色'}
      breadCrumbConfig={breadCrumbItems}
      onSubmitClick={confirmClick}
      onCancleClick={() => {
        window.history.back();
      }}
    >
      <Form
        form={formRef}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 18 }}
        autoComplete="off"
      >
        {id ? (
          <Form.Item name="name" label="角色编号	">
            {id}
          </Form.Item>
        ) : (
          <Form.Item
            name="number"
            label="角色编号	"
            rules={[{ required: true, message: '请输入角色编号' }]}
          >
            <Input placeholder="请输入角色编号	" />
          </Form.Item>
        )}
        <Form.Item
          name="name"
          label="角色名称	"
          rules={[{ required: true, message: '请输入角色名称' }]}
        >
          <Input placeholder="请输入角色名称	" />
        </Form.Item>
        <Form.Item
          name="description"
          label="角色描述"
          rules={[{ required: true, message: '请输入角色描述' }]}
        >
          <Input placeholder="请输入角色描述" />
        </Form.Item>
      </Form>
    </CommonEdit>
  );
};

export default React.memo(RoleManageEdit);
