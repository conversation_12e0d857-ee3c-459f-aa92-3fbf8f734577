/** 通用响应 */
export interface CommonResponse<T> {
  /** 响应码 */
  code: string;
  /** 响应信息 */
  message: string | null;
  /** 响应数据 */
  data: T | null;
}

/** 通用表格请求 */
export interface CommonTableRequest {
  /** 页码 */
  pageNum: number;
  /** 分页大小 */
  pageSize: number;
}

/** 通用表格响应 */
export interface CommonTableResponse<T> {
  /** 页码 */
  pageNum: number;
  /** 分页大小 */
  pageSize: number;
  /** 总页数 */
  pages: number;
  /** 总数据条数 */
  total: number;
  /** 数据列表（泛型） */
  list: T[];
}

/** 任务状态枚举 */
export enum TaskStatusEnum {
  /** 勘查中 */
  UNDER_EXPLORATION = 'UNDER_EXPLORATION',
  /** 待认领 */
  PENDING_CLAIM = 'PENDING_CLAIM',
  /** 采集中 */
  COLLECTING = 'COLLECTING',
  /** 待拷贝 */
  PENDING_COPY = 'PENDING_COPY',
  /** 拷贝中 */
  COPYING = 'COPYING',
  /** 拷贝完成 */
  COPY_COMPLETED = 'COPY_COMPLETED',
  /** 地图制作中 */
  MAP_IN_PROGRESS = 'MAP_IN_PROGRESS',
  /** 地图上线 */
  MAP_RELEASE = 'MAP_RELEASE',
  /** 已完成 */
  TASK_CLOSED = 'TASK_CLOSED',
  /** 任务已删除 */
  TASK_DELETED = 'TASK_DELETED',
  /** 未知 */
  UNKNOWN = 'UNKNOWN',
}

/** 勘查任务导出参数 */
export interface MapCollectionTaskExportVO {
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 任务状态列表，为空则查询全部 */
  taskStatusList?: TaskStatusEnum[];
  /** 城市ID，为空则查询全部 */
  cityId?: number;
  /** 站点ID，为空则查询全部 */
  stationId?: number;
  /** 任务创建人，为空则查询全部 */
  taskCreator?: string;
}

/** 勘查任务导出结果DTO */
export interface MapCollectionTaskExportResultDTO {
  /** 文件名称 */
  fileName: string;
  /** 下载URL */
  downloadUrl: string;
  /** 导出记录数 */
  recordCount: number;
}
