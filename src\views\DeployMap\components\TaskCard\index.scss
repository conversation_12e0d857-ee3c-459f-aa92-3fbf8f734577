.task-card,
.task-card-expanded,
.task-card-selected {
  position: relative;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  height: 24px;
  flex: 1;
  // 状态标识
  .status-badge {
    position: absolute;
    top: 0;
    right: 0;
    padding: 2px 10px;
    font-size: 12px;
    color: white;
    border-radius: 0 8px 0 8px;
  }

  // 卡片内容
  .card-content,
  .card-content-unexpanded {
    // 任务头部信息
    .task-header,
    .task-header-expanded {
      flex: 1;
      .task-id {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 500;
        color: rgba(60, 110, 240, 1);
        background: rgba(60, 110, 240, 0.1);
        border-radius: 10px;
        padding: 4px 6px;
        margin-bottom: 8px;
        width: min-content;
      }

      .task-name {
        font-size: 16px;
        font-family: PingFang SC;
        font-weight: 500;
        color: rgba(35, 37, 43, 1);
      }
    }
    .task-header-expanded {
      .task-name {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 600;
        color: rgba(82, 87, 101, 1);
      }
    }
    // 展开/收起图标
    .expand-icon {
      cursor: pointer;
      padding: 4px;

      img {
        width: 16px;
        height: 16px;
      }
    }
    .expand-icon-expanded {
      position: absolute;
      bottom: 0;
      right: 46%;
      background: url("../../../../assets/image/mapCollect/taskcard/collapse-icon-bg.png") no-repeat center center;
      background-size: 100% 100%;
      width: 56px;
      height: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0px;
    }
  }
  .card-content-unexpanded {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
  }
  // 展开后的详细信息
  .task-details {
    margin-top: 8px;
    .task-info-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      font-size: 14px;

      .info-icon {
        width: 12px;
        height: 12px;
        margin-right: 8px;
      }

      .label {
        color: #666;
        margin-right: 8px;
      }

      .value {
        color: #333;
        flex: 1;
      }
    }
  }

  // 操作区域
  .action-area {
    display: flex;
    justify-content: flex-end;
    flex-wrap: wrap;
    .ant-btn {
      margin-left: 12px;
      padding: 0;

      &:first-child {
        margin-left: 0;
      }
    }
  }

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.task-card-expanded {
  height: 148px;
  flex-direction: column;
}

.task-card-selected {
  box-shadow: -1px 1px 7px 5px #a9c1ff;
  &:hover {
    box-shadow: -1px 1px 7px 5px #a9c1ff;
  }
}
