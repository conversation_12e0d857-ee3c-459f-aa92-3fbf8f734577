
import { dropDownListKey, dropDown<PERSON>ey } from '@/utils/constant';
import { FieldItem, FormConfig } from '@/components';
export const tableColumns = [
  {
    title: '硬件名称',
    dataIndex: 'name',
    align: 'center',
    width: 180,
    ellipsis: true,
  },
  {
    title: '硬件型号',
    dataIndex: 'model',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '硬件类型',
    dataIndex: 'hardwareTypeName',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '硬件用途',
    dataIndex: 'hardwareModelUsageList',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '厂商名称',
    dataIndex: 'factoryName',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '硬件状态',
    dataIndex: 'enable',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '操作人',
    dataIndex: 'modifyUser',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 200,
    fixed: 'right',
  },
];
export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'name',
      label: '硬件名称',
      placeholder: '请输入硬件名称',
      type: 'input',
    },
    {
      fieldName: 'model',
      label: '硬件型号',
      placeholder: '请输入硬件型号',
      type: 'input',
    },
    {
      fieldName: 'hardwareTypeId',
      label: '硬件类型',
      placeholder: '请选择硬件类型',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.HARDWARE_TYPE,
      dropDownListKey: dropDownListKey.HARDWARE_TYPE,
    },
    {
      fieldName: 'enable',
      label: '硬件状态',
      placeholder: '请选择硬件状态',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.ENABLE,
      dropDownListKey: dropDownListKey.ENABLE,
    },
  ],
};
