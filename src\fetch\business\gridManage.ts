import { YESNO } from '@/utils/enum';
import { request } from '../core';
import { PageType } from '@/utils/enum';

export class GridManageApi {
  // 分页查询
  fetchTableList({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/box_template/box_grid_template_get_page_list',
      urlParams: {
        pageNum,
        pageSize,
      },
      body: searchForm,
    };
    return request(options);
  }
  //  新增编辑
  submitGrid({ type, requestBody }: { type: PageType; requestBody: Object }) {
    const options: RequestOptions = {
      method: type === PageType.EDIT ? 'PUT' : 'POST',
      path:
        type === PageType.EDIT
          ? '/k2/management/box_template/box_grid_template_edit'
          : '/k2/management/box_template/box_grid_template_add',
      body: requestBody,
    };
    return request(options);
  }
  // 获取详情
  fetchDetail(id: number | string) {
    const options: RequestOptions = {
      method: 'GET',
      path: '/k2/management/box_template/get_box_template_detail',
      urlParams: { id },
    };
    return request(options);
  }
  // 启用停用
  updateStatus({ id, enable }: { id: number; enable: YESNO }) {
    const options: RequestOptions = {
      method: 'PUT',
      path: '/k2/management/box_template/box_grid_template_update_enable',
      body: {
        id,
        enable,
      },
    };
    return request(options);
  }

  // 查看机器人格口规格配置信息
  getRobotGrid(type: string) {
    const options: RequestOptions = {
      method: 'GET',
      path: '/k2/management/box_template/get_robot_grid_info',
      urlParams: { type: type },
    };
    return request(options);
  }

  // 获取箱体格口的箱体详情
  getBoxTemplateInfo(boxTemplateId: number) {
    const options: RequestOptions = {
      method: 'GET',
      path: `/k2/management/box_template/get_box_template_info`,
      urlParams: {
        boxTemplateId,
      },
    };
    return request(options);
  }

  // 获取箱体格口模版的列表
  getBoxTemplateList() {
    const options: RequestOptions = {
      method: 'GET',
      path: `/k2/management/box_template/get_box_template_info_list`,
    };
    return request(options);
  }

  // 获取箱体格口名称列表
  getBoxNameList() {
    const options: RequestOptions = {
      method: 'GET',
      path: `/k2/management/box_template/get_box_name_list`,
    };
    return request(options);
  }
}
