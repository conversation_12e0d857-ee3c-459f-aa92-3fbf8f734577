const path = require('path');
const webpack = require('webpack');
const CopyPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const Envs = require('./env');
const StaticConfig = require('./static');
const DevServerConfig = require('./devServer');
const getClientEnvironment = Envs.getClientEnvironment;
const ProcessEnvValues = getClientEnvironment();
const isDevelopment = ProcessEnvValues.raw.NODE_ENV === 'development';
const ESLintPlugin = require('eslint-webpack-plugin');
function getOutputFilename() {
  if (isDevelopment) {
    return 'js/[name].bundle.js';
  } else {
    return 'js/[name]-[hash].bundle.min.js';
  }
}

function getOutputChunkFilename() {
  if (isDevelopment) {
    return 'js/[name].chunk.js';
  } else {
    return 'js/[name]-[chunkhash].chunk.min.js';
  }
}

let lastProgressPercentage = 0;
let lastProgressMessage = '';
function progressHandler(percentage, message, ...args) {
  let report = false;
  if (!report && lastProgressMessage !== message) {
    report = true;
  }
  if (
    !report &&
    (lastProgressPercentage === 0 || percentage - lastProgressPercentage >= 0.1)
  ) {
    report = true;
  }
  if (report) {
    console.log(
      `[PROGRESS: ${Math.floor(percentage * 100)}%] ${
        message ? message + ' ....' : 'done'
      }`
    );
  }
  lastProgressPercentage = percentage;
  lastProgressMessage = message;
}

const config = {
  mode: !isDevelopment ? 'production' : 'development',
  //页面入口文件配置
  entry: path.resolve(__dirname, '../src/index.tsx'),
  output: {
    asyncChunks: true,
    filename: getOutputFilename(),
    chunkFilename: getOutputChunkFilename(),
    path: path.resolve(__dirname, '../build'),
    publicPath: '/',
    environment: {
      arrowFunction: false,
    },
  },
  optimization: {
    nodeEnv: false,
    minimize: !isDevelopment, // 使用默认压缩 会生成LICENSE.txt文件
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          parse: {
            ecma: 8,
          },
          compress: {
            ecma: 5,
            warnings: false,
            comparisons: false,
            inline: 2,
            drop_console: true
          },
          mangle: {
            safari10: true,
          },
          output: {
            ecma: 5,
            comments: false,
            ascii_only: true,
          },
        },
      }),
    ],
    splitChunks: {
      cacheGroups: {
        default: {
          name: 'common',
          chunks: 'all',
          minChunks: 2, //模块被引用2次以上的才抽离
          reuseExistingChunk: true,
        },
        commons: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
          priority: -10,
          reuseExistingChunk: true,
        },
      },
    },
  },
  devtool: isDevelopment ? 'cheap-module-source-map' : 'source-map',
  cache: {
    type: 'filesystem',
    cacheDirectory: path.resolve(__dirname, '../.cache'),
  },
  resolve: {
    extensions: [
      '.js',
      '.json',
      '.png',
      '.ts',
      '.tsx',
      '.css',
      '.jsx',
      '.less',
    ],
    alias: {
      '@': path.resolve(__dirname, '../src'),
    },
  },
  // externals: {
  //   react: 'React',
  //   'react-dom': 'ReactDOM',
  //   moment: 'moment',
  // },
  module: {
    rules: [
      {
        test: /\.(js|jsx|ts|tsx)$/,
        exclude: /(node_modules|bower_components)/,
        use: [
          {
            loader: require.resolve('babel-loader'),
            options: {
              babelrc: false,
              presets: [
                require.resolve('@babel/preset-react'),
                [
                  require.resolve('@babel/preset-env'),
                  {
                    corejs: 3,
                    useBuiltIns: 'usage',
                  },
                ].filter(Boolean),
                require.resolve('@babel/preset-typescript'),
              ],
              plugins: [
                [
                  '@babel/plugin-proposal-class-properties',
                  {
                    loose: true,
                  },
                ],
                ['@babel/plugin-proposal-decorators', { legacy: true }],
                [
                  '@babel/plugin-proposal-private-property-in-object',
                  { loose: true },
                ],
                ['@babel/plugin-proposal-private-methods', { loose: true }],
                '@babel/plugin-transform-arrow-functions',
                path.resolve(__dirname, '../scripts/webpackAsyncModuleName.js'),
              ],
            },
          },
        ],
      },
      ...StaticConfig.rules(),
    ],
  },
  resolveLoader: {},
  plugins: [
    ...StaticConfig.plugins(),
    new webpack.DefinePlugin(ProcessEnvValues.stringified),
    new HtmlWebpackPlugin({
      filename: path.resolve(__dirname, '../build/index.html'),
      template: path.resolve(__dirname, '../static/index.html'),
      inject: 'body',
      minify: {
        collapseWhitespace: !isDevelopment,
        removeComments: true,
        removeRedundantAttributes: true,
        removeScriptTypeAttributes: false,
        removeStyleLinkTypeAttributes: false,
        useShortDoctype: true,
      },
    }),
    new CleanWebpackPlugin(),
    new webpack.ProgressPlugin(progressHandler),
    new ESLintPlugin(),
    new CopyPlugin({
      patterns: [
        {
          from: path.resolve(__dirname, '../static'),
          to: path.resolve(__dirname, '../build'), //放到output文件夹下
          globOptions: {
            dot: true,
            gitignore: false,
            ignore: [
              // 配置不用copy的文件
              '**/index.html',
            ],
          },
        },
      ],
    }),
  ],
};

if (isDevelopment) {
  config.plugins.push(new webpack.HotModuleReplacementPlugin());
  config.devServer = DevServerConfig;
}

module.exports = config;
