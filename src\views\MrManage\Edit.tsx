import React, { useEffect, useRef, useState } from 'react';
import { BreadCrumb } from '@/components';
import { CommonForm, CommonFormPro } from '@jd/x-coreui';
import {
  MrInfo,
  TestMethod,
  getTestPlanForm,
  TestTask,
  YESNO,
  TestResultValue,
} from './utils/constant';
import './edit.scss';
import { Button, Modal, message } from 'antd';
import { MrManageApi, CommonApi } from '@/fetch/business';
import { formatLocation } from '@/utils/utils';
import { HttpStatusCode } from '@/fetch/core/constant';
const fetchApi = new MrManageApi();
const breadCrumbItems = [
  {
    title: 'MR管理',
    route: '/app/mrManage',
  },
  {
    title: '编辑MR',
    route: '',
  },
];
const MrEdit = () => {
  const { id } = formatLocation(window.location.search);
  const commonFetchApi = new CommonApi();
  const mrInfoRef = useRef<any>(null);
  const testTaskRef = useRef<any>(null);
  const testPlanRef = useRef<any>(null);
  const [uploading, setUploading] = useState<boolean>(false);
  const [testPlanForm, setTestPlanForm] = useState<any>(getTestPlanForm());
  const [detail, setDetail] = useState<any>(null);
  const [isNeedTest, setIsNeedTest] = useState<boolean>(true);
  const submit = () => {
    if (uploading) {
      message.warning('请等待上传完成');
      return;
    }
    const promiseAll = [
      mrInfoRef.current?.validateFields(),
      testTaskRef.current?.validateFields(),
      testPlanRef.current?.validateFields(),
    ];
    Promise.all(promiseAll)
      .then((res) => {
        const [mrInfo, testTask, testPlan] = res;
        const { fixJiraKeyList, ...otherMrInfo } = mrInfo;
        const { fileList, imageList, fixBugDetailList, ...otherTestPlan } =
          testPlan;
        const cb = () => {
          fetchApi
            .submitJiraInfo({
              mrId: id,
              ...otherMrInfo,
              ...testTask,
              ...otherTestPlan,
              fixJiraKeyList: detail?.fixJiraKeyList
                ?.map((item: any) => item.jiraKey)
                ?.join(';'),
              bindBugList: fixBugDetailList,
              imageList: imageList?.map((item: any) => ({
                type: 'images',
                fileKey: item,
                bucketName: 'rover-operation',
              })),
              fileList: fileList?.map((item: any) => ({
                type: 'file',
                fileKey: item,
                bucketName: 'rover-operation',
              })),
            })
            .then((res) => {
              if (res.code === HttpStatusCode.Success) {
                message.success('操作成功');
                history.go(-1);
              } else {
                res?.message && message.error(res?.message);
              }
            });
        };
        // 从待发版变为已发版给出提示
        if (
          detail?.mrStatus == 'TO_BE_RELEASE' &&
          testPlan.mrStatus == 'RELEASED'
        ) {
          const mesArr = [
            mrInfo.dependencyMrId
              ? '依赖MR_id：' + mrInfo.dependencyMrId + ';'
              : false,
            testPlan.associationMrIds
              ? '关联MR_id：' + testPlan.associationMrIds
              : false,
          ];
          if (
            (mrInfo.dependencyMrId || testPlan.associationMrIds) &&
            testPlan.mrStatus
          ) {
            mesArr.push('一并改为已发版，确认提交吗？');
          }

          if (mesArr.filter(Boolean).length > 0) {
            Modal.confirm({
              content: mesArr.filter(Boolean).join(''),
              okText: '确定',
              cancelText: '取消',
              onOk: cb,
            });
          } else {
            cb();
          }
        } else if ([0, 1, 7].indexOf(testPlan.testResult) > -1) {
          const strMap = {
            TO_BE_RELEASE: '待发版',
            RELEASED: '已发版',
          };
          const mesArr = [
            mrInfo.dependencyMrId
              ? '依赖MR_id：' + mrInfo.dependencyMrId + ';'
              : false,
            testPlan.associationMrIds
              ? '关联MR_id：' + testPlan.associationMrIds
              : false,
          ];

          if (
            (mrInfo.dependencyMrId || testPlan.associationMrIds) &&
            testPlan.mrStatus
          ) {
            mesArr.push(`一并改为${strMap[testPlan.mrStatus]}，确认提交吗？`);
          }
          if (mesArr.filter(Boolean).length > 0) {
            Modal.confirm({
              content: mesArr.filter(Boolean).join(''),
              okText: '确定',
              cancelText: '取消',
              onOk: cb,
            });
          } else {
            cb();
          }
        } else {
          cb();
        }
      })
      .catch((e) => {});
  };

  const getMrDetailByMrId = (mrId: string) => {
    fetchApi.getMrDetailByMrId(mrId).then((res) => {
      if (res?.code === HttpStatusCode.Success) {
        const { isSpecificTest, fileList, imageList, testMethod, ...other } =
          res?.data || {};
        const imgField = testPlanForm.fields.find(
          (item) => item.fieldName === 'imageList',
        );
        const fileField = testPlanForm.fields.find(
          (item) => item.fieldName === 'fileList',
        );
        imgField.uploadedFileList = imageList?.map((item) => ({
          fileKey: item.fileKey,
          url: item.url,
          uid: item.fileKey,
        }));

        fileField.uploadedFileList = fileList?.map((item) => ({
          fileKey: item.fileKey,
          url: item.url,
          uid: item.fileKey,
        }));
        setTestPlanForm({
          ...testPlanForm,
        });
        setTimeout(() => {
          setDetail(res?.data);
        }, 200);
        setTimeout(() => {
          mrInfoRef.current.setFieldsValue(res?.data);
          testTaskRef.current.setFieldsValue(res?.data);
          testTaskRef.current.setFieldsValue({
            isSpecificTest: isSpecificTest != null ? isSpecificTest : YESNO.NO,
            testMethod:
              testMethod != null ? testMethod : TestMethod.ONLY_REAL_VEHICLE,
          });
          testPlanRef.current.setFieldsValue({
            ...other,
            imageList: imageList?.map((item) => item.fileKey),
            fileList: fileList?.map((item) => item.fileKey),
          });
        }, 500);
        if (res?.data?.testResult === TestResultValue.NO_NEED_TEST) {
          setIsNeedTest(false);
        }
      }
    });
  };
  useEffect(() => {
    getMrDetailByMrId(id);
  }, []);
  useEffect(() => {
    if (isNeedTest) {
      if (!detail?.isSpecificTest || !detail?.testMethod) {
        setDetail({
          ...detail,
          isSpecificTest: YESNO.NO,
          testMethod: TestMethod.ONLY_REAL_VEHICLE,
        });
      }
    }
  }, [isNeedTest]);

  return (
    <div className="mr-edit-container" style={{ padding: '10px' }}>
      <div className="bread-crub">
        <BreadCrumb items={breadCrumbItems} />
      </div>
      <div className="content">
        <h1 className="title">编辑MR</h1>
        <div className="form-title">
          <h2>Coding获取内容&获取行云关联解决的缺陷</h2>
        </div>
        <CommonForm
          formConfig={MrInfo}
          defaultValue={detail}
          getFormInstance={(ref: any) => {
            mrInfoRef.current = ref;
          }}
        />
        {isNeedTest && (
          <>
            <div className="form-title">
              <h2>分配测试任务</h2>
            </div>
            <CommonForm
              defaultValue={detail}
              formConfig={TestTask}
              getFormInstance={(ref: any) => {
                testTaskRef.current = ref;
              }}
            />
          </>
        )}
        <div className="form-title">
          <h2>测试进度</h2>
          <CommonFormPro
            defaultValue={{
              testResult: detail?.testResult,
              associationMrIds: detail?.associationMrIds,
              mrStatus: detail?.mrStatus,
              roverVersion: detail?.roverVersion,
              releaseWeek: detail?.releaseWeek,
              fixBugDetailList: detail?.fixBugDetailList,
              remark: detail?.remark,
            }}
            initLink={true}
            formConfig={testPlanForm}
            getUploadStatus={(
              status: boolean,
              uploadStatus: 'uploading' | 'error' | 'success' | 'delete',
              fieldName?: string | undefined,
            ) => {
              setUploading(status);
              const value = testPlanRef.current?.getFieldValue(fieldName);
              if (fieldName === 'imageList') {
                const label = document.querySelector(
                  'label[for="complex-form_imageList"]',
                );

                if (label) {
                  label.innerHTML = `上传图片（${value?.length}/5）`;
                }
              } else {
                const label = document.querySelector(
                  'label[for="complex-form_fileList"]',
                );

                if (label) {
                  label.innerHTML = `上传文件（${value?.length}/5）`;
                }
              }
            }}
            getFormInstance={(ref: any) => {
              testPlanRef.current = ref;
            }}
            onValueChange={(values: any, fieldName: string) => {
              if (fieldName === 'testResult') {
                if (values?.testResult === TestResultValue.NO_NEED_TEST) {
                  setIsNeedTest(false);
                } else if (
                  values?.testResult !== TestResultValue.NO_NEED_TEST
                ) {
                  setIsNeedTest(true);
                }
              }
            }}
          />
        </div>
        <div className="btn-group">
          <Button type="primary" onClick={submit}>
            确定
          </Button>
          <Button
            type="default"
            style={{ marginLeft: '20px' }}
            onClick={() => {
              history.go(-1);
            }}
          >
            取消
          </Button>
        </div>
      </div>
    </div>
  );
};

export default MrEdit;
