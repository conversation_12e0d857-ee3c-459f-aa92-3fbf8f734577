import type * as React from 'react';
declare module '*.png' {
  const image: string;
  export default image;
}
declare module '*.svg' {
  const image: string;
  export default image;
}
declare module '@jd/x-coreui';
declare type AnyFunc = (...arg: any[]) => any | void;
export type AnyObject = Record<PropertyKey, any>;

declare module 'react' {
  interface HTMLAttributes<T> extends AriaAttributes, DOMAttributes<T> {
    clstag?: string;
  }
}
