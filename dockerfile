FROM base_worker/nodejs-jd-centos6.6-node8.16.0-ngx197:latest
RUN yum install -y wget && yum clean all

#install nodejs
ARG NODEVERSION=node-v16.9.0-linux-x64
ARG SUB_NODEVERSION=v16.9.0
ARG NODEJS_HOME=/usr/local/nodejs
RUN wget https://nodejs.org/dist/$SUB_NODEVERSION/$NODEVERSION.tar.gz -O /tmp/$NODEVERSION.tar.gz && \
    cd /usr/local && tar xzvf /tmp/$NODEVERSION.tar.gz &&\
    ln -s /usr/local/$NODEVERSION /usr/local/nodejs && \
    rm -rf /tmp/$NODEVERSION.tar.gz &&\
    echo export PATH=$NODEJS_HOME/bin:$PATH >> /etc/profile
ENV NODEJS_HOME=$NODEJS_HOME
ENV PATH=$NODEJS_HOME/bin:$PATH
RUN    mkdir -p /export && npm config set cache /export --global