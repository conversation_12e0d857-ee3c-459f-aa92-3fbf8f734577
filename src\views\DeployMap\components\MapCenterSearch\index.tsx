import React, { useEffect, useRef, useState } from 'react';
import { Divider, Select, Spin } from 'antd';
import LocationSearch from '../LocationSearch';
import DeployMapApi, { LatLng } from '@/fetch/business/deployMap';
import './index.scss';
import { HttpStatusCode } from '@/fetch/core/constant';

const MapCenterSearch = ({
  mapCenterSearchInfo,
  changeMapCenter,
}: {
  mapCenterSearchInfo: any;
  changeMapCenter: any;
}) => {
  const fetchIdRef = useRef(0);
  const optionLabelName = new Map([
    ['STATION', '站点'],
    ['STOP', '停靠点'],
    ['VEHICLE', '车辆'],
    ['MAP_COLLECTION_TASK', '勘查任务'],
  ]);
  const [elementOptions, setElementOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const changeSelect = (val, type: 'type' | 'value') => {
    changeMapCenter(val, type);
  };

  const debounce = (func, wait) => {
    let timeout;
    return (...args) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  };

  const fetchElementOptions = debounce(async (val) => {
    if (!val) {
      setElementOptions([]);
      return;
    }
    setLoading(true);
    const fetchId = ++fetchIdRef.current;
    const res = await DeployMapApi.fuzzySearch(val);
    if (res.code === HttpStatusCode.Success && fetchId === fetchIdRef.current) {
      setElementOptions(
        res.data.map((v) => ({
          label: optionLabelName.get(v.elementType),
          title: optionLabelName.get(v.elementType),
          options: v.elementList.map((j) => ({
            value: `${v.elementType}-${j.elementId}`,
            label: j.elementName,
          })),
        })),
      );
      setLoading(false);
    } else {
      setElementOptions([]);
      setLoading(false);
    }
  }, 300);

  return (
    <div className="map-search-container">
      <Select
        className="type-select"
        value={mapCenterSearchInfo.type}
        options={[
          {
            label: '地理位置',
            value: 'location',
          },
          {
            label: '元素名称',
            value: 'element',
          },
        ]}
        onChange={(val) => {
          setElementOptions([]);
          changeSelect(
            {
              type: val,
              latitude: null,
              longitude: null,
              elementType: null,
              elementId: null,
            },
            'type',
          );
        }}
      />
      <Divider type="vertical" />
      {mapCenterSearchInfo.type === 'location' && (
        <LocationSearch
          changeSelect={(val) =>
            changeSelect(
              {
                type: mapCenterSearchInfo.type,
                latitude: val.lat,
                longitude: val.lon,
                elementType: null,
                elementId: null,
              },
              'value',
            )
          }
        />
      )}
      {mapCenterSearchInfo.type === 'element' && (
        <Select
          style={{ width: '100%' }}
          options={elementOptions}
          showSearch
          notFoundContent={loading ? <Spin size="small" /> : null}
          placeholder="输入点/线/车号/任务名称"
          filterOption={false}
          onSearch={fetchElementOptions}
          onChange={(val) => {
            const [elementType, elementId] = val
              ? val.split('-')
              : [null, null];
            changeSelect(
              {
                type: mapCenterSearchInfo.type,
                latitude: null,
                longitude: null,
                elementType: elementType,
                elementId: elementId,
              },
              'value',
            );
          }}
        />
      )}
    </div>
  );
};

export default React.memo(MapCenterSearch);
