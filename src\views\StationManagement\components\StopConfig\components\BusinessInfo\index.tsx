/* eslint-disable no-unused-vars */

import { Col, Form, FormInstance, Input, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import EditModuleTitle from '@/components/EditModuleTitle';
import TransferEdit from '../../components/TransferInfo';
import KeyWords from '../KeyWords';
import { StopType } from '@/utils/enum';

const BusinessInfo = ({
  form,
  editInfo,
  stopType,
}: {
  form: FormInstance;
  editInfo?: any;
  stopType: string;
}) => {
  const [keyWordList, setKeyWordList] = useState([]);
  const [showTransferInfo, setShowTransferInfo] = useState(false);
  useEffect(() => {
    if (editInfo) {
      form.setFieldsValue({
        waitingTime: editInfo.waitingTime,
        keyWordList: editInfo.keyWordList,
      });
      setKeyWordList(editInfo.keyWordList);
      setShowTransferInfo(
        editInfo.type === StopType.PICKUP || editInfo.type === StopType.HOME,
      );
    }
  }, [editInfo]);

  useEffect(() => {
    if (stopType) {
      setShowTransferInfo(
        stopType === StopType.PICKUP || stopType === StopType.HOME,
      );
    }
  }, [stopType]);

  return (
    <Form form={form} labelCol={{ span: 4 }}>
      <Row>
        <EditModuleTitle title={'业务信息'} />
      </Row>
      <Row style={{ paddingTop: 20 }}>
        <Col span={19}>
          <Form.Item
            name="waitingTime"
            label="等候时长默认值"
            rules={[
              { required: true, message: '请输入停靠点等候时长的初始默认值' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (
                    value === undefined ||
                    (value != undefined && value.length <= 0)
                  ) {
                    return Promise.reject(new Error(' '));
                  } else {
                    const inputer = `${value}`;
                    const reg = /^\+?[1-9][0-9]*$/;
                    if (!reg.test(inputer)) {
                      return Promise.reject(new Error('请输入合法的正整数'));
                    }
                    return Promise.resolve();
                  }
                },
              }),
            ]}
            tooltip="此处仅设置停靠点等候时长的初始默认值；具体停靠时长可在车端进行修改；每台车在每个停靠点都可以由独立的停靠时长。"
          >
            <Input
              placeholder="请输入停靠点等候时长的初始默认值"
              maxLength={3}
              onChange={(e) => {
                form.setFieldsValue({
                  waitingTime: e.target.value.slice(0, 3),
                });
              }}
            />
          </Form.Item>
          <Form.Item name="keyWordList" label="关键词">
            <KeyWords
              keywords={keyWordList.length <= 0 ? null : keyWordList}
              keywordsChanged={(keywords: any) => {
                setKeyWordList(keywords);
                form.setFieldsValue({
                  keyWordList: keywords,
                });
              }}
            />
          </Form.Item>
          <TransferEdit
            show={showTransferInfo}
            form={form}
            initValues={{
              stopCollectionUserList: editInfo?.stopCollectionUserList,
              stopConnectionUserList: editInfo?.stopConnectionUserList,
            }}
          />
        </Col>
        <Col>
          <Form.Item>
            <div style={{ marginLeft: 10 }}>min</div>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default React.memo(BusinessInfo);
