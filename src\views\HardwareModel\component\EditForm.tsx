/* eslint-disable no-unused-vars */
import { Checkbox, Form, FormInstance, Input, Radio, Select } from 'antd';
import React, { ReactNode, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { hardWareModelForm } from '../utils/constant';
import ReferencesItem from './ReferencesItem';
import { EnableDropDownList } from '../../../utils/constant';
import { HardwareModalApi } from '@/fetch/business';
import { useCommonDropDown } from '@/utils/hooks';
import { formatOptions } from '@/utils/utils';
import { HttpStatusCode } from '@/fetch/core/constant';
import { PageType } from '@/utils/enum';
import { dropDownListKey, dropDownKey } from '@/utils/constant';

interface Props {
  form: FormInstance;
  type: string;
  initValues?: any;
}

const EditForm = ({ form, type, initValues }: Props) => {
  const fetchApi = new HardwareModalApi();
  const [editConfiger, setEditConfiger] = useState<{
    usageList: any;
  }>({
    usageList: [],
  });

  const dropDownData = useCommonDropDown([
    dropDownKey.PRODUCT_MANUFACTORY,
    dropDownKey.ENABLE,
    dropDownKey.HARDWARE_TYPE,
  ]);

  useEffect(() => {
    if (initValues) {
      form.setFieldsValue({
        ...initValues,
      });
      initValues.hardwareTypeId &&
        fetchHardWareUsageList(initValues.hardwareTypeId);
    }
  }, [initValues]);

  const fetchHardWareUsageList = async (id: any) => {
    const res = await fetchApi.getSelectListById(id);
    if (res && res.code === HttpStatusCode.Success) {
      setEditConfiger({
        usageList: res.data?.map((item: any) => {
          return {
            label: item.name,
            value: item.code,
          };
        }),
      });
    }
  };

  const changeValueMaterial = (arr: any) => {
    if (arr) {
      const valiateArr = arr.filter((item: any) => {
        return item.name && item.address;
      });
      form.setFieldsValue({
        referenceList: valiateArr,
      });
    }
  };

  return (
    <div className="vendor-form-content">
      <Form
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 18 }}
        className="reform"
        form={form}
        initialValues={initValues}
        onValuesChange={(changedFields, allFields) => {
          if (changedFields.hardwareTypeId !== undefined) {
            form.setFieldsValue({
              manufactoryId: null,
              hardwareModelUsageIdList: null,
            });
            fetchHardWareUsageList(changedFields.hardwareTypeId);
          }
        }}
      >
        {hardWareModelForm.map((item: any) => {
          let domFragment: ReactNode;

          if (item.contentType === 'input') {
            if (item.name === 'referenceList') {
              domFragment = (
                <Form.Item
                  name={item.name}
                  label={item.label}
                  rules={[{ required: item.rules, message: item.message }]}
                >
                  <ReferencesItem
                    type={type}
                    material={initValues.referenceList}
                    changeValue={(arr: any) => changeValueMaterial(arr)}
                  />
                </Form.Item>
              );
            } else {
              let disabled = item.disable;
              if (type === PageType.READONLY) {
                disabled = true;
              } else {
                if (item.name === 'model' && type !== PageType.ADD) {
                  disabled = true;
                } else {
                  disabled = item.disable;
                }
              }
              domFragment = (
                <Form.Item
                  name={item.name}
                  label={item.label}
                  rules={[{ required: item.rules, message: item.message }]}
                >
                  <Input
                    disabled={disabled}
                    placeholder={item.placeholder}
                    type={item.inputType}
                    maxLength={item.maxLength}
                    onChange={(e) => {}}
                  />
                </Form.Item>
              );
            }
          } else if (item.contentType === 'radio') {
            domFragment = (
              <Form.Item
                name={item.name}
                label={item.label}
                rules={[{ required: item.rules, message: item.message }]}
              >
                <Radio.Group
                  options={formatOptions(dropDownData[dropDownListKey.ENABLE])}
                  disabled={type === PageType.READONLY ? true : false}
                />
              </Form.Item>
            );
          } else if (item.contentType === 'checkBox') {
            domFragment = (
              <Form.Item
                name={item.name}
                label={item.label}
                rules={[{ required: item.rules, message: item.message }]}
              >
                {editConfiger.usageList?.length > 0 && (
                  <Checkbox.Group
                    options={editConfiger.usageList}
                    className="check-item"
                    disabled={type === PageType.READONLY ? true : false}
                  />
                )}
              </Form.Item>
            );
          } else if (item.contentType === 'select') {
            if (item.name === 'hardwareTypeId') {
              domFragment = (
                <Form.Item
                  name={item.name}
                  label={item.label}
                  rules={[{ required: item.rules, message: item.message }]}
                >
                  <Select
                    options={formatOptions(
                      dropDownData[dropDownListKey.HARDWARE_TYPE],
                    )}
                    showSearch
                    disabled={type === PageType.READONLY}
                    filterOption={(input: any, option: any) => {
                      const label: any = option?.label || '';
                      return (
                        label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      );
                    }}
                  />
                </Form.Item>
              );
            } else if (item.name === 'manufactoryId') {
              domFragment = (
                <Form.Item
                  name={item.name}
                  label={item.label}
                  rules={[{ required: item.rules, message: item.message }]}
                >
                  <Select
                    options={formatOptions(
                      dropDownData[dropDownListKey.PRODUCT_MANUFACTORY],
                    )}
                    showSearch
                    disabled={type === PageType.READONLY}
                    filterOption={(input: any, option: any) => {
                      const label: any = option?.label || '';
                      return (
                        label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      );
                    }}
                  />
                </Form.Item>
              );
            } else {
              domFragment = (
                <Form.Item
                  name={item.name}
                  label={item.label}
                  rules={[{ required: item.rules, message: item.message }]}
                >
                  <Select
                    options={formatOptions(
                      dropDownData[dropDownListKey.PRODUCT_MANUFACTORY],
                    )}
                    showSearch={true}
                    placeholder={item.placeholder}
                    disabled={type === PageType.READONLY ? true : false}
                    filterOption={(input: any, option: any) => {
                      const label: any = option?.label || '';
                      return (
                        label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      );
                    }}
                    allowClear
                  />
                </Form.Item>
              );
            }
          }
          return <React.Fragment key={item.name}>{domFragment}</React.Fragment>;
        })}
        <Form.Item label=" " colon={false}>
          <div style={{ color: '#808080' }}>
            <div>状态说明：</div>
            <div>
              1、只有“有效”的硬件，才能被引用成为一个硬件选择项，“无效”的硬件，不能被引用；
            </div>
            <div>
              2、硬件状态从“有效”改为“无效”，历史有被引用，历史数据不受影响，再修改，该无效硬件不在选择项范围内。
            </div>
          </div>
        </Form.Item>
      </Form>
    </div>
  );
};
export default React.memo(EditForm);
