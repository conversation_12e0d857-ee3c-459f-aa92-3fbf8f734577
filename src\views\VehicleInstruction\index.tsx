import React, { useState, useEffect } from 'react';
import {
  CommonTable,
  CommonForm,
  TableOperateBtn,
  CustomButton,
  ButtonType,
} from '@/components';
import { searchConfig, tableColumns } from './utils/columns';
import { VehicleInstructionApi } from '@/fetch/business';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/store';
import { useNavigate } from 'react-router-dom';
import { ProductType, YESNO } from '@/utils/enum';
import { saveSearchValues } from '@/redux/reducer/searchForm';
import { HttpStatusCode } from '@/fetch/core/constant';
import { Form, Table, Modal, Input, Popconfirm, message } from 'antd';
import { PageType } from '@/utils/EditTitle';

const VehicleInstruction = () => {
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const [modalForm] = Form.useForm();
  const fetchApi = new VehicleInstructionApi();
  const initSearchCondition = {
    searchForm: {
      name: null,
      eventSource: null,
      maintainUser: null,
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] =
    useState<SearchCondition>(initSearchCondition);
  const [tableData, setTableData] = useState<any>({
    list: [],
    totalPage: 1,
    totalNumber: 1,
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [modalShow, setModalShow] = useState<boolean>(false);
  const [templateName, setTemplateName] = useState<string | null>(null);
  useEffect(() => {
    getTableListData();
  }, [searchCondition]);

  const getTableListData = async () => {
    setLoading(true);
    try {
      const res = await fetchApi.fetchTableList(searchCondition);
      if (res && res.code === HttpStatusCode.Success) {
        setTableData({
          list: res.data.list,
          totalPage: res.data.pages,
          totalNumber: res.data.total,
        });
      }
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };

  const middleBtns: any[] = [
    {
      show: true,
      title: '新建',
      onClick: () => handleEdit(PageType.ADD),
    },
  ];
  const formateColumns = () => {
    return tableColumns.map((col) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) => {
            const { pageSize, pageNum } = searchCondition;
            return <div>{(pageNum - 1) * pageSize + index + 1}</div>;
          };
          break;
        case 'extractVehicleInfo':
          col.render = (text: any, record: any, index: number) =>
            text ? '是' : '否';
          break;
        case 'extractVideo':
          col.render = (text: any, record: any, index: number) =>
            text ? '是' : '否';
          break;
        case 'extractThreeDimension':
          col.render = (text: any, record: any, index: number) =>
            text ? '是' : '否';
          break;
        case 'operation':
          col.render = (text: any, record: any, index: number) => {
            return (
              <div className="operate">
                <TableOperateBtn
                  title="查看指令模板"
                  handleClick={() => {
                    getTemplateContent(record.id);
                    setModalShow(true);
                    setTemplateName(record.name);
                  }}
                />
                <Popconfirm
                  title="确定删除该指令吗？"
                  okText="确定"
                  cancelText="取消"
                  onConfirm={() => {
                    delConf(record.id);
                  }}
                >
                  <a>删除</a>
                </Popconfirm>
              </div>
            );
          };
          break;
        default:
          col.render = (text: any, record: any, index: number) =>
            `${text || '-'}`;
          break;
      }
      return col;
    });
  };

  const getTemplateContent = async (id: any) => {
    const res = await fetchApi.fetchDetail(id);
    if (res && res.code === HttpStatusCode.Success) {
      modalForm.setFieldsValue({
        content: res.data,
      });
    }
  };

  const delConf = async (value: any) => {
    const res = await fetchApi.deleteInfo(value);
    if (res.code === HttpStatusCode.Success) {
      message.success(res.meaasge ?? '删除成功！');
      getTableListData();
    }
  };

  const handleEdit = (type: PageType, id?: number) => {
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: searchCondition,
      }),
    );
    navigator(
      id
        ? `/app/vehicleInstruction/edit?type=${type}&id=${id}`
        : `/app/vehicleInstruction/edit?type=${type}`,
    );
  };

  const onSearchClick = (val) => {
    const data = {
      searchForm: {
        eventSource: val?.eventSource?.value,
        maintainUser: val?.maintainUser,
        name: val?.name,
      },
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(data);
  };

  return (
    <>
      <CommonForm
        formConfig={searchConfig}
        defaultValue={searchCondition.searchForm}
        layout="inline"
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={() => setSearchCondition({ ...initSearchCondition })}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.totalNumber,
          totalPage: tableData?.totalPage,
        }}
        columns={formateColumns()}
        loading={loading}
        rowKey={'id'}
        middleBtns={middleBtns}
        searchCondition={searchCondition}
        onPageChange={(value: any) => setSearchCondition(value)}
      />
      <Modal
        width={700}
        className="shadow-modal"
        visible={modalShow}
        title={`${templateName}指令模板`}
        onCancel={() => {
          setModalShow(false);
          setTemplateName(null);
          modalForm.resetFields();
        }}
        footer={
          <CustomButton
            title="关闭"
            onSubmitClick={() => {
              setModalShow(false);
              setTemplateName(null);
              modalForm.resetFields();
            }}
            buttonType={ButtonType.DefaultButton}
          />
        }
      >
        <Form form={modalForm} wrapperCol={{ span: 18 }} labelCol={{ span: 4 }}>
          <Form.Item label="指令模板" name={'content'}>
            <Input.TextArea autoSize={{ minRows: 5 }} disabled={true} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default React.memo(VehicleInstruction);
