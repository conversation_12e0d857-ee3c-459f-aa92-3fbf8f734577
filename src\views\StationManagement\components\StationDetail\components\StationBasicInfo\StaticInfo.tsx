import React, { useEffect, useState } from 'react';
import { Descriptions } from 'antd';
import type { DescriptionsProps } from 'antd';
import { BasicStationInfo } from '@/types';
import ModulePart from '@/components/ModulePart';

const StaticInfo = (props: { stationDetail: BasicStationInfo | null }) => {
  const { stationDetail } = props;
  const basicInfoItems: DescriptionsProps['items'] = [
    {
      key: 'stationBaseId',
      label: '站点ID',
      children: stationDetail?.stationBaseId,
    },
    {
      key: 'name',
      label: '站点名称',
      children: stationDetail?.name,
    },
    {
      key: 'productTypeName',
      label: '站点类型',
      children: stationDetail?.productTypeName,
    },
    {
      key: 'typeName',
      label: '运营方类型',
      children: stationDetail?.typeName,
    },
    {
      key: 'useCaseName',
      label: '站点用途',
      children: stationDetail?.useCaseName,
    },
    {
      key: 'countryName',
      label: '国家',
      children: stationDetail?.countryName,
    },
    {
      key: 'stateName',
      label: '省份',
      children: stationDetail?.stateName,
    },
    {
      key: 'cityName',
      label: '城市',
      children: stationDetail?.cityName,
    },
    {
      key: 'address',
      label: '详细地址',
      children: stationDetail?.address,
    },
    {
      key: 'lat',
      label: '纬度',
      children: stationDetail?.lat,
    },
    {
      key: 'lon',
      label: '经度',
      children: stationDetail?.lon,
    },
  ];
  const contactInfoItems: DescriptionsProps['items'] = [
    {
      key: 'personName',
      label: '姓名',
      children: `${stationDetail?.personChineseName}/${stationDetail?.personErp}`,
    },
    {
      key: 'contact',
      label: '电话',
      children: stationDetail?.contact,
    },
  ];
  return (
    <>
      <ModulePart title="基础信息">
        <Descriptions items={basicInfoItems}></Descriptions>
      </ModulePart>
      <ModulePart title="负责人信息">
        <Descriptions items={contactInfoItems}></Descriptions>
      </ModulePart>
    </>
  );
};
export default React.memo(StaticInfo);
