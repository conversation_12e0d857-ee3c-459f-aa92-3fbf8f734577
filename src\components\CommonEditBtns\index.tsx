import { Button } from 'antd';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import './index.scss';

export enum EditBtnType {
  Confirm = 'Confirm',
  Cancel = 'Cancel',
  Close = 'Close',
}

const CommonEditBtns = ({
  btnList,
}: {
  btnList: {
    title: string;
    onClick?: Function;
    type: EditBtnType;
    otherStyle?: any;
  }[];
}) => {
  return (
    <div className="common-edit-btns-container">
      <div className="btns-container">
        {btnList.map((item) => {
          return (
            <Button
              key={item.type}
              type={item.type === EditBtnType.Confirm ? 'primary' : 'default'}
              style={item.otherStyle}
              onClick={() => {
                item.onClick && item.onClick();
              }}
            >
              {item.title}
            </Button>
          );
        })}
      </div>
    </div>
  );
};

export default React.memo(CommonEditBtns);
