import React, { useEffect, useRef, useState } from 'react';
import { CommonForm, FormConfig } from '@/components';
import IndicatorFetch from '../IndicatorConfig/utils/fetchApi';
import { ColumnType } from 'antd/lib/table';
import { issuePoolColumns, DataType } from './utils/columns';
import { message, Select, Table } from 'antd';
import { HttpStatusCode } from '@/fetch/core/constant';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { pageSizeOptions } from '@/utils/constant';
import { CommonApi } from '@/fetch/business';
const fetchApi = new IndicatorFetch();
const commonFetch = new CommonApi();
const IssuePoolConfig = () => {
  const [tableData, setTableData] = useState<any>({
    list: [],
  });
  const [editingKey, setEditingKey] = useState<string>();
  const [issuePoolDropList, setIssuePoolDropList] = useState<any[]>();
  const editDataRef = useRef<any>(new Map());
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const initSearchCondition = {
    searchForm: {
      indicatorName: null,
    },
  };
  const [searchCondition, setSearchCondition] = useState<any>(() => {
    return historySearchValues.searchValues
      ? historySearchValues.searchValues
      : initSearchCondition;
  });
  const formConfig: FormConfig = {
    fields: [
      {
        fieldName: 'indicatorName',
        type: 'input',
        label: '事件名称',
        placeholder: '请输入事件名称',
      },
    ],
  };
  const getIssuePoolTable = async () => {
    try {
      const res = await fetchApi.fetchIssuePoolList({
        indicatorName: searchCondition.searchForm.indicatorName,
      });
      if (res.code === HttpStatusCode.Success) {
        setTableData({
          list: res.data,
        });
        editDataRef.current = new Map();
        res.data &&
          res.data.forEach((item) => {
            editDataRef.current.set(item.indicatorNumber, item.issuePool);
          });
      }
    } catch (err) {
      console.error(err);
    }
  };
  const getIssuePoolDropList = async () => {
    const res = await commonFetch.getCommonDropDown({
      keyList: ['ISSUE_POOL'],
    });
    if (res.code === HttpStatusCode.Success) {
      res.data.issuePoolList &&
        setIssuePoolDropList(
          res.data.issuePoolList.map((item) => {
            return {
              label: item.name,
              value: item.code,
            };
          }),
        );
    }
  };
  useEffect(() => {
    getIssuePoolDropList();
  }, []);
  useEffect(() => {
    getIssuePoolTable();
  }, [searchCondition]);
  const onSearchClick = (val) => {
    const data = {
      searchForm: val,
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(data);
  };
  const saveIssuePool = async (indicatorNumber: string) => {
    try {
      const res = await fetchApi.editIssuePoolList({
        indicatorNumber: indicatorNumber,
        issuePool: editDataRef.current.get(indicatorNumber),
      });
      if (res.code === HttpStatusCode.Success) {
        message.success('保存成功！');
        setEditingKey('');
        getIssuePoolTable();
      } else {
        message.error(res.message);
      }
    } catch (err) {
      console.error(err);
    }
  };
  const formatColumns = () => {
    return issuePoolColumns.map((col: ColumnType<DataType>) => {
      switch (col.dataIndex) {
        case 'issuePoolName':
          return {
            ...col,
            render: (text, record) => {
              if (editingKey === record.indicatorNumber) {
                return (
                  <Select
                    options={issuePoolDropList}
                    defaultValue={record.issuePool}
                    onChange={(value) => {
                      editDataRef.current.set(record.indicatorNumber, value);
                    }}
                  ></Select>
                );
              } else {
                return <>{text}</>;
              }
            },
          };
        case 'operations':
          return {
            ...col,
            render: (text, record) => {
              if (editingKey === record.indicatorNumber) {
                return (
                  <a
                    onClick={() => {
                      saveIssuePool(record.indicatorNumber);
                    }}
                  >
                    保存
                  </a>
                );
              } else {
                return editingKey && editingKey !== record.indicatorNumber ? (
                  <a style={{ color: 'grey', cursor: 'not-allowed' }}>编辑</a>
                ) : (
                  <a
                    onClick={() => {
                      setEditingKey(record.indicatorNumber);
                    }}
                  >
                    编辑
                  </a>
                );
              }
            },
          };
        default:
          return col;
      }
    });
  };
  return (
    <>
      <CommonForm
        formConfig={formConfig}
        defaultValue={searchCondition.searchForm}
        layout="inline"
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={() => setSearchCondition({ ...initSearchCondition })}
      />
      <Table
        bordered
        rowKey="indicatorNumber"
        scroll={{
          y: 700,
        }}
        dataSource={tableData.list}
        columns={formatColumns()}
        pagination={false}
      ></Table>
    </>
  );
};
export default React.memo(IssuePoolConfig);
