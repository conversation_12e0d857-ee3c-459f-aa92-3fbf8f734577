import React, { useEffect, useState } from 'react';
import { HttpStatusCode } from '@/fetch/core/constant';
import { Descriptions } from 'antd';
import { EyeInvisibleOutlined, EyeOutlined } from "@ant-design/icons";
import './index.scss';
import { OrderManageFetchApi } from '../../fetch';

interface Props {
  tableColumns: any;
  title: string;
  info: any;
  column: number;
  id: number | string;
}
export enum PhoneType {
  CONTACT = 'CONTACT',  // 收货人手机号
  LOAD_USER_CONTACT = 'LOAD_USER_CONTACT',   // 分配人手机号
  COLLECTION_USER_CONTACT = 'COLLECTION_USER_CONTACT',   // 寄件人手机号
  LOAD_USER_CONTACT_A = 'LOAD_USER_CONTACT_A',  // 取件人手机号
}

const PhoneTypeMap = new Map([
  ['contact', PhoneType.CONTACT],
  ['loadUserContact', PhoneType.LOAD_USER_CONTACT],
  ['collectionUserContact', PhoneType.COLLECTION_USER_CONTACT],
  ['loadUserContactA', PhoneType.LOAD_USER_CONTACT_A]
])

const PhoneItem = (props: any) => {
  const { phoneKey, getPhone, item, updatePhone, updatePhoneSecret, clearPhone } = props;

  return <div className='phone'>
    <div style={{ color: '#31C2A6' }} onClick={() => {
      if (item.secret) {
        getPhone(PhoneTypeMap.get(phoneKey))
      };
      updatePhoneSecret(PhoneTypeMap.get(phoneKey), !item.secret);
      clearPhone()
    }}>
      {item.secret ? <EyeInvisibleOutlined /> : <EyeOutlined />}
    </div>
    {(!item.secret && updatePhone.phone && updatePhone.type === PhoneTypeMap.get(phoneKey)) ? updatePhone.phone : item.fakePhone}
  </div>
}

const DescriptionTable = (props: Props) => {
  const { tableColumns, column, title, info, id } = props;
  const fetchApi = new OrderManageFetchApi();
  const [updatePhone, setUpdatePhone] = useState<any>({
    type: null,
    phone: null
  });
  const [tableInfo, setTableInfo] = useState<any>(info)

  const getPhone = async (type: any) => {
    const res: any = await fetchApi.fetchPhoneNumber({
      id: id,
      type: type === PhoneType.LOAD_USER_CONTACT_A ? PhoneType.LOAD_USER_CONTACT : type
    });
    if (res.code === HttpStatusCode.Success) {
      setUpdatePhone({
        type: type,
        phone: res.data.phone
      });
    };
  };

  const updatePhoneSecret = (type: any, secret: any) => {
    switch (type) {
      case PhoneType.CONTACT:
        setTableInfo({
          ...tableInfo,
          "contactSecret": secret,
          "loadUserContactSecret": true,
          "loadUserContactASecret": true,
          "collectionUserContactSecret": true,
        });
        break;
      case PhoneType.COLLECTION_USER_CONTACT:
        setTableInfo({
          ...tableInfo,
          "contactSecret": true,
          "loadUserContactSecret": true,
          "loadUserContactASecret": true,
          "collectionUserContactSecret": secret,
        });
        break;
      case PhoneType.LOAD_USER_CONTACT:
        setTableInfo({
          ...tableInfo,
          "contactSecret": true,
          "loadUserContactSecret": secret,
          "collectionUserContactSecret": true,
          "loadUserContactASecret": true,
        });
        break;
      case PhoneType.LOAD_USER_CONTACT_A:
        setTableInfo({
          ...tableInfo,
          "contactSecret": true,
          "loadUserContactSecret": true,
          "collectionUserContactSecret": true,
          "loadUserContactASecret": secret,
        });
        break;
    }
  };

  return <div className='description-table'>
    <div className='title'>
      {title}
    </div>
    <div className='table'>
      <Descriptions bordered column={column}>
        {
          tableColumns.map((item: any) => {
            switch (item.key) {
              case 'contact':
                return <Descriptions.Item key={item.key} label={item.label} span={item.span}>
                  <PhoneItem
                    key={item.key}
                    phoneKey={item.key}
                    getPhone={(type: any) => getPhone(type)}
                    item={{
                      secret: tableInfo[`${item.key}Secret`],
                      fakePhone: tableInfo[item.key]
                    }}
                    updatePhone={updatePhone}
                    updatePhoneSecret={(type: any, secret: any) => updatePhoneSecret(type, secret)}
                    clearPhone={() => setUpdatePhone({ type: null, phone: null })}
                  />
                </Descriptions.Item>;
                break;
              case 'loadUserContact':
                return <Descriptions.Item key={item.key} label={item.label} span={item.span}>
                  <PhoneItem
                    key={item.key}
                    phoneKey={item.key}
                    getPhone={(type: any) => getPhone(type)}
                    item={{
                      secret: tableInfo[`${item.key}Secret`],
                      fakePhone: tableInfo[item.key]
                    }}
                    updatePhone={updatePhone}
                    updatePhoneSecret={(type: any, secret: any) => updatePhoneSecret(type, secret)}
                    clearPhone={() => setUpdatePhone({ type: null, phone: null })}
                  />
                </Descriptions.Item>;
                break;
              case 'loadUserContactA':
                return <Descriptions.Item key={item.key} label={item.label} span={item.span}>
                  <PhoneItem
                    key={item.key}
                    phoneKey={item.key}
                    getPhone={(type: any) => getPhone(type)}
                    item={{
                      secret: tableInfo[`${item.key}Secret`],
                      fakePhone: tableInfo[item.key]
                    }}
                    updatePhone={updatePhone}
                    updatePhoneSecret={(type: any, secret: any) => updatePhoneSecret(type, secret)}
                    clearPhone={() => setUpdatePhone({ type: null, phone: null })}
                  />
                </Descriptions.Item>;
                break;
              case 'collectionUserContact':
                return <Descriptions.Item key={item.key} label={item.label} span={item.span}>
                  <PhoneItem
                    key={item.key}
                    phoneKey={item.key}
                    getPhone={(type: any) => getPhone(type)}
                    item={{
                      secret: tableInfo[`${item.key}Secret`],
                      fakePhone: tableInfo[item.key]
                    }}
                    updatePhone={updatePhone}
                    updatePhoneSecret={(type: any, secret: any) => updatePhoneSecret(type, secret)}
                    clearPhone={() => setUpdatePhone({ type: null, phone: null })}
                  />
                </Descriptions.Item>;
                break;
              // case 'verifyCode':
              //   return <Descriptions.Item className={'verifyCode'} key={item.key} label={item.label} span={item.span}>{tableInfo[item.key] || '-'}</Descriptions.Item>;
              //   break;
              default:
                return <Descriptions.Item key={item.key} label={item.label} span={item.span}>{tableInfo[item.key] || '-'}</Descriptions.Item>;
            }

          })
        }
      </Descriptions>
    </div>
  </div>
}

export default React.memo(DescriptionTable);