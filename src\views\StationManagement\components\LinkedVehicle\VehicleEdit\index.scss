.manage-edit {
  background-color: white;
  width: 100%;
  min-height: 500px;
  height: 100%;
  margin-top: 15px;

  .contact-station {
    margin-top: 10px;
    width: 100%;

    .basic-title {
      height: 50px;
      width: 120px;
      line-height: 50px;
      padding-left: 20px;
      background-size: 120px 50px;
      background-repeat: no-repeat;
      font-size: 14px;
      font-family: "Arial Negreta", "Arial Normal", "Arial", sans-serif;
      font-weight: 700;
      font-style: normal;
    }

    .notice {
      font-family: "Arial Negreta", "Arial Normal", "Arial", sans-serif;
      font-style: normal;
      color: #999f9d;
      line-height: 23px;
      font-size: 14px;
      margin-left: 10%;

      p {
        &:last-child {
          text-indent: 40px;
        }
      }
    }

    .reform {
      width: 90%;
      margin: 0 auto;
      margin-top: 10px;

      .check-group {
        position: relative;

        .check-total {
          margin-right: 100px;
          line-height: 32px;
          width: 10%;
          position: absolute;
          top: -1px;
        }

        .check-item {
          width: 90%;
          margin-left: 150px;
        }
      }
    }

    .#{$ant-prefix}-tabs-tab.#{$ant-prefix}-tabs-tab-active .#{$ant-prefix}-tabs-tab-btn {
      color: black;
    }

    .#{$ant-prefix}-tabs-tab {
      &:hover {
        color: "#3c6ef0";
      }
    }

    .#{$ant-prefix}-tabs-ink-bar {
      background-color: "#3c6ef0";
    }
  }

  .submit {
    text-align: center;
    height: 100px;
    margin-top: 50px;

    button {
      width: 87px;
      height: 40px;
    }

    button:first-child {
      margin-right: 50px;
      background-color: "#3c6ef0";
      border: none;
      border-radius: 5px;
    }
  }
}
