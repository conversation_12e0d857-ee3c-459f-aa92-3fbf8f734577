import React, { useEffect, useRef, useState } from 'react';
import IndicatorFetch from '../IndicatorConfig/utils/fetchApi';
import { useNavigate } from 'react-router-dom';
import { indicatorLayerColumns, DataType } from './utils/column';
import { ColumnType } from 'antd/lib/table';
import { Input, message, Table, Typography } from 'antd';
import { HttpStatusCode } from '@/fetch/core/constant';
import { IssueIndicatorLayerEdit } from '@/types/issueIndicator';
const fetchApi = new IndicatorFetch();
const { Text } = Typography;
const IndicatorLayerConfig = () => {
  const [tableData, setTableData] = useState([]);
  const [editingKeys, setEditingKeys] = useState<string[]>([]);
  const editDataRef = useRef<any>(new Map());
  const navigator = useNavigate();

  const getIndicatorLayerTable = async () => {
    try {
      const res = await fetchApi.fetchIndicatorLayerList();
      if (res.code === HttpStatusCode.Success) {
        if (res.data) {
          setTableData(res.data);
          editDataRef.current = new Map();
          res.data.forEach((item) => {
            editDataRef.current.set(item.layerNumber, item.priority);
          });
        }
      } else {
        message.error(res.message || '接口请求失败');
      }
    } catch (err) {
      console.error(err);
    }
  };

  useEffect(() => {
    getIndicatorLayerTable();
  }, []);

  const saveLayerPriority = async () => {
    try {
      let total = 0;
      editDataRef.current.forEach((value) => {
        if (typeof value !== 'number') {
          message.error('权重不能为空!');
          throw new Error('权重不能为空！');
        } else {
          total += value;
        }
      });
      if (total > 100 || total < 100) {
        message.error('权重之和必须为100%！');
        return;
      } else {
        const layerInfoList: IssueIndicatorLayerEdit[] = [];
        editDataRef.current.forEach((value, key) => {
          layerInfoList.push({
            layerNumber: key,
            priority: value,
          });
        });
        const res = await fetchApi.editIndicatorLayerList(layerInfoList);
        if (res.code === HttpStatusCode.Success) {
          message.success('保存成功！');
          setEditingKeys([]);
          getIndicatorLayerTable();
        } else {
          message.error(res.message || '网络请求错误！');
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  const formatColumns = () => {
    return indicatorLayerColumns.map((col: ColumnType<DataType>) => {
      switch (col.dataIndex) {
        case 'priority':
          return {
            ...col,
            render: (text, record) => {
              if (editingKeys.includes(record.layerNumber)) {
                return (
                  <Input
                    type="number"
                    defaultValue={text}
                    onBlur={(e) => {
                      if (e.target.value && Number(e.target.value) >= 0) {
                        editDataRef.current.set(
                          record.layerNumber,
                          Number(
                            Number(
                              e.target.value.replace(
                                /[^\d{1,}\.\d{1,}|\d{1,}]/g,
                                '',
                              ),
                            ).toFixed(3),
                          ),
                        );
                      } else {
                        editDataRef.current.set(record.layerNumber, null);
                        message.error('请输入正数！');
                      }
                    }}
                  ></Input>
                );
              } else {
                return <span>{text}</span>;
              }
            },
          };
        case 'indicatorCount':
          return {
            ...col,
            render: (text, record) => {
              return record.indicatorCount >= 0 ? (
                <a
                  onClick={() => {
                    navigator(
                      `/app/indicatorConfig?layerNumber=${record.layerNumber}`,
                    );
                  }}
                >
                  {text}
                </a>
              ) : (
                <>--</>
              );
            },
          };
        case 'operations':
          return {
            ...col,
            onCell: (record, index) => {
              if (index === 0) {
                return {
                  rowSpan: tableData.length,
                };
              } else {
                return {
                  rowSpan: 0,
                };
              }
            },
            render: (text, record) => {
              if (editingKeys.includes(record.layerNumber)) {
                return (
                  <a
                    onClick={() => {
                      saveLayerPriority();
                    }}
                  >
                    保存
                  </a>
                );
              } else {
                return (
                  <a
                    onClick={() => {
                      setEditingKeys(
                        tableData &&
                          tableData.map((item: any) => {
                            return item.layerNumber;
                          }),
                      );
                    }}
                  >
                    编辑
                  </a>
                );
              }
            },
          };
        default:
          return col;
      }
    });
  };

  return (
    <>
      <Table
        bordered
        pagination={false}
        rowKey="layerNumber"
        scroll={{
          y: 800,
        }}
        dataSource={tableData}
        columns={formatColumns()}
        summary={(pageData) => {
          let totalPriority = 0;
          editDataRef.current.forEach((value) => {
            totalPriority += value;
          });
          return (
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={1} align="center">
                共计
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1} colSpan={3} align="center">
                <Text type={totalPriority >= 100 ? 'danger' : 'success'}>
                  {totalPriority.toFixed(3)}%
                </Text>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          );
        }}
      ></Table>
    </>
  );
};
export default React.memo(IndicatorLayerConfig);
