.mr-manage {
  .table-row-red td {
    background-color: rgb(252, 244, 243);
  }
  .table-row-grey td {
    background-color: rgb(170, 170, 170);
  }
  .x-coreui-table {
    .x-coreui-table-cell {
      padding: 5px 2px;
    }
  }
  .#{$ant-prefix}-tabs {
    margin-top: 16px;
    .#{$ant-prefix}-tabs-nav {
      margin-bottom: 0px;
    }
  }

  .common-table {
    margin-top: 0px;
    .middle-btn {
      padding: 10px 0 0 10px;
    }
    a {
      margin-right: 0;
    }
    .operate {
      display: flex;
    }
  }
}
