import { request } from '@/fetch/core';
import { Method } from '@/fetch/core/constant';
import {
  IssueIndicatorEdit,
  IssueIndicatorLayerEdit,
} from '@/types/issueIndicator';

export default class IndicatorFetch {
  public fetchIndicatorLayerList = async () => {
    const requestOptions: RequestOptions = {
      method: Method.POST,
      path: '/k2/management/issue_indicator_layer/get_list_info',
      body: {
        name: null,
      },
    };
    return request(requestOptions);
  };
  public editIndicatorLayerList = async (params: IssueIndicatorLayerEdit[]) => {
    const requestOptions: RequestOptions = {
      method: Method.POST,
      path: '/k2/management/issue_indicator_layer/edit',
      body: {
        layerInfoList: params,
      },
    };
    return request(requestOptions);
  };
  public fetchIndicatorList = async (params: {
    indicatorLayerNumber: string;
  }) => {
    const requestOptions: RequestOptions = {
      method: Method.POST,
      path: '/k2/management/issue_indicator/get_list_info',
      body: params,
    };
    return request(requestOptions);
  };
  public editIndicatorList = async (params: IssueIndicatorEdit) => {
    const requestOptions: RequestOptions = {
      method: Method.POST,
      path: '/k2/management/issue_indicator/edit_priority',
      body: params,
    };
    return request(requestOptions);
  };
  public fetchIssuePoolList = async (params: { indicatorName: string }) => {
    const requestOptions: RequestOptions = {
      method: Method.POST,
      path: '/k2/management/issue_indicator/get_page_list',
      body: {
        indicatorName: params.indicatorName,
      },
    };
    return request(requestOptions);
  };
  public editIssuePoolList = async (params: {
    indicatorNumber: string;
    issuePool: string;
  }) => {
    const requestOptions: RequestOptions = {
      method: Method.POST,
      path: '/k2/management/issue_indicator/edit_issue_pool',
      body: params,
    };
    return request(requestOptions);
  };
}
