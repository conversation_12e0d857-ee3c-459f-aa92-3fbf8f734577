import React, { useEffect, useState, useRef } from 'react';
import {
  BatchUploadColumns,
  InsuranceExcelTemplateColumns,
  InsuranceFormConfig,
} from './utils/columns';
import { CommonForm, ExcelUploader } from '@jd/x-coreui';
import { useNavigate } from 'react-router-dom';
import InsuranceManage from '@/fetch/business/insuranceManage';
import {
  Col,
  Button,
  Table,
  FormInstance,
  Row,
  InputNumber,
  message,
} from 'antd';
import { CommonEdit } from '@/components';
import { InsuranceTitle, PageType } from '@/utils/EditTitle';
import TextArea from 'antd/es/input/TextArea';
import { HttpStatusCode } from '@/fetch/core/constant';
import { isEmpty } from '@/utils/utils';
import { cloneDeep } from 'lodash';
const Add = () => {
  const navigate = useNavigate();
  const [batchUploadList, setBatchUploadList] = useState<any[]>([]);
  const [remark, setRemark] = useState('');
  const formRef = useRef<any>(null);
  const [visible, setVisible] = useState(false);
  const [templateUrl, setTemplateUrl] = useState('');
  const [formConfig, setFormConfig] = useState<any>({
    fields: [
      ...InsuranceFormConfig.fields,
      {
        fieldName: 'deviceInsuranceInfoList',
        label: '投保车辆',
        required: true,
        type: 'ReactNode',
        labelCol: { span: 3 },
        wrapperCol: { span: 21 },
        childrenList: ['station', 'deviceName', 'addDeviceList'],
      },
      {
        fieldName: 'station',
        label: '站点',
        type: 'select',
        showSearch: true,
        placeholder: '请选择站点',
        labelCol: { span: 3 },
        wrapperCol: { span: 21 },
        xxl: 8,
        xl: 12,
        options: [],
        isChild: true,
      },
      {
        fieldName: 'deviceName',
        label: '车牌号',
        type: 'input',
        labelCol: { span: 3 },
        wrapperCol: { span: 21 },
        xxl: 8,
        xl: 12,
        placeholder: '请输入车牌号',
        isChild: true,
      },
      {
        fieldName: 'addDeviceList',
        label: '',
        type: 'ReactNode',
        labelCol: { span: 3 },
        wrapperCol: { span: 21 },
        isChild: true,
        xxl: 8,
        xl: 12,
        renderFunc: (data, value) => {
          return (
            <div>
              <Button type="primary" onClick={onAddClick}>
                查询并添加
              </Button>
            </div>
          );
        },
      },
    ],
  });
  const formatColumns = () => {
    return BatchUploadColumns.map((item) => {
      switch (item.dataIndex) {
        case 'radius':
          return {
            ...item,
            render: (text, record) => {
              return (
                <InputNumber
                  value={record.radius}
                  onChange={(value) => {
                    setBatchUploadList(
                      batchUploadList.map((item) => {
                        if (item.deviceName === record.deviceName) {
                          return { ...item, radius: value };
                        }
                        return item;
                      }),
                    );
                  }}
                />
              );
            },
          };
        case 'operate':
          return {
            ...item,
            render: (text, record) => {
              return (
                <div>
                  <Button
                    type="link"
                    onClick={() => {
                      setBatchUploadList(
                        batchUploadList.filter(
                          (item) => item.deviceName !== record.deviceName,
                        ),
                      );
                    }}
                  >
                    删除
                  </Button>
                </div>
              );
            },
          };
        default:
          return {
            ...item,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  // 生成唯一key的函数
  const generateKey = (item: any) => `${item.deviceName}_${item.serialNo}`;

  // 合并数据的函数，保留旧数据
  const mergeData = (oldData: any[], newData: any[]) => {
    const existingKeys = new Set(oldData.map(generateKey));
    const newItems = newData.filter(
      (item) => !existingKeys.has(generateKey(item)),
    );
    return [...oldData, ...newItems];
  };

  const onAddClick = async () => {
    const formValues = formRef.current?.getFieldsValue();
    const { station, deviceName } = formValues;

    if (!station && !deviceName) {
      return;
    }

    try {
      const deviceInfo = await InsuranceManage.getDeviceInfoList({
        stationBaseId: station?.value || null,
        deviceName,
      });

      if (deviceInfo.data && deviceInfo.data.length > 0) {
        const newData = deviceInfo.data.map((item) => ({
          deviceName: item.deviceName,
          serialNo: item.serialNo,
          provinceName: item.provinceName,
          cityName: item.cityName,
          stationName: item.stationName,
        }));
        if (newData.some((item) => !item.stationName)) {
          message.error('添加失败，车辆未绑定站点！');
          return;
        }
        setBatchUploadList((prevList) => mergeData(prevList, newData));
      } else {
        message.error('车辆信息不存在，请修改！');
      }
    } catch (error) {
      console.error('获取设备信息失败', error);
    }
  };

  const onBatchUploadClick = () => {
    setVisible(true);
  };
  useEffect(() => {
    InsuranceManage.getStationList().then((res) => {
      const newConfig = cloneDeep(formConfig);
      if (res.code === HttpStatusCode.Success) {
        newConfig.fields.find(
          (item: any) => item.fieldName === 'station',
        ).options = res.data.map((item: any) => ({
          label: item.name,
          value: item.id,
        }));
        setFormConfig(newConfig);
      }
    });
    InsuranceManage.getInsuranceExcelTemplate().then((res) => {
      setTemplateUrl(res.data.url.replace('http://', 'https://'));
    });
  }, []);

  const onSubmitClick = async () => {
    try {
      const formValues = await formRef.current?.validateFields();
      const {
        otherAttachmentList,
        policyAttachment,
        effectiveTime,
        insuranceCompany,
        insuranceType,
        insuredEntity,
        policyCoverageAmount,
        policyNumber,
      } = formValues;
      const effectiveStartTime = effectiveTime[0].format('YYYY-MM-DD');
      const effectiveEndTime = effectiveTime[1].format('YYYY-MM-DD');
      const params = {
        policyAttachment: {
          type: 'file',
          bucketName: 'rover-operation',
          fileKey: policyAttachment[0],
        },
        otherAttachmentList: !isEmpty(otherAttachmentList)
          ? otherAttachmentList.map((item: any) => {
              return {
                type: 'file',
                bucketName: 'rover-operation',
                fileKey: item,
              };
            })
          : null,
        insuranceCompany,
        insuranceType,
        insuredEntity,
        policyCoverageAmount,
        policyNumber,
        effectiveStartTime,
        effectiveEndTime,
        remark,
        deviceInsuranceInfoList: batchUploadList.map((item) => {
          return {
            deviceName: item.deviceName,
            serialNo: item.serialNo,
            provinceName: item.provinceName,
            cityName: item.cityName,
            stationName: item.stationName,
            radius: item.radius,
          };
        }),
      };
      InsuranceManage.addInsurance(params).then((res) => {
        if (res.code === HttpStatusCode.Success) {
          message.success('添加成功');
          navigate('/app/insuranceManage');
        } else {
          message.error(res.message);
        }
      });
    } catch (err) {
      console.error(err);
    }
  };
  const onCancleClick = () => {
    navigate('/app/insuranceManage');
  };
  return (
    <CommonEdit
      title={InsuranceTitle[PageType.ADD]}
      breadCrumbConfig={[
        {
          title: '车辆保险管理',
          route: '',
        },
        {
          title: '新建',
          route: '',
        },
      ]}
      onSubmitClick={onSubmitClick}
      onCancleClick={onCancleClick}
      cancelTitle={'取消'}
    >
      <CommonForm
        formConfig={formConfig}
        formType="edit"
        layout="horizontal"
        colon={false}
        getFormInstance={(form: FormInstance) => {
          formRef.current = form;
        }}
      />
      <Row justify="space-around" style={{ marginBottom: 12 }}>
        <Col span={18}>
          <Button type="link" onClick={onBatchUploadClick}>
            批量上传车号
          </Button>
        </Col>
      </Row>
      <Row justify="space-around" style={{ marginBottom: 16 }}>
        <Col span={18}>
          <Table
            rowKey={(record) => record.deviceName}
            columns={formatColumns()}
            dataSource={batchUploadList}
            pagination={false}
            scroll={{ y: 300 }}
          ></Table>
        </Col>
      </Row>
      <Row justify="center">
        <Col span={1}>
          <label>备注</label>
        </Col>
        <Col span={18}>
          <TextArea
            rows={5}
            maxLength={200}
            placeholder="请输入备注信息"
            value={remark}
            onChange={(e) => {
              setRemark(e.target.value);
            }}
          />
        </Col>
      </Row>
      <ExcelUploader
        title="批量上传车牌号"
        description={
          '城市、站点、运营半径，按保单承保范围填写，如保到省，则后面三字段无需填写。'
        }
        downLoadTemplate={() => {
          window.open(templateUrl, '_self');
        }}
        columnConfig={InsuranceExcelTemplateColumns}
        visible={visible}
        onCancel={() => setVisible(false)}
        onSuccess={(value) => {
          if (Array.isArray(value)) {
            setBatchUploadList((prevList) => mergeData(prevList, value));
          }
        }}
        onError={(value) => {
          console.log(value);
        }}
        postProcessor={async (value) => {
          if (Array.isArray(value)) {
            const uniqueMap = new Map();
            const invalidData: any = [];
            const uniqueValue = value.filter((item) => {
              const key = `${item.deviceName}_${item.serialNo}`;
              if (!uniqueMap.has(key)) {
                uniqueMap.set(key, true);
                return true;
              }
              invalidData.push(item);
              return false;
            });

            return {
              validData: uniqueValue,
              invalidData: invalidData,
            };
          }
          return {
            validData: [],
            invalidData: value,
          };
        }}
      />
    </CommonEdit>
  );
};

export default Add;
