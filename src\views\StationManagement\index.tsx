import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { DEFAULT_PAGE } from '@/utils/constant';
import { YESNO } from '@/utils/enum';
import { useTableData } from '@/components/CommonTable/useTableData';
import { CommonApi, StationFetchApi } from '@/fetch/business';
import { CommonForm, CommonTable, FormConfig } from '@/components';
import { stationColumns } from './utils/column';
import { RootState } from '@/redux/store';
import { useCommonDropDown } from '@/utils/hooks';
import {
  removeSearchValues,
  saveSearchValues,
} from '@/redux/reducer/searchForm';
import { Popconfirm, message } from 'antd';
import { HttpStatusCode } from '@/fetch/core/constant';
import { ColumnsType } from 'antd/es/table';
import { StationRequest, StationResponse } from '@/types';
import { formatOptions } from '@/utils/utils';
import './index.scss';
import { isEqual } from 'lodash';
import BatchModifyStationPerson from './components/BatchModifyStationPerson';
const fetchApi = new StationFetchApi();
const commonFetch = new CommonApi();
const StationManagement = () => {
  const searchRef = useRef<HTMLDivElement>(null);
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const initSearchCondition: StationRequest = {
    searchForm: {
      stateId: null,
      cityId: null,
      deviceName: null,
      enable: null,
      productType: null,
      serialNo: null,
      type: null,
      useCase: null,
    },
    ...DEFAULT_PAGE,
  };
  const [tableKey, setTableKey] = useState('');
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValues.searchValues
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );
  const [stationList, setStationList] = useState([]);
  const { tableData, loading } = useTableData<StationRequest, StationResponse>(
    searchCondition,
    fetchApi.getStationTable,
    tableKey,
  );
  const [
    batchModifyStationPersonModalOpen,
    setBatchModifyStationPersonModalOpen,
  ] = useState(false);
  useEffect(() => {
    commonFetch.getStationDepartment({}).then((res) => {
      if (res.code === HttpStatusCode.Success) {
        setStationList(res.data);
      }
    });
  }, []);
  const dropdownData = useCommonDropDown([
    'STATION_USE_CASE',
    'STATION_TYPE',
    'ENABLE',
    'PRODUCT_TYPE',
  ]);

  const formConfig: FormConfig = {
    fields: [
      {
        fieldName: 'stationInfo',
        label: '省市站',
        placeholder: '请选择省市站信息',
        type: 'cascader',
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        mapRelation: { label: 'name', value: 'id', children: 'children' },
        options: stationList,
      },
      {
        fieldName: 'serialNo',
        label: '车架号',
        placeholder: '请输入车架号',
        type: 'input',
      },
      {
        fieldName: 'deviceName',
        label: '车牌号',
        placeholder: '请输入车牌号',
        type: 'input',
      },
      {
        fieldName: 'useCase',
        label: '用途',
        placeholder: '请选择站点用途',
        showSearch: true,
        type: 'select',
        labelInValue: false,
        options: formatOptions(dropdownData.stationUseCaseList),
      },
      {
        fieldName: 'productType',
        label: '站点类型',
        placeholder: '请选择站点类型',
        showSearch: true,
        type: 'select',
        labelInValue: false,
        options: formatOptions(dropdownData.productTypeList),
      },
      {
        fieldName: 'enable',
        label: '站点状态',
        placeholder: '请选择站点状态',
        showSearch: true,
        type: 'select',
        labelInValue: false,
        options: formatOptions(dropdownData.enableList),
      },
      {
        fieldName: 'type',
        label: '运营方类型',
        placeholder: '请选择运营方类型',
        showSearch: true,
        type: 'select',
        labelInValue: false,
        options: formatOptions(dropdownData.stationTypeList),
      },
    ],
  };
  const onSearchClick = (values) => {
    if (values.stationInfo) {
      console.log(values.stationInfo);
      const data = {
        searchForm: {
          ...values,
          stateId: values.stationInfo.length > 0 ? values.stationInfo[0] : null,
          cityId: values.stationInfo.length >= 2 ? values.stationInfo[1] : null,
          stationBaseId:
            values.stationInfo.length === 3 ? values.stationInfo[2] : null,
        },
        ...DEFAULT_PAGE,
      };
      // delete data.searchForm.stationInfo;
      dispatch(
        saveSearchValues({
          routeName: location.pathname,
          searchValues: data,
        }),
      );
      if (isEqual(data, searchCondition)) {
        setTableKey(new Date().getMilliseconds().toString());
      } else {
        setSearchCondition(data);
      }
    } else {
      delete values.stationInfo;
      const data = {
        searchForm: {
          ...values,
        },
        ...DEFAULT_PAGE,
      };
      dispatch(
        saveSearchValues({
          routeName: location.pathname,
          searchValues: data,
        }),
      );
      if (isEqual(data, searchCondition)) {
        setTableKey(new Date().getMilliseconds().toString());
      } else {
        setSearchCondition(data);
      }
    }
  };
  const onResetClick = () => {
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: initSearchCondition,
      }),
    );
    delete initSearchCondition.searchForm.cityId;
    delete initSearchCondition.searchForm.stateId;
    const data = {
      ...initSearchCondition,
      searchForm: {
        ...initSearchCondition.searchForm,
        stationInfo: null,
      },
    };
    setSearchCondition(data);
  };
  const middleBtns: any[] = [
    {
      show: true,
      title: '新建',
      key: 'stationManageAddStation',
      onClick: () => navigator('/app/stationManagement/addStation'),
    },
    {
      show: true,
      title: '批量修改站点负责人',
      key: 'stationManageChangePerson',
      onClick: () => {
        setBatchModifyStationPersonModalOpen(true);
      },
    },
  ];

  const handleStationStatusChanged = async (record: any, updateStatus: any) => {
    const response: any = await fetchApi.changeStationStatus(
      record.stationBaseId,
      updateStatus,
    );
    if (response.code === HttpStatusCode.Success) {
      message.success('操作成功！');
      setTableKey(new Date().getMilliseconds().toString());
    }
  };
  const formatColumns = (columns: ColumnsType<StationResponse>) => {
    return columns.map((item: any) => {
      switch (item.dataIndex) {
        case 'rowIndex':
          return {
            ...item,
            render: (text: any, record: any, index: number) => {
              return `${
                (searchCondition.pageNum - 1) * searchCondition.pageSize +
                index +
                1
              }`;
            },
          };
        case 'enableName':
          return {
            ...item,
            render: (text: any, record: any, index: number) => {
              const statusStyle =
                record.enable === YESNO.YES ? 'enable' : 'unenable';
              return <div className={statusStyle}>{record.enableName}</div>;
            },
          };
        case 'operation':
          return {
            ...item,
            render: (text: any, record: any, index: number) => {
              const statusStyle =
                record.enable === YESNO.NO ? 'enable' : 'unenable';
              const statusLabel = record.enable === YESNO.YES ? '停用' : '启用';
              const msg =
                record.enable === YESNO.YES
                  ? '站点停用后，会停用停靠点，解绑停靠点与车辆关系，请确认是否停用站点？'
                  : '确认启用吗?';
              return (
                <div className="operate">
                  <Popconfirm
                    placement="left"
                    title={msg}
                    onConfirm={() =>
                      handleStationStatusChanged(
                        record,
                        record.enable === YESNO.YES ? YESNO.NO : YESNO.YES,
                      )
                    }
                    okText="确定"
                    cancelText="取消"
                    overlayStyle={{ maxWidth: 800 }}
                  >
                    <a className={statusStyle}>{statusLabel}</a>
                  </Popconfirm>
                  <a
                    onClick={() => {
                      dispatch(
                        saveSearchValues({
                          routeName: location.pathname,
                          searchValues: searchCondition,
                        }),
                      );
                      const url = `/app/stationManagement/detail?stationBaseId=${record.stationBaseId}`;
                      navigator(url);
                    }}
                  >
                    查看详情
                  </a>
                </div>
              );
            },
          };
        default:
          return {
            ...item,
          };
      }
    });
  };

  return (
    <>
      <div className="form-table-wrapper">
        <div ref={searchRef}>
          <CommonForm
            formConfig={formConfig}
            layout="inline"
            formType="search"
            colon={false}
            onSearchClick={onSearchClick}
            defaultValue={searchCondition.searchForm}
            onResetClick={onResetClick}
          />
        </div>
        <CommonTable
          searchRef={searchRef}
          tableKey={tableKey}
          searchCondition={searchCondition}
          loading={loading}
          tableListData={{
            list: tableData?.list || [],
            totalPage: tableData?.pages,
            totalNumber: tableData?.total,
          }}
          columns={formatColumns(stationColumns)}
          middleBtns={middleBtns}
          rowKey={'stationBaseId'}
          onPageChange={(paginationData: any) => {
            setTableKey(Date.now().toString());
            setSearchCondition({
              ...searchCondition,
              pageNum: paginationData.pageNum,
              pageSize: paginationData.pageSize,
            });
          }}
        ></CommonTable>
      </div>
      <BatchModifyStationPerson
        open={batchModifyStationPersonModalOpen}
        setOpen={setBatchModifyStationPersonModalOpen}
      />
    </>
  );
};
export default React.memo(StationManagement);
