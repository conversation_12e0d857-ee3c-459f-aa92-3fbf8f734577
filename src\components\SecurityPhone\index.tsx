import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons';
import React from 'react'

const SecurityPhone = ({
  phone,
  security,
  onClick }: {
    phone: string,
    security: boolean,
    onClick: Function
  }) => {
  return (
    <div style={{
      display: "flex",
      flexDirection: "row",
      justifyContent: "center",
      fontSize: 13
    }}>
      <div
        style={{ height: 20, width: 20 }}
        onClick={() => {
          onClick && onClick(!security)
        }}
      >
        {security ? <EyeInvisibleOutlined /> : <EyeOutlined />}
      </div>
      {security ? phone && phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') || '' : phone}
    </div>)
}
export default SecurityPhone;
