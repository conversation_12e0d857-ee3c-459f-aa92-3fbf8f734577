import { FormConfig } from '@jd/x-coreui';

export const ModuleListOptions = [
  {
    label: '感知',
    value: 'PERCEPTION',
  },
  {
    label: 'PNC',
    value: 'PNC',
  },
  {
    label: '硬件',
    value: 'HARDWARE',
  },
  {
    label: '架构',
    value: 'ARCHITECTURE',
  },
  {
    label: '定位/地图',
    value: 'LOCALIZATION_MAP',
  },
];

export const getCategoryInfo = (type: 'ADD' | 'EDIT') => {
  const categoryInfo: FormConfig = {
    fields: [
      {
        label: '问题分类名称',
        fieldName: 'bugCategoryName',
        placeholder: '请输入分类名称',
        type: 'input',
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
        validatorRules: [
          { required: true, message: '请输入问题分类名称' },
          { max: 20, message: '最多输入20位' },
        ],
      },
      {
        label: '对应模块',
        fieldName: 'moduleCode',
        placeholder: '请选择模块',
        type: 'select',
        options: [],
        disabled: type === 'EDIT',
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
        labelInValue: false,
        validatorRules: [{ required: true, message: '请选择模块' }],
      },
    ],
  };

  return categoryInfo;
};
