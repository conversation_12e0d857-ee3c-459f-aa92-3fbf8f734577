import React, { useEffect, useState, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  EyeInvisibleOutlined,
  EyeOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import { Tooltip, Table, Form, message, Modal, Input, Watermark } from 'antd';
import './index.scss';
import {
  saveSearchValues,
  removeSearchValues,
} from '@/redux/reducer/searchForm';
import { HttpStatusCode } from '@/fetch/core/constant';
import { pageSizeOptions } from '@/utils/constant';
import { searchConfig, columns, subOrderColumns } from './utils/columns';
import { formatDateToSecond } from '@/utils/utils';
import DeliveryOrderDetail from './components/DeliveryOrderDetail';
import { PhoneType } from './components/DescriptionTable';
import { OrderManageFetchApi } from './fetch';
import { deliveryMode } from './utils/constant';
import CollectOrderDetail from './components/CollectOrderDetail';
import CustomButton from '@/components/CustomButton';
import { AnyFunc } from '@/global';
import { RootState } from '@/redux/store';
import { CommonForm, FieldItem, FormConfig } from '@/components';
import dayjs from 'dayjs';

const { Search } = Input;

const SubOrderModalContent = ({
  subOrderData,
  erp,
  handleSearch,
  handleClear,
  handleClose,
}: {
  subOrderData: {
    filteredData: any[];
    totalData: any[];
    originalOrderId: any;
    showModal: boolean;
    deliveryMode: any;
  };
  erp: string;
  handleSearch: AnyFunc;
  handleClear: AnyFunc;
  handleClose: AnyFunc;
}) => {
  const formateSubOrderCol = () => {
    return subOrderColumns.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          return {
            ...col,
            render: (params: any, record: any, index: any) => {
              return `${index + 1}`;
            },
          };
          break;
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  const total = subOrderData.totalData.reduce((pre, cur) => {
    return (pre = pre + cur.packageCount);
  }, 0);

  return (
    <Modal
      visible={subOrderData.showModal}
      title={
        subOrderData.deliveryMode === deliveryMode.B_DROP_OFF
          ? '箱号明细'
          : '接驳单明细'
      }
      onCancel={handleClose}
      footer={<CustomButton title="关闭" onSubmitClick={handleClose} />}
      width={800}
    >
      <Watermark
        content={erp}
        font={{ color: 'rgba(0,0,0,.07)' }}
        gap={[50, 90]}
      >
        <div style={{ display: 'flex', marginBottom: '10px' }}>
          <span style={{ marginRight: '20px' }}>{`${
            subOrderData.deliveryMode === deliveryMode.B_DROP_OFF
              ? '箱号'
              : '订单编号'
          }：${subOrderData.originalOrderId ?? '-'}`}</span>
          <span>{`包裹数：${total}`}</span>
        </div>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            marginBottom: '10px',
          }}
        >
          <span style={{ marginRight: '10px' }}>子订单编号</span>
          <Search
            placeholder="请输入子订单编号"
            allowClear
            style={{ width: '300px', marginRight: '10px' }}
            enterButton="查询"
            onSearch={(value: any) => {
              if (value) {
                handleSearch && handleSearch(value);
              }
            }}
            onChange={(e: any) => {
              if (!e.target.value || e.target.value.length <= 0) {
                handleClear && handleClear();
              }
            }}
          />
        </div>
        <Table
          rowKey={(record) => record.originalId}
          dataSource={subOrderData.filteredData}
          columns={formateSubOrderCol()}
          bordered
          pagination={false}
          scroll={{
            y: '400px',
          }}
        />
      </Watermark>
    </Modal>
  );
};

const OrderManager = () => {
  const fetchApi = new OrderManageFetchApi();
  const dispatch = useDispatch();

  const historySearchValue: any = useSelector(
    (state: RootState) => state.searchForm,
  );
  const startOfDay = dayjs().startOf('day');

  // 获取今天的结束时间（23:59:59）
  const endOfDay = dayjs().endOf('day').subtract(1, 'millisecond');

  const initSearchCondition = {
    searchForm: {
      originalOrderId: null,
      collectionUserContact: null,
      deviceName: null,
      gridNo: null,
      orderStatusList: [],
      startTime: startOfDay.format('YYYY-MM-DD HH:mm:ss'),
      endTime: endOfDay.format('YYYY-MM-DD HH:mm:ss'),
      scheduleName: null,
      scheduleTime: [startOfDay, endOfDay],
      stationBaseIdList: [],
      stopId: null,
      orderSourceCodeList: [],
      deliveryMode: null,
      loadMethod: null,
      stationInfo: null,
    },
    current: 1,
    pageSize: 10,
  };
  const formRef = useRef<any>(null);
  const [searchCondition, setSearchCondition] = useState<{
    searchForm: any;
    current: number;
    pageSize: number;
  }>(() => {
    return historySearchValue.searchValues
      ? historySearchValue.searchValues.searchForm
      : initSearchCondition;
  });
  const [currentPhone, setCurrentPhone] = useState<string | null>(null);
  const [searchFormConfig, setSearchFormConfig] =
    useState<FormConfig>(searchConfig);
  const [orderDetailInfo, setOrderDetailInfo] = useState<{
    deliveryVisible: boolean;
    collectVisible: boolean;
    id: string | number | null;
  }>({
    deliveryVisible: false,
    collectVisible: false,
    id: null,
  });
  const [subOrderData, setSubOrderData] = useState<any>({
    showModal: false,
    totalData: [],
    filteredData: [],
    originalOrderId: null,
    deliveryMode: null,
  });
  const userName: string | null = useSelector(
    (state: RootState) => state.common,
  ).userName;
  const getStopPoint = async (stationInfo: any) => {
    if (!stationInfo) {
      const stopIdField =
        searchFormConfig?.fields?.find(
          (field: FieldItem) => field.fieldName === 'stopId',
        ) || {};
      stopIdField!.options = [];
      setSearchFormConfig({
        ...searchFormConfig,
      });
      return;
    }
    if (stationInfo?.length > 0) {
      const res: any = await fetchApi.fetchStopPoint({
        stationBaseIdList: [stationInfo[2]],
      });
      if (res.code === HttpStatusCode.Success) {
        const stopIdField =
          searchFormConfig?.fields?.find(
            (field: FieldItem) => field.fieldName === 'stopId',
          ) || {};
        stopIdField!.options = res.data.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
        setSearchFormConfig({
          ...searchFormConfig,
        });
      }
    }
  };
  const getPhone = async (record: any) => {
    const res: any = await fetchApi.fetchPhoneNumber({
      id: record.id,
      type: PhoneType.COLLECTION_USER_CONTACT,
    });
    if (res.code === HttpStatusCode.Success) {
      setCurrentPhone(res.data.phone);
    }
  };

  const formateColumns = (columns: any) => {
    return columns.map((item: any) => {
      switch (item.dataIndex) {
        case 'orderId':
          return {
            ...item,
            render: (text: any) => `${text || '-'}`,
            title: (
              <div className="packageNo">
                <p>包裹号</p>
                <Tooltip
                  placement="right"
                  title={'包含“单号-包裹号-包裹数-配送次数”'}
                  overlayStyle={{ maxWidth: 600 }}
                  autoAdjustOverflow={false}
                >
                  <QuestionCircleOutlined className="question" />
                </Tooltip>
              </div>
            ),
          };
          break;
        case 'collectionUserName':
          return {
            ...item,
            render: (text: any) => `${text || '-'}`,
            title: (
              <div className="packageNo">
                <p>姓名</p>
                <Tooltip
                  placement="right"
                  title={'如配送方式是揽收，此姓名为“寄件人”，其他为“取件人”。'}
                  overlayStyle={{ maxWidth: 600 }}
                  autoAdjustOverflow={false}
                >
                  <QuestionCircleOutlined className="question" />
                </Tooltip>
              </div>
            ),
          };
          break;
        case 'collectionUserContact':
          return {
            ...item,
            title: (
              <div className="packageNo">
                <p>手机号</p>
                <Tooltip
                  placement="right"
                  title={
                    '如配送方式是揽收，此手机号为“寄件人”，其他为“取件人”。'
                  }
                  overlayStyle={{ maxWidth: 600 }}
                  autoAdjustOverflow={false}
                >
                  <QuestionCircleOutlined className="question" />
                </Tooltip>
              </div>
            ),
            render: (params: any, record: any, index: any) => {
              return (
                <div className="phone">
                  <div
                    style={{ color: '#1677ff' }}
                    onClick={() => {
                      if (params.item.secret) {
                        getPhone(params.item);
                      }
                      params.updateSecret(params.item.id, !params.item.secret);
                      setCurrentPhone(null);
                    }}
                  >
                    {params.item.secret ? (
                      <EyeInvisibleOutlined />
                    ) : (
                      <EyeOutlined />
                    )}
                  </div>
                  {!params.item.secret && currentPhone
                    ? currentPhone
                    : params.item.collectionUserContact}
                </div>
              );
            },
          };
          break;
        case 'operation':
          return {
            ...item,
            render: (params: any, record: any) => {
              return (
                <>
                  <div className="operate">
                    <a
                      style={{ marginRight: '10px' }}
                      onClick={() => {
                        setOrderDetailInfo({
                          deliveryVisible:
                            record.deliveryMode !== deliveryMode.DROP_OFF,
                          collectVisible: [
                            deliveryMode.DROP_OFF,
                            deliveryMode.B_DROP_OFF,
                          ].includes(record.deliveryMode),
                          id: record.id,
                        });
                      }}
                    >
                      详情
                    </a>
                    {record.deliveryMode === deliveryMode.TRANSPORT && (
                      <a
                        onClick={async () => {
                          const res = await fetchApi.fetchSubOrder({
                            batchCode: record.originalOrderId,
                          });
                          if (res.data && res.code === HttpStatusCode.Success) {
                            setSubOrderData({
                              showModal: true,
                              filteredData: res.data,
                              totalData: res.data,
                              originalOrderId: record.originalOrderId,
                              deliveryMode: record.deliveryMode,
                            });
                          } else {
                            message.error(res.message);
                          }
                        }}
                      >
                        接驳单明细
                      </a>
                    )}
                    {record.deliveryMode === deliveryMode.B_DROP_OFF && (
                      <a
                        onClick={async () => {
                          const res = await fetchApi.fetchBDropOffDetail(
                            record.boxCode,
                          );
                          if (res.data && res.code === HttpStatusCode.Success) {
                            setSubOrderData({
                              showModal: true,
                              filteredData: res.data,
                              totalData: res.data,
                              originalOrderId: record.boxCode,
                              deliveryMode: record.deliveryMode,
                            });
                          } else {
                            message.error(res.message);
                          }
                        }}
                      >
                        箱号明细
                      </a>
                    )}
                  </div>
                </>
              );
            },
          };
          break;
        default:
          return {
            ...item,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  const [loading, setLoading] = useState<boolean>(false);
  const [tableList, setTableList] = useState<{
    list: any[];
    totalPage: number;
    totalNumber: number;
  }>({
    list: [],
    totalPage: 0,
    totalNumber: 0,
  });
  const [orderTotal, setOrderTotal] = useState<number>();
  const [dropDownList, setDropDownList] = useState<any>({
    stop: [],
    orderStatusList: [],
    orderSourceCodeList: [],
    deliveryMode: [],
    loadMethod: [],
    vehicleName: [],
  });

  useEffect(() => {
    getTableList(searchCondition);
    getOrderTotal(searchCondition);
    getAllDropDownList();
  }, []);

  const getTableList = async (searchValue: any) => {
    setLoading(true);
    try {
      const res: any = await fetchApi.fetchOrderList({
        searchForm: searchValue.searchForm,
        page: searchValue.current,
        size: searchValue.pageSize,
      });
      if (res.code === HttpStatusCode.Success) {
        setTableList({
          list: res.data.list,
          totalPage: res.data.pages,
          totalNumber: res.data.total,
        });
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };

  const getOrderTotal = async (searchValue: any) => {
    const res: any = await fetchApi.fetchOrderTotal({
      searchForm: searchValue.searchForm,
      page: searchValue.current,
      size: searchValue.pageSize,
    });
    if (res.code === HttpStatusCode.Success) {
      setOrderTotal(res.data.totalOriginalOrderCount);
    } else {
      message.error(res.message);
    }
  };

  const getDropDownList = async () => {
    const res: any = await fetchApi.fetchSourceStatusDelivery();
    if (res.code === HttpStatusCode.Success) {
      return res.data;
    }
  };

  const getAllDropDownList = () => {
    getDropDownList().then((res) => {
      searchFormConfig.fields?.forEach((field: FieldItem) => {
        if (field.fieldName === 'orderStatusList') {
          field.options = res?.deliveryStatusList?.map((item: any) => {
            return {
              label: item.name,
              value: item.code,
            };
          });
        } else if (field.fieldName === 'orderSourceCodeList') {
          field.options = res?.orderSourceList?.map((item: any) => {
            return {
              label: item.name,
              value: item.code,
            };
          });
        } else if (field.fieldName === 'deliveryMode') {
          field.options = res?.deliveryModeList?.map((item: any) => {
            return {
              label: item.name,
              value: item.code,
            };
          });
        } else if (field.fieldName === 'loadMethod') {
          field.options = res?.loadMethodList?.map((item: any) => {
            return {
              label: item.name,
              value: item.code,
            };
          });
        }
      });
      setSearchFormConfig({
        ...searchFormConfig,
      });
    });
  };

  const onSearchClick = () => {
    const values = formRef.current.getFieldsValue();
    if (
      (values.province || values.city) &&
      (!values.station || values.station?.length <= 0)
    ) {
      message.warning('请选择站点!');
      return;
    }

    const data = {
      searchForm: {
        loadMethod: values?.loadMethod?.value,
        orderSourceCodeList: values?.orderSourceCodeList?.map(
          (i: any) => i.value,
        ),
        orderStatusList: values?.orderStatusList?.map((i: any) => i.value),
        stationBaseIdList: values?.stationInfo?.map((i: any) => i && i[2]),
        deliveryMode: values?.deliveryMode?.value,
        deviceName: values?.deviceName,
        startTime: formatDateToSecond(values.scheduleTime).startTime,
        endTime: formatDateToSecond(values.scheduleTime).endTime,
        gridNo: values?.gridNo,
        originalOrderId: values?.originalOrderId,
        collectionUserContact: values?.collectionUserContact,
        scheduleName: values?.scheduleName,
        stopId: values?.stopId?.value,
      },
      pageSize: 10,
      current: 1,
    };
    setSearchCondition(data);
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: data,
      }),
    );
    getTableList(data);
    getOrderTotal(data);
  };

  const onResetClick = () => {
    formRef.current.setFieldsValue(initSearchCondition.searchForm);
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: initSearchCondition,
      }),
    );
    const stopIdField =
      searchFormConfig?.fields?.find(
        (field: FieldItem) => field.fieldName === 'stopId',
      ) || {};
    stopIdField!.options = [];
    setSearchFormConfig({
      ...searchFormConfig,
    });
    setSearchCondition({ ...initSearchCondition });
    getTableList(initSearchCondition);
    getOrderTotal(initSearchCondition);
  };

  const resetTableData = (list: any, updateSecret: Function) => {
    if (!list) {
      return [];
    }
    const newList = list.map((item: any) => {
      if (item.secret === undefined) {
        item.secret = true;
      }
      return {
        ...item,
        collectionUserContact: { item, updateSecret },
      };
    });
    return newList;
  };

  const updatePhoneSecret = (id: any, secret: any) => {
    setTableList({
      ...tableList,
      list: tableList.list.map((item: any) => {
        if (item.id === id) {
          return {
            ...item,
            secret: secret,
          };
        } else {
          return {
            ...item,
            secret: true,
          };
        }
      }),
    });
  };

  return (
    <Watermark
      content={userName || ''}
      font={{ color: 'rgba(0,0,0,.07)' }}
      gap={[50, 90]}
    >
      <div className="order-list-content">
        <div className="search-form">
          <CommonForm
            layout="inline"
            formConfig={searchFormConfig}
            onSearchClick={onSearchClick}
            onResetClick={onResetClick}
            defaultValue={initSearchCondition.searchForm}
            formType="search"
            getFormInstance={(form: any) => {
              formRef.current = form;
            }}
            onValueChange={(values: any, fieldName: string) => {
              if (fieldName === 'stationInfo') {
                getStopPoint(values?.stationInfo && values?.stationInfo[0]);
                formRef.current?.setFieldValue('stopId', null);
              }
            }}
          />
        </div>
        <div className="table-list">
          <Table
            rowKey={(record: any) => record.id}
            columns={formateColumns(columns)}
            bordered
            loading={loading}
            dataSource={resetTableData(
              tableList?.list,
              (id: any, secret: any) => updatePhoneSecret(id, secret),
            )}
            scroll={{
              y: window.innerHeight - 200,
            }}
            pagination={{
              position: ['bottomCenter'],
              pageSizeOptions: pageSizeOptions,
              showQuickJumper: true,
              showSizeChanger: true,
              current: searchCondition.current,
              pageSize: searchCondition.pageSize,
              total: tableList?.totalNumber,
              showTotal: (total: any) =>
                `订单总量:${orderTotal},   共 ${tableList?.totalPage}页,${total} 条记录`,
            }}
            onChange={(
              pagination: any,
              filters: any,
              sorter: any,
              extra: any,
            ) => {
              if (extra.action === 'paginate') {
                const { current, pageSize } = pagination;
                const newSearchValue = {
                  ...searchCondition,
                  current: current!,
                  pageSize: pageSize!,
                };
                setSearchCondition(newSearchValue);
                getTableList(newSearchValue);
              }
            }}
          />
        </div>

        {orderDetailInfo.deliveryVisible && (
          <DeliveryOrderDetail
            id={orderDetailInfo.id}
            visible={orderDetailInfo.deliveryVisible}
            onCancel={() =>
              setOrderDetailInfo({
                deliveryVisible: false,
                collectVisible: false,
                id: null,
              })
            }
          />
        )}
        {orderDetailInfo.collectVisible && (
          <CollectOrderDetail
            id={orderDetailInfo.id}
            visible={orderDetailInfo.collectVisible}
            onCancel={() =>
              setOrderDetailInfo({
                deliveryVisible: false,
                collectVisible: false,
                id: null,
              })
            }
          />
        )}
        {subOrderData.showModal && (
          <SubOrderModalContent
            subOrderData={subOrderData}
            erp={userName}
            handleSearch={(value: any) => {
              setSubOrderData({
                ...subOrderData,
                filteredData: subOrderData.totalData.filter((item: any) => {
                  return item.originalId === value;
                }),
              });
            }}
            handleClear={() => {
              setSubOrderData({
                ...subOrderData,
                filteredData: subOrderData.totalData,
              });
            }}
            handleClose={() => {
              setSubOrderData({
                showModal: false,
                totalData: [],
                filteredData: [],
                originalOrderId: null,
              });
            }}
          />
        )}
      </div>
    </Watermark>
  );
};

export default React.memo(OrderManager);
