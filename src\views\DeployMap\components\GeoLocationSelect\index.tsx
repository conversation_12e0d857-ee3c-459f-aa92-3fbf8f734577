import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Select, Tabs, ConfigProvider, Empty, message, Tooltip } from 'antd';
import { debounce } from 'lodash';
import { buildURL } from '@/fetch/core/util';
import './index.scss';
import $ from 'jquery';

// 高亮文本的辅助函数
const highlightText = (text: string, keyword: string) => {
  if (!keyword || !text) return text;

  try {
    const parts = text.split(new RegExp(`(${keyword})`, 'gi'));
    return (
      <>
        {parts.map((part, index) =>
          part.toLowerCase() === keyword.toLowerCase() ? (
            <span key={index} style={{ color: '#1890ff', fontWeight: 'bold' }}>
              {part}
            </span>
          ) : (
            part
          ),
        )}
      </>
    );
  } catch (e) {
    // 如果正则表达式出错，直接返回原文本
    return text;
  }
};
export interface DockPoint {
  id: string;
  name: string;
  address?: string;
  [key: string]: any;
}

export interface LocationPoint {
  id: string;
  title: string;
  address: string;
  location?: {
    lat: number;
    lng: number;
  };
  [key: string]: any;
}

export interface GeoLocationSelectProps {
  /**
   * 停靠点数据
   */
  dockPoints: DockPoint[];

  /**
   * 默认选中的Tab，默认为"dockPoint"
   */
  defaultActiveTab?: 'dockPoint' | 'location';

  /**
   * 腾讯地图配置（可选，如果不提供则使用默认配置）
   */
  tencentMapConfig?: {
    key?: string; // 腾讯地图API密钥，默认使用fetchPoiList中的密钥
    debounceTime?: number; // 搜索防抖时间，默认300ms
  };

  /**
   * 选中项变化回调
   */
  onChange?: (value: any, option: any) => void;

  /**
   * 搜索框值变化回调
   */
  onSearch?: (value: string) => void;

  /**
   * 自定义渲染选项
   */
  optionRender?: (
    item: any,
    tabType: 'dockPoint' | 'location',
  ) => React.ReactNode;

  /**
   * 占位符文本
   */
  placeholder?: string;

  /**
   * 是否禁用
   */
  disabled?: boolean;

  /**
   * 样式类名
   */
  className?: string;

  /**
   * 样式对象
   */
  style?: React.CSSProperties;

  /**
   * 是否允许清除
   */
  allowClear?: boolean;

  /**
   * 当前选中的值
   */
  value?: any;

  /**
   * 默认选中的值
   */
  defaultValue?: any;
}

/**
 * 地理位置下拉搜索框组件
 */
const GeoLocationSelect: React.FC<GeoLocationSelectProps> = (props) => {
  const {
    dockPoints = [],
    defaultActiveTab = 'dockPoint',
    tencentMapConfig = {},
    onChange,
    onSearch,
    optionRender,
    placeholder = '请输入地点名称搜索',
    disabled = false,
    className = '',
    style = {},
    allowClear = true,
    value,
    defaultValue,
  } = props;

  // 状态
  const [activeTab, setActiveTab] = useState<'dockPoint' | 'location'>(
    defaultActiveTab,
  );
  const [searchText, setSearchText] = useState<string>('');
  const [locationOptions, setLocationOptions] = useState<LocationPoint[]>([]);
  const [dropdownVisible, setDropdownVisible] = useState<boolean>(false);
  const [selectedValue, setSelectedValue] = useState<any>(defaultValue);
  const [selectedLabel, setSelectedLabel] = useState<string>(''); // 保存选中项的文本内容
  const [errorCount, setErrorCount] = useState<number>(0); // 记录错误次数
  const [lastSearchTime, setLastSearchTime] = useState<number>(0); // 记录上次搜索时间

  // 通用防抖搜索（用于回调和停靠点过滤）
  const debouncedSearch = useRef(
    debounce((value: string) => {
      onSearch && onSearch(value);
    }, tencentMapConfig.debounceTime || 300),
  ).current;

  // 位置搜索专用防抖（更长的延迟，避免频繁请求API）
  const debouncedLocationSearch = useRef(
    debounce(
      (value: string) => {
        fetchLocationData(value);
      },
      tencentMapConfig.debounceTime ? tencentMapConfig.debounceTime * 2 : 600,
    ),
  ).current;

  // 过滤后的停靠点数据
  const filteredDockPoints = useMemo(() => {
    if (!searchText) return dockPoints;

    return dockPoints.filter(
      (item) =>
        item.name.includes(searchText) ||
        (item.address && item.address.includes(searchText)),
    );
  }, [dockPoints, searchText]);

  // 获取位置数据
  const fetchLocationData = async (keyword: string) => {
    // 如果没有关键词，清空选项但不显示加载状态
    if (!keyword) {
      setLocationOptions([]);
      return;
    }

    // 检查距离上次请求的时间间隔，至少间隔500ms
    const now = Date.now();
    if (now - lastSearchTime < 500) {
      return; // 如果请求过于频繁，直接返回
    }
    setLastSearchTime(now);

    // 如果错误次数过多，增加等待时间
    if (errorCount > 3) {
      await new Promise((resolve) => setTimeout(resolve, 1000)); // 额外等待1秒
      if (errorCount > 5) {
        message.warning('搜索请求过于频繁，请稍后再试');
        return;
      }
    }

    try {
      const results = await fetchPoiList(keyword);
      // 转换数据格式
      const formattedResults = results.map((item: any) => ({
        id: item.id,
        title: item.title,
        address: item.address || '',
        location: item.location || null,
        ...item,
      }));
      setLocationOptions(formattedResults);
      setErrorCount(0); // 成功后重置错误计数
    } catch (error) {
      console.error('搜索位置出错:', error);
      setLocationOptions([]);
      setErrorCount((prev) => prev + 1); // 增加错误计数

      // 显示友好的错误提示
      const err = error as Error;
      if (err.message && err.message.includes('此key每秒请求量已达到上限')) {
        message.error('搜索请求过于频繁，请稍后再试');
      }
    }
  };

  // 腾讯地图POI搜索
  const fetchPoiList = (searchText: string): Promise<any[]> => {
    return new Promise((resolve, reject) => {
      $.ajax({
        type: 'get',
        url: buildURL({
          absoluteURL: 'https://apis.map.qq.com/ws/place/v1/suggestion',
          urlParams: {
            key: 'KRBBZ-VJEWS-Q3IOZ-67LGG-RVU2E-UEF6X',
            keyword: encodeURI(searchText),
            output: 'jsonp',
          },
        }),
        dataType: 'jsonp',
        success: (res: any) => {
          if (res.status == 0) {
            resolve(res?.data ?? []);
          } else {
            reject(new Error(res.message || '搜索失败'));
          }
        },
        error: (err: any) => {
          reject(err);
        },
      });
    });
  };

  // 处理搜索输入
  const handleSearch = (value: string) => {
    setSearchText(value);

    // 无论是哪个Tab，都使用防抖
    if (activeTab === 'location') {
      // 位置Tab使用专用的防抖函数，延迟更长
      debouncedLocationSearch(value);
    } else {
      // 停靠点Tab使用通用防抖
      debouncedSearch(value);
    }
  };

  // 处理选择变化
  const handleChange = (val: any, option: any) => {
    // 由于使用了labelInValue模式，value是一个对象，包含value和label属性
    setSelectedValue(val);

    // 保存选中项的文本内容
    if (val && val.label) {
      const label =
        typeof val.label === 'string'
          ? val.label
          : option?.item?.name || option?.item?.title || '';
      setSelectedLabel(label);
      setSearchText(label); // 同时更新搜索文本，以便下次打开时使用
    } else {
      setSelectedLabel('');
      setSearchText('');
    }

    onChange && onChange(val, option);
  };

  // 监听外部value变化
  useEffect(() => {
    if (value !== undefined) {
      setSelectedValue(value);

      // 更新搜索文本
      if (value && value.label) {
        const label = typeof value.label === 'string' ? value.label : '';
        setSelectedLabel(label);
        setSearchText(label);
      }
    }
  }, [value]);

  // 初始化时，如果有默认值，设置搜索文本
  useEffect(() => {
    if (defaultValue && defaultValue.label) {
      const label =
        typeof defaultValue.label === 'string' ? defaultValue.label : '';
      setSelectedLabel(label);
      setSearchText(label);
    }
  }, []);

  // 处理Tab切换
  const handleTabChange = (key: string) => {
    // 先更新activeTab状态
    setActiveTab(key as 'dockPoint' | 'location');

    // 切换到位置标签页时，使用防抖触发搜索
    if (key === 'location' && searchText) {
      // 使用防抖搜索，避免频繁请求
      debouncedLocationSearch(searchText);
    }
  };

  // 自定义下拉菜单内容
  const dropdownRender = (menu: React.ReactElement) => {
    return (
      <div
        className="geo-location-dropdown"
        // 阻止整个下拉面板的点击事件冒泡
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          className="geo-location-tabs"
          // 阻止Tab点击事件冒泡，防止下拉框关闭
          onTabClick={(_, e) => {
            e.stopPropagation();
          }}
          items={[
            {
              key: 'dockPoint',
              label: '停靠点',
              children:
                filteredDockPoints.length > 0 ? (
                  menu
                ) : (
                  <Empty description="暂无数据" />
                ),
            },
            {
              key: 'location',
              label: '位置',
              children:
                locationOptions.length > 0 ? (
                  menu
                ) : (
                  <Empty
                    description={
                      searchText ? '未找到相关位置' : '请输入关键词搜索'
                    }
                  />
                ),
            },
          ]}
        />
      </div>
    );
  };

  // 根据当前Tab获取选项数据
  const getOptions = () => {
    if (activeTab === 'dockPoint') {
      return filteredDockPoints.map((item) => ({
        label: optionRender ? (
          optionRender(item, 'dockPoint')
        ) : (
          <div className="option-item">
            <Tooltip title={item.name} placement="topLeft">
              <div className="option-name">
                {highlightText(item.name, searchText)}
              </div>
            </Tooltip>
            {item.address && (
              <Tooltip title={item.address} placement="topLeft">
                <div className="option-address">
                  {highlightText(item.address, searchText)}
                </div>
              </Tooltip>
            )}
          </div>
        ),
        // 用于选中后显示的简洁标签
        customLabel: item.name,
        value: item.id,
        item,
      }));
    } else {
      return locationOptions.map((item) => ({
        label: optionRender ? (
          optionRender(item, 'location')
        ) : (
          <div className="option-item">
            <Tooltip title={item.title} placement="topLeft">
              <div className="option-name">
                {highlightText(item.title, searchText)}
              </div>
            </Tooltip>
            {item.address && (
              <Tooltip title={item.address} placement="topLeft">
                <div className="option-address">
                  {highlightText(item.address, searchText)}
                </div>
              </Tooltip>
            )}
          </div>
        ),
        // 用于选中后显示的简洁标签
        customLabel: item.title,
        value: item.id,
        item,
      }));
    }
  };

  return (
    <ConfigProvider prefixCls="x-coreui">
      <div className={`geo-location-select ${className}`} style={style}>
        <Select
          showSearch
          placeholder={placeholder}
          optionFilterProp="children"
          onChange={handleChange}
          onSearch={handleSearch}
          onDropdownVisibleChange={(visible) => {
            setDropdownVisible(visible);
            // 当下拉框打开时
            if (visible) {
              // 如果当前是位置Tab且有搜索文本，使用防抖搜索
              if (activeTab === 'location' && searchText) {
                debouncedLocationSearch(searchText);
              }
              // 如果是停靠点Tab，也应用搜索文本进行过滤
              // 这里不需要额外处理，因为filteredDockPoints已经基于searchText进行了过滤
            }
          }}
          dropdownRender={dropdownRender}
          options={getOptions()}
          disabled={disabled}
          allowClear={allowClear}
          className="geo-select"
          filterOption={false}
          // 固定下拉框宽度
          popupMatchSelectWidth={false}
          dropdownStyle={{ width: '300px' }}
          getPopupContainer={(triggerNode) => triggerNode.parentNode}
          open={dropdownVisible}
          // 自定义选中项的显示内容
          labelInValue
          fieldNames={{
            label: 'label',
            value: 'value',
          }}
          optionLabelProp="customLabel"
          value={selectedValue}
        />
      </div>
    </ConfigProvider>
  );
};

export default GeoLocationSelect;
