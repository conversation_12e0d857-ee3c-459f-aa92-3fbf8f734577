import React, { useState, useEffect } from 'react';
import { detailTableColumns } from './constant';
import { useTableData } from '@/components/CommonTable/useTableData';
import { TaskManageApi } from '@/fetch/business';
import { formatLocation } from '@/utils/utils';
import { useNavigate } from 'react-router-dom';
import { HttpStatusCode } from '@/fetch/core/constant';
import { pageSizeOptions } from '@/utils/constant';
import { Table, Breadcrumb, Col, Row } from 'antd';
import './taskDetail.scss';
const fetchApi = new TaskManageApi();
const TaskDetail = () => {
  const navigator = useNavigate();
  const { taskId } = formatLocation(window.location.search);
  const [searchCondition, setSearchCondition] = useState<{
    pageNum: number;
    pageSize: number;
    taskId: string;
  }>({
    taskId: taskId,
    pageNum: 1,
    pageSize: 10,
  });
  const [taskInfo, setTaskInfo] = useState<any>({});
  const { tableData } = useTableData(searchCondition, fetchApi.fetchTaskLog);
  const BreadcrumbItems = [
    {
      title: (
        <a
          onClick={() => {
            navigator('/app/taskManage');
          }}
        >
          任务管理
        </a>
      ),
    },
    {
      title: '任务日志',
    },
  ];
  const taskDetailTitle = [
    { label: '任务ID', value: 'taskId' },
    { label: '任务类型', value: 'taskTypeName' },
    { label: '执行机器人', value: 'robot' },
    { label: '任务状态', value: 'taskStatusName' },
    { label: '目的地', value: 'destination' },
    { label: '关联WCS运输单ID', value: 'orderNo' },
    { label: '任务创建时间', value: 'createTime' },
  ];

  useEffect(() => {
    fetchTaskDetail();
  }, [taskId]);

  const fetchTaskDetail = async () => {
    const res = await fetchApi.getTaskDetail(taskId);
    if (res.code === HttpStatusCode.Success) {
      setTaskInfo(res.data);
    }
  };

  const formateColumns = () => {
    return detailTableColumns.map((col) => {
      return {
        ...col,
        render: (text: any) => `${text || '-'}`,
      };
    });
  };

  return (
    <div className="task-detail">
      <Breadcrumb items={BreadcrumbItems} />
      <div className="task-info">
        <Row gutter={[0, 16]}>
          {taskDetailTitle.map((item) => {
            if (item.value === 'robot') {
              return (
                <Col span={6}>
                  <div key={item.value}>{`${item.label}：${
                    taskInfo.deviceName ?? ''
                  }-${taskInfo.deviceSerialNo ?? ''}`}</div>
                </Col>
              );
            }
            return (
              <Col span={6}>
                <div key={item.value}>{`${item.label}：${
                  taskInfo[item.value] ?? '-'
                }`}</div>
              </Col>
            );
          })}
        </Row>
      </div>
      <Table
        columns={formateColumns()}
        dataSource={tableData?.list}
        rowKey={(record: any) => record.startTime}
        pagination={{
          position: ['bottomRight'],
          total: tableData?.total,
          current: searchCondition?.pageNum,
          pageSize: searchCondition?.pageSize,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: pageSizeOptions,
          showTotal: (total) => `共${total}条记录`,
        }}
        scroll={{
          y: 500,
        }}
        onChange={(
          paginationData: any,
          filters: any,
          sorter: any,
          extra: any,
        ) => {
          if (extra.action === 'paginate') {
            const { current, pageSize } = paginationData;
            setSearchCondition({
              ...searchCondition,
              pageNum: current,
              pageSize: pageSize,
            });
          }
        }}
      />
    </div>
  );
};

export default React.memo(TaskDetail);
