/* eslint-disable no-unused-vars */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';
const activeTabKeySlice = createSlice({
  name: 'activeTabKey',
  initialState: {
    activeTabKey: null,
  },
  reducers: {
    saveActiveTabKey(state, event) {
      state.activeTabKey = event.payload;
    },
    removeActiveTabKey(state, event) {
      state.activeTabKey = null;
    },
  },
});

export const { saveActiveTabKey, removeActiveTabKey } =
  activeTabKeySlice.actions;
export const activeTabKeySelector = (state: RootState) => state.activeTabKey;
export default activeTabKeySlice.reducer;
