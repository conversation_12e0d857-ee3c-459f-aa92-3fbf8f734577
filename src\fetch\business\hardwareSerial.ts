import { YESNO } from '@/utils/enum';
import { request } from '../core';

export class HardwareSerialApi {
  // 分页查询数据列表
  fetchTableList({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/hardware_inner_params/hardware_serial_number_page_list',
      urlParams: {
        pageNum,
        pageSize,
      },
      body: searchForm,
    };
    return request(options);
  }
  // 获取详情
  fetchDetail(id: number | string) {
    const options: RequestOptions = {
      method: 'GET',
      path: '/k2/management/hardware_inner_params/get_hardware_serial_number_detail',
      urlParams: { id },
    };
    return request(options);
  }
}
