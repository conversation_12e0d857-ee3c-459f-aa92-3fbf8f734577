/* eslint-disable no-unused-vars */
import { Col, Form, Input, message, Row, Select, Image } from 'antd';
import type { FormInstance } from 'antd/es/form';
import React, { useEffect, useState } from 'react';
import EditModuleTitle from '@/components/EditModuleTitle';
import { HttpStatusCode } from '@/fetch/core/constant';
import { StationFetchApi } from '@/fetch/business';
import { EnvironmentOutlined } from '@ant-design/icons';
import ChooseDetailAddress from '../ChooseDetailAddress';
import { FileUpload } from '@jd/x-coreui';
import { request } from '@/fetch/core';

const layout = {
  labelCol: {
    span: 4,
  },
};

const makeStopPointTypeOptions = (configData: any) => {
  if (configData) {
    return configData.map((item: any) => {
      return { label: item.name, value: item.code ?? '' };
    });
  }
  return [];
};

const makeTransportPointOptions = (transportData: any) => {
  if (transportData) {
    return transportData.map((item: any) => {
      return { label: item.transportName, value: item.transportId ?? '' };
    });
  }
  return [];
};

const BasicInfoForm = ({
  form,
  editInfo,
  configData,
  transportData,
  onStationTypeChanged,
  type,
  cityStation,
  stationBaseId,
}: {
  form: FormInstance;
  editInfo?: any;
  configData: any;
  transportData: any;
  onStationTypeChanged: Function;
  type: string;
  cityStation: Object;
  stationBaseId: any;
}) => {
  const fetchApi = new StationFetchApi();
  const stopPointTypeOptions = makeStopPointTypeOptions(configData);
  const transportPointOptions = makeTransportPointOptions(transportData);
  const [thirdIdHidden, setThirdIdHidden] = useState<boolean>(true);
  const [thirdId, setThirdId] = useState<any>(null);
  const [validStopName, setValidStopName] = useState<{
    validateStatus: 'success' | 'warning' | 'error' | 'validating' | '';
    message: string | null;
  }>({
    validateStatus: '',
    message: null,
  });
  const [chooseDetailAddressShow, setChooseDetailAddressShow] =
    useState<boolean>(false);

  useEffect(() => {
    if (editInfo) {
      if (type === 'add') {
        //复制并新建
        form.setFieldsValue({
          number: editInfo.number,
          name: editInfo.name,
          stopType: editInfo.type,
          addressName: editInfo.addressName,
        });
        setThirdIdHidden(true);
        setThirdId(null);
      } else {
        //编辑
        form.setFieldsValue({
          id: editInfo.id,
          number: editInfo.number,
          name: editInfo.name,
          stopType: editInfo.type,
          addressName: editInfo.addressName,
          thirdId: editInfo.thirdId,
          thirdName: editInfo.thirdName,
        });
        setThirdIdHidden(editInfo.thirdId ? false : true);
        setThirdId(editInfo.thirdId);
      }
    }
  }, [editInfo]);

  return (
    <div>
      <Form form={form} {...layout}>
        <Row>
          <Form.Item>
            <EditModuleTitle title={'基本信息'} />
          </Form.Item>
        </Row>
        <Row>
          <Col span={21}>
            <Form.Item label="点位ID" name="id">
              <Input placeholder="系统生成" disabled />
            </Form.Item>
            <Form.Item label="点位编号" name="number">
              <Input placeholder="系统生成" disabled />
            </Form.Item>
            <Form.Item
              label="点位名称"
              name="name"
              rules={[{ required: true, message: '请输入点位名' }]}
              validateStatus={validStopName.validateStatus}
              hasFeedback
              help={validStopName.message}
            >
              <Input
                placeholder="请输入点位名称"
                maxLength={20}
                onBlur={async (e) => {
                  setValidStopName({
                    validateStatus: 'validating',
                    message: null,
                  });
                  const response: any = await fetchApi.checkDuplicateStopName({
                    id: editInfo?.id,
                    name: e.target.value,
                  });
                  if (response.code != HttpStatusCode.Success) {
                    setValidStopName({
                      validateStatus: 'error',
                      message: response.message,
                    });
                  } else {
                    setValidStopName({
                      validateStatus: 'success',
                      message: null,
                    });
                  }
                }}
              />
            </Form.Item>
            <Form.Item
              label="点位类型"
              name="type"
              rules={[{ required: true, message: '请选择点位类型' }]}
            >
              <Select
                placeholder="请选择点位类型"
                options={stopPointTypeOptions}
                onChange={(value: any) => {
                  onStationTypeChanged && onStationTypeChanged(value);
                  form.setFieldsValue({
                    type: value,
                  });
                  if (value === 'HOME' && form.getFieldValue('thirdId')) {
                    message.error(
                      '保存失败，已绑定接驳点时，点位类型不能为HOME点',
                    );
                    form.setFieldsValue({
                      type: null,
                    });
                    return;
                  }
                }}
                allowClear
              />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={21}>
            <Form.Item label="点位详细地址" name="addressName">
              <Input
                allowClear
                maxLength={50}
                placeholder="请“图上选点”到详细地址，允许修改，格式要求：国家_省_城市_乡镇_街道_详细地址_地址名称"
                addonAfter={
                  <EnvironmentOutlined
                    style={{ color: '#3c6ef0' }}
                    onClick={() => {
                      setChooseDetailAddressShow(true);
                    }}
                  />
                }
              />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={21}>
            <Form.Item label="终端系统接驳点" name="thirdName">
              <Select
                placeholder="可选择在快快终端系统的接驳点，自动匹配相关信息"
                options={transportPointOptions}
                onChange={(value: any) => {
                  if (value) {
                    setThirdIdHidden(false);
                    form.setFieldsValue({
                      thirdId: value,
                      thirdName: transportPointOptions.find(
                        (option) => option.value === value,
                      )?.label,
                      type: 'PICKUP',
                      name: transportPointOptions.find(
                        (option) => option.value === value,
                      )?.label,
                    });
                  } else {
                    setThirdIdHidden(true);
                    form.setFieldsValue({
                      thirdId: value,
                    });
                  }
                  setThirdId(value);
                }}
                allowClear
              />
            </Form.Item>
            <Form.Item label="上传停车指引" name="parkPictureList">
              <FileUpload
                fileListType="picture"
                accept="image/*"
                getPreSignatureUrl={`${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}/infrastructure/oss/getPreUrl`}
                LOPDN={process.env.JDX_APP_REQUEST_HEADER!}
                needMd5Validete={false}
                maxCount={3}
                uploadedFileList={
                  editInfo?.parkPictureList?.map((v) => ({
                    fileKey: v.s3Key,
                    url: v.url,
                    uid: v.s3Key,
                  })) ?? []
                }
                bucketName="rover-operation"
                onEnd={(fileKey) => {
                  const fileKeyList =
                    form.getFieldsValue().parkPictureList ?? [];
                  fileKeyList.push(fileKey);
                  form.setFieldsValue({
                    parkPictureList: fileKeyList,
                  });
                }}
                onDelete={(fileKey) => {
                  const fileKeyList =
                    form.getFieldsValue().parkPictureList ?? [];
                  form.setFieldsValue({
                    parkPictureList: fileKeyList.filter((v) => v !== fileKey),
                  });
                }}
              />
            </Form.Item>
            <Form.Item
              label="终端系统接驳点编号"
              hidden={thirdIdHidden}
              name="thirdId"
            >
              {thirdId}
            </Form.Item>
          </Col>
        </Row>
      </Form>

      {chooseDetailAddressShow && (
        <ChooseDetailAddress
          visiable={chooseDetailAddressShow}
          department={cityStation}
          onCancle={() => {
            setChooseDetailAddressShow(false);
          }}
          onSubmit={(address: any) => {
            form.setFieldsValue({
              addressName: address,
            });
            setChooseDetailAddressShow(false);
          }}
        />
      )}
    </div>
  );
};

export default React.memo(BasicInfoForm);
