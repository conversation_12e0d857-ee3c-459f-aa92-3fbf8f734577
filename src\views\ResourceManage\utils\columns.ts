import {
  dropDownListKey,
  dropDown<PERSON>ey,
  EnableDropDownList,
} from '@/utils/constant';
import { FormConfig } from '@/components';
export const tableColumns: any[] = [
  {
    title: '资源编号',
    dataIndex: 'number',
    align: 'center',
    width: 250,
    ellipsis: true,
  },
  {
    title: '资源名称',
    dataIndex: 'name',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '资源描述',
    dataIndex: 'description',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '应用系统',
    dataIndex: 'appName',
    align: 'center',
    width: 110,
    ellipsis: true,
  },
  {
    title: '资源类型',
    dataIndex: 'typeName',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '资源标识',
    dataIndex: 'resourceCode',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '路径',
    dataIndex: 'path',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '方法',
    dataIndex: 'method',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '排序值',
    dataIndex: 'orderBy',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 200,
    align: 'center',
    fixed: 'right',
  },
];

export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'name',
      label: '资源名称',
      placeholder: '请输入资源名称',
      type: 'input',
    },
    {
      fieldName: 'app',
      label: '应用系统',
      placeholder: '请选择应用系统',
      type: 'select',
      showSearch: true,
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.RESOURCE_APP,
      dropDownListKey: dropDownListKey.RESOURCE_APP,
    },
    {
      fieldName: 'resourceCode',
      label: '资源标识',
      placeholder: '请输入资源标识',
      type: 'input',
    },
  ],
};
