import { FormConfig } from '@jd/x-coreui';

export const Columns = [
  {
    title: '类型编号',
    dataIndex: 'shelfTypeNo',
    align: 'center',
    width: 100,
  },
  {
    title: '类型名称',
    dataIndex: 'shelfTypeName',
    align: 'center',
    width: 150,
  },
  {
    title: '长（cm）',
    dataIndex: 'length',
    align: 'center',
    width: 80,
  },
  {
    title: '宽（cm）',
    dataIndex: 'width',
    align: 'center',
    width: 80,
  },
  {
    title: '高（cm）',
    dataIndex: 'height',
    align: 'center',
    width: 80,
  },
  {
    title: '双面格口',
    dataIndex: 'doubleSide',
    align: 'center',
    width: 80,
    render: (value: any) => (value == 1 ? '是' : '否'),
  },
  {
    title: 'A面格口数量',
    dataIndex: 'aSideGridCount',
    align: 'center',
    width: 80,
  },
  {
    title: 'B面格口数量',
    dataIndex: 'bSideGridCount',
    align: 'center',
    width: 80,
  },
  {
    title: '已绑定的车型',
    dataIndex: 'bindDeviceTypeCount',
    align: 'center',
    width: 80,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 80,
  },
];

export const SearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'shelfTypeName',
      label: '类型名称',
      type: 'input',
      placeholder: '请输入名称',
      labelCol: { span: 8 },
    },
    {
      fieldName: 'doubleSide',
      label: '双面格口',
      type: 'select',
      placeholder: '请选择',
      labelCol: { span: 8 },
      labelInValue: false,
      options: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
    },
  ],
};

export const getShelfTypeConfig = (type: 'add' | 'edit'): FormConfig => {
  return {
    fields: [
      {
        fieldName: 'shelfTypeName',
        label: '类型名称',
        type: 'input',
        placeholder: '请输入名称',
        maxLength: 30,
        disabled: type === 'edit',
        validatorRules: [{ required: true, message: '请输入名称' }],
      },
      {
        fieldName: 'length',
        label: '长（cm）',
        type: 'inputNumber',
        min: 0,
        placeholder: '请输入长度',
        max: 99999999,
        validatorRules: [{ required: true, message: '请输入长度' }],
      },
      {
        fieldName: 'width',
        label: '宽（cm）',
        type: 'inputNumber',
        min: 0,
        placeholder: '请输入宽度',
        max: 99999999,
        validatorRules: [{ required: true, message: '请输入宽度' }],
      },
      {
        fieldName: 'height',
        label: '高（cm）',
        type: 'inputNumber',
        min: 0,
        placeholder: '请输入高度',
        max: 99999999,
        validatorRules: [{ required: true, message: '请输入高度' }],
      },
      {
        fieldName: 'doubleSide',
        label: '是否双面格口',
        type: 'radioGroup',
        disabled: type === 'edit',
        options: [
          {
            label: '是',
            value: 1,
          },
          {
            label: '否',
            value: 0,
          },
        ],
      },
      {
        fieldName: 'aSideGridCount',
        label: 'A面格口数量',
        type: 'inputNumber',
        min: 0,
        max: 99,
        placeholder: '请输入格口数量',
        disabled: type === 'edit',
        validatorRules: [{ required: true, message: '请输入格口数量' }],
      },
      {
        fieldName: 'bSideGridCount',
        label: 'B面格口数量',
        type: 'inputNumber',
        hidden: true,
        min: 0,
        max: 99,
        placeholder: '请输入格口数量',
        disabled: type === 'edit',
        validatorRules: [{ required: true, message: '请输入格口数量' }],
      },
    ],
    linkRules: {
      doubleSide: [
        {
          linkFieldName: 'bSideGridCount',
          rule: 'visible',
          dependenceData: [1],
        },
      ],
    },
  };
};
