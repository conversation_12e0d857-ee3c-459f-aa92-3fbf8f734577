/* eslint-disable react/display-name */
/* eslint-disable no-unused-vars */

import { Form, FormInstance, message } from 'antd';
import React, { useEffect, useState } from 'react';
import ChooseLocation from '../ChooseLocation';
import ChooseLocationByVehicle from '../ChooseLocationByVehicle';
import SideAddbleTable from '../SideAddableTable';
import { HttpStatusCode } from '@/fetch/core/constant';
import { StationFetchApi } from '@/fetch/business';
const win = window as any;
const { rad } = win;

interface Parking {
  id: any;
  level: number;
  name: string | null;
  latitude: number | null;
  longitude: number | null;
  heading: number | null;
  newAdd?: boolean;
}
interface vehicleTypeData {
  backEdgeToCenter: string | number;
  frontEdgeToCenter: string | number;
  length: string | number;
  width: string | number;
}

const ParkingEdit = ({
  form,
  initValue,
  department,
}: {
  form: FormInstance;
  initValue: any;
  department: any;
}) => {
  const fetchApi = new StationFetchApi();
  const [parkingList, setParkingList] = useState<Parking[]>([]);
  const moveItemAtIndex = (
    direction: 'up' | 'down',
    list: any[],
    index: number,
  ) => {
    if (
      (index === 0 && direction === 'up') ||
      (index === list?.length - 1 && direction === 'down')
    ) {
      return list;
    }
    const item = list[index];
    if (direction === 'down') {
      const nextItem = list[index + 1];
      list[index + 1] = item;
      list[index] = nextItem;
    } else if (direction === 'up') {
      const preItem = list[index - 1];
      list[index - 1] = item;
      list[index] = preItem;
    }
    return list;
  };
  // 实车打点画车型示意图的数据
  const [vehicleTypeData, setVehicleTypeData] = useState<vehicleTypeData>();
  // 实车打点画车型示意图停靠四周距离均为50厘米的最外框的数据
  const [bigVehicleTypeData, setBigVehicleTypeData] =
    useState<vehicleTypeData>();
  useEffect(() => {
    getVehicleType();
  }, []);
  // 获取试算车型数据
  const getVehicleType = async () => {
    const res: any = await fetchApi.getSizeOfVehicle();
    if (res && res.code === HttpStatusCode.Success) {
      setVehicleTypeData(res.data);
      setBigVehicleTypeData({
        width: Number(res.data.width) + 1,
        length: Number(res.data.length) + 1,
        frontEdgeToCenter: Number(res.data.frontEdgeToCenter) + 0.5,
        backEdgeToCenter: Number(res.data.backEdgeToCenter) + 0.5,
      });
    }
  };
  const parkingColums: any = [
    {
      title: '停车点优先级',
      dataIndex: 'level',
      align: 'center',
      width: 90,
      ellipsis: true,
      render: (text: any, record: number, index: number) => index + 1,
    },
    {
      title: '停车点名称',
      dataIndex: 'name',
      align: 'center',
      editable: true,
      width: 200,
      onCell: (record: any) => ({
        record,
        disabled: false,
        editable: true,
        dataIndex: 'name',
        title: '停车点名称',
        existList: parkingList,
        handleSave: (row: any) => {
          const filteredList = parkingList.filter(
            (item) => item.name === row.name && item.id !== row.id,
          );
          setParkingList([
            ...parkingList.map((item) => {
              if (item.id === row.id) {
                return row;
              }
              return item;
            }),
          ]);
        },
      }),
    },
    {
      title: '经度',
      dataIndex: 'longitude',
      align: 'center',
      width: 140,
      editable: true,
      onCell: (record: any) => ({
        record,
        disabled: true,
        editable: true,
        dataIndex: 'longitude',
        title: '经度',
      }),
    },
    {
      title: '纬度',
      dataIndex: 'latitude',
      align: 'center',
      width: 140,
      editable: true,
      onCell: (record: any) => ({
        record,
        disabled: true,
        editable: true,
        dataIndex: 'latitude',
        title: '纬度',
      }),
    },
    {
      title: '朝向',
      dataIndex: 'heading',
      align: 'center',
      width: 140,
      editable: true,
      onCell: (record: any) => ({
        record,
        disabled: true,
        editable: true,
        dataIndex: 'heading',
        title: '朝向',
      }),
    },
    {
      title: '操作',
      dataIndex: '',
      align: 'center',
      width: 180,
      render: (text: any, _: any, index: number) => {
        const canMoveUp =
          index !== 0 &&
          text.name !== null &&
          text.name !== undefined &&
          text.name?.length > 0 &&
          text.latitude !== null;
        const canMoveDown =
          index !== parkingList?.length - 1 &&
          text.name !== null &&
          text.name !== undefined &&
          text.name?.length > 0 &&
          text.latitude !== null;
        const moveUpColor = canMoveUp ? '#1890ff' : '#999';
        const moveDownColor = canMoveDown ? '#1890ff' : '#999';
        return (
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-around',
            }}
          >
            <a
              onMouseDown={() => {
                if (!department?.stationObjc) {
                  message.error('请先选择站点');
                } else {
                  setVehicleChooseMapVisiable({ show: true, parking: text });
                }
              }}
            >
              实车打点
            </a>
            <a
              onMouseDown={() => {
                if (!department?.stationObjc) {
                  message.error('请先选择站点');
                } else {
                  setMapChooseVisiable({ show: true, parking: text });
                }
              }}
            >
              图上选点
            </a>
            <a
              style={{ color: moveDownColor }}
              onMouseDown={() => {
                if (canMoveDown) {
                  setParkingList([
                    ...moveItemAtIndex('down', parkingList, index),
                  ]);
                }
              }}
            >
              下移
            </a>
            <a
              style={{ color: moveUpColor }}
              onMouseDown={() => {
                if (canMoveUp) {
                  setParkingList([
                    ...moveItemAtIndex('up', parkingList, index),
                  ]);
                }
              }}
            >
              上移
            </a>
          </div>
        );
      },
    },
  ];

  // const [showParkingTable, setShowParkingTable] = useState(false)
  const [mapChooseVisiable, setMapChooseVisiable] = useState<{
    show: boolean;
    parking: Parking | null;
  }>({ show: false, parking: null });
  const [vehicleChooseMapVisiable, setVehicleChooseMapVisiable] = useState<{
    show: boolean;
    parking: Parking | null;
  }>({ show: false, parking: null });

  const makeParkingDatasource = () => {
    return parkingList.map((item) => {
      return {
        ...item,
        key: item.id,
      };
    });
  };
  useEffect(() => {
    if (initValue && initValue.length > 0) {
      // setShowParkingTable(initValue.length > 0)
      setParkingList([...initValue]);
    } else {
      setParkingList([
        {
          id: Date.now(),
          level: 2,
          name: null,
          latitude: null,
          longitude: null,
          heading: null,
          newAdd: true,
        },
      ]);
    }
  }, [initValue]);

  useEffect(() => {
    form.setFieldsValue({
      parkingSpotList: parkingList,
    });
  }, [JSON.stringify(parkingList)]);

  return (
    <>
      {/* <StatusItem
      form={form}
      name="addParking"
      label="添加多个停车点"
      items={[
        { code: 1, name: "是" }, { code: 0, name: "否" }
      ]}
      defaultValue={0}
      initValue={initValue?.length > 0 ? 1 : 0}
      onValueChanged={(value: any) => {
        setShowParkingTable(value === 1)
      }}
    /> */}
      <Form.Item
        label=" "
        name="parkingSpotList"
        colon={false}
        wrapperCol={{ span: 22 }}
        labelCol={{ span: 2 }}
      >
        <>
          <SideAddbleTable
            columns={parkingColums}
            dataSource={makeParkingDatasource()}
            deleteConfirmTips="确认删除此停车点吗？"
            onAddClick={() => {
              const filteredList = parkingList.filter(
                (item) =>
                  item.name === null ||
                  item.name === undefined ||
                  item.name?.length <= 0 ||
                  item.latitude === null,
              );
              if (filteredList.length > 0) {
                message.error('请先完善当前停车点信息');
                return;
              }
              setParkingList([
                ...parkingList,
                {
                  id: Date.now(),
                  level: 2,
                  name: null,
                  latitude: null,
                  longitude: null,
                  heading: null,
                  newAdd: true,
                },
              ]);
            }}
            onDeleteClick={(index: number) => {
              parkingList.splice(index, 1);
              setParkingList([...parkingList]);
            }}
          />
          <div style={{ color: '#999', marginTop: 10 }}>
            注意：停靠点优先级最高为1，其他停车点顺排，数字越大级别越低。
          </div>
        </>
      </Form.Item>
      {vehicleChooseMapVisiable.show ? (
        <ChooseLocationByVehicle
          station={department.stationObjc}
          visiable={vehicleChooseMapVisiable.show}
          vehicleTypeData={vehicleTypeData}
          bigVehicleTypeData={bigVehicleTypeData}
          parking={vehicleChooseMapVisiable.parking}
          onCancle={() => {
            setVehicleChooseMapVisiable({
              show: false,
              parking: null,
            });
          }}
          onSubmit={(location: any) => {
            const editedParking = vehicleChooseMapVisiable.parking!;
            editedParking.heading = location.head;
            editedParking.latitude = location.lat;
            editedParking.longitude = location.lon;
            setParkingList([
              ...parkingList.map((item) => {
                if (item.id === editedParking.id) {
                  return {
                    ...editedParking,
                    name: item.name,
                  };
                }
                return item;
              }),
            ]);
            setVehicleChooseMapVisiable({
              show: false,
              parking: null,
            });
          }}
        />
      ) : null}
      {mapChooseVisiable.show ? (
        <ChooseLocation
          initPosition={
            mapChooseVisiable.parking &&
            mapChooseVisiable.parking.longitude != null
              ? {
                  lon: mapChooseVisiable.parking.longitude,
                  lat: mapChooseVisiable.parking.latitude,
                  head: rad(mapChooseVisiable.parking.heading),
                }
              : null
          }
          department={department}
          visiable={mapChooseVisiable.show}
          onCancle={() => {
            setMapChooseVisiable({ show: false, parking: null });
          }}
          onSubmit={(location: any) => {
            const editedParking = mapChooseVisiable.parking!;
            editedParking.heading = location.head ? location.head : 0;
            editedParking.latitude = location.lat;
            editedParking.longitude = location.lon;
            setParkingList([
              ...parkingList.map((item) => {
                if (item.id === editedParking.id) {
                  return {
                    ...editedParking,
                    name: item.name,
                  };
                }
                return item;
              }),
            ]);
            setMapChooseVisiable({ show: false, parking: null });
          }}
        />
      ) : null}
    </>
  );
};

export default React.memo(ParkingEdit);
