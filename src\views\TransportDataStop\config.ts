import { TransportDataApi } from "@/fetch/business/transportDataStop";
import { HttpStatusCode } from "@/fetch/core/constant";
import { FormConfig } from "@jd/x-coreui";
import dayjs from "dayjs";

const fetchApi = new TransportDataApi();
export const SearchConfig: FormConfig = {
    fields: [
        {
            fieldName: 'cityIdList',
            label: '城市',
            placeholder: '请选择城市,可多选',
            type: 'select',
            multiple: true,
            showSearch: true,
            labelInValue: false,
        },
        {
            fieldName: 'stationIdList',
            label: '站点',
            placeholder: '请选择站点,可多选',
            type: 'select',
            multiple: true,
            showSearch: true,
            labelInValue: false,
        },
        {
            fieldName: 'stopIdList',
            label: '停靠点',
            placeholder: '请选择停靠点,可多选',
            type: 'select',
            multiple: true,
            showSearch: true,
            labelInValue: false,
        },
        {
            fieldName: 'dt',
            label: '日期范围',
            placeholder: '请选择日期范围',
            type: 'rangeTime',
            format: 'YYYY-MM-DD',
            showTime: false,
            disabledDate: (current) => {
                return current > dayjs().subtract(1, 'day');
            },
            xl: 12,
            xxl: 8,
            lg: 16,
            labelCol: {
                span: 4
            },
            wrapperCol: {
                span: 20
            },
        },
    ],
    linkRules: {
        cityIdList: [
            {
                linkFieldName: 'stationIdList',
                rule: 'clear'
            },
            {
                linkFieldName: 'stationIdList',
                rule: 'fetchData',
                fetchFunc: async (val) => {
                    const params = {
                        dimType: 'STATION',
                        filterIds: val,
                    };
                    const res = await fetchApi.getDimSelect(params);
                    if (res.code === HttpStatusCode.Success) {
                        return res.data.map((item) => {
                            return {
                                value: item.dimValue,
                                label: item.dimName,
                            };
                        });
                    } else {
                        return [];
                    }
                },
            },
            {
                linkFieldName: 'stopIdList',
                rule: 'clear'
            },
        ],
        stationIdList: [
            {
                linkFieldName: 'stopIdList',
                rule: 'clear'
            },
            {
                linkFieldName: 'stopIdList',
                rule: 'fetchData',
                fetchFunc: async (val) => {
                    const params = {
                        dimType: 'STOP',
                        filterIds: val,
                    };
                    const res = await fetchApi.getDimSelect(params);
                    if (res.code === HttpStatusCode.Success) {
                        return res.data.map((item) => {
                            return {
                                value: item.dimValue,
                                label: item.dimName,
                            };
                        });
                    } else {
                        return [];
                    }
                },
            },
        ],
    }
}

export const TableConfig: any[] = [
    {
        title: '站点',
        dataIndex: 'stationName',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '城市',
        dataIndex: 'cityName',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '停靠点',
        dataIndex: 'stopName',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '停靠点创建日期',
        dataIndex: 'creationDate',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '接驳趟次',
        dataIndex: 'transportTimes',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
]