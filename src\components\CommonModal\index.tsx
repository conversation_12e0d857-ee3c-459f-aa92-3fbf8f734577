import React, { ReactNode, useState } from 'react';
import { Button, Modal } from 'antd';
import './index.scss';
import {
  render as reactRender,
  unmount as reactUnmount,
} from 'rc-util/lib/React/render';
import { AnyFunc } from '@/global';
interface ModalProps {
  onClose?: AnyFunc;
  onOk?: AnyFunc;
  visible?: boolean;
  content?: ReactNode;
  title?: ReactNode;
  footer?: {
    showOk?: boolean;
    showCancel?: boolean;
    okText?: string;
    cancelText?: string;
    okFunc?: AnyFunc;
    cancelFunc?: AnyFunc;
  };
  width?: number;
  okButtonProps?: any;
}
const CommonModal = (props: ModalProps) => {
  const {
    visible,
    content,
    title,
    footer,
    onOk,
    onClose,
    okButtonProps,
    width,
  } = props;
  const [modalVisible, setModalVisible] = useState(visible);
  const handleClose = () => {
    const el = document.getElementById('commonModal');
    if (el) {
      el.parentElement && el.parentElement.removeChild(el);
    }
    setModalVisible(false);
    onClose && onClose();
  };

  return (
    <Modal
      title={title}
      destroyOnClose={true}
      open={modalVisible}
      maskClosable={false}
      onCancel={handleClose}
      onOk={() => {
        onOk && onOk(handleClose);
      }}
      okButtonProps={okButtonProps}
      className="common-modal"
      footer={
        <>
          {footer?.showCancel && (
            <Button
              onClick={() => {
                footer?.cancelFunc && footer?.cancelFunc(handleClose);
              }}
              style={{ marginRight: '10px' }}
            >
              {footer?.cancelText ?? '取消'}
            </Button>
          )}
          {footer?.showOk && (
            <Button
              type="primary"
              onClick={() => {
                footer?.okFunc && footer?.okFunc(handleClose);
              }}
            >
              {footer?.okText ?? '确定'}
            </Button>
          )}
        </>
      }
      width={width || 600}
    >
      {content}
    </Modal>
  );
};

const showModal = (options: ModalProps) => {
  let el = document.getElementById('commonModal');
  if (!el) {
    el = document.createElement('div');
    el.setAttribute('id', 'commonModal');
    document.body.appendChild(el);
  }
  reactRender(
    <CommonModal
      content={options.content}
      onClose={options.onClose}
      onOk={options.onOk}
      okButtonProps={options.okButtonProps || null}
      visible={true}
      title={options.title}
      footer={options.footer}
      width={options.width}
    />,
    el,
  );
};
export default showModal;
