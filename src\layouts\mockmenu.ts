const pageIdConnect = new Map([
  ['jdxvehicle-ui-test1.jdl.cn', '/'],
  ['jdxvehicle-ui-beta.jdl.cn', ''],
  ['jdxvehicle-ui.jdl.cn', '_'],
]);
export const MenuClstag = new Map([
  [
    'deployManage:deployPlan',
    `app${pageIdConnect.get(
      window.location.host,
    )}deployPlan|menu_click_deployPlan`,
  ],
]);

export const menuData = [
  {
    openType: 'now',
    orderBy: 1,
    path: 'businessConfig',
    resourceCode: 'businessConfig',
    type: 'menu',
    label: '业务基础数据配置',
  },
  {
    openType: 'now',
    orderBy: 1,
    path: '/app/stationManagement',
    resourceCode: 'businessConfig:StationManagement',
    type: 'menu',
    label: '站点管理',
  },
  {
    openType: 'now',
    orderBy: 2,
    path: '/app/deviceLife',
    resourceCode: 'businessConfig:DeviceLifeCycle',
    type: 'menu',
    label: '设备生命周期管理',
  },
  {
    openType: 'now',
    orderBy: 3,
    path: '/app/repairOrderManagement',
    resourceCode: 'businessConfig:RepairOrderManagement',
    type: 'menu',
    label: '维修单管理',
  },

  {
    openType: 'now',
    orderBy: 2,
    path: 'deployManage',
    resourceCode: 'deployManage',
    type: 'menu',
    label: '运营部署管理',
  },
  {
    openType: 'now',
    orderBy: 1,
    path: '/app/deployPlan',
    resourceCode: 'deployManage:deployPlan',
    type: 'menu',
    label: '站点部署计划',
  },
  {
    openType: 'now',
    orderBy: 2,
    path: '/app/deployMap',
    resourceCode: 'deployManage:deployMap',
    type: 'menu',
    label: '业务部署地图',
  },
  {
    orderBy: 3,
    path: 'operateBusiness',
    resourceCode: 'operateBusiness',
    type: 'menu',
    label: '无人车运营业务管理',
  },
  {
    openType: 'now',
    orderBy: 1,
    path: '/app/ordermanagement',
    resourceCode: 'operateBusiness:ordermanagement',
    type: 'menu',
    label: '订单调度管理',
  },
  {
    openType: 'now',
    orderBy: 2,
    path: '/app/blacklist',
    resourceCode: 'operateBusiness:blacklist',
    type: 'menu',
    label: '用户黑名单',
  },
  {
    openType: 'now',
    orderBy: 3,
    path: '/app/operationtime',
    resourceCode: 'operateBusiness:operationtime',
    type: 'menu',
    label: '运营工作时长设置',
  },
  {
    openType: 'now',
    orderBy: 4,
    path: '/app/insuranceManage',
    resourceCode: 'operateBusiness:InsuranceManage',
    type: 'menu',
    label: '车辆保险管理',
  },
  {
    openType: 'now',
    orderBy: 5,
    path: '/app/costAllocationManage',
    resourceCode: 'operateBusiness:CostAllocationManage',
    type: 'menu',
    label: '成本划拨管理',
  },
  {
    openType: 'now',
    orderBy: 6,
    path: '/app/maintainAbnormalDuration',
    resourceCode: 'operateBusiness:MaintainAbnormalDuration',
    type: 'menu',
    label: '车辆异常时长维护',
  },
  {
    openType: 'now',
    orderBy: 4,
    path: 'deviceConfig',
    resourceCode: 'deviceConfig',
    type: 'menu',
    label: '设备基础信息管理',
  },
  {
    openType: 'now',
    orderBy: 1,
    path: '/app/hardwareType',
    resourceCode: 'deviceConfig:HardwareType',
    type: 'menu',
    label: '硬件类型管理',
  },
  {
    openType: 'now',
    orderBy: 2,
    path: '/app/factoryManage',
    resourceCode: 'deviceConfig:FactoryManage',
    type: 'menu',
    label: '厂商管理',
  },
  {
    openType: 'now',
    orderBy: 3,
    path: '/app/hardwareModel',
    resourceCode: 'deviceConfig:HardwareModel',
    type: 'menu',
    label: '硬件型号管理',
  },
  {
    openType: 'now',
    orderBy: 4,
    path: '/app/sensorSolution',
    resourceCode: 'deviceConfig:SensorSolution',
    type: 'menu',
    label: '传感器方案管理',
  },
  {
    openType: 'now',
    orderBy: 6,
    path: '/app/gridManage',
    resourceCode: 'deviceConfig:GridManage',
    type: 'menu',
    label: '箱体格口模版管理',
  },
  {
    openType: 'now',
    orderBy: 7,
    path: '/app/vehicleType',
    resourceCode: 'deviceConfig:VehicleType',
    type: 'menu',
    label: '车型管理',
  },
  {
    openType: 'now',
    orderBy: 8,
    path: '/app/deviceInfo',
    resourceCode: 'deviceConfig:DeviceInfo',
    type: 'menu',
    label: '设备基础信息管理',
  },
  {
    openType: 'now',
    orderBy: 5,
    path: 'parallelDriving',
    resourceCode: 'parallelDriving',
    type: 'menu',
    label: '中控远驾配置管理',
  },
  {
    openType: 'now',
    orderBy: 1,
    path: '/app/cockpitteam',
    resourceCode: 'parallelDriving:cockpitteam',
    type: 'menu',
    label: '远驾团队管理',
  },
  {
    openType: 'now',
    orderBy: 2,
    path: '/app/cockpitmanagement',
    resourceCode: 'parallelDriving:cockpitmanagement',
    type: 'menu',
    label: '座席管理',
  },
  {
    openType: 'now',
    orderBy: 6,
    path: 'ticketConfig',
    resourceCode: 'ticketConfig',
    type: 'menu',
    label: '工单配置管理',
  },
  {
    openType: 'now',
    orderBy: 1,
    path: '/app/indicatorLayerConfig',
    resourceCode: 'ticketConfig:IndicatorLayerConfig',
    type: 'menu',
    label: '工单指标层配置',
  },
  {
    openType: 'now',
    orderBy: 2,
    path: '/app/indicatorConfig',
    resourceCode: 'ticketConfig:IndicatorConfig',
    type: 'menu',
    label: '工单指标配置',
  },
  {
    openType: 'now',
    orderBy: 3,
    path: '/app/issuePoolConfig',
    resourceCode: 'ticketConfig:IssuePoolConfig',
    type: 'menu',
    label: '工单池管理',
  },
  {
    openType: 'now',
    orderBy: 7,
    path: 'technicalConfig',
    resourceCode: 'technicalConfig',
    type: 'menu',
    label: '技术配置管理',
  },
  {
    openType: 'now',
    orderBy: 1,
    path: '/app/errorCodeConf',
    resourceCode: 'technicalConfig:ErrorCodeConf',
    type: 'menu',
    label: '错误码信息翻译',
  },
  {
    openType: 'now',
    orderBy: 4,
    path: '/app/vehicleInstruction',
    resourceCode: 'technicalConfig:vehicleInstruction',
    type: 'menu',
    label: '车辆指令管理',
  },
  {
    openType: 'now',
    orderBy: 5,
    path: '/app/abnormalStart',
    resourceCode: 'technicalConfig:abnormalStart',
    type: 'menu',
    label: '开机启动异常管理',
  },
  {
    openType: 'now',
    orderBy: 6,
    path: '/app/hardwareSerial',
    resourceCode: 'technicalConfig:hardwareSerial',
    type: 'menu',
    label: '硬件序列号管理',
  },
  {
    openType: 'now',
    orderBy: 7,
    path: '/app/appOperation',
    resourceCode: 'technicalConfig:appOperation',
    type: 'menu',
    label: '应用操作日志',
  },
  {
    openType: 'now',
    orderBy: 8,
    path: 'testingManage',
    resourceCode: 'testingManage',
    type: 'menu',
    label: '集成测试管理',
  },
  {
    openType: 'now',
    orderBy: 1,
    path: '/app/mrManage',
    resourceCode: 'testingManage:MrManage',
    type: 'menu',
    label: 'MR管理',
  },
  {
    openType: 'now',
    orderBy: 3,
    path: '/app/issueCategoryManage',
    resourceCode: 'testingManage:IssueCategoryManage',
    type: 'menu',
    label: '问题分类管理',
  },
  {
    openType: 'now',
    orderBy: 4,
    path: '/app/issueLabelManage',
    resourceCode: 'testingManage:IssueLabelManage',
    type: 'menu',
    label: '问题标签管理',
  },
  {
    openType: 'now',
    orderBy: 2,
    path: '/app/testing',
    resourceCode: 'testingManage:TestingManage',
    type: 'menu',
    label: '缺陷管理',
  },

  {
    openType: 'now',
    orderBy: 9,
    path: 'otaManagement',
    resourceCode: 'otaManagement',
    type: 'menu',
    label: 'OTA管理',
  },
  {
    openType: 'now',
    orderBy: 1,
    path: `/ota/configManagement`,
    resourceCode: 'otaManagement:configManagement',
    type: 'menu',
    label: '配置模板管理',
  },
  {
    openType: 'now',
    orderBy: 2,
    path: `/ota/vehicleTypeManage`,
    resourceCode: 'otaManagement:vehicletype',
    type: 'menu',
    label: '车型配置管理',
  },
  {
    openType: 'now',
    orderBy: 3,
    path: `/ota/vehicleConfigManage`,
    resourceCode: 'otaManagement:vehicletemplete',
    type: 'menu',
    label: '设备配置模板管理',
  },
  {
    openType: 'now',
    orderBy: 4,
    path: `/ota/upgrademanagement`,
    resourceCode: 'otaManagement:upgrademanagement',
    type: 'menu',
    label: '升级包管理',
  },
  {
    openType: 'now',
    orderBy: 5,
    path: `/ota/vehicleConfigAndRelease`,
    resourceCode: 'otaManagement:vehiclerelease',
    type: 'menu',
    label: '设备配置与升级',
  },
  // 在运管上根据环境配置全路径地址
  {
    openType: 'now',
    orderBy: 6,
    path: `/ota/releasePlanManage`,
    resourceCode: 'otaManagement:relaseplanning',
    type: 'menu',
    label: '发布计划管理',
  },
  {
    openType: 'now',
    orderBy: 10,
    path: 'xMapManage',
    resourceCode: 'xMapManage',
    type: 'menu',
    label: '地图管理',
  },
  {
    openType: 'now',
    orderBy: 1,
    path: '/app/xMapInfoManagement',
    resourceCode: 'xMapManage:xMapInfoManagement',
    type: 'menu',
    label: '地图列表',
  },
  {
    openType: 'now',
    orderBy: 2,
    path: '/app/xMapVersionManagement',
    resourceCode: 'xMapManage:xMapVersionManagement',
    type: 'menu',
    label: '发版历史',
  },
  {
    openType: 'now',
    orderBy: 11,
    path: 'permissionManage',
    resourceCode: 'permissionManage',
    type: 'menu',
    label: '旧版权限管理',
  },
  {
    openType: 'now',
    orderBy: 1,
    path: '/app/roleManage',
    resourceCode: 'permissionManage:roleManage',
    type: 'menu',
    label: '角色管理',
  },
  {
    openType: 'now',
    orderBy: 2,
    path: '/app/resourceManage',
    resourceCode: 'permissionManage:resourceManage',
    type: 'menu',
    label: '资源管理',
  },
  {
    type: 'menu',
    resourceCode: 'historySystemManage',
    path: 'historySystemManage',
    openType: 'now',
    orderBy: 12,
  },
  {
    type: 'menu',
    resourceCode: 'historySystemManage:monitorManage',
    path: 'https://jdxmonitor.jdl.cn',
    openType: 'new',
    orderBy: 1,
  },
  {
    type: 'menu',
    resourceCode: 'historySystemManage:videoManage',
    path: 'https://rover-vod.jdl.cn',
    openType: 'new',
    orderBy: 2,
  },
  {
    type: 'menu',
    resourceCode: 'historySystemManage:dataAnalysis',
    path: 'https://rover-bi.jd.com',
    openType: 'new',
    orderBy: 3,
  },
  {
    type: 'menu',
    resourceCode: 'allInOneManage',
    path: 'allInOneManage',
    openType: 'now',
    label: '多合一',
    orderBy: 13,
  },
  {
    openType: 'now',
    orderBy: 1,
    path: '/app/robotMapManage',
    resourceCode: 'allInOneManage:robotMapManage',
    type: 'menu',
    label: '地图管理',
  },
  {
    openType: 'now',
    orderBy: 2,
    path: '/app/taskManage',
    resourceCode: 'allInOneManage:taskManage',
    type: 'menu',
    label: '任务管理',
  },
  {
    openType: 'now',
    orderBy: 3,
    path: '/app/shelfTypeManage',
    resourceCode: 'allInOneManage:shelfTypeManage',
    type: 'menu',
    label: '上装类型管理',
  },
  {
    openType: 'now',
    orderBy: 4,
    path: '/app/deviceShelfTypeManage',
    resourceCode: 'allInOneManage:deviceShelfTypeManage',
    type: 'menu',
    label: '车型与上装关系管理',
  },
  {
    openType: 'now',
    orderBy: 5,
    path: '/app/stopRangeManage',
    resourceCode: 'allInOneManage:stopRangeManage',
    type: 'menu',
    label: '停靠范围管理',
  },
  {
    openType: 'now',
    orderBy: 14,
    path: 'generalDeviceManagement',
    resourceCode: 'generalDeviceManagement',
    type: 'menu',
    label: '通用设备管理',
  },
  {
    openType: 'now',
    orderBy: 1,
    path: `/ota/group`,
    resourceCode: 'generalDeviceManagement:group',
    type: 'menu',
    label: '分组管理',
  },
  {
    openType: 'now',
    orderBy: 2,
    path: `/ota/product`,
    resourceCode: 'generalDeviceManagement:product',
    type: 'menu',
    label: '产品管理',
  },
  {
    openType: 'now',
    orderBy: 3,
    path: `/ota/device`,
    resourceCode: 'generalDeviceManagement:device',
    type: 'menu',
    label: '设备管理',
  },
  {
    openType: 'now',
    orderBy: 4,
    path: '/ota/commandControl',
    resourceCode: 'generalDeviceManagement:commandControl',
    type: 'menu',
    label: '指令控制',
  },
  {
    openType: 'now',
    orderBy: 5,
    path: 'upgradePackage',
    resourceCode: 'generalDeviceManagement:upgradePackage',
    type: 'menu',
    label: '升级包管理',
  },
  {
    openType: 'now',
    orderBy: 1,
    path: `/ota/firmware`,
    resourceCode: 'generalDeviceManagement:upgradePackage:firmware',
    type: 'menu',
    label: '固件',
  },
  {
    openType: 'now',
    orderBy: 2,
    path: `/ota/app`,
    resourceCode: 'generalDeviceManagement:upgradePackage:app',
    type: 'menu',
    label: '应用',
  },
  {
    openType: 'now',
    orderBy: 6,
    path: `/ota/releasePlan`,
    resourceCode: 'generalDeviceManagement:releasePlan',
    type: 'menu',
    label: '发布计划管理',
  },
  {
    openType: 'now',
    orderBy: 15,
    path: 'businessData',
    resourceCode: 'businessData',
    type: 'menu',
    label: '业务数据',
  },
  {
    openType: 'now',
    orderBy: 1,
    path: '/app/transportModelSiteConfig',
    resourceCode: 'businessData:transportModelSiteConfig',
    type: 'menu',
    label: '站点配置表',
  },
  {
    openType: 'now',
    orderBy: 2,
    path: '/app/transportModelRecommendResult',
    resourceCode: 'businessData:transportModelRecommendResult',
    type: 'menu',
    label: '接驳线路推荐',
  },
  {
    openType: 'now',
    orderBy: 3,
    path: '/app/transportDataStop',
    resourceCode: 'businessData:transportDataStop',
    type: 'menu',
    label: '停靠点明细',
  },
  {
    openType: 'now',
    orderBy: 4,
    path: '/app/transportDataSchedule',
    resourceCode: 'businessData:transportDataSchedule',
    type: 'menu',
    label: '调度明细',
  },
  {
    openType: 'now',
    orderBy: 5,
    path: '/app/transportDataStation',
    resourceCode: 'businessData:transportDataStation',
    type: 'menu',
    label: '站点数据',
  },
  {
    openType: 'now',
    orderBy: 6,
    path: '/app/transportDataCity',
    resourceCode: 'businessData:transportDataCity',
    type: 'menu',
    label: '城市数据',
  },
  {
    openType: 'now',
    orderBy: 7,
    path: '/app/transportDataState',
    resourceCode: 'businessData:transportDataState',
    type: 'menu',
    label: '省份数据',
  },
];
