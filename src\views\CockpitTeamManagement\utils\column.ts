import { FormConfig } from '@/components';
import { DropDownType } from '@/utils/constant';
import { dropDownKey, dropDownListKey } from '@/utils/enum';
export const tableConfigData: any[] = [
  {
    title: '序号',
    dataIndex: 'rowIndex',
    width: 100,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '远驾团队编号',
    dataIndex: 'cockpitTeamNumber',
    width: 150,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '远驾团队名称',
    dataIndex: 'cockpitTeamName',
    width: 180,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '团队成员数量',
    dataIndex: 'memberNum',
    width: 100,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '远驾团队状态',
    dataIndex: 'enableName',
    width: 200,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作人',
    dataIndex: 'modifyUser',
    width: 120,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    width: 180,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 160,
    align: 'center',
    ellipsis: true,
    fixed: 'right',
  },
];

export const searchConfig: any[] = [
  {
    name: 'cockpitTeamName',
    title: '远驾团队名称',
    placeHolder: '请输入远驾团队名称',
    type: DropDownType.INPUT,
  },
];

export const driveTeamVehiceTableConfig: any[] = [
  {
    title: '序号',
    dataIndex: 'rowIndex',
    width: 40,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '站点名称',
    dataIndex: 'stationName',
    width: 200,
    align: 'center',
    ellipsis: true,
  },
];

export const EditStationSearchformConfig: FormConfig = {
  fields: [
    {
      fieldName: 'stationInfo',
      label: '省市站',
      placeholder: '请选择省市站信息',
      type: 'cascader',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      mapRelation: {
        label: 'name',
        value: 'id',
        children: 'children',
      },
      specialFetch: 'station',
    },
    {
      fieldName: 'stationUseCaseList',
      label: '站点用途',
      placeholder: '请选择站点用途/可多选',
      type: 'select',
      dropDownKey: dropDownKey.STATION_USECASE,
      dropDownListKey: dropDownListKey.STATION_USECASE,
      specialFetch: 'commonDown',
    },
    {
      fieldName: 'stationTypeList',
      label: '站点类型',
      placeholder: '请选择站点类型',
      type: 'select',
      dropDownListKey: dropDownListKey.STATION_TYPE,
      dropDownKey: dropDownKey.STATION_TYPE,
      specialFetch: 'commonDown',
    },
  ],
};

export const EditStationTableColumns: any[] = [
  {
    title: '绑定国家',
    width: 60,
    dataIndex: 'countryName',
    align: 'center',
    ellipsis: true,
    render: (text: any) => `${text || ''}`,
  },
  {
    title: '绑定省份',
    width: 80,
    dataIndex: 'provinceName',
    align: 'center',
    ellipsis: true,
    render: (text: any) => `${text || ''}`,
  },
  {
    title: '绑定城市',
    width: 80,
    dataIndex: 'cityName',
    align: 'center',
    ellipsis: true,
    render: (text: any) => `${text || ''}`,
  },
  {
    title: '绑定站点',
    width: 90,
    dataIndex: 'stationName',
    align: 'center',
    ellipsis: true,
    render: (text: any) => `${text || ''}`,
  },
  {
    title: '站点用途',
    width: 60,
    dataIndex: 'stationUseCaseName',
    align: 'center',
    ellipsis: true,
    render: (text: any) => `${text || ''}`,
  },
  {
    title: '站点类型',
    width: 40,
    dataIndex: 'stationTypeName',
    align: 'center',
    ellipsis: true,
    render: (text: any) => `${text || ''}`,
  },
];
