import { dropDownListKey, dropDownKey } from '@/utils/constant';
import { FieldItem, FormConfig } from '@/components';
export const tableColumns = [
  {
    title: '模板名称',
    dataIndex: 'name',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '模板id',
    dataIndex: 'id',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '所属产品',
    dataIndex: 'productTypeName',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '箱体名称',
    dataIndex: 'hardwareModelName',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '箱体型号',
    dataIndex: 'hardwareModelModel',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '格口数',
    dataIndex: 'gridNum',
    align: 'center',
    width: 80,
    ellipsis: true,
  },
  {
    title: '驱动类型',
    dataIndex: 'driverType',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '串口号',
    dataIndex: 'deviceId',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '波特率',
    dataIndex: 'baudRate',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '模板状态',
    dataIndex: 'enable',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '操作人',
    dataIndex: 'modifyUser',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    align: 'center',
    width: 240,
    ellipsis: true,
    fixed: 'right',
  },
];
export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'name',
      label: '模版名称',
      placeholder: '请输入模版名称',
      type: 'input',
    },
    {
      fieldName: 'hardwareModelName',
      label: '箱体名称',
      placeholder: '请输入箱体名称',
      type: 'input',
    },
    {
      fieldName: 'productType',
      label: '所属产品',
      placeholder: '请选择所属产品',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.PRODUCT_TYPE,
      dropDownListKey: dropDownListKey.PRODUCT_TYPE,
    },
    {
      fieldName: 'hardwareModelModel',
      label: '箱体型号',
      placeholder: '请输入箱体型号',
      type: 'input',
    },
    {
      fieldName: 'gridNum',
      label: '格口数',
      placeholder: '请输入格口数',
      type: 'input',
    },
    {
      fieldName: 'enable',
      label: '模版状态',
      placeholder: '请选择模版状态',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.ENABLE,
      dropDownListKey: dropDownListKey.ENABLE,
    },
  ],
};
