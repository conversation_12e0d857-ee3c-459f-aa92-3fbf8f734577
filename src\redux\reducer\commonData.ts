import { createSlice } from '@reduxjs/toolkit';
// eslint-disable-next-line no-unused-vars
import { RootState } from '../store';

const commonData = createSlice({
  name: 'commonData',
  initialState: {
    gridPositionValue: {},
    gridSizeList: [],
    gridList: [],
    refreshPreview: Date.now(),
  },
  reducers: {
    setGridPositionValueAction(state, action) {
      state.gridPositionValue = action.payload;
    },
    setGridSizeListAction(state, action) {
      state.gridSizeList = action.payload;
    },
    setGridListAction(state, action) {
      state.gridList = action.payload;
      state.refreshPreview = Date.now();
    },
  },
});

export const {
  setGridPositionValueAction,
  setGridSizeListAction,
  setGridListAction,
} = commonData.actions;
export const commonDataSelector = (state: RootState) => {
  return state.commonData;
};
export default commonData.reducer;
