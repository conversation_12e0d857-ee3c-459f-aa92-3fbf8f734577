import { ProductType, YESNO } from '@/utils/enum';
import { request } from '../core';
import { PageType } from '@/utils/enum';
import { parseResponseBody } from '@/utils/utils';
import { kebabCase } from 'lodash';

export class VehicleTypeApi {
  // 分页查询
  fetchTableList({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/device_type/device_type_get_page_list',
      urlParams: {
        pageNum,
        pageSize,
      },
      body: searchForm,
    };
    return request(options);
  }
  //  新增编辑
  async submitInfo({
    type,
    requestBody,
  }: {
    type: PageType.EDIT | PageType.ADD;
    requestBody: Object;
  }) {
    const initReq: RequestInit = {};
    initReq.method = type === PageType.EDIT ? 'PUT' : 'POST';
    initReq.mode = 'cors';
    initReq.credentials = 'include';
    const formData = new FormData();

    Object.keys(requestBody).forEach((k: any) => {
      const v = requestBody[k];
      if (k === 'pictureList') {
        Array.isArray(v) &&
          v.length > 0 &&
          v.forEach((sunVal, i) => {
            formData.append(k, sunVal);
          });
      } else if (k === 'hardwareModelList') {
        formData.append(k, v);
      } else {
        v && formData.append(k, v);
      }
    });
    initReq.body = formData;
    const uploadUrl =
      type === PageType.EDIT
        ? '/k2/management/device_type/device_type_edit'
        : '/k2/management/device_type/device_type_add';
    const response = await window.fetch(
      `//${process.env.JDX_APP_FETCH_DATA_DOMAIN}${uploadUrl}`,
      initReq,
    );
    const respBody = await parseResponseBody(response, 'application/json');
    if (respBody) {
      return respBody;
    } else {
      return {
        code: '-200',
        message: '提交失败',
      };
    }
  }
  // 获取详情
  fetchDetail(id: number | string) {
    const options: RequestOptions = {
      method: 'GET',
      path: '/k2/management/device_type/device_type_get_detail',
      urlParams: { deviceTypeBaseId: id },
    };
    return request(options);
  }
  // 启用停用
  updateStatus({ id, enable }: { id: number; enable: YESNO }) {
    const options: RequestOptions = {
      method: 'PUT',
      path: '/k2/management/device_type/device_type_update_enable',
      body: {
        id,
        enable,
      },
    };
    return request(options);
  }

  // 获取车型配置信息
  getDeviceConf({ type }: { type: string }) {
    const options: RequestOptions = {
      method: 'GET',
      path: '/k2/management/device_type/get_conf_of_device_type',
    };
    return request(options);
  }

  //   获取设备硬件模板信息
  getDeviceTemplate({
    type,
    productType,
  }: {
    type: 'device_type' | 'device';
    productType: ProductType;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/device_template/get_device_template_info',
      body: {
        type,
        productType,
      },
    };
    return request(options);
  }
}
