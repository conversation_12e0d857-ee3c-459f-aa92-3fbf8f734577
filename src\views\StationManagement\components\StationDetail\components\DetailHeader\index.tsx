import React, { useEffect, useState } from 'react';
import {
  Row,
  Col,
  Descriptions,
  Divider,
  DescriptionsProps,
  message,
} from 'antd';
import PopconfirmBtn from '@/components/PopconfirmBtn';
import { BasicStationInfo } from '@/types';
import { YESNO } from '@/utils/enum';
import { StationFetchApi } from '@/fetch/business';
import { AnyFunc } from '@/global';
import { HttpStatusCode } from '@/fetch/core/constant';
interface Props {
  stationDetail: BasicStationInfo | null;
  updateDataCb: AnyFunc;
}
const fetchApi = new StationFetchApi();
const DetailHeader = (props: Props) => {
  const { stationDetail, updateDataCb } = props;
  const updateStatus = async (stationBaseId: number, enable: number) => {
    if (stationBaseId) {
      const res = await fetchApi.changeStationStatus(stationBaseId, enable);
      if (res.code === HttpStatusCode.Success) {
        message.success('修改站点状态成功！');
        updateDataCb();
      }
    }
  };
  const enableConfig = {
    btnText: '已启用',
    popMsg:
      '站点停用后，会停用停靠点，解绑停靠点与车辆关系，请确认是否停用站点？',
    onConfirm: () => {
      stationDetail && updateStatus(stationDetail.stationBaseId, YESNO.NO);
    },
  };
  const disableConfig = {
    btnText: '已停用',
    popMsg: ' 确认启用吗?',
    onConfirm: () => {
      stationDetail && updateStatus(stationDetail.stationBaseId, YESNO.YES);
    },
  };
  const stationItems: DescriptionsProps['items'] = [
    {
      key: 'productTypeName',
      label: '站点类型',
      children: stationDetail?.productTypeName,
    },
    {
      key: 'useCaseName',
      label: '站点用途',
      children: stationDetail?.useCaseName,
    },
    {
      key: 'typeName',
      label: '运营方类型',
      children: stationDetail?.typeName,
    },
    {
      key: 'personName',
      label: '负责人姓名',
      children: `${stationDetail?.personChineseName}/${stationDetail?.personErp}`,
    },
    {
      key: 'contact',
      label: '负责人电话',
      children: stationDetail?.contact,
    },
  ];
  return (
    <>
      <div className="header-title">
        <span className="station-name">{stationDetail?.name}</span>
        <span className="station-base-id">
          站点ID：{stationDetail?.stationBaseId}
        </span>
      </div>
      <Row justify="space-between" align={'middle'} style={{ padding: '8px' }}>
        <Col>
          <Descriptions
            items={stationItems}
            column={5}
            extra={
              stationDetail?.enable === YESNO.YES ? (
                <PopconfirmBtn
                  btnProps={{ style: { color: 'green' }, type: 'link' }}
                  {...enableConfig}
                />
              ) : (
                <PopconfirmBtn btnProps={{ danger: true }} {...disableConfig} />
              )
            }
          />
        </Col>
      </Row>
      <Divider style={{ margin: '5px 0' }} />
    </>
  );
};
export default React.memo(DetailHeader);
