.legend-container {
  position: absolute;
  z-index: 99;
  right: 104px;
  bottom: 32px;
  width: 500px;
  height: 40px;
  padding-left: 8px;
  padding-right: 8px;
  background: rgba(255, 255, 255, 1);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(35, 37, 43, 0.1);

  .legend-label {
    width: fit-content;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(35, 37, 43, 1);
    margin-left: 8px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    cursor: pointer;
    &.HDMap {
      width: calc(100% - 16px);
      padding-right: 8px;
      padding-left: 8px;
      margin-left: 0px;
      margin-right: 0px;
    }
    &:hover {
      box-shadow: -1px 1px 7px 5px #a9c1ff;
    }
    i {
      display: inline-block;
      width: 16px;
      height: 16px;
      background-size: 100% 100%;
      margin-right: 8px;
      &.close {
        background-image: url("../../../../assets//image/common/close-eye-icon.png");
      }
      &.open {
        background-image: url("../../../../assets//image/common/show-eye-icon.png");
      }
    }
  }
}

.map-legend {
  display: flex;
  justify-content: center;
  align-items: center;
  .arrow {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-size: 100% 100%;
    &.close {
      background-image: url("../../../../assets/image/mapCollect/taskcard/task-card-expand-icon.png");
    }
    &.open {
      background-image: url("../../../../assets/image/mapCollect/taskcard/task-card-collapse-icon.png");
    }
  }
}

.HD-maps-legend,
.station-legend,
.stop-legend,
.point-name-legend {
  height: auto;
  background: rgba(255, 255, 255, 1);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(35, 37, 43, 0.1);
  position: absolute;
  right: 0px;
  bottom: 40px;
  margin-bottom: 3px;
  transition:
    opacity 0.7s ease,
    visibility 0.7s ease;
  padding-top: 4px;
  padding-bottom: 4px;
  z-index: 100;

  &.show {
    opacity: 1;
    visibility: visible;
  }
  &.hidden {
    opacity: 0;
    visibility: hidden;
  }
}
.station-legend {
  left: 80px;
}
.stop-legend {
  left: 160px;
}
.point-name-legend {
  left: 240px;
  width: 180px;
}

.HD-maps-legend {
  height: 240px;
}

.station-legend,
.stop-legend {
  width: 120px;
}
