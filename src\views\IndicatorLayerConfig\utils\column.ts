import { FormConfig } from '@/components';
import { TableColumnsType } from 'antd';
export interface DataType {
  layerNumber: string;
  layerName: string;
  priority: number;
  indicatorCount: number;
  modifyUser: string;
  modifyTime: string;
}
export const indicatorLayerColumns: TableColumnsType<DataType> = [
  {
    title: '指标层id',
    dataIndex: 'layerNumber',
    width: 150,
    align: 'center',
  },
  {
    title: '指标层名称',
    dataIndex: 'layerName',
    width: 150,
    align: 'center',
  },
  {
    title: '权重',
    dataIndex: 'priority',
    width: 150,
    align: 'center',
  },
  {
    title: '指标数量',
    dataIndex: 'indicatorCount',
    width: 150,
    align: 'center',
  },
  {
    title: '操作人',
    dataIndex: 'modifyUser',
    width: 150,
    align: 'center',
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    width: 150,
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operations',
    width: 100,
    align: 'center',
  },
];
