import React, { useEffect, useRef, useState } from 'react';
import BreadCrumb from '@/components/BreadCrumb';
import './edit.scss';
import { Button, message } from 'antd';
import {
  CommonForm,
  CommonFormPro,
  FieldItem,
  FormConfig,
  FormProConfig,
} from '@jd/x-coreui';
import { JiraTestProgress, XingyunEditForm } from './constant';
import { getJiraDetail, submitJiraEdit } from '@/fetch/business/jiraManage';
import { formatLocation } from '@/utils/utils';
import { HttpStatusCode } from '@/fetch/core/constant';

import { useNavigate } from 'react-router';

const breadCrumbItems = [
  {
    title: '缺陷管理',
    route: '/app/testing',
  },
  {
    title: '编辑缺陷',
    route: '',
  },
];
const TestingEdit = () => {
  const navigator = useNavigate();
  const { bugCode } = formatLocation(window.location.search);
  const [detail, setDetail] = useState<any>(null);
  const [detailForm, setDetailForm] = useState<FormConfig>(XingyunEditForm);
  const [testProgressForm, setTestProgressForm] =
    useState<FormProConfig>(JiraTestProgress);
  const formRef = useRef<any>(null);
  const [uploading, setUploading] = useState<boolean>(false);
  const submit = async (type: 'submit' | 'tempsave') => {
    if (uploading) {
      message.warning('请等待上传完成');
      return;
    }
    try {
      const values =
        type === 'submit'
          ? await formRef.current?.validateFields()
          : await formRef.current?.getFieldsValue();
      submitJiraEdit({
        ...values,
        bugCode: detail.bugCode,
        isSubmit: type === 'submit' ? true : false,
        imageList: values?.imageList?.map((item) => ({
          type: 'images',
          fileKey: item,
          bucketName: 'rover-operation',
        })),
      }).then((res) => {
        if (res?.code === HttpStatusCode.Success) {
          message.success('编辑成功');
          history.go(-1);
        } else {
          res?.message && message.error(res?.message);
        }
      });
    } catch (e) {}
  };
  useEffect(() => {
    getJiraDetail(bugCode).then((res) => {
      if (res?.code === HttpStatusCode.Success) {
        setDetail(res?.data);
        const { imageList, ...other } = res?.data || {};
        formRef.current?.setFieldsValue({
          ...other,
          imageList: imageList?.map((item) => item?.fileKey),
        });
        const imgField =
          testProgressForm.fields.find(
            (item) => item.fieldName === 'imageList',
          ) || {};
        const simulationRecordField = detailForm.fields.find(
          (item: FieldItem) => item.fieldName === 'simulationRecordInfoList',
        );
        const bugCodeField = detailForm.fields.find(
          (item: FieldItem) => item.fieldName === 'bugCode',
        );
        const associatedBugList = detailForm.fields.find(
          (item: FieldItem) => item.fieldName === 'associatedBugList',
        );
        const fixMrIdList = detailForm.fields.find(
          (item: FieldItem) => item.fieldName === 'fixMrIdList',
        );
        simulationRecordField!.type = 'ReactNode';
        bugCodeField!.type = 'ReactNode';
        associatedBugList!.type = 'ReactNode';
        fixMrIdList!.type = 'ReactNode';
        imgField.uploadedFileList = res?.data?.imageList?.map((item) => ({
          fileKey: item.fileKey,
          url: item.url,
          uid: item.fileKey,
        }));
        simulationRecordField!.renderFunc = () => {
          return (
            <>
              {res?.data?.simulationRecordInfoList?.map((item) => (
                <a
                  href={item?.simulationRecordUrl}
                  target="_blank"
                  key={item?.simulationRecordName}
                >
                  {item?.simulationRecordName}；
                </a>
              ))}
            </>
          );
        };
        bugCodeField!.renderFunc = () => {
          return (
            <a href={res?.data?.bugUrl} target="_blank">
              {res?.data?.bugCode}
            </a>
          );
        };
        associatedBugList!.renderFunc = () => {
          return (
            <>
              {res?.data?.associatedBugList?.map((item) => {
                return (
                  <a
                    style={{ marginRight: '4px' }}
                    href={item?.url}
                    target="_blank"
                  >
                    {item?.code}；
                  </a>
                );
              })}
            </>
          );
        };
        fixMrIdList!.renderFunc = () => {
          return (
            <>
              {res?.data?.fixMrIdList?.map((item) => {
                return (
                  <a
                    style={{ marginRight: '4px' }}
                    onClick={() => {
                      navigator('/app/mrManage?mrNumber=' + item);
                    }}
                  >
                    {item}；
                  </a>
                );
              })}
            </>
          );
        };
        setDetailForm({ ...detailForm });
        setTestProgressForm({ ...testProgressForm });
      }
    });
  }, []);
  return (
    <div className="jira-edit-container" style={{ padding: '10px' }}>
      <div className="bread-crub">
        <BreadCrumb items={breadCrumbItems} />
      </div>
      <div className="content">
        <h1 className="title">编辑缺陷</h1>
        <div className="form-title">
          <h2>Coding获取内容&获取行云关联解决的缺陷</h2>
        </div>
        <CommonForm
          defaultValue={detail}
          formConfig={detailForm}
          layout="inline"
        />
        <div className="form-title">
          <h2>测试进度</h2>
        </div>
        <CommonFormPro
          defaultValue={{
            isValid: detail?.isValid,
            invalidReason: detail?.invalidReason,
            isSimulation: detail?.isSimulation,
            simulationTaskName: detail?.simulationTaskName,
            isReproducible: detail?.isReproducible,
            nonReproducibleReason: detail?.nonReproducibleReason,
            isRecallable: detail?.isRecallable,
            nonRecallableReason: detail?.nonRecallableReason,
            rollbackTaskName: detail?.rollbackTaskName,
            rollbackResult: detail?.rollbackResult,
            rollbackRemark: detail?.rollbackRemark,
            remark: detail?.remark,
          }}
          formConfig={testProgressForm}
          name="jiraProgress"
          getFormInstance={(ref: any) => {
            formRef.current = ref;
          }}
          getUploadStatus={(
            status: boolean,
            uploadStatus: 'uploading' | 'error' | 'success' | 'delete',
            fieldName?: string | undefined,
          ) => {
            setUploading(status);
            const value = formRef.current?.getFieldValue(fieldName);
            if (fieldName === 'imageList') {
              const label = document.querySelector(
                'label[for="jiraProgress_imageList"]',
              );
              if (label) {
                label.innerHTML = `上传图片（${value?.length || 0}/3）`;
              }
            }
          }}
        />
        <div className="btn-group">
          <Button
            type="default"
            onClick={() => submit('tempsave')}
            style={{ marginRight: '20px' }}
          >
            暂存
          </Button>
          <Button type="primary" onClick={() => submit('submit')}>
            确定
          </Button>
          <Button
            type="default"
            style={{ marginLeft: '20px' }}
            onClick={() => {
              history.go(-1);
            }}
          >
            取消
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TestingEdit;
