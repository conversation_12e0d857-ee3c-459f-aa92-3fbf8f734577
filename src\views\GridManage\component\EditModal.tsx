/* eslint-disable react/jsx-key */

/* eslint-disable no-unused-vars */
import { Col, Form, Input, message, Modal, Row, Select } from 'antd';
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { boxGridSelector } from '@/redux/reducer/boxGrid';
import { gridEditModalFormConfiger } from './dataProvider';
import { StatusItem } from '@/components';
import LittleGridTable from './LittleGridEditTable';
import FormRadio from './FormRadio';
import { arrUnique } from '@/utils/utils';
import { useCommonDropDown } from '@/utils/hooks';
import { formatOptions } from '@/utils/utils';
import { dropDownListKey, dropDownKey } from '@/utils/constant';
import { ProductType } from '@/utils/enum';
const GridEditModal = ({
  show,
  initValue,
  onSubmit,
  productionType,
}: {
  show: boolean;
  initValue: any;
  onSubmit: Function;
  productionType: ProductType;
}) => {
  const [formRef] = Form.useForm();
  let dropdownList = useCommonDropDown([
    dropDownKey.GRID_POSITION,
    dropDownKey.ENABLE,
  ]);
  const [selectbleConfiger, setSelectbleConfiger] = useState<{
    gridPosition: any;
    gridStatus: any;
    newGridPosition?: any;
    gridSizeList: any[];
    boardNoList: any[];
  }>({
    gridPosition: null,
    gridStatus: null,
    newGridPosition: null,
    gridSizeList: [],
    boardNoList: [
      { code: 1, name: 1 },
      { code: 2, name: 2 },
    ],
  });

  // 这个状态是为了保存编辑数据的，和初始化数据是不冲突的
  const [palletList, setPalletList] = useState([]);
  const [shouldHavePallet, setShouldHavePallet] = useState(false);
  const commonData = useSelector(boxGridSelector);

  const valueAsNumberAndMorethanOrEqual0 = (key: string, inputValue: any) => {
    let value = inputValue;
    const reg = isNaN(value);
    if (reg) {
      value = value.replace(/[^1-9]*$/g, '');
    }
    if (`${value}`.indexOf('0') === 0) {
      value = '0';
    }
    adjustInputValue(key, value);
  };
  const valueAsNumberAndMorethan0 = (key: string, inputValue: any) => {
    let value = inputValue;
    const reg = isNaN(value);
    if (reg) {
      value = value.replace(/[^0-9]*$/g, '');
    }
    if (`${value}`.indexOf('0') === 0) {
      value = null;
    }
    adjustInputValue(key, value);
  };
  const withComma = (key: string, inputValue: any) => {
    let value = inputValue;
    const reg = isNaN(value);
    if (reg) {
      value = value.replace(/[^0-9,]*$/g, '');
    }
    if (`${value}`.indexOf('0') === 0) {
      value = '0';
    }
    adjustInputValue(key, value);
  };

  const adjustInputValue = (key: string, value: any) => {
    if (value && `${value}`.length <= 0) {
      formRef.setFieldsValue({
        [key]: null,
        // [key === "gridNo" ? 'lockNo' : '']: null
      });
    } else {
      formRef.setFieldsValue({
        [key]: value,
        // [key === "gridNo" ? 'lockNo' : '']: value
      });
    }
  };

  useEffect(() => {
    setSelectbleConfiger({
      ...selectbleConfiger,
      gridStatus: dropdownList[dropDownListKey.ENABLE],
      gridPosition: dropdownList[dropDownListKey.GRID_POSITION],
    });
  }, [dropdownList]);

  useEffect(() => {
    if (selectbleConfiger.gridPosition) {
      const newGridPosition = selectbleConfiger.gridPosition.filter(
        (item: any) => {
          return (
            (item.code == '左侧' &&
              commonData.gridPositionValue.leftBoxColumnNum != 0 &&
              commonData.gridPositionValue.leftBoxColumnNum) ||
            (item.code == '右侧' &&
              commonData.gridPositionValue.rightBoxColumnNum != 0 &&
              commonData.gridPositionValue.rightBoxColumnNum)
          );
        },
      );
      let tempArr: any = [];
      commonData.gridSizeList.map((item: any, idx: number) => {
        if (item.sizeStr) {
          tempArr.push({ code: idx, name: item.sizeStr });
        }
      });
      tempArr = arrUnique(tempArr, 'name');
      setSelectbleConfiger({
        ...selectbleConfiger,
        newGridPosition: newGridPosition,
        gridSizeList: tempArr,
      });
      const sizeIdx = tempArr.findIndex(
        (item: any) => item.name === initValue.sizeStr,
      );
      if (sizeIdx > -1) {
        formRef.setFieldsValue({
          size: sizeIdx,
        });
      }
    }
  }, [selectbleConfiger.gridPosition, commonData]);

  useEffect(() => {
    if (initValue) {
      // 将id为null的小格口配一下null
      const palletList = initValue.palletList?.map(
        (ele: any, index: number) => {
          // newAdd记得得加上，important!!!
          return {
            ...ele,
            id: ele.id || Date.now() + index,
            newAdd: ele.id === null || ele.id === undefined,
          };
        },
      );
      formRef.setFieldsValue({
        ...initValue,
        palletList,
        hasPallet: palletList && palletList.length > 0 ? 1 : 0,
      });
      setShouldHavePallet(palletList && palletList.length > 0);
      setPalletList(palletList);
    }
  }, [initValue]);

  return (
    <Modal
      width={'60vw'}
      visible={show}
      getContainer={false}
      title="格口配置"
      maskClosable={false}
      mask={false}
      onOk={async () => {
        const formValue = await formRef.validateFields();
        const ele =
          (selectbleConfiger.gridStatus &&
            selectbleConfiger.gridStatus.find(
              (ele: any) => ele.code == formValue.enable,
            )) ||
          {};
        formValue.enable = ele.code;
        if (initValue) {
          formValue.id = initValue.id;
          formValue.newAdd = initValue.newAdd;
        } else {
          formValue.id = Date.now();
          formValue.newAdd = true;
        }
        if (shouldHavePallet) {
          // 选择了有小格口
          if (formValue.palletList.length == 0) {
            message.warning('请添加小格口!');
            return;
          }
          const editPallietList: any = formValue.palletList;
          if (editPallietList.length <= 0) return; // 如果选择了有小格口但是没有添加，则返回
          const filteredList = formValue.palletList?.filter(
            (ele: any) => ele.invalid !== undefined,
          );
          if (filteredList.length > 0) return; // 如果有未编辑完的行，则返回
          // 这里过滤其它无用字段，只保留id和name，但是会造成一个问题，再次进入编辑，id会是null；这里的解决办法是如果是null，则给一个Date.now()
          formValue.palletList = formValue.palletList?.map((ele: any) => ({
            id: ele.newAdd ? null : ele.id,
            name: ele.name,
          }));
        } else {
          formValue.palletList = [];
        }
        const curSize = commonData.gridSizeList[formValue.size];
        formValue.width = curSize.width;
        formValue.height = curSize.height;
        formValue.length = curSize.length;
        formValue.sizeStr = curSize.sizeStr;
        formValue.type = curSize.type;
        onSubmit && onSubmit(formValue);
      }}
      onCancel={() => {
        onSubmit && onSubmit(null);
      }}
    >
      <Form form={formRef} labelCol={{ span: 6 }} autoComplete="off">
        <Row gutter={20}>
          {gridEditModalFormConfiger.map((item) => {
            const hidden =
              productionType !== ProductType.VEHICLE && item.name === 'side' ? true : false;
            return (
              <Col span={18} key={item.name}>
                {!hidden && (
                  <Form.Item
                    name={item.name}
                    label={item.label}
                    rules={[{ required: true, message: item.placeHolder }]}
                  >
                    {item.type === 'input' ? (
                      <Input
                        disabled={item.disabled}
                        placeholder={item.placeHolder}
                        maxLength={item.length}
                        onChange={(e) => {
                          const value = e.target.value;
                          if (item.rule === 'thanOrEqual0') {
                            valueAsNumberAndMorethanOrEqual0(item.name, value);
                          } else if (item.rule === 'morethan0') {
                            valueAsNumberAndMorethan0(item.name, value);
                          } else if (item.rule === 'withComma') {
                            withComma(item.name, value);
                          }
                        }}
                      />
                    ) : (
                      <Select
                        disabled={item.disabled}
                        placeholder={item.placeHolder}
                        options={dropdownList[
                          dropDownListKey.GRID_POSITION
                        ]?.map((e: any) => {
                          return { name: e.name, value: e.code };
                        })}
                      />
                    )}
                  </Form.Item>
                )}
              </Col>
            );
          })}
          <Col span={18}>
            {productionType === 'vehicle' && (
              <FormRadio
                form={formRef}
                name="boardNo"
                label="板卡号"
                items={selectbleConfiger.boardNoList}
                initValue={initValue?.boardNo}
                errMsg="请选择板卡号"
              />
            )}
          </Col>
          <Col span={18}>
            <FormRadio
              form={formRef}
              name="size"
              label="格口规格(单位:mm)"
              items={selectbleConfiger.gridSizeList}
              initValue={initValue?.size}
              errMsg="请选择格口规格"
              direction="vertical"
            />
          </Col>
          <Col span={18}>
            <StatusItem
              form={formRef}
              name="enable"
              label="格口状态"
              items={selectbleConfiger.gridStatus}
              defaultValue={1}
              initValue={initValue?.enable}
            />
          </Col>
        </Row>
        <Row>
          <Col span={18}>
            {productionType === 'vehicle' && (
              <StatusItem
                form={formRef}
                name="hasPallet"
                label="是否有小格口"
                items={[
                  { code: 1, name: '有' },
                  { code: 0, name: '无' },
                ]}
                defaultValue={0}
                onValueChanged={(value: any) => {
                  setShouldHavePallet(value === 1);
                }}
              />
            )}
          </Col>
        </Row>
        {shouldHavePallet ? (
          <Row>
            <Col span={24}>
              <Form.Item
                labelCol={{ span: 2 }}
                name="palletList"
                colon={false}
                label=" "
              >
                <LittleGridTable
                  onChange={(list: any) => {
                    formRef.setFieldsValue({
                      palletList: list,
                    });
                    setPalletList(list);
                  }}
                  initValue={palletList}
                />
              </Form.Item>
            </Col>
          </Row>
        ) : null}
      </Form>
    </Modal>
  );
};

export default React.memo(GridEditModal);
