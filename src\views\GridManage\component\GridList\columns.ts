export const operateGirdColumns: any[] = [
  {
    title: '格口号',
    align: 'center',
    dataIndex: 'gridNo',
    key: 'gridNo',
    width: 80,
  },
  {
    title: '板号',
    width: 90,
    dataIndex: 'boardNo',
    key: 'boardNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '方位',
    width: 90,
    dataIndex: 'side',
    key: 'side',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '锁号',
    width: 90,
    dataIndex: 'lockNo',
    key: 'lockNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '格口宽度（mm）',
    width: 110,
    dataIndex: 'width',
    key: 'width',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '格口高度（mm）',
    width: 110,
    dataIndex: 'height',
    key: 'height',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '格口深度（mm）',
    width: 110,
    dataIndex: 'length',
    key: 'length',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '格口状态',
    width: 90,
    dataIndex: 'statusName',
    key: 'statusName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
    align: 'center',
    width: 180,
  },
];

export const operateGirdReadOnlyColumns: any[] = [
  {
    title: '格口号',
    align: 'center',
    dataIndex: 'gridNo',
    key: 'gridNo',
    width: 80,
  },
  {
    title: '板号',
    width: 90,
    dataIndex: 'boardNo',
    key: 'boardNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '方位',
    width: 90,
    dataIndex: 'side',
    key: 'side',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '锁号',
    width: 90,
    dataIndex: 'lockNo',
    key: 'lockNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '格口宽度（mm）',
    width: 110,
    dataIndex: 'width',
    key: 'width',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '格口高度（mm）',
    width: 110,
    dataIndex: 'height',
    key: 'height',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '格口深度（mm）',
    width: 110,
    dataIndex: 'length',
    key: 'length',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '格口状态',
    width: 90,
    dataIndex: 'statusName',
    key: 'statusName',
    align: 'center',
    ellipsis: true,
  },
];

export const girdForm: any[] = [
  {
    name: 'gridNo',
    label: '格口号',
    rules: true,
    message: '',
    contentType: 'input', // input or radio
    placeholder: '请输入格口号',
    inputType: 'number',
    min: 0,
  },
  {
    name: 'side',
    label: '格口方位',
    rules: true,
    message: '请选择格口方位!',
    contentType: 'select', // input or radio
    placeholder: '请选择格口方位',
  },
  {
    name: 'gridNo',
    label: '锁号',
    rules: true,
    disable: true,
    message: '',
    contentType: 'input',
    placeholder: '请输入锁号',
    inputType: 'number',
    min: 0,
  },
  {
    name: 'width',
    label: '宽度（mm)',
    rules: true,
    message: '',
    contentType: 'input',
    placeholder: '请输入宽度',
    inputType: 'number',
    min: 1,
  },
  {
    name: 'height',
    label: '高度（mm)',
    rules: true,
    message: '',
    contentType: 'input',
    placeholder: '请输入高度',
    inputType: 'number',
    min: 1,
  },
  {
    name: 'length',
    label: '深度（mm)',
    rules: true,
    message: '',
    contentType: 'input',
    placeholder: '请输入深度',
    inputType: 'number',
    min: 1,
  },
  {
    name: 'boardNo',
    label: '板号',
    rules: true,
    message: '',
    contentType: 'input',
    placeholder: '请输入板号',
    inputType: 'number',
    min: 0,
  },
  {
    name: 'statusId',
    label: '格口状态',
    rules: true,
    message: '请选择格口状态',
    contentType: 'radio',
  },
];

export const statusList = [
  {
    code: '1',
    name: '启用',
  },
  {
    code: '0',
    name: '停用',
  },
];
