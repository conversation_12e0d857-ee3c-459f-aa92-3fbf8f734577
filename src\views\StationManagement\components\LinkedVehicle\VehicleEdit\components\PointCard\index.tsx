import { Button, Col, Row } from 'antd';
import React from 'react';
import StopPointNameTag from '../StopPointNameTag';
import { StopPointType } from '@/utils/constant';

interface Props {
  onClickSelect: Function;
  type: any;
  data: any[];
}
const PointCard = (props: Props) => {
  const { onClickSelect, type, data } = props;

  return (
    <>
      {type === StopPointType.HOME ? (
        <Row justify="start">
          <Col span={2}>
            <Button
              style={{
                color: '#3c6ef0',
                border: 'none',
                fontWeight: '700',
              }}
              onClick={() => onClickSelect(type)}
            >
              {data?.length > 0 ? '更换' : '添加'}
            </Button>
          </Col>
          <Col span={5}>
            {data?.length > 0
              ? data.map((item: any) => {
                  return (
                    <StopPointNameTag
                      name={item.name}
                      waitingTime={item.waitingTime}
                      id={item.id}
                      key={item.id}
                    />
                  );
                })
              : ''}
          </Col>
        </Row>
      ) : (
        <div>
          <Button
            style={{
              color: '#3c6ef0',
              border: 'none',
              fontWeight: '700',
              marginBottom: '10px',
            }}
            onClick={() => onClickSelect(type)}
          >
            {data?.length > 0 ? '更换' : '添加'}
          </Button>
          <Row justify="start" gutter={[8, 8]}>
            {data?.length > 0
              ? data.map((item: any) => {
                  return (
                    <Col span={4} key={item.id}>
                      <StopPointNameTag
                        name={item.name}
                        waitingTime={item.waitingTime}
                        id={item.id}
                      />
                    </Col>
                  );
                })
              : ''}
          </Row>
        </div>
      )}
    </>
  );
};

export default React.memo(PointCard);
