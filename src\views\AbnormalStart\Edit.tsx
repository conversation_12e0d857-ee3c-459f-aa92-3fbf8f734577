import { Form, Input, message, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { CommonEdit, CommonForm, EditModuleTitle } from '@/components';
import { AbnormalStartApi, CommonApi, StationFetchApi } from '@/fetch/business';
import { editFormConfig } from './utils/columns';
import { useEditPageData } from '@/utils/hooks';
import { formatLocation } from '@/utils/utils';
import { PageType, AbnormalStartTitle } from '@/utils/EditTitle';
import { useSelector, useDispatch } from 'react-redux';
import { HttpStatusCode } from '@/fetch/core/constant';
import { dropDownKey, dropDownListKey } from '@/utils/constant';

const breadCrumbItemsMap = new Map([
  [
    'add',
    [
      {
        title: '技术配置管理',
        route: '',
      },
      {
        title: '开机启动异常管理',
        route: '',
      },
      {
        title: '新建开机异常模块',
        route: '',
      },
    ],
  ],
  [
    'edit',
    [
      {
        title: '技术配置管理',
        route: '',
      },
      {
        title: '开机启动异常管理',
        route: '',
      },
      {
        title: '编辑开机异常模块',
        route: '',
      },
    ],
  ],
]);
const AbnormalStartEdit = () => {
  const fetchApi = new AbnormalStartApi();
  const commonApi = new CommonApi();
  const stationApi = new StationFetchApi();
  const navigator = useNavigate();
  const [formRef] = Form.useForm();
  const { id, type } = formatLocation(window.location.search);
  const [dropDownMap, setDropDownMap] = useState<any>({
    device: [],
    module: [],
    principalErps: [],
  });

  useEffect(() => {
    getAllDropDownList();
    if (type !== PageType.ADD) {
      getDetail();
    } else {
      formRef.setFieldsValue({ number: null });
    }
  }, []);

  const getCommonDropDown = async () => {
    const res = await commonApi.getCommonDropDown({
      keyList: [
        dropDownKey.VEHICLE_EXCEPTION_DEVICE,
        dropDownKey.VEHICLE_EXCEPTION_MODULE,
      ],
    });
    if (res && res.data && res.code === HttpStatusCode.Success) {
      return res.data;
    }
  };

  const getERPDropDown = async () => {
    const res = await stationApi.getUserInfoList();
    if (res && res.data && res.code === HttpStatusCode.Success) {
      return res.data;
    }
  };

  const getAllDropDownList = () => {
    Promise.all([getCommonDropDown(), getERPDropDown()]).then((res) => {
      const commonList = res[0];
      setDropDownMap({
        device: commonList[dropDownListKey.VEHICLE_EXCEPTION_DEVICE]?.map(
          (item: any) => {
            return {
              label: item.name,
              value: item.code,
            };
          },
        ),
        module: commonList[dropDownListKey.VEHICLE_EXCEPTION_MODULE]?.map(
          (item: any) => {
            return {
              label: item.name,
              value: item.code,
            };
          },
        ),
        principalErps: res[1]?.map((item: any) => {
          return {
            label: item.erp,
            value: item.erp,
          };
        }),
      });
    });
  };

  const getDetail = async () => {
    const res = await fetchApi.fetchDetail(id);
    if (res && res.data && res.code === HttpStatusCode.Success) {
      formRef.setFieldsValue({
        ...res.data,
        device: { lable: res.data.deviceName, value: res.data.device },
        module: { lable: res.data.moduleName, value: res.data.module },
        principalErps: res.data.principalErps?.map((item: any) => {
          return {
            label: item,
            value: item,
          };
        }),
      });
    }
  };

  const onSubmitClick = async () => {
    try {
      const value = await formRef.validateFields();
      const submitParams = {
        ...value,
        device: value.device?.value,
        module: value.module?.value,
        principalErps: value.principalErps?.map((item: any) => item.value),
      };
      const res = await fetchApi.submitEditInfo({
        type,
        requestBody: submitParams,
      });
      if (res.code === HttpStatusCode.Success) {
        message.success(res.message);
        navigator('/app/abnormalStart');
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    } finally {
      //
    }
  };

  const onCancleClick = () => {
    navigator('/app/abnormalStart');
  };

  return (
    <>
      <CommonEdit
        title={AbnormalStartTitle[type]}
        breadCrumbConfig={breadCrumbItemsMap.get(type)}
        onSubmitClick={onSubmitClick}
        onCancleClick={onCancleClick}
      >
        <Form form={formRef} labelCol={{ span: 4 }} wrapperCol={{ span: 16 }}>
          <EditModuleTitle title={'基础信息'} />
          <Form.Item name={'number'} label="编号">
            <Input placeholder="系统生成" disabled />
          </Form.Item>
          <Form.Item
            name={'device'}
            label="设备"
            rules={[{ required: true, message: '请选择设备' }]}
          >
            <Select
              placeholder={'请选择设备'}
              options={dropDownMap.device}
              labelInValue
              filterOption={(input: any, option: any) => {
                const label: any = option?.label || '';
                return (
                  label.toString().toLowerCase().indexOf(input.toLowerCase()) >=
                  0
                );
              }}
              allowClear
              showSearch
            />
          </Form.Item>
          <Form.Item
            name={'module'}
            label="模块所属"
            rules={[{ required: true, message: '请选择模块所属' }]}
          >
            <Select
              placeholder={'请选择模块所属'}
              options={dropDownMap.module}
              labelInValue
              filterOption={(input: any, option: any) => {
                const label: any = option?.label || '';
                return (
                  label.toString().toLowerCase().indexOf(input.toLowerCase()) >=
                  0
                );
              }}
              allowClear
              showSearch
            />
          </Form.Item>
          <Form.Item
            name={'moduleField'}
            label="模块字段名"
            rules={[{ required: true, message: '请输入模块字段名' }]}
          >
            <Input.TextArea
              placeholder="请输入模块字段名"
              maxLength={200}
              autoSize={{ minRows: 1, maxRows: 4 }}
              allowClear
            />
          </Form.Item>
          <Form.Item
            name={'moduleFieldName'}
            label="模块名称"
            rules={[{ required: true, message: '请输入模块名称' }]}
          >
            <Input.TextArea
              placeholder="请输入模块名称"
              maxLength={200}
              autoSize={{ minRows: 1, maxRows: 4 }}
              allowClear
            />
          </Form.Item>
          <Form.Item
            name={'normalTime'}
            label="常规耗时(s)"
            required
            rules={[
              {
                validator: (_, value: any) => {
                  const reg = /^([1-9]\d{0,}|[1-9]\d{1,})?$/;
                  if (!value) {
                    return Promise.reject(Error('请输入模块常规耗时(s)'));
                  }
                  if (!reg.test(value)) {
                    return Promise.reject(new Error('只能输入≥1的正整数'));
                  }
                  if (value < 1 || value > 300) {
                    return Promise.reject(Error('请输入1~300（包含）数值'));
                  } else {
                    return Promise.resolve();
                  }
                },
              },
            ]}
          >
            <Input placeholder="请输入模块常规耗时(s)" allowClear />
          </Form.Item>
          <Form.Item
            name={'overtimeThreshold'}
            label="超时阈值(s)"
            required
            rules={[
              {
                validator: (_, value: any) => {
                  const reg = /^([1-9]\d{0,}|[1-9]\d{1,})?$/;
                  if (!value) {
                    return Promise.reject(Error('请输入模块超时阈值(s)'));
                  }
                  if (!reg.test(value)) {
                    return Promise.reject(new Error('只能输入≥1的正整数'));
                  }
                  if (value < 1 || value > 300) {
                    return Promise.reject(Error('请输入1~300（包含）数值'));
                  } else {
                    return Promise.resolve();
                  }
                },
              },
            ]}
          >
            <Input placeholder="请输入模块超时阈值(s)" allowClear />
          </Form.Item>
          <EditModuleTitle title={'消息接收'} />
          <Form.Item
            name={'principalErps'}
            label="责任人ERP"
            rules={[{ required: true, message: '请选择责任人ERP' }]}
          >
            <Select
              placeholder={'请选择责任人ERP'}
              options={dropDownMap.principalErps}
              labelInValue
              mode="multiple"
              filterOption={(input: any, option: any) => {
                const label: any = option?.label || '';
                return (
                  label.toString().toLowerCase().indexOf(input.toLowerCase()) >=
                  0
                );
              }}
              allowClear
              showSearch
            />
          </Form.Item>
        </Form>
      </CommonEdit>
    </>
  );
};

export default React.memo(AbnormalStartEdit);
