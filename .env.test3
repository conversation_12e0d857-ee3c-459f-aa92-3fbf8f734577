NODE_ENV=test3
PUBLIC_URL=/
port=3000
#变量以JDX_APP_ 开头定义


JDX_APP_FETCH_DATA_DOMAIN = 'jdxgateway-autotest.jdl.cn'
JDX_APP_LOCATION_HOST = 'jdxvehicle-ui-autotest.jdl.cn'


JDX_APP_PASSPORT_LOGIN_HOST = 'https://sso.jdl.cn/sso/login?ReturnUrl='
JDX_APP_JD_LOGOUT_HOST = 'https://sso.jd.com/exit?callback=?'
JDX_APP_OTA_URL=https://jdxota-autotest.jdl.cn
JDX_APP_MAP_EDITOR=jdxgateway-autotest.jdl.cn
JDX_APP_BI_URL=https://bi-autotest.jdl.cn


#跳转地图组链接
JDX_APP_MAP_GROUP=https://out-test-data-task.jd.com/#/warehouseBlueprint