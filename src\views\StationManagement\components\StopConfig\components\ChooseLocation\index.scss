@import "../../../../../../assets/css/index.scss";
.stopPoint-modal-container {
  display: flex;
  flex-direction: column;
  padding: 10px 20px;
}

.tencent-map-container {
  height: 510px;
}

.wms-map-container {
  height: 510px;

  .map {
    height: 460px;
  }

  .map-bottom {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background-color: #f1f1f1;
    height: 49px;
    color: $text-normal-color;
    font-size: 14px;
    text-align: center;
    padding: 0 20px;
  }
}

.head {
  height: 70px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  font-size: 20px;

  .search {
    width: 100%;
    display: flex;
    flex-direction: row;
    margin-right: 20px;
  }

  .function {
    font-size: 13px;
    display: flex;
    flex-direction: row;
    justify-content: center;
  }
}

.nextStepHead {
  height: 70px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  font-size: 20px;
  width: 100%;
}
.nextStepFooter {
  .bottom-btn {
    font-size: 13px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin: 30px 0;

    .cancel {
      margin-left: 30px;
      color: $text-normal-color;
      background: white;
      border-color: $text-normal-color;
    }
  }
}
