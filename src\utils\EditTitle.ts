import { PageType } from './enum';

export { PageType };
export const HardwareTypeTitle = {
  [PageType.ADD]: '新建硬件类型',
  [PageType.EDIT]: '编辑硬件类型',
  [PageType.READONLY]: '查看硬件类型',
};

export const FactoryTitle = {
  [PageType.ADD]: '新建厂商',
  [PageType.EDIT]: '编辑厂商',
  [PageType.READONLY]: '查看厂商',
};

export const GridTitle = {
  [PageType.ADD]: '新建箱体格口模板',
  [PageType.EDIT]: '编辑箱体格口模板',
  [PageType.READONLY]: '查看箱体格口模板',
};
export const HardwareModalTitle = {
  [PageType.ADD]: '新建硬件型号',
  [PageType.EDIT]: '编辑硬件型号',
  [PageType.READONLY]: '查看硬件型号',
};
export const SensorSolutionTitle = {
  [PageType.ADD]: '新建传感器方案',
  [PageType.EDIT]: '编辑传感器方案',
  [PageType.READONLY]: '查看传感器方案',
};
export const VehicleTypeTitle = {
  [PageType.ADD]: '新建车型信息',
  [PageType.EDIT]: '编辑车型信息',
  [PageType.READONLY]: '查看车型信息',
};

export const DeviceTitle = {
  [PageType.ADD]: '新建设备信息',
  [PageType.EDIT]: '编辑设备信息',
  [PageType.READONLY]: '查看设备信息',
};
export const ErrorCodeConfTitle = {
  [PageType.ADD]: '新建错误信息翻译',
  [PageType.EDIT]: '编辑错误信息翻译',
};
export const AbnormalStartTitle = {
  [PageType.ADD]: '新建开机异常模块',
  [PageType.EDIT]: '编辑开机异常模块',
};

export const InsuranceTitle = {
  [PageType.ADD]: '新建车辆保险',
  [PageType.EDIT]: '编辑车辆保险',
};
