/* eslint-disable */

/**
 *  * 提供了百度坐标（BD09）、国测局坐标（火星坐标，GCJ02）、和WGS84坐标系之间的转换  
 */
 var coordtransform = function() {
	// 定义一些常量
	var x_PI = 3.14159265358979324 * 3000.0 / 180.0;
	var PI = 3.1415926535897932384626;
	var a = 6378245.0;
	var ee = 0.00669342162296594323;
/**
 *    * 百度坐标系 (BD-09) 与 火星坐标系 (GCJ-02)的转换    * 即 百度 转 谷歌、高德    *
 * 
 * @param bd_lon    *
 * @param bd_lat    *
 * @returns {*[]}    
 */
	var bd09togcj02 = function bd09togcj02(bd_lon, bd_lat) {
		var bd_lon = +bd_lon;
		var bd_lat = +bd_lat;
		var x = bd_lon - 0.0065;
		var y = bd_lat - 0.006;
		var z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_PI);
		var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_PI);
		var gg_lng = z * Math.cos(theta);
		var gg_lat = z * Math.sin(theta);
		return [gg_lng, gg_lat];
	};

/**
 *    * 火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换    * 即谷歌、高德 转 百度    *
 * 
 * @param lng    *
 * @param lat    *
 * @returns {*[]}    
 */
	var gcj02tobd09 = function gcj02tobd09(lng, lat) {
		var lat = +lat;
		var lng = +lng;
		var z = Math.sqrt(lng * lng + lat * lat) + 0.00002
				* Math.sin(lat * x_PI);
		var theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * x_PI);
		var bd_lng = z * Math.cos(theta) + 0.0065;
		var bd_lat = z * Math.sin(theta) + 0.006;
		return [bd_lng, bd_lat];
	};

/**
 *    * WGS84转GCj02    *
 * 
 * @param lng    *
 * @param lat    *
 * @returns {*[]}    
 */
	var wgs84togcj02 = function wgs84togcj02(lng, lat) {
		var lat = +lat;
		var lng = +lng;
		if (out_of_china(lng, lat)) {
			return [lng, lat];
		} else {
			var dlat = transformlat(lng - 105.0, lat - 35.0);
			var dlng = transformlng(lng - 105.0, lat - 35.0);
			var radlat = lat / 180.0 * PI;
			var magic = Math.sin(radlat);
			magic = 1 - ee * magic * magic;
			var sqrtmagic = Math.sqrt(magic);
			dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI);
			dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI);
			var mglat = lat + dlat;
			var mglng = lng + dlng;
			return [mglng, mglat];
		}
	};

/**
 *    * GCJ02 转换为 WGS84    *
 * 
 * @param lng    *
 * @param lat    *
 * @returns {*[]}    
 */
	var gcj02towgs84 = function gcj02towgs84(lng, lat) {
		var lat = +lat;
		var lng = +lng;
		if (out_of_china(lng, lat)) {
			return [lng, lat];
		} else {
			var dlat = transformlat(lng - 105.0, lat - 35.0);
			var dlng = transformlng(lng - 105.0, lat - 35.0);
			var radlat = lat / 180.0 * PI;
			var magic = Math.sin(radlat);
			magic = 1 - ee * magic * magic;
			var sqrtmagic = Math.sqrt(magic);
			dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI);
			dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI);
			var mglat = lat + dlat;
			var mglng = lng + dlng;
			return [lng * 2 - mglng, lat * 2 - mglat];
		}
	};

	var transformlat = function transformlat(lng, lat) {
		var lat = +lat;
		var lng = +lng;
		var ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng
				* lat + 0.2 * Math.sqrt(Math.abs(lng));
		ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0
				* Math.sin(2.0 * lng * PI))
				* 2.0 / 3.0;
		ret += (20.0 * Math.sin(lat * PI) + 40.0 * Math.sin(lat / 3.0 * PI))
				* 2.0 / 3.0;
		ret += (160.0 * Math.sin(lat / 12.0 * PI) + 320
				* Math.sin(lat * PI / 30.0))
				* 2.0 / 3.0;
		return ret;
	};

	var transformlng = function transformlng(lng, lat) {
		var lat = +lat;
		var lng = +lng;
		var ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat
				+ 0.1 * Math.sqrt(Math.abs(lng));
		ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0
				* Math.sin(2.0 * lng * PI))
				* 2.0 / 3.0;
		ret += (20.0 * Math.sin(lng * PI) + 40.0 * Math.sin(lng / 3.0 * PI))
				* 2.0 / 3.0;
		ret += (150.0 * Math.sin(lng / 12.0 * PI) + 300.0
				* Math.sin(lng / 30.0 * PI))
				* 2.0 / 3.0;
		return ret;
	};

/**
 *    * 判断是否在国内，不在国内则不做偏移    *
 * 
 * @param lng    *
 * @param lat    *
 * @returns {boolean}    
 */
	var out_of_china = function out_of_china(lng, lat) {
		var lat = +lat;
		var lng = +lng;
		// 纬度3.86~53.55,经度73.66~135.05 
		return !(lng > 73.66 && lng < 135.05 && lat > 3.86 && lat < 53.55);
	};

	return {
		bd09togcj02 : bd09togcj02,
		gcj02tobd09 : gcj02tobd09,
		wgs84togcj02 : wgs84togcj02,
		gcj02towgs84 : gcj02towgs84
	};
};

// 根据点经纬度+方位角+距离计算到达点的经纬度
function destinationVincenty(lonlat, brng, dist) {
	var VincentyConstants = {
		a : 6378137,
		b : 6356752.3142,
		f : 1 / 298.257223563
	};
	var ct = VincentyConstants;
	var a = ct.a, b = ct.b, f = ct.f;
	var lon1 = lonlat.lon*1;//乘一（*1）是为了确保经纬度的数据类型为number
	var lat1 = lonlat.lat*1;
	var s = dist;
	var alpha1 = rad(brng);
	var sinAlpha1 = Math.cos(alpha1);
	var cosAlpha1 = Math.sin(alpha1);
	var tanU1 = (1-f) * Math.tan(rad(lat1));
	var cosU1 = 1 / Math.sqrt((1 + tanU1*tanU1)), sinU1 = tanU1*cosU1;
	var sigma1 = Math.atan2(tanU1, cosAlpha1);
	var sinAlpha = cosU1 * sinAlpha1;
	var cosSqAlpha = 1 - sinAlpha*sinAlpha;
	var uSq = cosSqAlpha * (a*a - b*b) / (b*b);
	var A = 1 + uSq/16384*(4096+uSq*(-768+uSq*(320-175*uSq)));
	var B = uSq/1024 * (256+uSq*(-128+uSq*(74-47*uSq)));
	var sigma = s / (b*A), sigmaP = 2*Math.PI;
	while (Math.abs(sigma-sigmaP) > 1e-12) {
		var cos2SigmaM = Math.cos(2*sigma1 + sigma);
		var sinSigma = Math.sin(sigma);
		var cosSigma = Math.cos(sigma);
		var deltaSigma = B*sinSigma*(cos2SigmaM+B/4*(cosSigma*(-1+2*cos2SigmaM*cos2SigmaM)- B/6*cos2SigmaM*(-3+4*sinSigma*sinSigma)*(-3+4*cos2SigmaM*cos2SigmaM)));
		sigmaP = sigma;
		sigma = s / (b*A) + deltaSigma;
	}
	var tmp = sinU1*sinSigma - cosU1*cosSigma*cosAlpha1;
	var lat2 = Math.atan2(sinU1*cosSigma + cosU1*sinSigma*cosAlpha1,(1-f)*Math.sqrt(sinAlpha*sinAlpha + tmp*tmp));
	var lambda = Math.atan2(sinSigma*sinAlpha1, cosU1*cosSigma - sinU1*sinSigma*cosAlpha1);
	var C = f/16*cosSqAlpha*(4+f*(4-3*cosSqAlpha));
	var L = lambda - (1-C) * f * sinAlpha * (sigma + C*sinSigma*(cos2SigmaM+C*cosSigma*(-1+2*cos2SigmaM*cos2SigmaM)));
	var revAz = Math.atan2(sinAlpha, -tmp);// final bearing
	
	var lon_destina = lon1*1+deg(L);
	
	//var num_lon_dest = lon_destina*1;
	
	//var lon_destina = new Number;
	
	var lonlat_destination = {lon: lon_destina, lat: deg(lat2)};
	
	return lonlat_destination;
}

function destinationVincenty2(lonlat_destinations,lonlat_dis){
	var new_x = lonlat_destinations.lat + lonlat_dis.lat;
	var new_y = lonlat_destinations.lon + lonlat_dis.lon;
	return {lon: new_y, lat: new_x};
}
function destinationVincenty2dis(lonlat_destinations,lonlat_destinatione){
	var _x = lonlat_destinatione.lat - lonlat_destinations.lat;
	var _y = lonlat_destinatione.lon - lonlat_destinations.lon;
	return {lon: _y, lat: _x};
}
/**
* 度换成弧度
* @param  {Float} d  度
* @return {[Float}   弧度
*/
function rad(d){
	return d * Math.PI / 180.0;
}

/**
* 弧度换成度
* @param  {Float} x 弧度
* @return {Float}   度
*/
function deg(x) {
	return x*180/Math.PI;
} 

/**
 已知某一点坐标，旋转角度，长度，求另一点坐标
 */
function calculateCoordinatePoint(originPoint, degree, len){
    var rotate = (degree - 90 + 360) % 360; //这里是因为一开始以y轴下方为0度的
    var point = {
        x: len,
        y: 0
    };
 //计算某一点旋转后的坐标点，这里假设传入的点为原点
    var relativeOriginPoint = calculateRotate(point, rotate);
//计算相对坐标系的坐标
    var points = calculateCoordinateRelativePoint(originPoint, relativeOriginPoint);
    return points;
}
/**
 * 计算某一点旋转后的坐标点
 * @param point
 * @param degree
 */
function calculateRotate(point, degree){
    var x = point.x * Math.cos(degree * Math.PI / 180) + point.y * Math.sin(degree * Math.PI / 180);
    var y = -point.x * Math.sin(degree * Math.PI / 180) + point.y * Math.cos(degree * Math.PI / 180);
    var relativeOriginPoint = {
        x: Math.round(x * 1000000000) / 1000000000,
        y: Math.round(y * 1000000000) / 1000000000
    };
    return relativeOriginPoint;
}

/**
 * 计算相对坐标系的坐标
 */
function calculateCoordinateRelativePoint(origin, relativeOriginPoint){
    var x = relativeOriginPoint.x + origin.x;
    var y = relativeOriginPoint.y + origin.y;
    var points = {
        x: Math.round(x * 1000000000) / 1000000000,
        y: Math.round(y * 1000000000) / 1000000000
    };
    return points;
}