import React, { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { CommonForm, CommonTable, useTableData } from '@jd/x-coreui';
import { Columns, SearchConfig, getShelfTypeConfig } from './constant';
import {
  addShelfType,
  deleteShelfTypeInfo,
  editShelfTypeInfo,
  getShelfTypeInfo,
  getShelfTypePageList,
} from '@/fetch/business/integrate';
import { showModal } from '@/components';
import { HttpStatusCode } from '@/fetch/core/constant';
import { message } from 'antd/lib';
import { Popconfirm } from 'antd';
import { isEqual } from 'lodash';
const initSearch = {
  pageNum: 1,
  pageSize: 10,
  shelfTypeName: null,
  doubleSide: null,
};
const ShlefTypeManage = () => {
  const formRef = useRef<any>(null);
  const [searchCondition, setSearchCondition] = useState(initSearch);
  const { tableData, reloadTable } = useTableData(
    searchCondition,
    getShelfTypePageList,
  );
  const navigator = useNavigate();

  const onSearchClick = (values: any) => {
    const newValue = {
      pageNum: 1,
      pageSize: 10,
      ...(values || {}),
    };
    if (!isEqual(newValue, searchCondition)) {
      setSearchCondition(newValue);
    } else {
      reloadTable();
    }
  };

  const onResetClick = () => {
    setSearchCondition(initSearch);
    formRef.current.setFieldsValue({
      shelfTypeName: null,
      doubleSide: null,
    });
  };
  const showCreateFormModal = () => {
    let formRef: any = null;
    showModal({
      title: '新建上装类型',
      content: (
        <CommonForm
          defaultValue={{
            doubleSide: 1,
          }}
          formConfig={getShelfTypeConfig('add')}
          getFormInstance={(ref: any) => {
            formRef = ref;
          }}
        />
      ),
      footer: {
        showCancel: true,
        showOk: true,
        cancelFunc: (cb) => cb(),
        okFunc: async (cb) => {
          try {
            const values = await formRef.validateFields();
            addShelfType(values).then((res) => {
              if (res?.code === HttpStatusCode.Success) {
                message.success('新建成功');
                reloadTable();
                cb();
              } else {
                message.error(res?.message || '新建失败');
              }
            });
          } catch (e) {}
        },
      },
    });
  };

  const showEditFormModal = (record: any) => {
    getShelfTypeInfo(record.shelfTypeId).then((res) => {
      if (res?.code === HttpStatusCode.Success) {
        let formRef: any = null;
        showModal({
          title: '编辑上装类型',
          content: (
            <CommonForm
              defaultValue={{
                ...(res?.data || {}),
              }}
              formConfig={getShelfTypeConfig('edit')}
              getFormInstance={(ref: any) => {
                formRef = ref;
                formRef.setFieldsValue(res?.data);
              }}
            />
          ),
          footer: {
            showCancel: true,
            showOk: true,
            cancelFunc: (cb) => cb(),
            okFunc: async (cb) => {
              try {
                const values = await formRef.validateFields();
                editShelfTypeInfo({
                  shelfTypeId: record.shelfTypeId,
                  length: values.length,
                  width: values.width,
                  height: values.height,
                }).then((res) => {
                  if (res?.code === HttpStatusCode.Success) {
                    message.success('编辑成功');
                    reloadTable();
                    cb();
                  } else {
                    message.error(res?.message || '新建失败');
                  }
                });
              } catch (e) {}
            },
          },
        });
      } else {
        message.error(res?.message || '数据获取失败');
      }
    });
  };

  const handleDelete = (shelfTypeId: number) => {
    deleteShelfTypeInfo(shelfTypeId).then((res) => {
      if (res?.code === HttpStatusCode.Success) {
        message.success('删除成功');
        reloadTable();
      } else {
        message.error(res?.message || '删除失败');
      }
    });
  };
  const formatColumns = () => {
    return Columns.map((col: any) => {
      switch (col.dataIndex) {
        case 'operate':
          col.render = (value: any, record: any) => {
            return (
              <>
                <a onClick={showEditFormModal.bind(null, record)}>编辑</a>
                <Popconfirm
                  trigger={'click'}
                  title="确认删除吗？"
                  onConfirm={handleDelete.bind(null, record.shelfTypeId)}
                >
                  <a>删除</a>
                </Popconfirm>
              </>
            );
          };
          break;
        case 'bindDeviceTypeCount':
          col.render = (value: any, record: any) => {
            return (
              <a
                onClick={() => {
                  navigator('/app/deviceShelfTypeManage')
                }}
              >
                {value}
              </a>
            );
          };
          break;
      }
      return col;
    });
  };
  return (
    <div className="shelf-type-manage" style={{ marginTop: '10px' }}>
      <CommonForm
        formConfig={SearchConfig}
        layout={'inline'}
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={onResetClick}
        getFormInstance={(ref: any) => {
          formRef.current = ref;
        }}
      />
      <CommonTable
        columns={formatColumns()}
        rowKey="shelfTypeId"
        middleBtns={[
          {
            title: '新建',
            onClick: showCreateFormModal,
          },
        ]}
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        onPageChange={(paginationData: any) => {
          setSearchCondition({
            ...searchCondition,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      />
    </div>
  );
};

export default ShlefTypeManage;
