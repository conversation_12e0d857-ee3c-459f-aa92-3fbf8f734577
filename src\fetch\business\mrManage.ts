import { YESNO } from '@/utils/enum';
import { request } from '../core';

export class Mr<PERSON>anageA<PERSON> {
  fetchDomain = `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}`;
  // 分页查询列表
  fetchTableList({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: any;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/merge_request_test/merge_request_get_page_list',
      urlParams: {
        pageNum,
        pageSize,
      },
      body: {
        mrBranch: searchForm?.mrBranch?.value,
        mrStatus: searchForm?.mrStatus?.value,
        testResultList: searchForm.testResultList,
        startTime: searchForm.startTime,
        endTime: searchForm.endTime,
        mrNumber: searchForm.mrNumber,
        fixJiraKey: searchForm.fixJiraKey,
        isSubmitTest: searchForm.isSubmitTest,
        module: searchForm.module,
        testPassStartDate: searchForm.testPassStartDate,
        testPassEndDate: searchForm.testPassEndDate,
        mrReleaseStartDate: searchForm.mrReleaseStartDate,
        mrReleaseEndDate: searchForm.mrReleaseEndDate,
        isDependOnConf: searchForm.isDependOnConf,
        isSpecificTest: searchForm.isSpecificTest,
        specificTestName: searchForm.specificTestName,
        developer: searchForm.developer,
      },
    };
    return request(options);
  }

  batchToBeReleased(mrIdList: any[]) {
    return request({
      path: '/k2/management/merge_request_test/batch_edit_to_be_release',
      method: 'POST',
      body: mrIdList,
    });
  }
  batchReleased(param: any) {
    return request({
      path: '/k2/management/merge_request_test/batch_edit_released',
      method: 'PUT',
      body: param,
    });
  }
  getRoverVersion = () => {
    return request({
      path: '/ota/web/application_version_get_list',
      method: 'POST',
      body: {
        appName: 'rover',
        enable: 1,
      },
    });
  };

  //  编辑
  submitJiraInfo(requestBody: Object) {
    const options: RequestOptions = {
      method: 'PUT',
      path: '/k2/management/merge_request_test/merge_request_edit',
      body: requestBody,
    };
    return request(options);
  }
  // 获取详情
  fetchDetail(mrId: number | string) {
    const options: RequestOptions = {
      method: 'GET',
      path: `/k2/management/merge_request_test/merge_request_test_get_detail/${mrId}`,
    };
    return request(options);
  }
  // 获取一段时间内的合并请求
  updataMR() {
    const options: RequestOptions = {
      method: 'GET',
      path: '/k2/management/merge_request_test/merge_request_info_get_list',
    };
    return request(options);
  }
  // 导出选中的合并请求测试信息
  exportMR(mrIds: any[]) {
    const options: RequestOptions = {
      method: 'GET',
      path: '/k2/management/merge_request_test/merge_request_info_list_export',
      urlParams: {
        // mrIds,
      },
    };
    return request(options);
  }

  // 同步测试结果同步至JIRA
  syncJira(mrId: number) {
    const options: RequestOptions = {
      method: 'GET',
      path: `/k2/management/merge_request_test/merge_request_sync_jira/${mrId}`,
    };
    return request(options);
  }
  getMrBranchList() {
    const options: RequestOptions = {
      method: 'GET',
      path: '/k2/management/merge_request_test/get_branch_list',
    };
    return request(options);
  }

  getMrDetailByMrId(mrId: number | string) {
    const options: RequestOptions = {
      method: 'GET',
      path: `/k2/management/merge_request_test/merge_request_test_get_detail/${mrId}`,
    };
    return request(options);
  }
  getExportFiledList() {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/k2/management/merge_request_test/getExportFiledList`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
    };
    return request(options);
  }
  exportExcel(params: { mrIds: any[]; exportFields: any[] }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/merge_request_test/merge_request_info_list_export',
      body: params,
    };
    return request(options);
  }
  updateAttentionLevel(params: { attentionLevel: string; mrIds: any[] }) {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/k2/management/merge_request_test/updateAttentionLevel`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: params,
    };
    return request(options);
  }
}
