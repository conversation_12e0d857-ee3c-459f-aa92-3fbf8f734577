import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { DEFAULT_PAGE } from '@/utils/constant';
import { YESNO } from '@/utils/enum';
import { useTableData } from '@/components/CommonTable/useTableData';
import { StationFetchApi } from '@/fetch/business';
import { CommonForm, CommonTable, FormConfig } from '@/components';
import { StopConfigColumns } from '../../utils/column';
import { RootState } from '@/redux/store';
import {
  removeSearchValues,
  saveSearchValues,
} from '@/redux/reducer/searchForm';
import { Modal, Popconfirm, message } from 'antd';
import { HttpStatusCode } from '@/fetch/core/constant';
import { ColumnsType } from 'antd/es/table';
import { StopRequest, StopResponse } from '@/types';
import { useCommonDropDown } from '@/utils/hooks';
import { formatOptions } from '@/utils/utils';
import './index.scss';
import { AnyFunc } from '@/global';
import { isEqual } from 'lodash';

const fetchApi = new StationFetchApi();
interface Props {
  stationBaseId: number;
  tableKey: string;
  setTableKey: AnyFunc;
}
const StopConfig = (props: Props) => {
  const { stationBaseId, tableKey, setTableKey } = props;
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const [updateThirdStationLoading, setUpdateThirdStationLoading] =
    useState<boolean>(false);
  const initSearchCondition: StopRequest = {
    searchForm: {
      stationBaseId: stationBaseId,
      stopName: null,
      stopType: null,
      enable: null,
    },
    ...DEFAULT_PAGE,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValues.routeName === location.pathname
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );
  const { tableData, loading } = useTableData<StopRequest, StopResponse>(
    searchCondition,
    fetchApi.getStopTable,
    tableKey,
  );
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
      setSelectedRowKeys(selectedRowKeys);
    },
  };
  const dropDownData = useCommonDropDown(['ENABLE', 'STOP_TYPE']);
  const stopConfigSearchConfig: FormConfig = {
    fields: [
      {
        fieldName: 'stopName',
        label: '停靠点名称',
        placeholder: '请输入停靠点',
        type: 'input',
      },
      {
        fieldName: 'stopType',
        label: '停靠点类型',
        placeholder: '请选择停靠点类型',
        type: 'select',
        options: formatOptions(dropDownData.stopTypeList),
        labelInValue: false,
      },
      {
        fieldName: 'enable',
        label: '停靠点状态',
        placeholder: '请选择停靠点状态',
        type: 'select',
        options: formatOptions(dropDownData.enableList),
        labelInValue: false,
      },
    ],
  };
  // 更新停靠点状态
  const updateStopStatus = async (
    record: any,
    updateStatus: number,
    type: string,
  ) => {
    const idList = type === 'single' ? [record.id] : record;
    const response: any = await fetchApi.changeStopStatus({
      stopIdList: idList,
      enable: updateStatus,
    });
    if (response.code === HttpStatusCode.Success) {
      setSelectedRowKeys([]);
      message.success('操作成功！');
      setTableKey(new Date().getMilliseconds().toString());
    } else if (updateStatus === YESNO.YES) {
      Modal.warning({
        width: 600,
        title: '提示',
        content: response.message,
      });
    } else {
      message.error('数据请求失败');
    }
  };
  const formatColumns = (columns: ColumnsType<StopResponse>) => {
    return columns.map((item) => {
      if ('dataIndex' in item) {
        switch (item.dataIndex) {
          case 'rowIndex':
            return {
              ...item,
              render: (
                text: string | number,
                record: StopResponse,
                index: number,
              ) => {
                return `${
                  (searchCondition.pageNum - 1) * searchCondition.pageSize +
                  index +
                  1
                }`;
              },
            };
          case 'waitingTime':
            return {
              ...item,
              render: (
                text: string | number,
                record: StopResponse,
                index: number,
              ) => {
                return (
                  <div style={{ color: 'blue' }}> {record.waitingTime} </div>
                );
              },
            };
          case 'enableName':
            return {
              ...item,
              render: (
                text: string | number,
                record: StopResponse,
                index: number,
              ) => {
                const statusStyle =
                  record.enable === YESNO.YES ? 'enable' : 'unenable';
                return <div className={statusStyle}>{record.enableName}</div>;
              },
            };
          case 'operation':
            return {
              ...item,
              render: (
                text: string | number,
                record: StopResponse,
                index: number,
              ) => {
                const statusStyle =
                  record.enable === YESNO.NO ? 'enable' : 'unenable';
                const statusLabel =
                  record.enable === YESNO.YES ? '停用' : '启用';
                const msg =
                  record.enable === YESNO.YES
                    ? '停靠点停用后，解绑停靠点与车辆关系，请确认是否停用停靠点？'
                    : '确认启用吗?';
                return (
                  <div className="operate">
                    <Popconfirm
                      placement="left"
                      title={msg}
                      onConfirm={() =>
                        updateStopStatus(
                          record,
                          record.enable === YESNO.YES ? YESNO.NO : YESNO.YES,
                          'single',
                        )
                      }
                      okText="确定"
                      cancelText="取消"
                      overlayStyle={{ maxWidth: 800 }}
                    >
                      <a className={statusStyle}> {statusLabel} </a>
                    </Popconfirm>
                    <a
                      onClick={() => {
                        dispatch(
                          saveSearchValues({
                            routeName: location.pathname,
                            searchValues: searchCondition,
                          }),
                        );
                        navigator(
                          `/app/stationManagement/detail/editStopPoint?stationBaseId=${stationBaseId}&type=edit&stopId=${record.id}`,
                        );
                      }}
                    >
                      编辑/配置
                    </a>
                    <a
                      onClick={() => {
                        dispatch(
                          saveSearchValues({
                            routeName: location.pathname,
                            searchValues: searchCondition,
                          }),
                        );
                        navigator(
                          `/app/stationManagement/detail/addStopPoint?stationBaseId=${stationBaseId}&type=add&stopId=${record.id}`,
                        );
                      }}
                    >
                      复制并新建
                    </a>
                  </div>
                );
              },
            };
          default:
            return {
              ...item,
              render: (text: string | number) => `${text || '-'}`,
            };
        }
      }
    });
  };
  const onSearchClick = (val) => {
    const data = {
      searchForm: {
        ...searchCondition.searchForm,
        ...val,
      },
      pageNum: 1,
      pageSize: 10,
    };
    if (isEqual(data, searchCondition)) {
      setTableKey(new Date().getMilliseconds().toString());
    } else {
      setSearchCondition(data);
    }
  };
  const middleBtns: any[] = [
    {
      show: true,
      title: '新建停靠点',
      key: 'addStop',
      onClick: () => {
        navigator(
          `/app/stationManagement/detail/addStopPoint?stationBaseId=${stationBaseId}&type=add`,
        );
      },
    },
    {
      show: true,
      title: '批量启用',
      key: 'enableStop',
      onClick: () => {
        if (selectedRowKeys.length) {
          updateStopStatus(selectedRowKeys, YESNO.YES, 'batch');
        } else {
          message.error('请至少选择一条数据进行提交');
        }
      },
      enablePopConfirm: true,
      popConfirmContent: '是否批量启用停靠点？',
    },
    {
      title: '一键更新三方站点',
      key: 'updateThirdStation',
      loading: updateThirdStationLoading,
      onClick: async () => {
        try {
          (window as any).__qd__ &&
            (window as any).__qd__.click({
              cls: 'app_stationManagement_detail|updateThirdStationBtn',
            });
        } catch (e) {
          console.log(e);
        }

        try {
          setUpdateThirdStationLoading(true);
          const res = await fetchApi.updateThirdStation(stationBaseId);
          if (res.code === HttpStatusCode.Success) {
            setTableKey(new Date());
          } else {
            message.error(res.message);
          }
        } catch (e) {
          console.log(e);
        } finally {
          setUpdateThirdStationLoading(false);
        }
      },
    },
  ];
  return (
    <>
      <CommonForm
        formConfig={stopConfigSearchConfig}
        defaultValue={searchCondition.searchForm}
        layout="inline"
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={() => setSearchCondition({ ...initSearchCondition })}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list || [],
          totalPage: tableData?.pages,
          totalNumber: tableData?.total,
        }}
        rowSelection={rowSelection}
        columns={formatColumns(StopConfigColumns)}
        loading={loading}
        rowKey={'id'}
        middleBtns={middleBtns}
        searchCondition={searchCondition}
        onPageChange={(paginationData: any) => {
          setSearchCondition({
            ...searchCondition,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      />
    </>
  );
};
export default React.memo(StopConfig);
