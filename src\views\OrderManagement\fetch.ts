import { request } from '@/fetch/core';

export class OrderManageFetchApi {
  fetchPhoneNumber(value: any) {
    const params = {
      path: `/k2/management/order/get_phone`,
      method: 'POST',
      body: value,
    };
    return request(params);
  }

  fetchStopPoint(value: any) {
    const params = {
      path: `/k2/management/station_stop/get_stop_list_by_station_base_id_list`,
      method: 'POST',
      body: value,
    };
    return request(params);
  }

  fetchOrderList(params: any) {
    const options: RequestOptions = {
      method: 'POST',
      path: `/k2/management/order/get_page_list`,
      urlParams: {
        pageNum: params.page,
        pageSize: params.size,
      },
      body: params.searchForm,
    };
    return request(options);
  }

  fetchOrderTotal(params: any) {
    const options: RequestOptions = {
      method: 'POST',
      path: `/k2/management/order/get_original_order_total`,
      urlParams: {
        pageNum: params.page,
        pageSize: params.size,
      },
      body: params.searchForm,
    };
    return request(options);
  }

  fetchSourceStatusDelivery() {
    return request({
      path: `/k2/management/order/order_source_status_delivery_model_list_get`,
      method: 'GET',
    });
  }

  fetchVehicleList() {
    return request({
      path: `/k2/management/get_vehicle_list`,
      method: 'GET',
    });
  }

  fetchOrderDetail(params: any) {
    return request({
      path: `/k2/management/order/get_detail`,
      method: 'GET',
      urlParams: {
        id: params,
      },
    });
  }

  public fetchSubOrder = (params: { batchCode: string }) => {
    return request({
      path: '/k2/management/order/get_sub_order',
      method: 'GET',
      urlParams: {
        batchCode: params.batchCode,
      },
    });
  };

  fetchBDropOffDetail(params: any) {
    return request({
      path: '/k2/management/order/get_box_code_detail',
      method: 'GET',
      urlParams: {
        boxCode: params,
      },
    });
  }
}
