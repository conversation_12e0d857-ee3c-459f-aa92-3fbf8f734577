import { FormConfig } from '@jd/x-coreui';
import { createDefaultColumnsState } from '@jd/x-coreui/es/components/CommonTable/columnUtils';
import { CommonApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { DeployPlanSession } from '@/utils/enum';

const fetchApi = new CommonApi();

export enum RecordMode {
  VEHICLE = 'VEHICLE',
  STATION = 'STATION',
}
export const SearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'planNumber',
      label: '计划编号',
      placeholder: '请输入计划编号',
      type: 'input',
      allowClear: true,
      clstag: 'app_deployPlan|searchForm_planNumber',
    },
    {
      fieldName: 'planType',
      label: '计划类型',
      placeholder: '请选择计划类型',
      type: 'select',
      showSearch: true,
      allowClear: true,
      labelInValue: false,
      options: [],
      clstag: 'app_deployPlan|searchForm_planType',
    },
    {
      fieldName: 'stationInfo',
      label: '省市站',
      placeholder: '请选择省市站信息',
      type: 'cascader',
      showSearch: true,
      allowClear: true,
      options: [],
      mapRelation: { label: 'name', value: 'id', children: 'children' },
      clstag: 'app_deployPlan|searchForm_stationInfo',
    },
    {
      fieldName: 'currentStage',
      label: '计划阶段',
      placeholder: '请选择计划阶段/可多选',
      type: 'select',
      showSearch: true,
      allowClear: true,
      labelInValue: false,
      multiple: true,
      options: [],
      clstag: 'app_deployPlan|searchForm_currentStage',
    },
    {
      fieldName: 'creatorErp',
      label: '计划创建人',
      placeholder: '请输入计划创建人',
      type: 'input',
      allowClear: true,
      clstag: 'app_deployPlan|searchForm_creatorErp',
    },
    {
      fieldName: 'currentExecutorErp',
      label: '当前执行人',
      placeholder: '请输入当前执行人',
      type: 'input',
      allowClear: true,
      clstag: 'app_deployPlan|searchForm_currentExecutorErp',
    },
  ],
  linkRules: {
    fetchData: [
      {
        linkFieldName: 'currentStage',
        rule: 'fetchData',
        fetchFunc: async (val) => {
          const res = await fetchApi.getCommonDropDown({
            keyList: ['STAGE_TYPE'],
          });
          if (
            res.code === HttpStatusCode.Success &&
            Array.isArray(res?.data?.stageTypeList)
          ) {
            return res.data.stageTypeList.map((v) => ({
              label: v.name,
              value: v.code,
            }));
          }
          return [];
        },
      },
      {
        linkFieldName: 'planType',
        rule: 'fetchData',
        fetchFunc: async (val) => {
          const res = await fetchApi.getCommonDropDown({
            keyList: ['PLAN_TYPE'],
          });
          if (
            res.code === HttpStatusCode.Success &&
            Array.isArray(res?.data?.planTypeList)
          ) {
            return res.data.planTypeList.map((v) => ({
              label: v.name,
              value: v.code,
            }));
          }
          return [];
        },
      },
      {
        linkFieldName: 'stationInfo',
        rule: 'fetchData',
        fetchFunc: async (val) => {
          const res = await fetchApi.getStationDepartment({});
          if (res.code === HttpStatusCode.Success) {
            return res.data;
          }
          return [];
        },
      },
    ],
  },
};

export const TableColumns: any[] = [
  {
    title: '站点',
    width: 140,
    dataIndex: 'stationName',
    align: 'center',
  },
  {
    title: '计划编号',
    width: 200,
    dataIndex: 'planNumber',
    align: 'center',
  },
  {
    title: '计划类型',
    width: 130,
    dataIndex: 'planTypeName',
    align: 'center',
  },
  {
    title: '当前阶段',
    width: 130,
    dataIndex: 'currentStage',
    align: 'center',
  },
  {
    title: '当前执行人',
    width: 130,
    dataIndex: 'currentExecutorErp',
    align: 'center',
  },
  {
    title: '持续天数',
    width: 90,
    dataIndex: 'durationDays',
    align: 'center',
  },
  {
    title: '车数',
    width: 60,
    dataIndex: 'providedVehicleCount',
    align: 'center',
  },
  {
    title: '车号确认完成日期',
    width: 150,
    dataIndex: `${DeployPlanSession.VEHICLE_CONFIRMATION}/stageCompleteDate`,
    align: 'center',
  },
  {
    title: '车辆保险完成日期',
    width: 150,
    dataIndex: `${DeployPlanSession.VEHICLE_INSURANCE}/stageCompleteDate`,
    align: 'center',
  },
  {
    title: '车辆到站日期',
    width: 150,
    dataIndex: `${DeployPlanSession.VEHICLE_ALLOCATION}/stageCompleteDate`,
    align: 'center',
  },
  {
    title: '确认路线完成日期',
    width: 150,
    dataIndex: `${DeployPlanSession.ROUTE_CONFIRMATION}/stageCompleteDate`,
    align: 'center',
  },
  {
    title: '发起人核实完成日期',
    width: 160,
    dataIndex: `${DeployPlanSession.INITIATOR_VERIFICATION}/stageCompleteDate`,
    align: 'center',
  },
  {
    title: '地图采集完成日期',
    width: 150,
    dataIndex: `${DeployPlanSession.MAP_COLLECTION}/stageCompleteDate`,
    align: 'center',
  },
  {
    title: '地图制作完成日期',
    width: 150,
    dataIndex: `${DeployPlanSession.MAP_PRODUCTION}/stageCompleteDate`,
    align: 'center',
  },
  {
    title: '线路试跑完成日期',
    width: 150,
    dataIndex: `${DeployPlanSession.ROUTE_TEST_RUN}/stageCompleteDate`,
    align: 'center',
  },
  {
    title: '交付运营日期',
    width: 120,
    dataIndex: `${DeployPlanSession.STATION_DELIVERY}/stageCompleteDate`,
    align: 'center',
  },
  {
    title: '创建人',
    width: 115,
    dataIndex: 'creatorErp',
    align: 'center',
  },
  {
    title: '创建时间',
    width: 110,
    dataIndex: 'createDate',
    align: 'center',
  },
  {
    title: '操作',
    width: 120,
    dataIndex: 'operation',
    align: 'center',
  },
];

// 默认隐藏的列
export const defaultHiddenColumns = ['planNumber'];

// 默认固定在左侧的列
export const defaultLeftFixedColumns = ['stationName'];

// 默认固定在右侧的列
export const defaultRightFixedColumns = ['operation'];

// 创建默认列配置
export const getDefaultColumnsState = () => {
  return createDefaultColumnsState(
    TableColumns,
    defaultHiddenColumns,
    defaultLeftFixedColumns,
    defaultRightFixedColumns,
  );
};
