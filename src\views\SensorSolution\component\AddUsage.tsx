/* eslint-disable no-unused-vars */

import { Form, FormInstance, Table, message, Popconfirm } from 'antd';
import React, { useEffect, useState } from 'react';
import SensorEditModal from './SensorEditModal';
import { ButtonType, CustomButton } from '@/components';
import { SensorSolutionApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';

interface SensorCombination {
  id: number | null; // 方案id
  hardwareTypeId: number; // 传感器设备id
  hardwareTypeName: string; // 传感器设备名称
  hardwareTypeUsageId: number; // 用途id
  hardwareTypeUsageName: string; // 用途名称
  hardwareModelId: number; // 硬件id
  hardwareModelName: string; // 硬件名称
  hardwareModelTypeName: string; // 硬件型号
  useNumber: number; // 使用个数
  enable: number;
  newAdd?: boolean;
}

const AddUsage = ({
  disable,
  form,
  initValues,
  schemeId,
}: {
  disable?: boolean;
  form: FormInstance;
  initValues?: SensorCombination[];
  schemeId?: any;
}) => {
  const fetchApi = new SensorSolutionApi();
  const usageNameColums: any[] = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
      width: 90,
      ellipsis: true,
      render: (text: any, record: any, index: number) => index + 1,
    },
    {
      title: '传感器硬件类型',
      dataIndex: 'hardwareTypeName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '用途',
      dataIndex: 'hardwareTypeUsageName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '传感器硬件型号',
      dataIndex: 'hardwareModelName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '型号',
      dataIndex: 'hardwareModelModel',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '使用个数',
      dataIndex: 'useNumber',
      width: 130,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'operate',
      align: 'center',
      width: 150,
      render: (params: any) => {
        return (
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-around',
            }}
          >
            <a
              onClick={() => {
                setShowModal({
                  combination: params,
                  show: true,
                });
              }}
            >
              编辑
            </a>
            <Popconfirm
              title="是否确认删除此条方案"
              onConfirm={async () => {
                deleteScheme(params.id, params.newAdd);
              }}
            >
              <a style={{ color: 'red' }}>删除</a>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  const [usageList, setUsageList] = useState<SensorCombination[]>([]);

  const [showUsageModal, setShowModal] = useState<{
    show: boolean;
    combination: SensorCombination | null;
  }>({
    show: false,
    combination: null,
  });

  const deleteScheme = async (id: any, newAdd: any) => {
    if (newAdd) {
      setUsageList([...usageList.filter((item) => item.id != id)]);
    } else {
      const response: any = await fetchApi.canDelete({ id: schemeId });
      if (response.code === HttpStatusCode.Success) {
        setUsageList([
          ...usageList.map((item) => {
            if (item.id === id) {
              item.enable = 0;
            }
            return item;
          }),
        ]);
      } else {
        message.error(response.message);
      }
    }
  };

  const makeTableDatasource = () => {
    return usageList
      .map((item, index) => {
        return {
          ...item,
          operate: item,
          key: item.id,
        };
      })
      .filter(
        (item) =>
          (item.newAdd === undefined && item.enable === 1) || item.newAdd,
      );
  };

  const checkUsageNameList = (_: any, value: any) => {
    if (usageList.length > 0) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('请添加用途'));
  };

  const checkRepeat = (newValue: any) => {
    const { combination } = showUsageModal;
    if (combination) {
      const filteredList = usageList.filter(
        (item) =>
          item.id !== newValue.id &&
          `${item.hardwareTypeId}-${item.hardwareModelId}-${item.hardwareTypeUsageId}` ===
            `${newValue.hardwareTypeId}-${newValue.hardwareModelId}-${newValue.hardwareTypeUsageId}`,
      );
      if (filteredList.length > 1) {
        return true;
      }
    } else {
      const filteredList = usageList.filter(
        (item) =>
          `${item.hardwareTypeId}-${item.hardwareModelId}-${item.hardwareTypeUsageId}` ===
          `${newValue.hardwareTypeId}-${newValue.hardwareModelId}-${newValue.hardwareTypeUsageId}`,
      );
      if (filteredList.length === 1) {
        return true;
      }
    }
    return false;
  };

  const onModalSubmit = async (newValue: any) => {
    if (checkRepeat(newValue)) {
      message.error('新建方案已存在');
      return;
    }
    if (showUsageModal.combination) {
      setUsageList([
        ...usageList.map((item) => {
          let newItem = item;
          if (item.id === newValue.id) {
            newItem = {
              ...newValue,
              enable: 1,
            };
          }
          return newItem;
        }),
      ]);
    } else {
      setUsageList([...usageList.concat([newValue])]);
    }
    setShowModal({
      combination: null,
      show: false,
    });
  };

  const onModalCancel = () => {
    setShowModal({
      combination: null,
      show: false,
    });
  };

  useEffect(() => {
    if (initValues) {
      setUsageList(initValues);
    }
  }, [initValues]);

  useEffect(() => {
    form.setFieldsValue({
      usageList: usageList,
    });
  }, [usageList]);

  return (
    <Form.Item
      name="usageList"
      label="添加方案"
      required
      rules={[{ validator: checkUsageNameList }]}
    >
      <div>
        <div>
          {disable ? null : (
            <CustomButton
              disable={disable}
              title="新建"
              buttonType={ButtonType.PrimaryButton}
              onSubmitClick={() => {
                setShowModal({
                  ...showUsageModal,
                  show: true,
                });
              }}
            />
          )}
        </div>
        <div style={{ marginTop: '10px' }}>
          <Table
            pagination={false}
            size="small"
            bordered
            columns={usageNameColums.filter((item) =>
              disable ? item.dataIndex != 'operate' : true,
            )}
            dataSource={makeTableDatasource()}
          />
        </div>
        {showUsageModal.show ? (
          <SensorEditModal
            visable={showUsageModal.show}
            itemValue={showUsageModal.combination}
            onSubmit={onModalSubmit}
            onCancel={onModalCancel}
          />
        ) : null}
      </div>
    </Form.Item>
  );
};

export default React.memo(AddUsage);
