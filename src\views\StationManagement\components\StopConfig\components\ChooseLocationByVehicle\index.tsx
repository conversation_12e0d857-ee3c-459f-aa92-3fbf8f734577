/* eslint-disable require-jsdoc */
/* eslint-disable no-unused-vars */
import { Button, message, Modal, Select } from 'antd';
import LineString from 'ol/geom/LineString';
import Feature from 'ol/Feature.js';
import TileLayer from 'ol/layer/Tile.js';
import Point from 'ol/geom/Point.js';
import { Circle as CircleStyle, Fill, Stroke, Style } from 'ol/style.js';
import Polygon from 'ol/geom/Polygon.js';
import { XYZ, TileWMS } from 'ol/source.js';
import VectorLayer from 'ol/layer/Vector.js';
import VectorSource from 'ol/source/Vector.js';
import React, { Component, useEffect, useRef, useState } from 'react';
import CustomButton, { ButtonType } from '@/components/CustomButton';
import { StationFetchApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { calculateCoordinatePoint } from '@/utils/MapEditorTool/util';
import MapTools from '@/utils/MapEditorTool';
import { returnTileLayers, LayerIdEnum } from '@/utils/constant';
import './index.scss';
const win = window as any;
const { proj4, deg } = win;
export interface MapPosition {
  lon: number;
  lat: number;
  head: number;
}

const utm2wgs84 = (Lon: any, Lat: any, zoneNum: any) => {
  const utm = '+proj=utm +zone=' + zoneNum;
  const wgs84 = '+proj=longlat +ellps=WGS84 +datum=WGS84 +no_defs';
  return proj4(utm, wgs84, [Lon, Lat]);
};

const wgs842utm = (Lon: any, Lat: any, zoneNum: any) => {
  const utm = '+proj=utm +zone=' + zoneNum;
  const wgs84 = '+proj=longlat +ellps=WGS84 +datum=WGS84 +no_defs';
  return proj4(wgs84, utm, [Lon, Lat]);
};

const fetchApi = new StationFetchApi();
const ChooseLocationByVehicle = (props: any) => {
  const { visiable, station, parking, vehicleTypeData, onSubmit, onCancle } =
    props;
  const vehicleId = useRef<string | null>(null);
  const mapRef = useRef<any>(null);
  const zoneRef = useRef<any>(null);
  const [, forceUpdate] = useState(0);
  const _lonLatHead = useRef<any>({
    lon: parking.longitude ?? 0,
    lat: parking.latitude ?? 0,
    head: parking.heading ?? 0,
  });

  const [downListOptions, setListOptions] = useState<any[]>([]);
  const [loadingPosition, setLoadingPosition] = useState<boolean>(false);
  const [mapInfo, setMapInfo] = useState<any>(null);
  const getMapByPosition = async (lonlat: number[]) => {
    const res = await fetchApi.getMapByPosition({
      longitude: lonlat[0],
      latitude: lonlat[1],
    });
    setMapInfo(res?.data);
  };
  const _fetchVehicleList = async () => {
    const respData: any = await fetchApi.getVehicleListOfStation(station.code);
    if (respData.code === HttpStatusCode.Success) {
      const dynamicVehicleList = respData.data?.map(
        (item: any) => {
          return { label: item.deviceName, value: item.deviceBaseId };
        },
      );
      setListOptions(dynamicVehicleList);
    }
  };

  const _fetchVehiclePosition = async (vehicleId: string) => {
    setLoadingPosition(true);
    const respData: any = await fetchApi.getVehicleLocation(vehicleId);
    if (respData.code === HttpStatusCode.Success) {
      const data = respData.data;
      _lonLatHead.current = {
        lon: parseFloat(Number(data.lon).toFixed(8)),
        lat: parseFloat(Number(data.lat).toFixed(8)),
        head: parseFloat(Number(deg(data.heading)).toFixed(8)),
      };
      setLoadingPosition(false);
      mapRef.current.changeMapCenter([data.lon, data.lat]);
      getMapByPosition([data.lon, data.lat]);
      _markerUpdate({
        lon: data.lon,
        lat: data.lat,
        head: parseFloat(Number(deg(data.heading)).toFixed(8)),
      });
    } else {
      message.error('无法获取选中的车辆位置');
      setLoadingPosition(false);
    }
  };

  const initMap = () => {
    const { lat, lon } = _lonLatHead.current;
    mapRef.current = new MapTools({
      container: document.getElementById('vehicle-wmsmap-container')!,
      centerPoint: [lon ?? 0, lat ?? 0],
    });

    // 天地图矢量底图
    const tianMapLayer = new TileLayer({
      source: new XYZ({
        url: 'https://t{0-7}.tianditu.gov.cn/DataServer?x={x}&y={y}&l={z}&T=vec_c&tk=dd515bb481c4d4c7f5b9056229e36e68',
        crossOrigin: 'anonymous',
        projection: 'EPSG:4326',
        minZoom: 10,
        maxZoom: 18,
      }),
    });

    // 天地图注记图
    const tdtcvaLayer = new TileLayer({
      source: new XYZ({
        url: 'https://t{0-7}.tianditu.gov.cn/DataServer?T=cva_w&x={x}&y={y}&l={z}&tk=dd515bb481c4d4c7f5b9056229e36e68',
        crossOrigin: 'anonymous',
        minZoom: 10,
        maxZoom: 18,
      }),
    });

    mapRef.current.addLayers([
      {
        layer: tianMapLayer,
        layerId: LayerIdEnum.TIANMAP_LAYER,
      },
      {
        layer: tdtcvaLayer,
        layerId: LayerIdEnum.TDTCVA_LAYER,
      },
    ]);
  };

  const initMapEvents = () => {
    //移动点调整事件开关
    mapRef.current.map.on('pointerdown', function (event: any) {
      const pixel = mapRef.current.map.getEventPixel(event.originalEvent);
      const feature = mapRef.current.map.getFeaturesAtPixel(pixel, {
        hitTolerance: 3,
      });
      if (feature) {
        // 移动前如果点中图形要素了，需要判断是原点还是调整点；移动后有可能点中图形要素但不是原点或调整点的，此时判断状态值
        if (
          feature[0]?.values_?.name == 'anchor' ||
          mapRef.current.isAnchorMoved == 1
        ) {
          if (mapRef.current.isAnchorMoved == 1) {
            mapRef.current.isAnchorMoved = 0;
          } else {
            mapRef.current.isAnchorMoved = 1;
          }
        } else if (
          feature[0]?.values_?.name == 'endPnt' ||
          mapRef.current.isEndPntMoved == 1
        ) {
          if (mapRef.current.isEndPntMoved == 1) {
            mapRef.current.isEndPntMoved = 0;
          } else {
            mapRef.current.isEndPntMoved = 1;
          }
        }
        // 移动后可能没点中图形要素，这时用状态值判断
      } else if (mapRef.current.isAnchorMoved == 1) {
        if (mapRef.current.isAnchorMoved == 1) {
          mapRef.current.isAnchorMoved = 0;
        } else {
          mapRef.current.isAnchorMoved = 1;
        }
      } else if (mapRef.current.isEndPntMoved == 1) {
        if (mapRef.current.isEndPntMoved == 1) {
          mapRef.current.isEndPntMoved = 0;
        } else {
          mapRef.current.isEndPntMoved = 1;
        }
      }

      // 设置移动时的鼠标样式
      if (feature === null) {
        mapRef.current.map.getTargetElement().style.cursor = 'auto';
      } else if (
        feature[0]?.values_?.name === 'anchor' ||
        feature[0]?.values_?.name === 'endPnt'
      ) {
        mapRef.current.map.getTargetElement().style.cursor = 'move';
      }
    });
    //移动点调整事件
    mapRef.current.map.on('pointermove', function (event: any) {
      // 鼠标的坐标
      const pos = event.coordinate;
      if (mapRef.current.isAnchorMoved == 1) {
        _markerUpdate({
          lon: pos[0],
          lat: pos[1],
          head: _lonLatHead.current.head,
        });
        _lonLatHead.current = {
          ..._lonLatHead.current,
          lon: parseFloat(Number(pos[0]).toFixed(8)),
          lat: parseFloat(Number(pos[1]).toFixed(8)),
        };
        forceUpdate(Date.now());
      } else if (mapRef.current.isEndPntMoved == 1) {
        //调整点移动
        //修改坐标
        drawDragDirectionLine(pos[0], pos[1]);
        const pnts = mapRef.current.directionLineItem
          .getGeometry()
          .getCoordinates();
        zoneRef.current = pnts[0][0] / 6.0 + 31;
        const startPoint = wgs842utm(pnts[0][0], pnts[0][1], zoneRef.current);
        const endPnt = wgs842utm(pnts[1][0], pnts[1][1], zoneRef.current);
        let angles = Math.atan2(
          endPnt[1] - startPoint[1],
          endPnt[0] - startPoint[0],
        );
        angles = (angles * 180) / Math.PI; //转换为角度
        _lonLatHead.current.head = parseFloat(Number(angles).toFixed(8));
        forceUpdate(Date.now());
        drawDirectionRectangle(
          startPoint[0],
          startPoint[1],
          angles,
          {
            width: vehicleTypeData?.width,
            frontEdgeToCenter: vehicleTypeData?.frontEdgeToCenter,
            backEdgeToCenter: vehicleTypeData?.backEdgeToCenter,
          },
          'inner',
        );

        drawDirectionRectangle(
          startPoint[0],
          startPoint[1],
          angles,
          {
            width: Number(vehicleTypeData?.width) + 1,
            frontEdgeToCenter: Number(vehicleTypeData?.frontEdgeToCenter) + 0.5,
            backEdgeToCenter: Number(vehicleTypeData?.backEdgeToCenter) + 0.5,
          },
          'outline',
        );
      } else {
        mapRef.current.map.getTargetElement().style.cursor = 'auto';
      }
    });
  };

  const drawPoint = (
    coordinates: any,
    imgSrc: any,
    type: string,
    anchor: any,
  ) => {
    const piontFeture = mapRef.current.createPointFeature(
      coordinates,
      imgSrc,
      type,
      null,
      anchor,
    );
    if (type === 'anchor') {
      if (mapRef.current.startPoint != null) {
        mapRef.current.startPoint.setGeometry(new Point(coordinates));
        return;
      } else {
        const source = new VectorSource();
        source.addFeature(piontFeture);
        const _layer = new VectorLayer({
          source: source,
        });
        mapRef.current.startPoint = piontFeture;
        mapRef.current.map.addLayer(_layer);
      }
    } else if (type === 'endPnt') {
      if (mapRef.current.endPoint != null) {
        mapRef.current.endPoint.setGeometry(new Point(coordinates));
        return;
      } else {
        const source = new VectorSource();
        source.addFeature(piontFeture);
        const _layer = new VectorLayer({
          source: source,
        });
        mapRef.current.endPoint = piontFeture;
        mapRef.current.map.addLayer(_layer);
      }
    }
  };

  const createLineFeature = (
    coordinates: any,
    type: 'dash' | 'solid',
    name: string,
  ) => {
    const featureLine = new Feature({
      geometry: new LineString(coordinates),
      zIndex: 9999,
      name,
      id: `${name}_${Date.now()}`,
    });
    const style = new Style({
      stroke: new Stroke({
        // 线样式
        lineDash: type === 'dash' ? [4, 4, 4, 4] : [1, 1, 1, 1, 1, 1],
        color: 'red',
        width: 4,
      }),
    });
    featureLine.setStyle(style);
    return featureLine;
  };

  const createPolygonFeature = (
    coordinates: any[],
    type: 'inner' | 'outline',
  ) => {
    const polygonFeature = new Feature({
      geometry: new Polygon(coordinates),
      zIndex: 9999,
      name: 'rectangleLayer',
    });
    polygonFeature.setId(`popOverlay${Date.now()}`);
    const style = new Style({
      fill: new Fill({
        // 填充样式
        color: 'rgba(60, 110, 240, 0.2)',
      }),
      stroke:
        type === 'inner'
          ? new Stroke({
              // 线样式
              lineDash: [1, 1, 1, 1, 1, 1],
              color: '#0D85FF',
              width: 2,
            })
          : new Stroke({
              // 线样式
              lineDash: [4, 4, 4, 4],
              color: '#0D85FF',
              width: 2,
            }),
    });
    polygonFeature.setStyle(style);
    return polygonFeature;
  };

  const drawBaseLine = function (x: number, y: number) {
    const baseLinePnts = new Array();
    const baseLineFromPnt = new Array();
    baseLineFromPnt[0] = x;
    baseLineFromPnt[1] = y;
    baseLinePnts.push(baseLineFromPnt);
    const baseLineToPnt = new Array();
    baseLineToPnt[0] = x + 3;
    baseLineToPnt[1] = y;
    baseLinePnts.push(baseLineToPnt);
    const transData = baseLinePnts.map((i) =>
      utm2wgs84(i[0], i[1], zoneRef.current),
    );
    const lineFeature = createLineFeature(transData, 'solid', 'baseline');
    if (mapRef.current.baseLineItem != null) {
      mapRef.current.baseLineItem.setGeometry(new LineString(transData));
      return;
    } else {
      const source = new VectorSource();
      source.addFeature(lineFeature);
      const _layer = new VectorLayer({
        source,
      });
      mapRef.current.map.addLayer(_layer);
      mapRef.current.baseLineItem = lineFeature;
      mapRef.current.baseLineLayer = _layer;
    }
  };

  const drawDirectionLine = (x: number, y: number, angles: number) => {
    const baseLinePnts = new Array();
    const baseLineFromPnt = new Array();
    baseLineFromPnt[0] = x;
    baseLineFromPnt[1] = y;
    baseLinePnts.push(baseLineFromPnt);
    const lonlat = {
      x: y,
      y: x,
    };
    const restPnt = calculateCoordinatePoint(lonlat, angles, 3);
    const baseLineToPnt = new Array();
    baseLineToPnt[0] = restPnt.y;
    baseLineToPnt[1] = restPnt.x;
    baseLinePnts.push(baseLineToPnt);
    const transData = baseLinePnts.map((i) =>
      utm2wgs84(i[0], i[1], zoneRef.current),
    );
    drawPoint(transData[1], require('./u209e.png'), 'endPnt', [0.5, 0.5]);
    const lineFeture = createLineFeature(transData, 'dash', 'directionLine');
    if (mapRef.current.directionLineItem != null) {
      mapRef.current.directionLineItem.setGeometry(new LineString(transData));
      return;
    } else {
      const source = new VectorSource();
      source.addFeature(lineFeture);
      const _layer = new VectorLayer({
        source,
      });
      mapRef.current.map.addLayer(_layer);
      mapRef.current.directionLineItem = lineFeture;
      mapRef.current.directionLineLayer = _layer;
    }
  };

  const drawDragDirectionLine = (x: number, y: number) => {
    const eventlnglat = [x ?? 0, y ?? 0];
    zoneRef.current = eventlnglat[0] / 6.0 + 31;
    const pos = wgs842utm(eventlnglat[0], eventlnglat[1], zoneRef.current);
    if (mapRef.current.directionLineItem == null) return;
    const pnts = mapRef.current.directionLineItem
      .getGeometry()
      .getCoordinates();
    if (pnts == null || 2 != pnts.length) {
      return;
    }
    if (mapRef.current.directionLineItem) {
      mapRef.current.directionLineItem.setGeometry(
        new LineString([pnts[0], [x, y]]),
      );
    }
    const startPoint = wgs842utm(pnts[0][0], pnts[0][1], zoneRef.current);
    drawPoint([x, y], require('./u209e.png'), 'endPnt', [0.5, 0.5]);
    let angles = Math.atan2(pos[1] - startPoint[1], pos[0] - startPoint[0]);
    angles = (angles * 180) / Math.PI; //转换为角度
    _lonLatHead.current = {
      lon: pnts[0][0],
      lat: pnts[0][1],
      head: parseFloat(Number(angles).toFixed(8)),
    };
    forceUpdate(Date.now());
  };

  const _markerUpdate = (position: any) => {
    const eventlnglat = [position?.lon ?? 0, position?.lat ?? 0];
    zoneRef.current = eventlnglat[0] / 6.0 + 31;
    const pos = wgs842utm(eventlnglat[0], eventlnglat[1], zoneRef.current);

    drawDirectionRectangle(
      pos[0],
      pos[1],
      position?.head ?? 20,
      {
        width: vehicleTypeData?.width ?? 0,
        frontEdgeToCenter: vehicleTypeData?.frontEdgeToCenter ?? 0,
        backEdgeToCenter: vehicleTypeData?.backEdgeToCenter ?? 0,
      },
      'inner',
    );
    drawDirectionRectangle(
      pos[0],
      pos[1],
      position?.head ?? 20,
      {
        width: Number(vehicleTypeData?.width) + 1,
        frontEdgeToCenter: Number(vehicleTypeData?.frontEdgeToCenter) + 0.5,
        backEdgeToCenter: Number(vehicleTypeData?.backEdgeToCenter) + 0.5,
      },
      'outline',
    );
    drawBaseLine(pos[0], pos[1]);
    drawDirectionLine(pos[0], pos[1], position?.head ?? 20);
    drawPoint(eventlnglat, require('./location.png'), 'anchor', [0.5, 1]);
  };

  const drawDirectionRectangle = function (
    x: number,
    y: number,
    angles: number,
    vehicleTypeData: any,
    type: 'inner' | 'outline',
  ) {
    const longLen = Math.sqrt(
      Math.pow(vehicleTypeData.width / 2, 2) +
        Math.pow(vehicleTypeData.frontEdgeToCenter, 2),
    );
    const shortLen = Math.sqrt(
      Math.pow(vehicleTypeData.width / 2, 2) +
        Math.pow(vehicleTypeData.backEdgeToCenter, 2),
    );
    const longAtan = Math.atan(
      vehicleTypeData.width / 2 / vehicleTypeData.frontEdgeToCenter,
    );
    const shortAtan = Math.atan(
      vehicleTypeData.width / 2 / vehicleTypeData.backEdgeToCenter,
    );
    const longAngle = (longAtan * 180) / Math.PI;
    const shortAngle = (shortAtan * 180) / Math.PI;
    // 从左下角起顺时针旋转，依次为P1,P2,P3,P4
    const angleP1 = 180 + angles + shortAngle;
    const angleP2 = 180 + angles - shortAngle;
    const angleP3 = angles + longAngle;
    const angleP4 = angles - longAngle;
    const lonlat = {
      x: y,
      y: x,
    };
    const P1 = calculateCoordinatePoint(lonlat, angleP1, shortLen);
    const P2 = calculateCoordinatePoint(lonlat, angleP2, shortLen);
    const P3 = calculateCoordinatePoint(lonlat, angleP3, longLen);
    const P4 = calculateCoordinatePoint(lonlat, angleP4, longLen);
    drawRectangle(
      [
        [
          utm2wgs84(P1.y, P1.x, zoneRef.current),
          utm2wgs84(P2.y, P2.x, zoneRef.current),
          utm2wgs84(P3.y, P3.x, zoneRef.current),
          utm2wgs84(P4.y, P4.x, zoneRef.current),
          utm2wgs84(P1.y, P1.x, zoneRef.current),
        ],
      ],
      type,
    );
  };

  const drawRectangle = (coordinates: any[], type: 'inner' | 'outline') => {
    const polygonFeature = createPolygonFeature(coordinates, type);
    if (type === 'inner') {
      if (mapRef.current.innerRactangle != null) {
        mapRef.current.innerRactangle.setGeometry(new Polygon(coordinates));
        return;
      } else {
        mapRef.current.innerRactangle = polygonFeature;
        const source = new VectorSource();
        source.addFeature(polygonFeature);
        const _layer = new VectorLayer({
          source: source,
        });
        mapRef.current.map.addLayer(_layer);
      }
    } else if (type === 'outline') {
      if (mapRef.current.outRactangle != null) {
        mapRef.current.outRactangle.setGeometry(new Polygon(coordinates));
        return;
      } else {
        mapRef.current.outRactangle = polygonFeature;
        const source = new VectorSource();
        source.addFeature(polygonFeature);
        const _layer = new VectorLayer({
          source: source,
        });
        mapRef.current.map.addLayer(_layer);
      }
    }
  };

  useEffect(() => {
    _fetchVehicleList();
    getMapByPosition([_lonLatHead.current.lon, _lonLatHead.current.lat]);
    initMap();
    initMapEvents();
  }, []);
  useEffect(() => {
    if (mapInfo?.mapId && mapInfo?.mapVersion) {
      for (let item of returnTileLayers()) {
        mapRef.current.clearLayer(item.layerId);
        const layer = new TileLayer({
          source: new TileWMS({
            url: item.url,
            params: {
              ...item.params,
              CQL_FILTER: `map_id=${mapInfo?.mapId} and map_version=${mapInfo?.mapVersion}`,
            },
          }),
        });
        mapRef.current.addLayers([
          {
            layer: layer,
            layerId: item.layerId,
          },
        ]);
      }
    }
  }, [mapInfo?.mapId, mapInfo?.mapVersion]);
  return (
    <Modal
      bodyStyle={{ padding: 0 }}
      maskClosable={false}
      visible={visiable}
      closable={false}
      footer={null}
      forceRender={true}
      width={1000}
      destroyOnClose={true}
    >
      <div className="map-vehicle-modal-container">
        <div className="head">
          <div style={{ textAlign: 'center', width: '100%' }}>
            停靠点位置选择
          </div>
          <div className="vehicle-choose">
            <div>
              <Select
                style={{ width: 160, marginRight: 10 }}
                placeholder="请选择车号"
                options={downListOptions}
                onChange={(value: string) => {
                  vehicleId.current = value;
                  _fetchVehiclePosition(value);
                }}
                filterOption={(input: any, option: any) => {
                  const label: any = option?.label || '';
                  return label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                }}
                showSearch={true}
                allowClear
              />
            </div>
          </div>
        </div>
        <div className="wms-map-container">
          <div className="map" id="vehicle-wmsmap-container" />
          <div className="map-bottom">
            <div>{`当前经度：${_lonLatHead.current.lon} E`}</div>
            <div>{`当前纬度：${_lonLatHead.current.lat} N`}</div>
            <div>{`当前朝向：${_lonLatHead.current.head} °`}</div>
          </div>
        </div>
        <div style={{ marginTop: 20 }}>
          <p style={{ color: 'red', marginBottom: 0 }}>说明：</p>
          <p style={{ color: 'red', marginBottom: 0 }}>
            1、系统按配送车最大车型(id:19)的长*宽(2.450m×1.183m)加载停靠效果，(
            <span style={{ color: '#31C2A6' }}>绿实线框</span>
            )车型示意图停靠四周距离均为50厘米(
            <span style={{ color: '#31C2A6' }}>绿虚线框</span>
            )，如2个框整体在黄色车道线内，则满足停靠标准。
          </p>
          <p style={{ color: 'red', marginBottom: 0 }}>
            2、红色虚线原点为车头方向，坐标中心点为车辆后轴中心点。
          </p>
          <p style={{ color: 'red', marginBottom: 0 }}>
            3、点击位置图标，可移动打点位置；点击角度图标，可调整打点角度
          </p>
        </div>
        <div className="bottom-btn">
          <CustomButton
            title="确定"
            height={35}
            otherCSSProperties={{ marginRight: 10 }}
            onSubmitClick={() => {
              if (_lonLatHead.current.lat != 0) {
                onSubmit ? onSubmit(_lonLatHead.current) : null;
              } else {
                message.error('请先选择车辆并获取其定位');
              }
            }}
          />
          <CustomButton
            title="取消"
            height={35}
            buttonType={ButtonType.DefaultButton}
            onSubmitClick={() => {
              onCancle ? onCancle() : null;
            }}
          />
        </div>
      </div>
    </Modal>
  );
};

export default ChooseLocationByVehicle;
