import React, { useEffect, useMemo, useRef, useState } from 'react';
import { HttpStatusCode } from '@/fetch/core/constant';
import { CommonTable } from '@/components';
import { Form, InputNumber, message } from 'antd';
import {
  editStopRangeList,
  getStopRangeList,
  resetStopRange,
} from '@/fetch/business/integrate';
import './index.scss';

const ParkingRangeManage = () => {
  const [form] = Form.useForm();
  const toleranceRef = useRef<
    {
      taskType: string;
      toleranceFrontBack: number;
      toleranceLeftRight: number;
      toleranceYaw: number;
    }[]
  >([]);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [tableList, setTableList] = useState<any[]>([]);

  const getStopRangeListData = (taskType?: string) => {
    getStopRangeList(taskType).then((res) => {
      if (res?.code === HttpStatusCode.Success) {
        setTableList(res?.data || []);
        toleranceRef.current = res?.data || [];
        res?.data?.forEach((item: any) => {
          form.setFieldValue(
            [item.taskType, 'toleranceFrontBack'],
            item.toleranceFrontBack,
          );
          form.setFieldValue(
            [item.taskType, 'toleranceLeftRight'],
            item.toleranceLeftRight,
          );
          form.setFieldValue(
            [item.taskType, 'toleranceYaw'],
            item.toleranceYaw,
          );
        });
      }
    });
  };

  const saveData = async () => {
    try {
      const values = await form.validateFields();
      const toleranceList: any[] = [];
      for (const [key, value] of Object.entries(values || {})) {
        console.log(key, value);
        toleranceList.push({
          ...(value || {}),
          taskType: key,
        });
      }
      setIsEdit(false);
      editStopRangeList(toleranceList).then((res) => {
        if (res?.code === HttpStatusCode.Success) {
          message.success('保存成功');
        } else {
          message.error(res?.message || '保存失败');
        }
      });
    } catch (errorInfo) {
      console.log('Failed:', errorInfo);
    }
  };

  const changeEditStatus = () => {
    setIsEdit(true);
  };

  const onChange = (
    taskType: string,
    fieldName: string,
    value: number | string | null,
  ) => {
    const tolerance = toleranceRef.current?.find((item: any) => {
      return item.taskType === taskType;
    });
    if (tolerance) {
      tolerance[fieldName] = value;
    }
  };

  const handleResetStopRange = (record: any) => {
    resetStopRange(record.taskType).then((res) => {
      if (res?.code === HttpStatusCode.Success) {
        form.setFieldValue(
          [record.taskType, 'toleranceFrontBack'],
          res?.data?.toleranceFrontBack,
        );
        form.setFieldValue(
          [record.taskType, 'toleranceLeftRight'],
          res?.data?.toleranceLeftRight,
        );
        form.setFieldValue(
          [record.taskType, 'toleranceYaw'],
          res?.data?.toleranceYaw,
        );
        record.toleranceFrontBack = res?.data?.toleranceFrontBack;
        record.toleranceLeftRight = res?.data?.toleranceLeftRight;
        record.toleranceYaw = res?.data?.toleranceYaw;
        setTableList([...tableList]);
      }
    });
  };

  useEffect(() => {
    getStopRangeListData();
  }, []);
  const formatColumns = useMemo(() => {
    return [
      {
        title: '任务类型',
        dataIndex: 'taskTypeName',
        align: 'center',
      },
      {
        title: '±前后平移距离(cm)',
        dataIndex: 'toleranceFrontBack',
        align: 'center',
        render: (text: number, record: any) =>
          isEdit ? (
            <Form.Item
              name={[record.taskType, 'toleranceFrontBack']}
              rules={[
                {
                  required: true,
                  message: '请输入前后平移距离',
                }
              ]}
            >
              <InputNumber
                defaultValue={text}
                placeholder="请输入"
                min={0}
                max={9999}
                onChange={(value: number | string | null) => {
                  onChange(record.taskType, 'toleranceFrontBack', value);
                }}
                formatter={(value) => `${value}`.replace(/\./g, '')}
              />
            </Form.Item>
          ) : (
            text
          ),
      },
      {
        title: '±左右平移距离(cm)',
        dataIndex: 'toleranceLeftRight',
        align: 'center',
        render: (text: number, record: any) =>
          isEdit ? (
            <Form.Item
              name={[record.taskType, 'toleranceLeftRight']}
              rules={[
                {
                  required: true,
                  message: '请输入左右平移距离',
                }
              ]}
            >
              <InputNumber
                defaultValue={text}
                placeholder="请输入"
                min={0}
                max={9999}
                onChange={(value: number | string | null) => {
                  onChange(record.taskType, 'toleranceLeftRight', value);
                }}
                formatter={(value) => `${value}`.replace(/\./g, '')}
              />
            </Form.Item>
          ) : (
            text
          ),
      },
      {
        title: '±yaw角(度)',
        dataIndex: 'toleranceYaw',
        align: 'center',
        render: (text: number, record: any) =>
          isEdit ? (
            <Form.Item
              name={[record.taskType, 'toleranceYaw']}
              rules={[
                {
                  required: true,
                  message: '请输入yaw角',
                }
              ]}
            >
              <InputNumber
                defaultValue={text}
                placeholder="请输入"
                min={0}
                max={999}
                onChange={(value: number | string | null) => {
                  onChange(record.taskType, 'toleranceYaw', value);
                }}
                formatter={(value) => `${value}`.replace(/\./g, '')}
              />
            </Form.Item>
          ) : (
            text
          ),
      },
      isEdit && {
        title: '操作',
        dataIndex: 'operate',
        render: (text: any, record: any) => {
          return (
            <a onClick={handleResetStopRange.bind(null, record)}>恢复初始值</a>
          );
        },
      },
    ].filter(Boolean);
  }, [isEdit]);
  return (
    <div className="parking-range-manage" style={{ marginTop: '10px' }}>
      <div className="title">停靠范围管理</div>
      <Form name="range" form={form}>
        <CommonTable
          columns={formatColumns}
          rowKey="taskType"
          middleBtns={
            isEdit
              ? [
                  {
                    title: '保存',
                    onClick: saveData,
                    style: {
                      margin: '10px',
                    },
                  },
                ]
              : [
                  {
                    title: '编辑',
                    onClick: changeEditStatus,
                    style: {
                      margin: '10px',
                    },
                  },
                ]
          }
          tableListData={{
            list: tableList,
          }}
          notPage={true}
        />
      </Form>
    </div>
  );
};

export default ParkingRangeManage;
