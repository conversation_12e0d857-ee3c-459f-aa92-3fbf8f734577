import { request } from '@/fetch/core';

class XMapManageFetch {
  public async fetchXMapVersionData({
    mapId,
    currentPage,
    pageSize,
  }: {
    currentPage: number;
    pageSize: number;
    mapId?: number | null;
  }) {
    const requestOptions: RequestOptions = {
      path: '/map/web/mapVersion',
      method: 'GET',
      urlParams: mapId
        ? {
            mapId: mapId,
            currentPage: currentPage,
            pageSize: pageSize,
          }
        : {
            currentPage: currentPage,
            pageSize: pageSize,
          },
    };
    return request(requestOptions);
  }

  public async fetchXMapInfoData({
    mapId,
    currentPage,
    pageSize,
  }: {
    currentPage: number;
    pageSize: number;
    mapId?: number | null;
  }) {
    const requestOptions: RequestOptions = {
      path: '/map/web/mapInfo',
      method: 'GET',
      urlParams: mapId
        ? {
            id: mapId,
            currentPage: currentPage,
            pageSize: pageSize,
          }
        : {
            currentPage: currentPage,
            pageSize: pageSize,
          },
    };
    return request(requestOptions);
  }
}

export default XMapManageFetch;
