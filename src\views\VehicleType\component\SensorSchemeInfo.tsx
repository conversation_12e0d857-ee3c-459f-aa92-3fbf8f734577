/* eslint-disable no-unused-vars */
import { Form, FormInstance, Select, Table } from 'antd';
import React, { useState, useEffect } from 'react';
import { SensorSolutionApi } from '@/fetch/business';
import { TriangularContainer } from '@/components';
import { sensorTableColumn } from '../utils/columns';
import { HttpStatusCode } from '@/fetch/core/constant';

const SensorSchemeInfo = ({
  disabled,
  options,
  form,
  initScheme,
}: {
  disabled?: boolean;
  options: any[];
  form: FormInstance;
  initScheme?: any;
}) => {
  const fetchApi = new SensorSolutionApi();
  const [sensorInfo, setSensorInfo] = useState<{
    loading: boolean;
    schemeId: any;
    schemeName: string;
    dataSource: any[];
  }>({
    loading: false,
    schemeId: {},
    schemeName: '',
    dataSource: [],
  });

  useEffect(() => {
    form.setFieldsValue({
      sensorSchemeId: initScheme,
    });
    if (initScheme.value) {
      fetchSensorSchemeInfo(initScheme);
    }
    if (!initScheme?.value) {
      setSensorInfo({
        ...sensorInfo,
        schemeId: null,
      });
    }
  }, [initScheme]);

  const fetchSensorSchemeInfo = async (value: any) => {
    setSensorInfo({
      ...sensorInfo,
      loading: true,
    });
    const response: any = await fetchApi.fetchSensorSchemeDetail(value.value);
    if (response && response.code === HttpStatusCode.Success) {
      setSensorInfo({
        schemeName: response.data.name,
        schemeId: value,
        dataSource: response.data.sensorSchemeDetailList,
        loading: false,
      });
    }
  };

  return (
    <Form.Item
      label="传感器方案名称"
      name="sensorSchemeId"
      rules={[{ required: true, message: '请选择传感器方案名称' }]}
    >
      <>
        <Select
          value={sensorInfo.schemeId}
          options={options}
          labelInValue
          disabled={disabled}
          placeholder="请选择传感器方案名称"
          showSearch
          filterOption={(input, option) => {
            const label: any = option?.label || '';
            return label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          }}
          onChange={(value: any) => {
            setSensorInfo({
              ...sensorInfo,
              schemeName: value.label,
              schemeId: value.value,
            });
            fetchSensorSchemeInfo(value);
            form.setFieldsValue({
              sensorSchemeId: value,
            });
          }}
        />
        <div>
          <TriangularContainer
            customStyle={{ backgroundColor: '#f1f1f1', padding: '10px' }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <div
                style={{
                  height: 20,
                  width: 5,
                  backgroundColor: 'red',
                  marginRight: 6,
                }}
              />
              {`方案id： ${
                sensorInfo.schemeId?.value ? sensorInfo.schemeId?.value : ''
              }`}
            </div>
            <div style={{ marginTop: 10 }}>
              <Table
                loading={sensorInfo.loading}
                rowKey={(record) => record.id}
                size="small"
                bordered
                pagination={false}
                columns={sensorTableColumn}
                dataSource={sensorInfo.dataSource}
              />
            </div>
          </TriangularContainer>
        </div>
      </>
    </Form.Item>
  );
};

export default React.memo(SensorSchemeInfo);
