import { message, Modal, Table } from 'antd';
import React, { useEffect, useState } from 'react';
import { DeviceLifeApi } from '@/fetch/business/deviceLife';
import { checkResultColumns } from '../../utils/column';
import { pageSizeOptions } from '@/utils/constant';
import { HttpStatusCode } from '@/fetch/core/constant';
interface Props {
  visible: boolean;
  id: any;
  onOk: () => void;
  onCancel: () => void;
}
const fetchApi = new DeviceLifeApi();
const CheckStatusModal = (props: Props) => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [totalPage, setTotalPage] = useState<number>(0);
  const [totalNumber, setTotalNumber] = useState<number>(0);
  const [checkResultList, setCheckResultList] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const { visible, id } = props;
  const formatCheckResultColumns = () => {
    return checkResultColumns.map((col: any) => {
      switch (col.dataIndex) {
        case 'checkOperate':
          // eslint-disable-next-line react/display-name
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                <a onClick={() => downLoadCheckResult(record)}>下载图片</a>
              </div>
            );
          };
          break;
        default:
          return col;
      }
      return col;
    });
  };
  const downLoadCheckResult = async (record: any) => {
    try {
      const response: any = await fetchApi.deviceCheckResultDownload(record.id);
      if (response.code === HttpStatusCode.Success) {
        // window.location.href = response.data && response.data.recordUrl
        if (response.data && response.data.recordUrl) {
          const url = response.data.recordUrl;
          const newUrl = url.replace('http', 'https');
          window.open(newUrl, '_self');
        }
      } else {
        message.error(response.message);
      }
    } catch (e) {
      console.log(e);
    }
  };
  useEffect(() => {
    if (id) {
      fetchCheckResultList();
    }
  }, [id, currentPage, pageSize]);
  const fetchCheckResultList = async () => {
    setLoading(true);
    try {
      const params = {
        deviceBaseId: props.id,
        pageNum: currentPage,
        pageSize: pageSize,
      };
      const response: any = await fetchApi.fetchDeviceCheckResultTable(params);
      if (response.code === HttpStatusCode.Success) {
        response.data && response.data.list
          ? setCheckResultList(response.data.list)
          : setCheckResultList([]);
        response.data && response.data.pages
          ? setTotalPage(response.data.pages)
          : setTotalPage(0);
        response.data && response.data.total
          ? setTotalNumber(response.data.total)
          : setTotalNumber(0);
      } else {
        message.error(response.message);
      }
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };
  const handleOk = () => {
    props.onOk();
  };
  const handleCancel = () => {
    props.onCancel();
  };
  return (
    <Modal
      title="查看标定结果"
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={800}
      // destroyOnClose={true}
    >
      <Table
        rowKey={(record: any) => record.id}
        columns={formatCheckResultColumns()}
        dataSource={checkResultList}
        loading={loading}
        scroll={{
          y: '500px',
        }}
        pagination={{
          position: ['bottomCenter'],
          total: totalNumber,
          current: currentPage,
          pageSize: pageSize,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: pageSizeOptions,
          showTotal: () => `共 ${totalPage}页,${totalNumber} 条记录`,
        }}
        onChange={(paginationData: any) => {
          setCurrentPage(paginationData.current);
          setPageSize(paginationData.pageSize);
        }}
        bordered
      ></Table>
    </Modal>
  );
};

export default React.memo(CheckStatusModal);
