/* eslint-disable no-unused-vars */
/* eslint-disable react/display-name */
import { Col, Popconfirm, Button, Row, Table } from 'antd';
import React from 'react';
import EditableTable from '../EditableTable';

const SideAddbleTable = ({
  columns,
  dataSource,
  deleteConfirmTips,
  onAddClick,
  onDeleteClick,
}: {
  columns: any[];
  dataSource: any[];
  deleteConfirmTips: string;
  onAddClick?: Function;
  onDeleteClick?: Function;
}) => {
  const makeEditDatasource = () => {
    const editList = dataSource?.map((item, index) => {
      return {
        name: index === 0 ? '+' : '-',
        key: item.id,
      };
    });
    return editList;
  };

  return (
    <>
      <Row>
        <Col span={22}>
          <EditableTable columns={columns} dataSource={dataSource} />
        </Col>
        <Col span={2}>
          <Table
            columns={[
              {
                title: 'has',
                dataIndex: 'name',
                align: 'left',
                onHeaderCell: (data, index) => {
                  return {
                    style: {
                      backgroundColor: 'rgba(0, 0, 0, 0)',
                      color: 'white',
                      borderWidth: 0,
                    },
                  };
                },
                onCell: (data, index) => {
                  return {
                    style: {
                      backgroundColor: 'white',
                      borderWidth: 0,
                      padding: 0,
                      height: 65,
                    },
                  };
                },
                render: (name, _: any, index: number) => {
                  const bgColor = name === '+' ? '##3c6ef0' : '#D9001B';
                  return name === '-' ? (
                    <Popconfirm
                      title={deleteConfirmTips}
                      onConfirm={() => {
                        onDeleteClick && onDeleteClick(index);
                      }}
                      placement={'left'}
                      overlayStyle={{ width: '210px', height: '90px' }}
                    >
                      <Button
                        style={{
                          marginLeft: 15,
                          color: bgColor,
                          width: 55,
                          borderColor: bgColor,
                          borderRadius: 4,
                        }}
                      >
                        {' '}
                        {name}
                      </Button>
                    </Popconfirm>
                  ) : (
                    <Button
                      onClick={() => {
                        onAddClick && onAddClick();
                      }}
                      style={{
                        marginLeft: 15,
                        color: bgColor,
                        width: 55,
                        borderColor: bgColor,
                        borderRadius: 4,
                      }}
                    >
                      {' '}
                      {name}
                    </Button>
                  );
                },
              },
            ]}
            dataSource={makeEditDatasource()}
            pagination={false}
          />
        </Col>
      </Row>
    </>
  );
};

export default React.memo(SideAddbleTable);
