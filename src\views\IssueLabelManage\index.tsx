import React, { useState, useEffect, useRef } from 'react';
import { CommonTable, CommonForm, useTableData } from '@jd/x-coreui';
import { searchConfig, tableColumns } from './utils/columns';
import { IssueLabelManageApi, getBugCategoryDownList } from '@/fetch/business';
import { useSelector, useDispatch } from 'react-redux';
import { saveSearchValues } from '@/redux/reducer/searchForm';
import { RootState } from '@/redux/store';
import { FieldItem, TableOperateBtn } from '@/components';
import { useNavigate } from 'react-router-dom';
import './index.scss';
import { PageType } from '@/utils/EditTitle';
import { formatLocation } from '@/utils/utils';
import { Popconfirm, message } from 'antd';

import { HttpStatusCode } from '@/fetch/core/constant';

const fetchApi = new IssueLabelManageApi();
const IssueCategoryManage = () => {
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const [config, setConfig] = useState<any>(searchConfig);
  const { bugCategoryId, bugCategoryName } = formatLocation(
    window.location.search,
  );
  const searchFormRef = useRef<any>(null);
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const initSearchCondition = {
    searchForm: {
      bugCategoryId: bugCategoryId,
      bugLabelName: null,
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValues.searchValues
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );

  const { tableData, loading, reloadTable } = useTableData(
    {
      searchForm: {
        ...searchCondition.searchForm,
      },
      pageNum: searchCondition.pageNum,
      pageSize: searchCondition.pageSize,
    },
    fetchApi.fetchTableList,
  );
  useEffect(() => {
    getBugCategoryDownList().then((res) => {
      if (res?.code === HttpStatusCode.Success) {
        const field = config.fields.find(
          (item: FieldItem) => item.fieldName === 'bugCategoryId',
        );
        field!.options = res?.data?.map((item) => ({
          label: item.bugCategoryName,
          value: item.bugCategoryId,
        }));
        setConfig({ ...config });
        searchFormRef.current.setFieldValue(
          'bugCategoryId',
          bugCategoryId
            ? {
                label: bugCategoryName,
                value: bugCategoryId,
              }
            : null,
        );
      }
    });
  }, []);

  const middleBtns: any[] = [
    {
      show: true,
      title: '新建',
      key: 'getNewLabel',
      onClick: () => {
        navigator(`/app/issueLabelManage/edit?type=${PageType.ADD}`);
      },
    },
  ];

  const formateColumns = () => {
    return tableColumns.map((col) => {
      switch (col.dataIndex) {
        case 'associatedBugCount':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return (
                <a
                  key={record.associatedBugCount}
                  onClick={() => {
                    navigator(
                      `/app/testing?labelId=${record.bugLabelId}&moduleCode=${record.moduleCode}&bugCategoryId=${record.bugCategoryId}`,
                    );
                  }}
                >
                  {record.associatedBugCount}
                </a>
              );
            },
          };

        case 'operation':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return (
                <div className="operate">
                  <TableOperateBtn
                    title="编辑"
                    handleClick={() => {
                      dispatch(
                        saveSearchValues({
                          routeName: location.pathname,
                          searchValues: searchCondition,
                        }),
                      );
                      navigator(
                        `/app/issueLabelManage/edit?type=${PageType.EDIT}&id=${record.bugLabelId}`,
                      );
                    }}
                  />
                  <Popconfirm
                    placement="left"
                    title={`请确认删除该【${record.bugLabelName}】标签吗？`}
                    onConfirm={() => {
                      fetchApi
                        .delete({ bugLabelId: record.bugLabelId })
                        .then((res) => {
                          if (res.code === HttpStatusCode.Success) {
                            message.success('删除成功');
                            reloadTable();
                          } else {
                            message.error(res.message);
                          }
                        });
                    }}
                    okText="确定"
                    cancelText="取消"
                    overlayStyle={{ maxWidth: 800 }}
                  >
                    <a
                      style={{
                        marginRight: '5px',
                        marginLeft: '5px',
                        color: 'red',
                        cursor: 'pointer',
                      }}
                    >
                      删除
                    </a>
                  </Popconfirm>
                </div>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };
  const onSearchClick = (val) => {
    const data = {
      searchForm: {
        bugCategoryId: val?.bugCategoryId?.value,
        bugLabelName: val?.bugLabelName,
      },
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(data);
  };

  return (
    <div className="issue-label-manage">
      <CommonForm
        className="mr-search-form"
        formConfig={config}
        layout="inline"
        formType="search"
        colon={false}
        getFormInstance={(ref: any) => {
          searchFormRef.current = ref;
        }}
        onSearchClick={onSearchClick}
        onResetClick={() => {
          setSearchCondition({ ...initSearchCondition });
          searchFormRef.current.setFieldValue(
            'bugCategoryId',
            bugCategoryId
              ? {
                  label: bugCategoryName,
                  value: bugCategoryId,
                }
              : null,
          );
        }}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        columns={formateColumns()}
        loading={loading}
        middleBtns={middleBtns}
        onPageChange={(value: any) => setSearchCondition(value)}
      />
    </div>
  );
};

export default React.memo(IssueCategoryManage);
