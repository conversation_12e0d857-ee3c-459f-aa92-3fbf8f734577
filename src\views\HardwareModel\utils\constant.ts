

export interface VendorFormItemType {
  name: string;
  label: string;
  rules: boolean;
  message: string;
  contentType: string; // input or radio
  inputType?: string; // number | password | text
  inputMaxLength?: number;
  placeholder?: string;
  min?: number;
  max?: number;
  colon?: boolean;
  disable?: boolean;
  maxLength?: number;
}

export const hardWareModelForm: VendorFormItemType[] = [
  {
    name: 'name',
    label: '硬件名称',
    rules: true,
    message: '请输入硬件名称!',
    contentType: 'input',
    placeholder: '请输入硬件名称',
    inputType: 'text',
    maxLength: 20,
  },
  {
    name: 'model',
    label: '硬件型号',
    rules: true,
    message: '请输入硬件型号!',
    contentType: 'input',
    placeholder: '请输入硬件型号',
    inputType: 'text',
    maxLength: 20
  },
  {
    name: 'hardwareTypeId',
    label: '硬件类型',
    rules: true,
    message: '请选择硬件类型!',
    placeholder: '请选择硬件类型',
    contentType: 'select',
  },
  {
    name: 'hardwareModelUsageIdList',
    label: '硬件用途',
    rules: true,
    message: '请选择硬件用途',
    contentType: 'checkBox',
  },
  {
    name: 'manufactoryId',
    label: '生产厂商',
    rules: true,
    message: '请输入生产厂商名字',
    contentType: 'select',
    placeholder: '请输入厂商名字',
    maxLength: 50,
  },
  {
    name: 'enable',
    label: '硬件状态',
    rules: true,
    message: '请选择硬件状态!',
    contentType: 'radio',
  },
  {
    name: 'referenceList',
    label: '硬件资料链接地址',
    rules: false,
    message: '请输入硬件名称!',
    contentType: 'input',
    placeholder: '请输入硬件名称',
    inputType: 'text',
    maxLength: 20,
  },
];
