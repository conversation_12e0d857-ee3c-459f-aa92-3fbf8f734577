export enum ClstagKey {
  // 订单管理
  originalOrderId = 'originalOrderId', // 订单编号
  collectionUserContact = 'collectionUserContact', // 手机号
  orderVehicleName = 'orderVehicleName', // 车牌号
  gridNo = 'gridNo', // 格口号
  orderStatusList = 'orderStatusList', // 订单状态
  scheduleName = 'scheduleName', // 调度单号
  scheduleTime = 'scheduleTime', // 订单装载时间
  orderProvince = 'orderProvince', // 所在省份
  orderCity = 'orderCity', // 所在城市
  orderStation = 'orderStation', // 站点名称
  orderStop = 'orderStop', // 停靠点名称
  orderSourceCodeList = 'orderSourceCodeList', // 订单来源
  deliveryMode = 'deliveryMode', // 配送方式
  loadMethod = 'loadMethod', // 投递方式

  // 站点管理
  statsionCountry = 'statsionCountry', // 所在大区
  statsionProvince = 'statsionProvince', // 所在省份
  statsionCity = 'statsionCity', // 所在城市
  statsionStation = 'statsionStation', // 站点名称
  useCase = 'useCase', // 站点用途
  stationType = 'stationType', // 站点类型
  stationEnable = 'stationEnable', // 站点状态

  // 停靠点管理
  stopProvince = 'stopProvince', // 所在省份
  stopCity = 'stopCity', // 所在城市
  stopStation = 'stopStation', // 站点名称
  stopType = 'stopType', // 点位类型
  stopEnable = 'stopEnable', // 站点状态
  stopName = 'stopName', // 点位名称

  // 代收点管理
  collectCountry = 'collectCountry', // 所在大区
  collectProvince = 'collectProvince', // 所在省份
  collectCity = 'collectCity', // 所在城市
  collectStation = 'collectStation', // 站点名称
  collectStopName = 'collectStopName', // 停靠点名称
  collectEnable = 'collectEnable', // 代收点状态
  collectName = 'collectName', // 代收点名称

  // 车辆管理
  vehicleProvince = 'vehicleProvince', // 所在省份
  vehicleCity = 'vehicleCity', // 所在城市
  vehicleStation = 'vehicleStation', // 站点名称
  vehicleName = 'vehicleName', // 车牌号
  vehicleLifeCycle = 'vehicleLifeCycle', // 车辆生命周期
  vehicleCheckStatus = 'vehicleCheckStatus', // 标定检测状态
  vehicleBusinessType = 'vehicleBusinessType', // 车辆类型
  vehicleOwnerUseCase = 'vehicleOwnerUseCase', // 车辆归属方
  vehicleHasRepairOrder = 'vehicleHasRepairOrder', // 存在未完成维修单
  isVirtualVehicle = 'isVirtualVehicle', // 是否为虚拟车
  vehicleTypeName = 'vehicleTypeName', // 车型名称

  // 维修单管理
  repairOrderNumber = 'repairOrderNumber', // 维修单号
  repairVehicleName = 'repairVehicleName', // 车牌号
  repairContactPerson = 'repairContactPerson', // 联系人
  repairStation = 'repairStation', // 站点名称
  repairReportTime = 'repairReportTime', // 提报时间
  repairOrderStatus = 'repairOrderStatus', // 维修单状态
  isInfluenceOperation = 'isInfluenceOperation', // 是否影响运营
}
// beta
// export const BuryPointClstagMap = new Map([
//   // 订单管理
//   [ClstagKey.originalOrderId, 'vehicle_ui_orderSearch_1679991633667|1'],
//   [ClstagKey.collectionUserContact, 'vehicle_ui_orderSearch_1679991633667|2'],
//   [ClstagKey.vehicleName, 'vehicle_ui_orderSearch_1679991633667|3'],
//   [ClstagKey.gridNo, 'vehicle_ui_orderSearch_1679991633667|4'],
//   [ClstagKey.orderStatusList, 'vehicle_ui_orderSearch_1679991633667|5'],
//   [ClstagKey.scheduleName, 'vehicle_ui_orderSearch_1679991633667|6'],
//   [ClstagKey.scheduleTime, 'vehicle_ui_orderSearch_1679991633667|7'],
//   [ClstagKey.orderProvince, 'vehicle_ui_orderSearch_1679991633667|8'],
//   [ClstagKey.orderCity, 'vehicle_ui_orderSearch_1679991633667|9'],
//   [ClstagKey.orderStation, 'vehicle_ui_orderSearch_1679991633667|10'],
//   [ClstagKey.orderStop, 'vehicle_ui_orderSearch_1679991633667|11'],
//   [ClstagKey.orderSourceCodeList, 'vehicle_ui_orderSearch_1679991633667|12'],
//   [ClstagKey.deliveryMode, 'vehicle_ui_orderSearch_1679991633667|13'],
//   [ClstagKey.loadMethod, 'vehicle_ui_orderSearch_1679991633667|14'],
// ]);

// 线上
export const BuryPointClstagMap = new Map([
  // 订单管理
  [ClstagKey.originalOrderId, 'order_online_search_1680249921742|1'],
  [ClstagKey.collectionUserContact, 'order_online_search_1680249921742|2'],
  [ClstagKey.orderVehicleName, 'order_online_search_1680249921742|3'],
  [ClstagKey.gridNo, 'order_online_search_1680249921742|4'],
  [ClstagKey.orderStatusList, 'order_online_search_1680249921742|5'],
  [ClstagKey.scheduleName, 'order_online_search_1680249921742|6'],
  [ClstagKey.scheduleTime, 'order_online_search_1680249921742|7'],
  [ClstagKey.orderProvince, 'order_online_search_1680249921742|8'],
  [ClstagKey.orderCity, 'order_online_search_1680249921742|9'],
  [ClstagKey.orderStation, 'order_online_search_1680249921742|10'],
  [ClstagKey.orderStop, 'order_online_search_1680249921742|11'],
  [ClstagKey.orderSourceCodeList, 'order_online_search_1680249921742|12'],
  [ClstagKey.deliveryMode, 'order_online_search_1680249921742|13'],
  [ClstagKey.loadMethod, 'order_online_search_1680249921742|14'],

  // 站点管理
  [ClstagKey.statsionCountry, 'station_online_search_1681983378766|1'],
  [ClstagKey.statsionProvince, 'station_online_search_1681983378766|2'],
  [ClstagKey.statsionCity, 'station_online_search_1681983378766|3'],
  [ClstagKey.statsionStation, 'station_online_search_1681983378766|4'],
  [ClstagKey.useCase, 'station_online_search_1681983378766|5'],
  [ClstagKey.stationType, 'station_online_search_1681983378766|6'],
  [ClstagKey.stationEnable, 'station_online_search_1681983378766|7'],

  // 停靠点管理
  [ClstagKey.stopProvince, 'stop_online_search_1681984521922|1'],
  [ClstagKey.stopCity, 'stop_online_search_1681984521922|2'],
  [ClstagKey.stopStation, 'stop_online_search_1681984521922|3'],
  [ClstagKey.stopType, 'stop_online_search_1681984521922|4'],
  [ClstagKey.stopEnable, 'stop_online_search_1681984521922|5'],
  [ClstagKey.stopName, 'stop_online_search_1681984521922|6'],

  // 代收点管理
  [ClstagKey.collectCountry, 'collect_online_search_1681984982383|1'],
  [ClstagKey.collectProvince, 'collect_online_search_1681984982383|2'],
  [ClstagKey.collectCity, 'collect_online_search_1681984982383|3'],
  [ClstagKey.collectStation, 'collect_online_search_1681984982383|4'],
  [ClstagKey.collectStopName, 'collect_online_search_1681984982383|5'],
  [ClstagKey.collectEnable, 'collect_online_search_1681984982383|6'],
  [ClstagKey.collectName, 'collect_online_search_1681984982383|7'],

  // 车辆管理
  [ClstagKey.vehicleProvince, 'vehicle_online_search_1681985543836|2'],
  [ClstagKey.vehicleCity, 'vehicle_online_search_1681985543836|3'],
  [ClstagKey.vehicleStation, 'vehicle_online_search_1681985543836|4'],
  [ClstagKey.vehicleName, 'vehicle_online_search_1681985543836|5'],
  [ClstagKey.vehicleLifeCycle, 'vehicle_online_search_1681985543836|6'],
  [ClstagKey.vehicleCheckStatus, 'vehicle_online_search_1681985543836|7'],
  [ClstagKey.vehicleBusinessType, 'vehicle_online_search_1681985543836|8'],
  [ClstagKey.vehicleOwnerUseCase, 'vehicle_online_search_1681985543836|9'],
  [ClstagKey.vehicleHasRepairOrder, 'vehicle_online_search_1681985543836|10'],
  [ClstagKey.isVirtualVehicle, 'vehicle_online_search_1681985543836|1'],
  [ClstagKey.vehicleTypeName, 'vehicle_online_search_1681985543836|11'],

  // 代收点管理
  [ClstagKey.repairOrderNumber, 'repair_online_search_1682067836762|1'],
  [ClstagKey.repairVehicleName, 'repair_online_search_1682067836762|2'],
  [ClstagKey.repairContactPerson, 'repair_online_search_1682067836762|3'],
  [ClstagKey.repairStation, 'repair_online_search_1682067836762|4'],
  [ClstagKey.repairReportTime, 'repair_online_search_1682067836762|5'],
  [ClstagKey.repairOrderStatus, 'repair_online_search_1682067836762|6'],
  [ClstagKey.isInfluenceOperation, 'repair_online_search_1682067836762|7'],

  // 维修单管理
  [ClstagKey.repairOrderNumber, 'repair_online_search_1682067836762|1'],
  [ClstagKey.repairVehicleName, 'repair_online_search_1682067836762|2'],
  [ClstagKey.repairContactPerson, 'repair_online_search_1682067836762|3'],
  [ClstagKey.repairStation, 'repair_online_search_1682067836762|4'],
  [ClstagKey.repairReportTime, 'repair_online_search_1682067836762|5'],
  [ClstagKey.repairOrderStatus, 'repair_online_search_1682067836762|6'],
  [ClstagKey.isInfluenceOperation, 'repair_online_search_1682067836762|7'],
]);
