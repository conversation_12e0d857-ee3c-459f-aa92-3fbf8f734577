/* eslint-disable react/prop-types */
/* eslint-disable require-jsdoc */
/* eslint-disable no-unused-vars */
/* eslint-disable no-invalid-this */
import React, { useContext, useState, useEffect, useRef } from 'react';

import { Table, Input, Form } from 'antd';
const EditableContext = React.createContext(null);

const EditableRow = ({ index, ...props }: any) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form as any}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

const EditableCell = ({
  title,
  editable,
  disabled,
  children,
  dataIndex,
  record,
  existList,
  handleSave,
  ...restProps
}: any) => {
  const [editing, setEditing] = useState(true);
  const inputRef = useRef(null);
  const form: any = useContext(EditableContext);
  // useEffect(() => {
  //   if (editing) {
  //     const target: any = inputRef.current
  //     target?.focus();
  //   }
  // }, [editing]);

  const toggleEdit = () => {
    setEditing(!editing);
    // form?.setFieldsValue({
    //   [dataIndex]: record[dataIndex],
    // });
  };
  useEffect(() => {
    if (record && record.name !== null) {
      form?.setFieldsValue({
        name: record.name,
      });
    }
    if (record && record.longitude !== null) {
      form?.setFieldsValue({
        longitude: record.longitude,
        latitude: record.latitude,
        heading: record.heading,
      });
    }
  }, [record]);
  const save = async () => {
    if (dataIndex === 'name') {
      try {
        const values = await form.validateFields();
        toggleEdit();
        handleSave && handleSave({ ...record, ...values });
      } catch (errInfo) {
        const values = errInfo.values;
        toggleEdit();
        handleSave && handleSave({ ...record, name: values.name });
      }
    }
  };

  let childNode = children;
  if (editable) {
    childNode = disabled ? (
      <Form.Item
        style={{ margin: 0 }}
        name={dataIndex}
        wrapperCol={{ span: 16 }}
        rules={[{ required: true, message: `请选择${title}` }]}
      >
        <Input
          placeholder={`请选择${title}`}
          style={{ textAlign: 'center' }}
          disabled={true}
        />
      </Form.Item>
    ) : (
      <Form.Item
        style={{ margin: 0 }}
        name={dataIndex}
        rules={[{ required: true, message: '请输入停车点名称' }]}
      >
        <Input
          placeholder="请输入停车点名称"
          style={{ textAlign: 'center' }}
          ref={inputRef}
          onPressEnter={save}
          onBlur={save}
          onMouseLeave={save}
        />
      </Form.Item>
    );
  }

  return <td {...restProps}>{childNode}</td>;
};

const EditableTable = ({ columns, dataSource }: any) => {
  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  };

  return (
    <Table
      components={components}
      bordered
      dataSource={dataSource}
      columns={columns}
      pagination={false}
    />
  );
};

export default EditableTable;
