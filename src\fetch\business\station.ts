import { request } from '@/fetch/core';
import {
  StationRequest,
  NewStationInfo,
  BasicStationInfo,
  StationVehicleRequest,
  StopRequest,
  NewStopInfo,
  EditStopInfo,
} from '@/types';
import { Method } from '../core/constant';

export class StationFetchApi {
  fetchDomain = `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}`;

  updateThirdStation = (stationBaseId: any) => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/k2/management/station_stop/sync_stop_of_third_part`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: {
        stationBaseId,
      },
    };
    return request(options);
  };
  getMapByPosition = (params: any) => {
    const requestParam: RequestOptions = {
      method: 'GET',
      path: `/map/web/metadata/getMapVersionByPosition/${params.longitude}/${params.latitude}`,
    };
    return request(requestParam);
  };
  // 列表页面
  /**
   * 1.联动获取父级位置接口
   * 省份，城市，站点级联父级位置选择
   * @param params An object containing the id of the entity and the level to query.
   * @returns A promise with the request result.
   */
  public async getParentLinked(params: {
    id: number;
    level: 'stop' | 'station' | 'city' | 'province' | 'country';
  }) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/common/common_get_parent_linked',
      method: Method.POST,
      body: params,
    };
    return request(requestOptions);
  }

  /**
   * 2.联动获取当前位置接口
   * 省份，城市，站点级联当前位置
   * @param params 包含筛选级别和可选的ID列表以及启用状态。
   * @returns 返回根据请求选项发起的请求结果。
   */
  public async getCurrentDownList(params: {
    level: 'stop' | 'station' | 'city' | 'province' | 'country';
    countryIdList?: number[];
    provinceIdList?: number[];
    cityIdList?: number[];
    stationBaseIdList?: number[];
    enable?: number;
  }) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/common/common_get_current_down_list',
      method: Method.POST,
      body: params,
    };
    return request(requestOptions);
  }

  /**
   * 3.获取下拉框枚举接口
   * @param {string[]} params
   * @return
   */
  public async getCommonDropDown(params: string[]) {
    const requestParam: RequestOptions = {
      method: Method.POST,
      path: '/k2/management/common/common_get_down_list',
      body: {
        keyList: params,
      },
    };
    return request(requestParam);
  }
  // 4.分页查询站点列表
  public async getStationTable(searchOptions: StationRequest) {
    const { pageNum, pageSize, searchForm } = searchOptions;
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_base/station_get_page_list',
      method: Method.POST,
      urlParams: {
        pageNum: pageNum,
        pageSize: pageSize,
      },
      body: {
        ...searchForm,
      },
    };
    return request(requestOptions);
  }

  // 5.变更站点状态
  public async changeStationStatus(stationBaseId: number, enable: number) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_base/change_status',
      method: Method.POST,
      body: {
        stationBaseId,
        enable,
      },
    };
    return request(requestOptions);
  }

  // 新建页面
  /**
   * 7.获取用户信息下拉列表
   * @returns A promise with the user info list response.
   */
  public async getUserInfoList() {
    const requestOptions: RequestOptions = {
      path: '/k2/management/common/get_user_info_list',
      method: Method.GET,
    };
    return request(requestOptions);
  }
  /**
   * 7.9校验站点名称重复接口
   * @param params - Object containing stationBaseId and name to check.
   * @returns A promise with the request response.
   */
  public async checkDuplicateStationName(params: {
    name: string;
    stationBaseId?: number;
  }) {
    const options: RequestOptions = {
      method: Method.POST,
      path: '/k2/management/station_base/station_check_duplicate_name',
      body: {
        ...params,
      },
    };
    return request(options);
  }
  public async checkDuplicateStationNumber(params: {
    number: string;
    stationBaseId?: number;
  }) {
    const options: RequestOptions = {
      method: Method.POST,
      path: '/k2/management/station_base/station_check_duplicate_number',
      body: {
        ...params,
      },
    };
    return request(options);
  }
  // 8.新建站点
  public async addStation(stationInfo: NewStationInfo) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_base/station_add',
      method: Method.POST,
      body: stationInfo,
    };

    return request(requestOptions);
  }

  // 8.1校验停靠点名称重复接口
  public async checkDuplicateStopName(params: { name: string; id?: number }) {
    const options: RequestOptions = {
      path: '/k2/management/station_base/station_check_duplicate_name',
      method: Method.POST,
      body: params,
    };
    return request(options);
  }

  // 站点信息页面
  // 9.获取站点基础信息
  public async fetchStationBasicInfo(stationBaseId: number) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_base/get_station_base_info',
      method: Method.GET,
      urlParams: { stationBaseId },
    };
    return request(requestOptions);
  }
  // 10.编辑站点基础信息
  public async editStationInfo(
    stationInfo: Omit<
      BasicStationInfo,
      | 'useCaseName'
      | 'typeName'
      | 'productTypeName'
      | 'deviceCount'
      | 'cityName'
      | 'stateId'
      | 'stateName'
      | 'countryId'
      | 'countryName'
      | 'enable'
    >,
  ) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_base/edit_station_base_info',
      method: Method.POST,
      body: stationInfo,
    };
    return request(requestOptions);
  }

  // 无人车站点业务信息页面
  // 11.获取无人车站点业务信息
  public async getVehicleStationBusinessInfo(stationBaseId: number) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station/get_vehicle_station_business_info',
      method: Method.GET,
      urlParams: { stationBaseId },
    };
    return request(requestOptions);
  }

  // 12.编辑无人车站点业务信息
  public async editVehicleStationBusinessInfo(params: {
    stationBaseId: number;
    number: string;
    minDropOffTime: number;
    autoChargeLimit: number;
    missionChargeLimit: number;
    forceChargeLimit: number;
  }) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station/edit_vehicle_station_business_info',
      method: Method.POST,
      body: params,
    };
    return request(requestOptions);
  }

  // 编辑多合一站点业务信息
  public async editWarehouseStationBusinessInfo(params: {
    stationBaseId: number;
    stationNumber: string;
    minDropOffTime: number;
    autoChargeLimit: number;
    missionChargeLimit: number;
    forceChargeLimit: number;
    reminderTime: number;
    chargeType: string;
    groupBusinessList: any[];
  }) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_warehouse/edit_warehouse_station_business_info',
      method: Method.POST,
      body: params,
      newGeteway: true,
    };
    return request(requestOptions);
  }
  // 无人车站点关联车辆页面
  // 13.分页查询无人车站点下的车辆列表
  public async getStationVehicleTable(searchOptions: StationVehicleRequest) {
    const { pageNum, pageSize, searchForm } = searchOptions;
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_vehicle/get_station_vehicle_page_list',
      method: Method.POST,
      urlParams: {
        pageNum,
        pageSize,
      },
      body: searchForm,
    };
    return request(requestOptions);
  }

  // 14.获取车辆配置详情信息
  public async getStationVehicleDetail(deviceBaseId: number) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_vehicle/get_station_vehicle_detail',
      method: Method.GET,
      urlParams: { deviceBaseId },
    };
    return request(requestOptions);
  }

  // 15.获取车辆货箱详情信息
  public async getStationVehicleBoxDetail(deviceBaseId: number) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_vehicle/vehicle_get_box_detail',
      method: Method.GET,
      urlParams: { deviceBaseId },
    };
    return request(requestOptions);
  }

  // 16.获取货箱模板详情接口
  public async getBoxTemplateDetail(boxTemplateId: number) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/box_template/get_box_template_detail',
      method: Method.GET,
      urlParams: { id: boxTemplateId },
    };
    return request(requestOptions);
  }

  // 17.编辑车辆货箱信息接口
  public async editStationVehicleBox({
    deviceBaseId,
    boxTemplateId,
    gridList,
  }: {
    deviceBaseId: number;
    boxTemplateId: number;
    gridList: {
      id: number;
      enable: number;
    }[];
  }) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_vehicle/edit_station_vehicle_box',
      method: Method.POST,
      body: {
        deviceBaseId,
        boxTemplateId,
        gridList,
      },
    };
    return request(requestOptions);
  }

  // 18.获取车型详情信息
  public async getDeviceTypeDetail(deviceTypeBaseId: number) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_type/get_device_type_info',
      method: Method.GET,
      urlParams: {
        deviceTypeBaseId,
      },
    };
    return request(requestOptions);
  }

  // 19.获取城市下的站点列表:绑定跨站停靠点时获取站点列表 批量绑定停靠点时获取站点列表
  public async getStationInfoListOfCity({
    cityId,
    type,
    exclusionStationBaseIdList,
  }: {
    cityId: string;
    type: string;
    exclusionStationBaseIdList?: number[];
  }) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_base/get_station_info_list_of_city',
      method: Method.POST,
      body: {
        cityId: cityId,
        type: type,
        exclusionStationBaseIdList: exclusionStationBaseIdList,
      },
    };
    return request(requestOptions);
  }

  // 20.根据站点ID和停靠点类型查询停靠点信息:绑定跨站停靠点时按类型获取的停靠点列表 批量绑定停靠点时按类型获取的停靠点列表
  public async getStopListByTypeOfStation({
    stationBaseId,
    type,
  }: {
    stationBaseId: number;
    type: string;
  }) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_stop/get_stop_list_by_type_of_station',
      method: Method.GET,
      urlParams: {
        stationBaseId,
        type,
      },
    };
    return request(requestOptions);
  }

  /**
   * 21.车辆配置确定接口
   * 车辆配置修改后的确定接口.
   * @param {number} param.deviceBaseId - 设备基础信息表id.
   * @param {string} param.vcode - 无人车在青龙系统中的编号.
   * @param {number} param.homeStopId - 本站home点id.
   * @param {number[]} param.linkedStopList - 绑定的其他停靠点id列表.
   * @returns A promise with the request response.
   */
  public async editStationVehicleDetail({
    deviceBaseId,
    vcode,
    homeStopId,
    linkedStopList,
  }: {
    deviceBaseId: number;
    vcode: string;
    homeStopId: number;
    linkedStopList: number[];
  }) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_vehicle/edit_station_vehicle_detail',
      method: Method.POST,
      body: {
        deviceBaseId,
        vcode,
        homeStopId,
        linkedStopList,
      },
    };
    return request(requestOptions);
  }

  // 22.判断车辆是否可以解绑
  public async isVehicleUnlinkable(deviceBaseId: number) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_vehicle/vehicle_whether_unlink',
      method: Method.GET,
      urlParams: { deviceBaseId },
    };
    return request(requestOptions);
  }

  /**
   * 23.解绑车辆接口
   * 用于车辆交付生命周期下的车辆解绑站点调用
   * @param deviceBaseId 车辆基础信息表id.
   * @param stationType 站点类型 'integrate' | 'robot' | 'vehicle'
   * @returns A promise resolving to the request's response.
   */
  public async unlinkVehicleStation(
    deviceBaseId: number,
    stationType: 'integrate' | 'robot' | 'vehicle',
  ) {
    const path =
      stationType === 'integrate'
        ? '/k2/management/station_warehouse/unlink_warehouse_station'
        : '/k2/management/station_vehicle/unlink_vehicle_station';
    const requestOptions: RequestOptions = {
      path: path,
      method: Method.GET,
      urlParams: { deviceBaseId },
    };
    return request(requestOptions);
  }

  /**
   * 24.批量绑定停靠点确定接口
   * 批量绑定停靠点.
   * @param {object} params - The parameters for the binding operation.
   * @param {number[]} params.deviceBaseIdList - 设备基础信息表id列表.
   * @param {number} params.homeStopId - 本站home点id.
   * @param {number[]} params.linkedStopList - 绑定的其他停靠点id列表.
   * @returns A promise representing the outcome of the bind operation.
   */
  public async stationVehicleBindStopBatch({
    deviceBaseIdList,
    homeStopId,
    linkedStopList,
    stationBaseId,
  }: {
    deviceBaseIdList: number[];
    homeStopId: number;
    stationBaseId: number;
    linkedStopList: number[];
  }) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_vehicle/station_vehicle_bind_stop_batch',
      method: Method.POST,
      body: {
        deviceBaseIdList,
        homeStopId,
        linkedStopList,
        stationBaseId,
      },
    };
    return request(requestOptions);
  }

  // 无人车站点停靠点配置页面
  /**
   * 25.分页查询无人车站点下的停靠点列表
   * Retrieves a paginated list of stops based on the provided search criteria.
   * @param searchOptions The options for searching, including page number, page size, and search form.
   * @returns A promise with the stop table data.
   */
  public async getStopTable(searchOptions: StopRequest) {
    const { pageNum, pageSize, searchForm } = searchOptions;
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_stop/station_stop_get_page_list',
      method: Method.POST,
      urlParams: {
        pageNum,
        pageSize,
      },
      body: searchForm,
    };
    return request(requestOptions);
  }

  /**
   * 26.批量变更停靠点状态
   * 批量变更停靠点状态，用于停用、启用、批量启用.
   * @param stopIdList - 停靠点列表.
   * @param enable - 状态.
   * @returns A promise with the request response.
   */
  public async changeStopStatus({
    stopIdList,
    enable,
  }: {
    stopIdList: number[];
    enable: number;
  }) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_stop/change_status',
      method: Method.POST,
      body: {
        stopIdList,
        enable,
      },
    };
    return request(requestOptions);
  }

  /**
   * 27.获取停靠点详情
   * Retrieves the detailed information of a specific stop by its ID.
   * @param stopId The unique identifier of the stop.
   * @returns A promise with the stop detail.
   */
  public async getStopDetail(stopId: number) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_stop/get_stop_detail',
      method: Method.GET,
      urlParams: { stopId },
    };
    return request(requestOptions);
  }

  // 28.获取车型的长宽高
  public async getSizeOfVehicle() {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device_type/get_size_of_device',
      method: Method.GET,
    };
    return request(requestOptions);
  }

  // 29.获取站点下的车辆列表
  public async getVehicleListOfStation(stationBaseId: number) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_vehicle/get_vehicle_list_of_station',
      method: Method.GET,
      urlParams: { stationBaseId },
    };
    return request(requestOptions);
  }

  // 30.获取车辆位置
  public async getVehicleLocation(deviceBaseId: string) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_stop/get_vehicle_location',
      method: Method.GET,
      urlParams: { deviceBaseId },
    };
    return request(requestOptions);
  }

  // 31.新建停靠点接口
  public async addStationStop(newStopInfo: NewStopInfo) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_stop/add_station_stop',
      method: Method.POST,
      body: newStopInfo,
    };
    return request(requestOptions);
  }

  // 32.停靠点编辑接口
  public async editStationStop(editStopInfo: EditStopInfo) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_stop/edit_station_stop',
      method: Method.POST,
      body: editStopInfo,
    };
    return request(requestOptions);
  }

  // 33.设备启用停用
  public async updateDeviceStatus(param: {
    deviceBaseId: number;
    enable: number;
  }) {
    return request({
      path: '/k2/management/station_warehouse/update_device_status',
      method: 'PUT',
      body: param,
    });
  }
  /**
   * 34.获取站点下的所有地图最新版本的停靠点列表，并按类型分组
   * @param {number} stationBaseId 站点基础id
   * @return {Promise}
   */
  public async getParkingPoint(stationBaseId: number): Promise<any> {
    return request({
      path: '/k2/management/station_warehouse/get_all_map_point_list',
      method: 'GET',
      urlParams: {
        stationBaseId,
      },
    });
  }
  /**
   * 35.下发导航任务
   * @param {number} deviceBaseId 设备基础id
   * @return {Promise}
   */
  public async deviceNavigation(param: {
    deviceBaseId: number;
    pointNo: string;
    pointType: string;
  }): Promise<any> {
    return request({
      path: '/k2/management/station_warehouse/device_navigation',
      method: 'POST',
      body: param,
    });
  }

  // 多合一站点关联车辆页面
  // 36.分页查询多合一无人车站点下的车辆列表
  public async getWarehouseTable(searchOptions: StationVehicleRequest) {
    const { pageNum, pageSize, searchForm } = searchOptions;
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_warehouse/get_device_page_list',
      method: Method.POST,
      urlParams: {
        pageNum,
        pageSize,
      },
      body: searchForm,
    };
    return request(requestOptions);
  }

  //37.根据站点ID获取终端可用接驳点列表
  public async getTransportPointList(stationBaseId: number) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_stop/get_third_transport_list',
      method: Method.GET,
      urlParams: { stationBaseId },
    };
    return request(requestOptions);
  }

  /**
   * 获取停车点列表
   * @param stationBaseId - 站点基础ID
   * @returns Promise对象，包含停车点列表数据
   */
  public async getParkPoints(stationBaseId: number) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_warehouse/get_home_point_list',
      method: Method.POST,
      body: {
        stationBaseId,
      },
    };
    return request(requestOptions);
  }

  /**
   * 更新停车点信息
   * @param deviceBaseId 设备基础ID
   * @param homePointNo 停车点编号
   * @returns Promise对象，包含更新结果
   */
  public async updateParkPoint(deviceBaseId: number, homePointNo: string) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_warehouse/update_device_config',
      method: Method.PUT,
      body: {
        deviceBaseId,
        homePointNo,
      },
    };
    return request(requestOptions);
  }

  /**
   * 解除设备与停车点的绑定
   * @param deviceBaseId 设备基础ID
   * @returns 解除绑定的结果
   */
  public async unbindParkPoint(deviceBaseId: number) {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_warehouse/unbind_device_point',
      method: Method.POST,
      body: {
        deviceBaseId,
      },
    };
    return request(requestOptions);
  }

  public async batchModifyStationPerson(params: any) {
    return request({
      absoluteURL: `${this.fetchDomain}/k2/management/station/batch_edit_station_person_info`,
      method: 'POST',
      body: params,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
    });
  }
}
