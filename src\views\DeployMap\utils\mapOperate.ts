import { Feature } from 'ol';
import { MapLayerEnum } from './enum';
import ScaleLine from 'ol/control/ScaleLine';
import { Style } from 'ol/style';
import { Stroke } from 'ol/style';
import { TASK_ROUTE_COLOR, TASK_ROUTE_HIGHLIGHT_COLOR } from '@/utils/constant';
import * as turf from '@turf/turf';
import LayerVector from 'ol/layer/Vector.js';
import SourceVector from 'ol/source/Vector';
import Point from 'ol/geom/Point.js';
import Icon from 'ol/style/Icon.js';
class MapOperate {
  map: any;
  points: any[] = [];
  constructor(mapInstance) {
    this.map = mapInstance;
    this.initScaleLine();
  }

  initScaleLine = () => {
    const scaleLineControl = new ScaleLine({
      className: 'map-scale-line',
    });
    scaleLineControl.setUnits('metric');
    this.map.addControl(scaleLineControl);
  };

  /**
   * 添加图层，默认把绘制图层也添加上
   * @param layers 图层数组
   */
  addLayers = (layers: { layer: any; layerId: MapLayerEnum }[]) => {
    if (!layers || !Array.isArray(layers)) {
      console.error('Invalid layers parameter:', layers);
      return;
    }

    layers.forEach((item: any) => {
      try {
        if (!item || !item.layer) {
          console.error('Invalid layer item:', item);
          return;
        }

        const _layer = item.layer;
        _layer.set('layerId', item.layerId);
        this.map.addLayer(_layer);
      } catch (error) {
        console.error(`Error adding layer ${item?.layerId}:`, error);
      }
    });
  };

  /**
   * 清空指定图层
   */
  clearLayer(layerId: string) {
    const l = this.getLayer(layerId);
    l && l.getSource().clear();
    l && this.map.removeLayer(l);
  }

  /**
   * 创建图层
   * @param opts 图层配置 style zIndex等
   * @param {string} layerId 图层Id
   */
  createLayer = (opts: any, layerId: string) => {
    const newSource = new SourceVector();
    const newLayer = new LayerVector({
      ...opts,
      source: newSource,
    });
    newLayer.set('layerId', layerId);
    this.map.addLayer(newLayer);
    return newLayer;
  };

  /**
   * 获取图层
   * @param {string | undefined | null} layerId 图层Id
   */
  getLayer = (layerId: string | undefined | null) => {
    if (!layerId) {
      return null;
    }
    let layers = this.map.getLayers().getArray();
    for (let l of layers) {
      if (l.values_.layerId === layerId) {
        return l;
      }
    }
    return null;
  };

  /**
   * 更改地图中心点
   * @param lat 纬度
   * @param lon 经度
   * @param r
   */
  changeCenter = (lat: number, lon: number) => {
    const curView = this.map.getView();
    curView.animate({
      zoom: 18,
      center: [lon, lat],
      duration: 400,
    });
  };

  /**
   * 点的feature
   * @param {Array} coordinates 点的位置
   * @param {string} imgSrc 图片地址
   * @param {string} pointId 点的id
   * @param {object} otherStyle 点的其他样式
   */
  createPointFeature = (
    coordinates: any,
    imgSrc: string,
    pointId?: string,
    otherStyle?: object,
    anchor?: any,
    zIndex?: number,
  ) => {
    const pointFeature = new Feature({
      geometry: new Point(coordinates),
    });
    pointFeature.setId(
      pointId === undefined ? `point${this.points.length + 1}` : pointId,
    );
    const style = new Style({
      image: new Icon({
        ...otherStyle,
        src: imgSrc,
        anchor: anchor ?? [0.5, 1],
      }),
      zIndex: zIndex ?? 100,
    });
    pointFeature.setStyle(style);
    this.points.push(pointFeature);
    return pointFeature;
  };

  /**
   *
   * 更改搜索点
   * @param {[number, number]} coordinates 点的坐标
   * @param {string} layerId 搜索点添加在哪个图层上 不传的话搜索点是添加在LayerIdEnum.SEARCHPOINT_LAYER图层上
   */
  changeSearchPoint = (coordinates: [number, number], layerId?: string) => {
    const obtainedLayer = this.getLayer(MapLayerEnum.CENTERPOINT_LAYER);
    if (obtainedLayer) {
      const searchPointIdx = this.points.findIndex(
        (item) => item.getId() === 'searchPoint',
      );
      obtainedLayer
        .getSource()
        .getFeatureById('searchPoint')
        .getGeometry()
        .setCoordinates(coordinates);
      this.points.splice(searchPointIdx, 1);
    } else {
      const newLayer = this.createLayer(
        {
          zIndex: 120,
        },
        MapLayerEnum.CENTERPOINT_LAYER,
      );
      const searchPointFeature = this.createPointFeature(
        coordinates,
        require('../../../assets/image/common/location.png'),
        'searchPoint',
        {
          scale: 0.3,
        },
      );
      newLayer.getSource()!.addFeature(searchPointFeature);
    }
  };

  /**
   * 改变图层显隐
   * @param layerId 图层Id
   * @param visible 是否显示 false不显示 true显示
   * @param retryCount 重试次数，默认为0
   * @param maxRetries 最大重试次数，默认为5
   * @param retryDelay 重试延迟时间(ms)，默认为100ms
   */
  changeLayerVisible = (
    layerId: any,
    visible: boolean,
    retryCount: number = 0,
    maxRetries: number = 5,
    retryDelay: number = 100,
  ) => {
    const layer = this.getLayer(layerId);

    if (layer) {
      // 如果找到图层，直接设置可见性
      layer.setVisible(visible);
    } else if (retryCount < maxRetries) {
      // 如果没找到图层且未超过最大重试次数，延迟后重试
      // console.log(
      //   `图层 ${layerId} 未找到，${retryDelay}ms后重试 (${
      //     retryCount + 1
      //   }/${maxRetries})`,
      // );
      setTimeout(() => {
        this.changeLayerVisible(
          layerId,
          visible,
          retryCount + 1,
          maxRetries,
          retryDelay,
        );
      }, retryDelay);
    } else {
      // 超过最大重试次数，记录错误
      // console.error(
      //   `无法找到图层 ${layerId}，已达到最大重试次数 ${maxRetries}`,
      // );
    }
  };

  highlightRoute = (feature: Feature, normalColor: TASK_ROUTE_COLOR) => {
    // 创建阴影样式
    const shadowStyle = new Style({
      stroke: new Stroke({
        color: TASK_ROUTE_HIGHLIGHT_COLOR[normalColor], // 阴影颜色
        width: 6, // 阴影宽度要大于主线
        lineCap: 'round', // 线条端点样式
        lineJoin: 'round', // 线条连接处样式
      }),
      zIndex: 1001,
    });

    // 安全地获取坐标
    const geometry = feature.getGeometry();
    if (geometry && typeof (geometry as any).getCoordinates === 'function') {
      const coordinates = (geometry as any).getCoordinates();
      const turfFeature = turf.points(coordinates);
      const center = turf.center(turfFeature).geometry.coordinates;
      this.map.getView().setCenter(center);
      this.map.getView().fit(geometry);
    }

    const highlightStyle = [shadowStyle];
    feature.setStyle(highlightStyle);
  };

  /**
   * 取消高亮
   * @param feature
   * @param normalColor
   */
  cancelHighlight = (feature: Feature, normalColor: TASK_ROUTE_COLOR) => {
    const mainStyle = new Style({
      stroke: new Stroke({
        color: normalColor, // 主线颜色
        width: 6, // 主线宽度
        lineCap: 'round',
        lineJoin: 'round',
      }),
    });
    feature.setStyle(mainStyle);
  };

  /**
   * 更新起点或终点
   * @param type 点类型，'start'表示起点，'end'表示终点
   * @param point 点位信息，包含经纬度等
   */
  updateStartOrEndPoint = (type: 'start' | 'end', point: any) => {
    if (!point || !point.latitude || !point.longitude) {
      // 如果point为空，则移除对应的点
      this.removeStartOrEndPoint(type);
      return;
    }

    // 获取图标
    const iconSrc =
      type === 'start'
        ? require('@/assets/image/mapTrackPage/start-point-unselected.png')
        : require('@/assets/image/mapTrackPage/end-point-unselected.png');

    // 点的ID
    const pointId = `${type}Point`;
    // 坐标数组 [经度, 纬度]
    const coordinates: [number, number] = [point.longitude, point.latitude];

    // 获取或创建图层
    let layer = this.getLayer(MapLayerEnum.START_END_POINT_LAYER);

    // 为最后修改的点位设置更高的zIndex，确保它显示在最上层
    // 基础zIndex值为1000，最后修改的点位会获得额外的zIndex提升
    const baseZIndex = 1000;

    if (layer) {
      // 图层已存在，检查点是否存在
      const pointFeature = layer.getSource().getFeatureById(pointId);

      // 获取另一个点位（如果当前是起点，则获取终点；如果当前是终点，则获取起点）
      const otherType = type === 'start' ? 'end' : 'start';
      const otherPointId = `${otherType}Point`;
      const otherPointFeature = layer.getSource().getFeatureById(otherPointId);

      if (pointFeature) {
        // 点已存在，更新坐标
        pointFeature.getGeometry().setCoordinates(coordinates);

        // 更新样式，提高zIndex使其显示在最上层
        const currentStyle = pointFeature.getStyle();
        const newStyle = new Style({
          image: currentStyle.getImage(),
          zIndex: baseZIndex + 10, // 为最后修改的点位设置更高的zIndex
        });
        pointFeature.setStyle(newStyle);
        // 如果存在另一个点位，降低其zIndex
        if (otherPointFeature) {
          const otherStyle = otherPointFeature.getStyle();
          const newOtherStyle = new Style({
            image: otherStyle.getImage(),
            zIndex: baseZIndex,
          });
          otherPointFeature.setStyle(newOtherStyle);
        }
      } else {
        // 点不存在，创建新点
        const pointFeature = this.createPointFeature(
          coordinates,
          iconSrc,
          pointId,
          {
            scale: 0.5,
          },
          [0.5, 1], // 默认锚点
          baseZIndex + 10, // 为最后修改的点位设置更高的zIndex
        );
        layer.getSource().addFeature(pointFeature);

        // 如果存在另一个点位，降低其zIndex
        if (otherPointFeature) {
          const otherStyle = otherPointFeature.getStyle();
          const newOtherStyle = new Style({
            image: otherStyle.getImage(),
            zIndex: baseZIndex,
          });
          otherPointFeature.setStyle(newOtherStyle);
        }
      }
    } else {
      // 图层不存在，创建新图层和点
      layer = this.createLayer(
        {
          zIndex: 9999, // 确保在其他图层之上
        },
        MapLayerEnum.START_END_POINT_LAYER,
      );
      const pointFeature = this.createPointFeature(
        coordinates,
        iconSrc,
        pointId,
        {
          scale: 0.5,
        },
        [0.5, 1], // 默认锚点
        baseZIndex + 10, // 为最后修改的点位设置更高的zIndex
      );
      setTimeout(() => {
        if (layer.getSource()) {
          layer.getSource().addFeature(pointFeature);
        }
      }, 100);
    }

    // 将地图中心移动到该点
    this.changeCenter(point.latitude, point.longitude);
  };

  /**
   * 移除起点或终点
   * @param type 点类型，'start'表示起点，'end'表示终点
   */
  removeStartOrEndPoint = (type: 'start' | 'end') => {
    const pointId = `${type}Point`;
    const layer = this.getLayer(MapLayerEnum.START_END_POINT_LAYER);

    if (layer) {
      const pointFeature = layer.getSource().getFeatureById(pointId);
      if (pointFeature) {
        // 从图层中移除点
        layer.getSource().removeFeature(pointFeature);

        // 从points数组中移除
        const pointIdx = this.points.findIndex(
          (item) => item.getId() === pointId,
        );
        if (pointIdx !== -1) {
          this.points.splice(pointIdx, 1);
        }
      }
    }
  };
}
export default MapOperate;
