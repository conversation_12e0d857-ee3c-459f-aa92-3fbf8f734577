import { Button, Descriptions, Form, Input, message, Modal, Table } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import EditModuleTitle from '@/components/EditModuleTitle';
import { formatLocation } from '@/utils/utils';
import { gridColumns } from '@/views/DeviceLife/utils/column';

import './index.scss';
import { sensorTableColumn } from './columns';
import { vehicleTypeTableColumn } from './columns';
import { StationFetchApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
const fetchApi = new StationFetchApi();
const layout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 21 },
};

interface Props {
  basicForm: any;
}
const vehicleBasicInfoForm: any[] = [
  {
    name: 'productTypeName',
    label: '产品类型',
    rules: true,
    message: '请输入产品类型!',
    contentType: 'input',
    placeholder: '系统自动带入',
    disable: true,
  },
  {
    name: 'businessTypeName',
    label: '设备类型',
    rules: true,
    message: '请输入设备类型!',
    contentType: 'input',
    placeholder: '系统自动带入',
    disable: true,
  },
  {
    name: 'deviceName',
    label: '车牌号',
    rules: true,
    message: '请输入车牌号!',
    contentType: 'input',
    placeholder: '系统自动带入',
    disable: true,
  },
  {
    name: 'serialNo',
    label: '车架号',
    rules: true,
    message: '请输入车架号!',
    contentType: 'input',
    placeholder: '系统自动带入',
    disable: true,
  },
];
const BasicInfo = (props: Props) => {
  const { basicForm } = props;
  const navigator = useNavigate();
  const initialValues = { ...basicForm };
  const [form] = useForm();
  const [dataSource, setDataSource] = useState<any>();
  const [showDeviceTypeDetail, setShowDeviceTypeDetail] = useState(false);
  useEffect(() => {
    basicForm &&
      Object.keys(basicForm).forEach((itemKey: string) => {
        form.setFieldsValue({
          [itemKey]: basicForm[itemKey],
        });
      });
  }, [JSON.stringify(basicForm)]);

  const goGridEdit = () => {
    const locationMap = formatLocation(window.location.search);
    const vehicleId = locationMap.deviceBaseId;
    const url = `/app/stationManagement/detail/GridEdit?id=${vehicleId}`;
    navigator(url);
  };

  const gridTableDataSource = () => {
    return (
      (basicForm &&
        basicForm.gridList.map((ele: any) => {
          return { ...ele, pallet: ele.pallet || '无' };
        })) ||
      []
    );
  };

  const getDeviceTypeDetail = async () => {
    try {
      const res = await fetchApi.getDeviceTypeDetail(
        basicForm.deviceTypeBaseId,
      );
      if (res.code === HttpStatusCode.Success) {
        setDataSource(res.data);
      } else {
        message.error(res.message);
      }
    } catch (err) {
      console.error(err);
    }
  };
  return (
    <div className="basic-info">
      <EditModuleTitle title={'基本信息'} />
      <Form
        {...layout}
        className="reform"
        form={form}
        initialValues={initialValues}
      >
        {vehicleBasicInfoForm.map((item: any, index: number) => {
          return (
            <Form.Item label={item.label} name={item.name} key={index}>
              <Input placeholder={item.placeholder} disabled={item.disable} />
            </Form.Item>
          );
        })}

        <Form.Item label={'车型'} name={'deviceTypeName'}>
          <Input
            placeholder={'系统自动带入'}
            disabled={true}
            addonAfter={
              <Button
                type="link"
                size="small"
                onClick={() => {
                  getDeviceTypeDetail();
                  setShowDeviceTypeDetail(true);
                }}
              >
                详情
              </Button>
            }
          />
        </Form.Item>
        <Form.Item label={'格口模板'} name={'boxTemplateName'}>
          <Input placeholder={'系统自动带入'} disabled={true} />
        </Form.Item>
        <Form.Item label="格口详情" name="box">
          <Button type="primary" onClick={goGridEdit}>
            格口调整
          </Button>
        </Form.Item>
        <Form.Item label="  " name="table" colon={false}>
          <Table
            dataSource={gridTableDataSource()}
            columns={gridColumns}
            pagination={false}
            rowKey={(record) => record.id}
            bordered
          ></Table>
        </Form.Item>
      </Form>
      <Modal
        open={showDeviceTypeDetail}
        footer={null}
        width={800}
        onCancel={() => {
          setShowDeviceTypeDetail(false);
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
          }}
        >
          <div
            style={{
              height: 20,
              width: 5,
              backgroundColor: 'red',
              marginRight: 6,
            }}
          />
          {`车型id： ${dataSource?.deviceTypeBaseId}`}
        </div>
        <div style={{ marginTop: 10 }}>
          <Descriptions
            bordered
            column={2}
            items={[
              {
                key: 'sensorSchemeId',
                label: '传感器方案id',
                children: dataSource?.sensorSchemeId,
              },
              {
                key: 'sensorSchemeName',
                label: '传感器方案名称',
                children: dataSource?.sensorSchemeName,
              },
              {
                key: 'manufactoryId',
                label: '生产厂商id',
                children: dataSource?.manufactoryId,
              },
              {
                key: 'manufactoryName',
                label: '生产厂商名称',
                children: dataSource?.manufactoryName,
              },
            ]}
          />
        </div>

        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
          }}
        >
          <div
            style={{
              height: 20,
              width: 5,
              backgroundColor: 'red',
              marginRight: 6,
            }}
          />
          车型硬件信息
        </div>
        <div style={{ marginTop: 10 }}>
          <Table
            scroll={{
              x: '600px',
            }}
            rowKey={(record) => record.hardwareModelId}
            size="small"
            bordered
            columns={vehicleTypeTableColumn}
            dataSource={dataSource?.deviceTypeHardwareInfoList}
            pagination={false}
          />
        </div>

        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            marginTop: 20,
          }}
        >
          <div
            style={{
              height: 20,
              width: 5,
              backgroundColor: 'red',
              marginRight: 6,
            }}
          />
          传感器硬件信息
        </div>
        <div style={{ marginTop: 10 }}>
          <Table
            rowKey={(record) => record.hardwareModelId}
            size="small"
            bordered
            columns={sensorTableColumn}
            dataSource={dataSource?.sensorSchemeDetailList}
            pagination={false}
          />
        </div>
      </Modal>
    </div>
  );
};
export default React.memo(BasicInfo);
