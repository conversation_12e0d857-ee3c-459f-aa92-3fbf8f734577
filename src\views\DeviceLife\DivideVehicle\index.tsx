import { Button, Form, message, Modal } from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';
import {
  clearVehicleManage,
  vehicleManageSelector,
} from '@/redux/reducer/vehicleManage';
import DivideForm from '../components/DivideForm';
import { DeviceLifeApi } from '@/fetch/business';
import './index.scss';
import {
  vehicleOwnerUseCase,
  stationCaseCode,
  PageType,
} from '../utils/constant';
import { HttpStatusCode } from '@/fetch/core/constant';
import { AnyFunc } from '@/global';

const TitleMap = new Map([
  [PageType.productBindStation, '车辆绑站'],
  [PageType.scheduleChangeStation, '车辆转站'],
  [PageType.scheduleReceiveStation, '车辆接收'],
  [PageType.scheduleDeliverStation, '车辆交付'],
  [PageType.usingChangeStation, '车辆转站'],
  [PageType.repair, '转维修中心'],
]);
interface Props {
  pageType: PageType;
  show: boolean;
  setShow: AnyFunc;
  setTableKey: AnyFunc;
}
const DivideVehicle = (props: Props) => {
  const { pageType, show, setShow, setTableKey } = props;
  const fetchApi = new DeviceLifeApi();
  const [form] = Form.useForm();
  const [selectVehicleList, setSelectVehicleList] = useState([]);
  const [title, setTitle] = useState<string>();
  const selector = useSelector(vehicleManageSelector);
  const dispatch = useDispatch();
  useEffect(() => {
    return () => {
      dispatch(clearVehicleManage({}));
    };
  }, []);
  useEffect(() => {
    setTitle(TitleMap.get(pageType));
  }, [pageType]);
  const cancel = () => {
    setShow(false);
  };
  // 点击确定
  const confirm = async () => {
    await form.validateFields();
    const deviceNameList: string[] = [];
    selectVehicleList.forEach((item: any) =>
      deviceNameList.push(item.deviceName),
    );
    const params = {
      deviceNameList: deviceNameList,
      stationBaseId: form.getFieldsValue().stationInfo
        ? form.getFieldsValue().stationInfo[2]
        : null,
    };
    switch (pageType) {
      case PageType.productBindStation:
        deviceBatchBindStation(params);
        break;
      case PageType.scheduleChangeStation:
        deviceChangeStation(params);
        break;
      case PageType.scheduleReceiveStation:
        deviceReceive(params);
        break;
      case PageType.scheduleDeliverStation:
        if (
          form.getFieldsValue().ownerUseCase === vehicleOwnerUseCase.FIX ||
          form.getFieldsValue().ownerUseCase === vehicleOwnerUseCase.SCHEDULE ||
          form.getFieldsValue().ownerUseCase === vehicleOwnerUseCase.PRODUCT
        ) {
          Modal.warning({
            title: '提示',
            content: '【车辆交付】的车辆归属方不能为调度、生产、维修，请修改！',
          });
          return;
        }
        deviceDistribute({
          stationBaseId: params.stationBaseId,
          ownerUseCase: form.getFieldsValue().ownerUseCase,
          deviceNameList: params.deviceNameList,
        });
        break;
      case PageType.usingChangeStation:
        if (
          form.getFieldsValue().ownerUseCase === vehicleOwnerUseCase.FIX ||
          form.getFieldsValue().ownerUseCase === vehicleOwnerUseCase.SCHEDULE ||
          form.getFieldsValue().ownerUseCase === vehicleOwnerUseCase.PRODUCT
        ) {
          Modal.warning({
            title: '提示',
            content: '车辆归属方不能为“生产、维修、调度”，请修改！',
          });
          return;
        }
        deviceUsingChangeStation({
          stationBaseId: params.stationBaseId,
          ownerUseCase: form.getFieldsValue().ownerUseCase,
          deviceNameList: params.deviceNameList,
        });
        break;
      case PageType.repair:
        if (form.getFieldValue('useCase') !== stationCaseCode.FIX_CENTER) {
          Modal.warning({
            title: '提示',
            content: '仅站点用途为“车辆维修中心”的才允许提交！',
          });
          return;
        }
        deviceToRepairCenter(params);
        break;
    }
  };

  /** 车辆生产-批量绑站 */
  const deviceBatchBindStation = async (params) => {
    try {
      const res = await fetchApi.deviceBindStation(params);
      if (res.code === HttpStatusCode.Success) {
        message.success(res.message);
        setShow(false);
        setTableKey(new Date().getMilliseconds().toString());
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    }
  };
  /** 车辆调度-转站/批量转站 */
  const deviceChangeStation = async (params: any) => {
    try {
      const res: any = await fetchApi.deviceChangeStation(params);
      if (res.code === HttpStatusCode.Success) {
        message.success(res.message);
        setShow(false);
        setTableKey(new Date().getMilliseconds().toString());
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    }
  };
  /** 车辆调度-接收/批量接收 */
  const deviceReceive = async (params: any) => {
    try {
      const res: any = await fetchApi.deviceReceive(params);
      if (res.code === HttpStatusCode.Success) {
        message.success(res.message);
        setShow(false);
        setTableKey(new Date().getMilliseconds().toString());
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    }
  };
  /** 车辆调度-交付/批量交付 */
  const deviceDistribute = async (params: any) => {
    try {
      const res: any = await fetchApi.deviceDistribute(params);
      if (res.code === HttpStatusCode.Success) {
        message.success(res.message);
        setShow(false);
        setTableKey(new Date().getMilliseconds().toString());
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    }
  };
  /** 车辆交付-转站/批量转站 */
  const deviceUsingChangeStation = async (params: any) => {
    try {
      const res = await fetchApi.bactchChangeStation(params);
      if (res && res.code === HttpStatusCode.Success) {
        message.success(res.message);
        setShow(false);
        setTableKey(new Date().getMilliseconds().toString());
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    }
  };

  /** 车辆维修-转维修中心 */
  const deviceToRepairCenter = async (params: any) => {
    try {
      const res: any = await fetchApi.deviceToMaintain(params);
      if (res.code === HttpStatusCode.Success) {
        message.success(res.message);
        setShow(false);
        setTableKey(new Date().getMilliseconds().toString());
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    }
  };
  const tagList = selector.selectedRowKeys;
  const handleDivide = (values: any) => {
    setSelectVehicleList(values);
  };
  useEffect(() => {
    if (tagList) {
      setSelectVehicleList(tagList);
    } else {
      message.error('请重新选择车辆');
    }
  }, []);
  return (
    <Modal title={title} footer={null} open={show} onCancel={cancel}>
      <div className="divide">
        <DivideForm
          form={form}
          tagList={selectVehicleList}
          handleDivide={handleDivide}
          origin={pageType}
        ></DivideForm>
        <div className="submit">
          <Button type="primary" onClick={confirm}>
            确认
          </Button>
          <Button onClick={cancel}>取消</Button>
        </div>
      </div>
    </Modal>
  );
};
export default DivideVehicle;
