// 将JDX_APP开头的环境变量通过DefinePlugin注入到程序中
const JDX_APP = /^JDX_APP_/i;
function getClientEnvironment(publicUrl) {
  const scriptsArgv = process.argv.slice(2);
  const scriptIndex = scriptsArgv.findIndex(
    x => x === 'server'
  );
  const raw = Object.keys(process.env)
    .filter(key => JDX_APP.test(key))
    .reduce(
      (env, key) => {
        env[key] = process.env[key];
        return env;
      },
      {
        NODE_ENV: scriptIndex == -1 ? process.env.NODE_ENV : 'development',
        PUBLIC_URL: publicUrl || '/',
      }
    );

  const stringified = {
    'process.env': Object.keys(raw).reduce((env, key) => {
      env[key] = JSON.stringify(raw[key]);
      return env;
    }, {}),
  }

  return {
    raw,
    stringified,
  }
}

module.exports = {
  getClientEnvironment: getClientEnvironment,
};