// eslint-disable-next-line no-unused-vars
import { StationItemType } from './type';
import { StopPointType } from './constant';

export const findSelectedPoint = (values: any, selectedAll: boolean) => {
  const points: any = values;
  for (const key in values) {
    if (key === StopPointType.HOME && selectedAll) {
      points[key] = points[key].length > 0 ? [points[key][0]] : [];
    } else {
      points[key] = values[key]?.filter((item: any) => {
        if (selectedAll && key !== StopPointType.HOME) {
          return item;
        } else {
          if (item.isLinked) {
            return item;
          }
        }
      });
    }
  }
  return points;
};

export const formateSelectedPoint = (values: any) => {
  for (const key in values) {
    if (values.hasOwnProperty.call(key)) {
      values[key] = values[key]?.filter((item: any) => {
        return {
          id: item.id,
          waitingTime: item.waitingTime,
        };
      });
    }
  }
  return values;
};

export const getVehicleTypeNameByCode = (typeList: any, code: any) => {
  if (!typeList || !code) return null;
  const filteredList = typeList.filter((item: any) => item.code === code);
  if (filteredList.length > 0) {
    return filteredList[0].name;
  }
  return null;
};
