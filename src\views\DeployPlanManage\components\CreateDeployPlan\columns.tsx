import { FormConfig } from '@jd/x-coreui';
import { CommonApi, StationFetchApi, DeployPlan } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  DeployPlanSession,
  MapVehicleSource,
  VehicleSupplier,
} from '@/utils/enum';
import React from 'react';

const requestApi = new StationFetchApi();
const deployPlanApi = new DeployPlan();
export const CreateDeployForm1: FormConfig = {
  fields: [
    {
      fieldName: 'stationBaseId',
      label: '站点选择',
      placeholder: '请选择站点',
      type: 'select',
      options: [],
      labelInValue: false,
      showSearch: true,
      validatorRules: [
        {
          required: true,
          message: '请选择站点',
        },
      ],
      wrapperCol: {
        span: 21,
      },
      labelCol: {
        span: 3,
      },
    },
    {
      childrenList: ['needVehicleCount', 'supplier'],
      fieldName: 'vehicleInfo',
      label: '',
      wrapperCol: {
        span: 24,
      },
      labelCol: {
        span: 0,
      },
    },
    {
      fieldName: 'needVehicleCount',
      label: '需求车数',
      marginLeft: 11,
      marginRight: 8,
      width: '48%',
      isChild: true,
      placeholder: '请输入需求车数',
      type: 'inputNumber',
      allowClear: true,
      step: 1,
      formatter: (value) => {
        return value > 0 ? value : null;
      },
      parser: (value) => {
        return value > 0 ? value : null;
      },
      validatorRules: [
        {
          required: true,
          message: '请输入需求车数',
        },
      ],
    },
    {
      fieldName: 'supplier',
      label: '车辆供应商',
      marginLeft: 8,
      marginRight: 0,
      width: '48%',
      isChild: true,
      placeholder: '请选择车辆供应商',
      type: 'select',
      options: [],
      labelInValue: false,
      showSearch: true,
      validatorRules: [
        {
          required: true,
          message: '请选择车辆供应商',
        },
      ],
    },
    {
      fieldName: 'planRemark',
      label: '备注',
      type: 'input',
      placeholder: '非必填',
      maxLength: 50,
      wrapperCol: {
        span: 21,
      },
      labelCol: {
        span: 3,
      },
      validatorRules: [
        {
          pattern:
            /^[\u4E00-\u9FA5A-Za-z0-9\s.,!?，。！？、：；“”‘’—…《》【】]+$/g,
          message: '不支持特殊字符（仅支持文字、标点）',
        },
      ],
    },
  ],
  linkRules: {
    fetchData: [
      {
        linkFieldName: 'supplier',
        rule: 'fetchData',
        fetchFunc: async (val) => {
          const res = await requestApi.getCommonDropDown(['SUPPLIER']);
          if (
            res.code === HttpStatusCode.Success &&
            Array.isArray(res.data.supplierList)
          ) {
            return res.data.supplierList.map((v) => ({
              label: v.name,
              value: v.code,
            }));
          }
          return [];
        },
      },
      {
        linkFieldName: 'stationBaseId',
        rule: 'fetchData',
        fetchFunc: async (val) => {
          const res = await deployPlanApi.getStation();
          if (res.code === HttpStatusCode.Success && Array.isArray(res.data)) {
            return res.data.map((v) => ({
              label: v.name,
              value: v.id,
            }));
          } else {
            return [];
          }
        },
      },
    ],
  },
};

export const CreateDeployForm2: FormConfig = {
  fields: [
    {
      label: <h4>车号确认</h4>,
      childrenList: [
        `${DeployPlanSession.VEHICLE_CONFIRMATION}/appointorErp`,
        `${DeployPlanSession.VEHICLE_CONFIRMATION}/expectedDate`,
      ],
      fieldName: DeployPlanSession.VEHICLE_CONFIRMATION,
      labelCol: {
        span: 3,
      },
      wrapperCol: {
        span: 21,
      },
    },
    {
      fieldName: `${DeployPlanSession.VEHICLE_CONFIRMATION}/appointorErp`,
      label: '指派人',
      marginLeft: 0,
      marginRight: 8,
      width: '48%',
      isChild: true,
      placeholder: '请选择指派人',
      type: 'select',
      options: [],
      showSearch: true,
      labelInValue: false,
      validatorRules: [
        {
          required: true,
          message: '请选择指派人',
        },
      ],
    },
    {
      fieldName: `${DeployPlanSession.VEHICLE_CONFIRMATION}/expectedDate`,
      label: '期望完成日期',
      marginLeft: 8,
      marginRight: 0,
      width: '48%',
      isChild: true,
      type: 'datePicker',
      validatorRules: [
        {
          required: true,
          message: '请选择期望完成日期',
        },
      ],
    },
    {
      label: <h4>车辆保险</h4>,
      fieldName: DeployPlanSession.VEHICLE_INSURANCE,
      childrenList: [
        `${DeployPlanSession.VEHICLE_INSURANCE}/appointorErp`,
        `${DeployPlanSession.VEHICLE_INSURANCE}/expectedDate`,
      ],
      labelCol: {
        span: 3,
      },
      wrapperCol: {
        span: 21,
      },
    },
    {
      fieldName: `${DeployPlanSession.VEHICLE_INSURANCE}/appointorErp`,
      label: '指派人',
      marginLeft: 0,
      marginRight: 8,
      width: '48%',
      isChild: true,
      placeholder: '请选择指派人',
      type: 'select',
      options: [],
      labelInValue: false,
      showSearch: true,
      validatorRules: [
        {
          required: true,
          message: '请选择指派人',
        },
      ],
    },
    {
      fieldName: `${DeployPlanSession.VEHICLE_INSURANCE}/expectedDate`,
      label: '期望完成日期',
      marginLeft: 8,
      marginRight: 0,
      width: '48%',
      isChild: true,
      type: 'datePicker',
      validatorRules: [
        {
          required: true,
          message: '请选择期望完成日期',
        },
      ],
    },
    {
      label: <h4>车辆调拨</h4>,
      childrenList: [
        `${DeployPlanSession.VEHICLE_ALLOCATION}/appointorErp`,
        `${DeployPlanSession.VEHICLE_ALLOCATION}/expectedDate`,
        `${DeployPlanSession.VEHICLE_ALLOCATION}/receiveContact`,
      ],
      fieldName: DeployPlanSession.VEHICLE_ALLOCATION,
      labelCol: {
        span: 3,
      },
      wrapperCol: {
        span: 21,
      },
    },
    {
      fieldName: `${DeployPlanSession.VEHICLE_ALLOCATION}/appointorErp`,
      label: '指派人',
      marginLeft: 0,
      marginRight: 8,
      width: '48%',
      isChild: true,
      placeholder: '请选择指派人',
      type: 'select',
      options: [],
      labelInValue: false,
      showSearch: true,
      validatorRules: [
        {
          required: true,
          message: '请选择指派人',
        },
      ],
    },
    {
      fieldName: `${DeployPlanSession.VEHICLE_ALLOCATION}/expectedDate`,
      label: '期望完成日期',
      marginLeft: 8,
      marginRight: 0,
      width: '48%',
      isChild: true,
      type: 'datePicker',
      validatorRules: [
        {
          required: true,
          message: '请选择期望完成日期',
        },
      ],
    },
    {
      fieldName: `${DeployPlanSession.VEHICLE_ALLOCATION}/receiveContact`,
      label: '接收人联系方式',
      type: 'input',
      placeholder: '请输入接收人联系方式',
      maxLength: 50,
      required: true,
      marginLeft: -10,
      marginRight: 0,
      width: '100%',
      isChild: true,
      validatorRules: [
        {
          required: true,
          message: '请输入接收人联系方式',
        },
        {
          pattern:
            /^[\u4E00-\u9FA5A-Za-z0-9\s.,!?，。！？、：；“”‘’—…《》【】]+$/g,
          message: '不支持特殊字符（仅支持文字、标点）',
        },
      ],
      labelCol: {
        span: 5,
      },
      wrapperCol: {
        span: 19,
      },
    },
    {
      label: <h4>确认路线</h4>,
      childrenList: [
        `${DeployPlanSession.ROUTE_CONFIRMATION}/appointorErp`,
        `${DeployPlanSession.ROUTE_CONFIRMATION}/expectedDate`,
      ],
      fieldName: DeployPlanSession.ROUTE_CONFIRMATION,
      labelCol: {
        span: 3,
      },
      wrapperCol: {
        span: 21,
      },
    },
    {
      fieldName: `${DeployPlanSession.ROUTE_CONFIRMATION}/appointorErp`,
      label: '指派人',
      marginLeft: 0,
      marginRight: 8,
      width: '48%',
      isChild: true,
      placeholder: '请选择指派人',
      type: 'select',
      options: [],
      labelInValue: false,
      showSearch: true,
      validatorRules: [
        {
          required: true,
          message: '请选择指派人',
        },
      ],
    },
    {
      fieldName: `${DeployPlanSession.ROUTE_CONFIRMATION}/expectedDate`,
      label: '期望完成日期',
      marginLeft: 8,
      marginRight: 0,
      width: '48%',
      isChild: true,
      type: 'datePicker',
      validatorRules: [
        {
          required: true,
          message: '请选择期望完成日期',
        },
      ],
    },
    {
      label: <h4>地图采集</h4>,
      childrenList: [
        `${DeployPlanSession.MAP_COLLECTION}/appointorErp`,
        `${DeployPlanSession.MAP_COLLECTION}/expectedDate`,
        'mapVehicleSource',
        `${DeployPlanSession.MAP_COLLECTION}/mapVehicleRemark`,
      ],
      fieldName: DeployPlanSession.MAP_COLLECTION,
      labelCol: {
        span: 3,
      },
      wrapperCol: {
        span: 21,
      },
    },
    {
      fieldName: `${DeployPlanSession.MAP_COLLECTION}/appointorErp`,
      label: '指派人',
      marginLeft: 0,
      marginRight: 8,
      width: '48%',
      isChild: true,
      placeholder: '请选择指派人',
      type: 'select',
      showSearch: true,
      options: [],
      labelInValue: false,
      validatorRules: [
        {
          required: true,
          message: '请选择指派人',
        },
      ],
    },
    {
      fieldName: `${DeployPlanSession.MAP_COLLECTION}/expectedDate`,
      label: '期望完成日期',
      marginLeft: 8,
      marginRight: 0,
      width: '48%',
      isChild: true,
      type: 'datePicker',
      validatorRules: [
        {
          required: true,
          message: '请选择期望完成日期',
        },
      ],
    },
    {
      fieldName: 'mapVehicleSource',
      label: '采图车来源',
      marginLeft: 0,
      marginRight: 0,
      width: '60%',
      isChild: true,
      type: 'radioGroup',
      options: [
        {
          value: MapVehicleSource.ALLOCATED_VEHICLES,
          label: '自有车采图',
        },
        {
          value: MapVehicleSource.OTHER_VEHICLES,
          label: '借车采图',
        },
      ],
      validatorRules: [
        {
          required: true,
          message: '请选择采图车来源',
        },
      ],
    },
    {
      fieldName: `${DeployPlanSession.MAP_COLLECTION}/mapVehicleRemark`,
      label: '备注',
      marginLeft: 0,
      marginRight: 0,
      width: '38%',
      isChild: true,
      maxLength: 30,
      type: 'input',
      placeholder: '请输入备注',
      hidden: true,
      validatorRules: [
        {
          required: true,
          message: '请输入备注',
        },
        {
          pattern:
            /^[\u4E00-\u9FA5A-Za-z0-9\s.,!?，。！？、：；“”‘’—…《》【】]+$/g,
          message: '不支持特殊字符（仅支持文字、标点）',
        },
      ],
    },
    {
      label: <h4>地图制作</h4>,
      childrenList: [
        `${DeployPlanSession.MAP_PRODUCTION}/appointorErp`,
        `${DeployPlanSession.MAP_PRODUCTION}/expectedDate`,
      ],
      fieldName: DeployPlanSession.MAP_PRODUCTION,
      labelCol: {
        span: 3,
      },
      wrapperCol: {
        span: 21,
      },
    },
    {
      fieldName: `${DeployPlanSession.MAP_PRODUCTION}/appointorErp`,
      label: '指派人',
      marginLeft: 0,
      marginRight: 8,
      width: '48%',
      isChild: true,
      placeholder: '请选择指派人',
      type: 'select',
      showSearch: true,
      options: [],
      labelInValue: false,
      validatorRules: [
        {
          required: true,
          message: '请选择指派人',
        },
      ],
    },
    {
      fieldName: `${DeployPlanSession.MAP_PRODUCTION}/expectedDate`,
      label: '期望完成日期',
      marginLeft: 8,
      marginRight: 0,
      width: '48%',
      isChild: true,
      type: 'datePicker',
      validatorRules: [
        {
          required: true,
          message: '请选择期望完成日期',
        },
      ],
    },
    {
      label: <h4>线路试跑</h4>,
      childrenList: [
        `${DeployPlanSession.ROUTE_TEST_RUN}/appointorErp`,
        `${DeployPlanSession.ROUTE_TEST_RUN}/expectedDate`,
      ],
      fieldName: DeployPlanSession.ROUTE_TEST_RUN,
      labelCol: {
        span: 3,
      },
      wrapperCol: {
        span: 21,
      },
    },
    {
      fieldName: `${DeployPlanSession.ROUTE_TEST_RUN}/appointorErp`,
      label: '指派人',
      marginLeft: 0,
      marginRight: 8,
      width: '48%',
      isChild: true,
      placeholder: '请选择指派人',
      type: 'select',
      options: [],
      labelInValue: false,
      showSearch: true,
      validatorRules: [
        {
          required: true,
          message: '请选择指派人',
        },
      ],
    },
    {
      fieldName: `${DeployPlanSession.ROUTE_TEST_RUN}/expectedDate`,
      label: '期望完成日期',
      marginLeft: 8,
      marginRight: 0,
      width: '48%',
      isChild: true,
      type: 'datePicker',
      validatorRules: [
        {
          required: true,
          message: '请选择期望完成日期',
        },
      ],
    },
    {
      label: <h4>站点交付</h4>,
      childrenList: [
        `${DeployPlanSession.STATION_DELIVERY}/appointorErp`,
        `${DeployPlanSession.STATION_DELIVERY}/expectedDate`,
      ],
      fieldName: DeployPlanSession.STATION_DELIVERY,
      labelCol: {
        span: 3,
      },
      wrapperCol: {
        span: 21,
      },
    },
    {
      fieldName: `${DeployPlanSession.STATION_DELIVERY}/appointorErp`,
      label: '指派人',
      marginLeft: 0,
      marginRight: 8,
      width: '48%',
      isChild: true,
      placeholder: '请选择指派人',
      type: 'select',
      options: [],
      labelInValue: false,
      showSearch: true,
      validatorRules: [
        {
          required: true,
          message: '请选择指派人',
        },
      ],
    },
    {
      fieldName: `${DeployPlanSession.STATION_DELIVERY}/expectedDate`,
      label: '期望完成日期',
      marginLeft: 8,
      marginRight: 0,
      width: '48%',
      isChild: true,
      type: 'datePicker',
      validatorRules: [
        {
          required: true,
          message: '请选择期望完成日期',
        },
      ],
    },
  ],
  linkRules: {
    mapVehicleSource: [
      {
        linkFieldName: `${DeployPlanSession.MAP_COLLECTION}/mapVehicleRemark`,
        rule: 'visible',
        dependenceData: [MapVehicleSource.OTHER_VEHICLES],
      },
    ],
    supplier: [
      {
        linkFieldName: DeployPlanSession.VEHICLE_INSURANCE,
        rule: 'visible',
        dependenceData: [VehicleSupplier.JD],
      },
      {
        linkFieldName: `${DeployPlanSession.VEHICLE_INSURANCE}/appointorErp`,
        rule: 'visible',
        dependenceData: [VehicleSupplier.JD],
      },
      {
        linkFieldName: `${DeployPlanSession.VEHICLE_INSURANCE}/expectedDate`,
        rule: 'visible',
        dependenceData: [VehicleSupplier.JD],
      },
      {
        linkFieldName: DeployPlanSession.VEHICLE_CONFIRMATION,
        rule: 'visible',
        dependenceData: [VehicleSupplier.JD],
      },
    ],
  },
};
