import React, { ReactNode, useRef, useEffect } from 'react';
import './index.scss';

const Drawer = ({
  className,
  defaultOpened,
  children,
  openBtn,
  closeBtn,
  openWidth,
  closeWidth,
  editing,
}: {
  className?: string;
  defaultOpened: boolean;
  children: ReactNode;
  openBtn?: ReactNode;
  closeBtn?: ReactNode;
  openWidth: string;
  closeWidth?: string;
  editing?: boolean;
}) => {
  const drawerRef = useRef<HTMLDivElement>(null);
  const openBtnRef = useRef<HTMLDivElement>(null);
  const closeBtnRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (defaultOpened) {
      openBtnRef.current!.style.display = 'none';
      closeBtnRef.current!.style.display = 'block';
    } else {
      openBtnRef.current!.style.display = 'block';
      closeBtnRef.current!.style.display = 'none';
    }
  }, [defaultOpened]);

  useEffect(() => {
    if (editing) {
      openBtnRef.current!.style.display = 'none';
      closeBtnRef.current!.style.display = 'none';
      drawerRef.current!.style.width = openWidth;
    } else {
      closeBtnRef.current!.style.display = 'block';
    }
  }, [editing]);

  const handleClose = () => {
    drawerRef.current!.style.width = closeWidth ? closeWidth : '0px';
    openBtnRef.current!.style.display = 'block';
    closeBtnRef.current!.style.display = 'none';
  };

  const handleOpen = () => {
    drawerRef.current!.style.width = openWidth;
    closeBtnRef.current!.style.display = 'block';
    openBtnRef.current!.style.display = 'none';
  };

  return (
    <div
      className={`drawer-container ${className}`}
      ref={drawerRef}
      style={{ width: defaultOpened ? openWidth : 0 }}
    >
      {openBtn && (
        <div
          className="open-btn"
          ref={openBtnRef}
          style={{ display: 'none' }}
          onClick={handleOpen}
        >
          {openBtn}
        </div>
      )}
      {closeBtn && (
        <div
          className="close-btn"
          ref={closeBtnRef}
          style={{ display: 'none' }}
          onClick={handleClose}
        >
          {closeBtn}
        </div>
      )}
      {children}
    </div>
  );
};

export default React.memo(Drawer);
