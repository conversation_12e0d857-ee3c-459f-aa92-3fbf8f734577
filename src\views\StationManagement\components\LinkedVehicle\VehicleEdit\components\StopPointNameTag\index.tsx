import React, { useEffect, useState } from "react";
import './index.scss';
import { Tooltip } from 'antd';

interface Props {
  name: String;
  id: any;
  waitingTime: String;
}

const StopPointNameTag = (props: Props) => {
  const { name, id, waitingTime } = props;
  return <>
    <Tooltip title={`${name} ${waitingTime}分钟`} key={id}>
      <div className="stop-point-name-tag" key={id}>{`${name} ${waitingTime}分钟`}</div>
    </Tooltip>
  </>
};

export default React.memo(StopPointNameTag);