@import "../../../../../../assets/css/index.scss";
.map-vehicle-modal-container {
  display: flex;
  flex-direction: column;
  padding: 10px 20px;

  .wms-map-container {
    height: 490px;

    .map {
      height: 440px;
    }

    .map-bottom {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      background-color: #f1f1f1;
      height: 49px;
      color: $text-normal-color;
      font-size: 14px;
      text-align: center;
      padding: 0 20px;
    }
  }

  .head {
    height: 90px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    font-size: 20px;

    .vehicle-choose {
      width: 100%;
      height: 50px;
      padding: 10px 5px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .function {
        font-size: 13px;
        display: flex;
        flex-direction: row;
        justify-content: center;
      }
    }
  }

  .bottom-btn {
    font-size: 13px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin: 30px 0;

    .cancel {
      margin-left: 30px;
      color: $text-normal-color;
      background: white;
      border-color: $text-normal-color;
    }
  }
}