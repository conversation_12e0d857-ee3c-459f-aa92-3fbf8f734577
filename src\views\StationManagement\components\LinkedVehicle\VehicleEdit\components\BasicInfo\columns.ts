export const vehicleTypeTableColumn: any[] = [
  {
    title: '硬件类型名称',
    dataIndex: 'hardwareTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '硬件型号名称',
    dataIndex: 'hardwareModelName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '硬件型号',
    dataIndex: 'hardwareModelModel',
    align: 'center',
    ellipsis: true,
  },
];

export const sensorTableColumn: any[] = [
  {
    title: '硬件类型名称',
    dataIndex: 'hardwareTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '用途',
    dataIndex: 'hardwareTypeUsageName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '硬件型号名称',
    dataIndex: 'hardwareModelName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '硬件型号',
    dataIndex: 'hardwareModelModel',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '数量',
    dataIndex: 'useNumber',
    align: 'center',
    ellipsis: true,
    render: (text, record) => {
      if (!record.useNumber) {
        return '--';
      } else {
        return text;
      }
    },
  },
];
