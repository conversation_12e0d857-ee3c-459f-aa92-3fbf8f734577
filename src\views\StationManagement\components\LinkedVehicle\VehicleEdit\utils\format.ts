// eslint-disable-next-line no-unused-vars
import { StopPointType } from '@/utils/constant';

export interface StationItemType {
  label: string;
  value: number;
  isLinked: boolean;
  waitingTime: number;
}

export const formatLinPointList = (list: any) => {
  const linkPointList: any = {
    homeList: [],
    loadList: [],
    pickList: [],
    vendingList: [],
  };
  list.forEach((item: any) => {
    switch (item.stopType) {
      case 'HOME':
        linkPointList.homeList = [...item.stopList];
        break;
      case 'PICKUP':
        linkPointList.pickList = [...item.stopList];
        break;
      case 'VENDING':
        linkPointList.vendingList = [...item.stopList];
        break;
      case 'LOADING':
        linkPointList.loadList = [...item.stopList];
        break;
      default:
        break;
    }
  });
  return linkPointList;
};
export const formatCrossStopPointList = (list: any) => {
  const crossStopList: any = [];
  list.forEach((item: any) => {
    let homeList: any = [];
    let stopList: any = [];
    item.linkStopList.forEach((linkStop) => {
      if (linkStop.stopType === 'HOME') {
        homeList = [...linkStop.stopList];
      }
      if (linkStop.stopType === 'PICKUP') {
        stopList = [...linkStop.stopList];
      }
    });
    const crossStop = {
      sort: item.sort,
      stationInfo: item.stationBaseId,
      homeList: homeList,
      stopList: stopList,
    };
    crossStopList.push(crossStop);
  });
  return crossStopList;
};
export const findSelectedPoint = (values: any, selectedAll: boolean) => {
  const points: any = values;
  for (const key in values) {
    if (key === StopPointType.HOME && selectedAll) {
      points[key] = points[key].length > 0 ? [points[key][0]] : [];
    } else {
      points[key] = values[key]?.filter((item: any) => {
        if (selectedAll && key !== StopPointType.HOME) {
          return item;
        } else {
          if (item.isLinked) {
            return item;
          }
        }
      });
    }
  }
  return points;
};

export const formateSelectedPoint = (values: any) => {
  for (const key in values) {
    if (values.hasOwnProperty.call(key)) {
      values[key] = values[key]?.filter((item: any) => {
        return {
          id: item.id,
          waitingTime: item.waitingTime,
        };
      });
    }
  }
  return values;
};

export const getVehicleTypeNameByCode = (typeList: any, code: any) => {
  if (!typeList || !code) return null;
  const filteredList = typeList.filter((item: any) => item.code === code);
  if (filteredList.length > 0) {
    return filteredList[0].name;
  }
  return null;
};
