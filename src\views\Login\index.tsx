import React from 'react';
import './index.scss';
import { PassportLogin } from '@jd/x-coreui';
const Login = () => {
  return (
    <div className="login-page-com-content">
      <div className="login-left-content">
        <img
          src={require('@/assets/image/login/title.png')}
          className="title"
        ></img>
        <img
          src={require('@/assets/image/login/vehicle.png')}
          className="vehicle-img"
        ></img>
        <img
          src={require('@/assets/image/login/vehicle-project.png')}
          className="vehicle-project"
        ></img>
      </div>

      <div className="login-right-content">
        <div className="login-title">欢迎登录</div>
        <PassportLogin
          returnUrl={`${window.location.protocol}//${process.env.JDX_APP_LOCATION_HOST}`}
        />
      </div>
    </div>
  );
};

export default Login;
