import React, { useRef, useState } from 'react';
import { Button, Input } from 'antd';
import { useDispatch } from 'react-redux';
import { actionLogin, actionPermission } from '../../redux/action';
import './index.scss';
import { useNavigate } from 'react-router-dom';
import { request } from '@/fetch/core';
import { menuData } from '@/layouts/mockmenu';
let phoneValue = '';
const Login = () => {
  const navigator = useNavigate();
  const handlePassportLogin = () => {
    // localStorage.setItem('loginState', LoginState.LOGINING);
    const url = `${process.env.JDX_APP_PASSPORT_LOGIN_HOST}${window.location.protocol}//${process.env.JDX_APP_LOCATION_HOST}`;
    window.location.href = url;
  };

  return (
    <div className="login-page-com-content">
      <div className="login-left-content">
        <img
          src={require('@/assets/image/login/title.png')}
          className="title"
        ></img>
        <img
          src={require('@/assets/image/login/vehicle.png')}
          className="vehicle-img"
        ></img>
        <img
          src={require('@/assets/image/login/vehicle-project.png')}
          className="vehicle-project"
        ></img>
      </div>

      <div className="login-right-content">
        <div className="login-title">欢迎登录</div>

        <Button
          className="login-btn"
          onClick={() => {
            document.cookie =
              'ReturnUrl=0;domain=.jdl.cn;expires=' +
              new Date(0).toUTCString() +
              ';path=/';
            handlePassportLogin();
          }}
        >
          京东账号登录
        </Button>
      </div>
    </div>
  );
};

export default Login;
