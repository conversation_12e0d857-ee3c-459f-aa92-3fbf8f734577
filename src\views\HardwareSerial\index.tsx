import React, { useState, useEffect } from 'react';
import {
  CommonTable,
  CommonForm,
  TableOperateBtn,
  CustomButton,
  ButtonType,
} from '@/components';
import { searchConfig, tableColumns, detailConfig } from './utils/columns';
import { useTableData } from '@/components/CommonTable/useTableData';
import { HardwareSerialApi } from '@/fetch/business';
import { useNavigate } from 'react-router-dom';
import { HttpStatusCode } from '@/fetch/core/constant';
import { Form, Modal, Table, Input } from 'antd';


const HardwareSerial = () => {
  const navigator = useNavigate();
  const fetchApi = new HardwareSerialApi();
  const initSearchCondition = {
    searchForm: {
      serialNumber: null,
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] =
    useState<SearchCondition>(initSearchCondition);
  const [modalShow, setModalShow] = useState<boolean>(false);
  const [form] = Form.useForm();
  const layout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };
  const { tableData, loading } = useTableData(
    searchCondition,
    fetchApi.fetchTableList,
  );

  const formateColumns = () => {
    return tableColumns.map((col) => {
      switch (col.dataIndex) {
        case 'order':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              const { pageSize, pageNum } = searchCondition;
              return <div>{(pageNum - 1) * pageSize + index + 1}</div>;
            },
          };
        case 'operate':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return (
                <div className="operate">
                  <TableOperateBtn
                    title="查看"
                    handleClick={() => {
                      fetchDetail(record.id);
                      setModalShow(true);
                    }}
                  />
                </div>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  const fetchDetail = async (id: any) => {
    const res = await fetchApi.fetchDetail(id);

    if (res && res.code === HttpStatusCode.Success) {
      form.setFieldsValue(res.data);
    }
  };

  const onSearchClick = (val) => {
    const data = {
      searchForm: val,
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(data);
  };
  return (
    <>
      <CommonForm
        formConfig={searchConfig}
        defaultValue={searchCondition.searchForm}
        layout="inline"
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={() => setSearchCondition({ ...initSearchCondition })}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        columns={formateColumns()}
        loading={loading}
        rowKey={'id'}
        searchCondition={searchCondition}
        onPageChange={(value: any) => setSearchCondition(value)}
      />
      {modalShow && (
        <Modal
          title={'详情'}
          visible={modalShow}
          onCancel={() => {
            setModalShow(false);
          }}
          width={800}
          footer={
            <div>
              <CustomButton
                buttonType={ButtonType.DefaultButton}
                onSubmitClick={() => {
                  setModalShow(false);
                }}
                title={'关闭'}
              />
            </div>
          }
        >
          <Form {...layout} form={form}>
            {detailConfig?.map((item: any, index: any) => {
              return item.name === 'parameter' ? (
                <Form.Item key={index} name={item.name} label={item.label}>
                  <Input.TextArea rows={10} disabled />
                </Form.Item>
              ) : (
                <Form.Item key={index} name={item.name} label={item.label}>
                  <Input.TextArea rows={1} disabled />
                </Form.Item>
              );
            })}
          </Form>
        </Modal>
      )}
    </>
  );
};

export default React.memo(HardwareSerial);
