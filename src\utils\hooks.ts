import { useState, useEffect } from 'react';
import { message } from 'antd';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { CommonApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';

export const useEditPageData = (
  detailRequestParams: any,
  detailFetchApi: Function,
) => {
  const navigator = useNavigate();
  const [detailData, setDetailData] = useState<any>({});

  const getDetail = async () => {
    const res = await detailFetchApi(detailRequestParams);
    if (res.code === HttpStatusCode.Success) {
      setDetailData(res.data);
    } else {
      message.error(res.message);
      navigator(-1);
    }
  };

  useEffect(() => {
    if (detailRequestParams) {
      getDetail();
    }
  }, [detailRequestParams]);

  return detailData;
};

export const useCommonDropDown = (keyList: string[]) => {
  const commonFetchApi = new CommonApi();
  const [dropdownData, setDropdownData] = useState<any>({});
  useEffect(() => {
    const getCommonDropDown = async () => {
      const res = await commonFetchApi.getCommonDropDown({ keyList });
      if (res.code === HttpStatusCode.Success) {
        if (res.code === HttpStatusCode.Success) {
          setDropdownData(res.data);
        }
      }
    };
    if (keyList?.length > 0) {
      getCommonDropDown();
    }
  }, [JSON.stringify(keyList)]);
  return dropdownData;
};
