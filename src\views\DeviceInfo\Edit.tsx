import { Form, Checkbox, Radio, Select, Input, message } from 'antd';
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CommonEdit, EditModuleTitle, HardwareTemplate } from '@/components';
import {
  DeviceInfoApi,
  GridManageApi,
} from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { formatLocation, formatOptions, isEmpty } from '@/utils/utils';
import VehicleModel from './component/VehicleModel';
import { useCommonDropDown } from '@/utils/hooks';
import { DeviceTitle, PageType } from '@/utils/EditTitle';
import AddCard from './component/AddCard';
import {
  dropDownListKey,
  dropDownKey,
  SUPPLIER,
  STATION_USECASE,
} from '@/utils/constant';
import { ProductType } from '@/utils/enum';

const DeviceInfoEdit = () => {
  const fetchApi = new DeviceInfoApi();
  const gridFetchApi = new GridManageApi();
  const navigator = useNavigate();
  const [form] = Form.useForm();
  const { id, type, productType, supplier } = formatLocation(
    window.location.search,
  );
  const [detailData, setDetailData] = useState<any>();
  const [isVirtualList, setIsVirtualList] = useState<any[]>([
    { label: '是', value: 1 },
    { label: '否', value: 0 },
  ]);

  const [boxList, setBoxList] = useState([]);
  const [stationList, setStationList] = useState([]);
  const [productTypeList, setProductTypeList] = useState<any[]>([]);
  const [deviceTypeList, setDeviceTypeList] = useState<any[]>([]);
  const [boxformVisible, setBoxformVisible] = useState(false);
  let dropdownData = useCommonDropDown([
    dropDownKey.PRODUCT_TYPE,
    dropDownKey.VEHICLE_BUSINESS_TYPE,
    dropDownKey.TAG_LIST,
    dropDownKey.TELECOM_OPERATOR,
    dropDownKey.VEHICLE_TYPE,
    dropDownKey.SUPPLIER,
  ]);
  const vehicleBusinessType = [
    { value: 'DISPATCH', label: '配送车' },
    { value: 'VENDING', label: '售卖车' },
    { value: 'COLLECT_MAP', label: '采图车' },
  ];
  const robotBusinessType = [
    { value: 'JING_LIN', label: '京麟车' },
    { value: 'ZHONG_DE', label: '众德车' },
  ];

  useEffect(() => {
    if (supplier && supplier !== SUPPLIER.JD) {
      getStation(productType, [STATION_USECASE.OPEN_OPERATION]);
    } else {
      getStation(productType);
    }
    getBoxList();
    if (!id) {
      form.setFieldsValue({
        productType: ProductType.VEHICLE,
        supplier: SUPPLIER.JD,
        vehicleBusinessType: 'DISPATCH',
      });
    } else {
      getDetail();
    }
  }, []);
  useEffect(() => {
    setProductTypeList(
      formatOptions(dropdownData[dropDownListKey.PRODUCT_TYPE]),
    );
    setDeviceTypeList(
      formatOptions(dropdownData[dropDownListKey.VEHICLE_TYPE]),
    );
  }, [dropdownData]);
  useEffect(() => {
    if (supplier) {
      if (supplier === SUPPLIER.JD) {
        setBoxformVisible(true);
      } else {
        setBoxformVisible(false);
      }
    } else {
      if (
        !form.getFieldsValue().supplier ||
        form.getFieldsValue().supplier === SUPPLIER.JD
      ) {
        setBoxformVisible(true);
      } else {
        setBoxformVisible(false);
      }
    }
  }, [supplier, form.getFieldsValue().supplier]);
  const getDetail = async () => {
    const res = await fetchApi.fetchDetail(id);
    if (res.code === HttpStatusCode.Success) {
      setDetailData(res.data);
      form.setFieldsValue({
        serialNo: res.data.serialNo,
        name: res.data.name,
        productType: res.data.productType,
        vehicleBusinessType: res.data.businessType,
        isVirtual: res.data.isVirtual,
        tagList: res.data.tagList,
        station: res.data.stationBaseId
          ? {
              label: res.data.stationBaseName,
              value: res.data.stationBaseId,
            }
          : null,
        boxTemplateName: {
          label: res.data.boxTemplateName,
          value: res.data.boxTemplateId,
        },
        boxModelName: res.data.hardwareModelName,
        routerId: {
          label: res.data.routerName,
          value: res.data.routerId,
        },
        pushStreamDeviceId: {
          label: res.data.pushStreamDeviceName,
          value: res.data.pushStreamDeviceId,
        },
        pushStreamCameraId: {
          label: res.data.pushStreamCameraName,
          value: res.data.pushStreamCameraId,
        },
        gridNum: res.data.gridNum,
        hardwareModelModel: res.data.hardwareModelModel,
        supplier: res.data.supplier,
      });
    } else {
      message.error(res.message);
      navigator(-1);
    }
  };

  const getBoxList = async () => {
    const res = await gridFetchApi.getBoxTemplateList();
    if (res.code === HttpStatusCode.Success && Array.isArray(res.data)) {
      setBoxList(
        res.data?.map((v) => {
          return {
            label: v.name,
            value: v.id,
          };
        }),
      );
    }
  };
  const getStation = async (
    productType,
    useCase = [STATION_USECASE.PRODUCTION_FACTORY],
  ) => {
    const res = await fetchApi.getStation({
      stationProductType: productType,
      stationUseCaseList: useCase,
    });
    if (res.code === HttpStatusCode.Success && Array.isArray(res.data)) {
      setStationList(
        res.data?.map((v) => {
          return {
            label: v.name,
            value: v.id,
          };
        }),
      );
    }
  };

  const formatHardwareVal = (
    targetList,
    sourceList,
    type: 'device_type' | 'sensor_scheme',
  ) => {
    for (let i = 0; i < sourceList?.length; i++) {
      const val = sourceList[i];
      targetList.push({
        source: type,
        hardwareModelId: val.hardwareModelId,
        hardwareNumber: val.hardwareNumber,
        num: val.useNumber,
      });
    }
    return targetList;
  };

  const onSubmitClick = async () => {
    const value = await form.validateFields();
    const deviceHardwareModelList: any = [];
    const { deviceTypeHardwareInfoList, sensorSchemeDetailList } =
      value.deviceHardwareModelList;
    Object.keys(value).forEach((key) => {
      if (key.includes('hardwareModel_')) {
        const hardwareTypeId = key.split('_')[1];
        deviceHardwareModelList.push({
          source: 'device',
          hardwareModelId: value[key]?.value,
          hardwareNumber: value[`${hardwareTypeId}_number`],
          num: null,
        });
      }
    });
    const deviceTypeData = formatHardwareVal(
      deviceHardwareModelList,
      deviceTypeHardwareInfoList,
      'device_type',
    );
    const sensorSchemeData = formatHardwareVal(
      deviceTypeData,
      sensorSchemeDetailList,
      'sensor_scheme',
    );
    const cardNoList: any[] = [];
    value?.vehicleCardNoList?.forEach((item: any) => {
      if (item.enable === 1) {
        cardNoList.push({
          id: item.newAdd ? null : item.id,
          telecomOperator: item.telecomOperator,
          cardNo: item.cardNo,
          enable: item.enable,
        });
      }
    });
    const res = await fetchApi.submitInfo({
      type,
      requestBody: {
        id: id,
        name: value.name,
        serialNo:
          !value?.supplier || value?.supplier === SUPPLIER.JD
            ? value.serialNo
            : value.name,
        productType: value.productType,
        boxTemplateId: value.boxTemplateName?.value,
        deviceTypeBaseId: value.deviceHardwareModelList.deviceTypeBaseId,
        businessType:
          value.productType === ProductType.INTEGRATE
            ? 'COMMON_WAREHOUSE'
            : value.vehicleBusinessType,
        stationBaseId: value.station?.value,
        isVirtual: value.isVirtual,
        tagList: value.tagList,
        deviceHardwareModelList: sensorSchemeData,
        cardNoList: cardNoList,
        supplier: value?.supplier ?? null,
      },
    });
    if (res.code === HttpStatusCode.Success) {
      message.success(res.message);
      navigator('/app/deviceInfo');
    } else {
      message.error(res.message);
    }
  };

  const onCancleClick = () => {
    navigator('/app/deviceInfo');
  };

  return (
    <>
      <CommonEdit
        title={DeviceTitle[type]}
        breadCrumbConfig={[
          {
            title: '设备基础信息管理',
            route: '',
          },
          {
            title: DeviceTitle[type],
            route: '',
          },
        ]}
        onSubmitClick={onSubmitClick}
        onCancleClick={onCancleClick}
        hideSubmit={type === PageType.READONLY}
        cancelTitle={type === PageType.READONLY ? '返回' : '取消'}
      >
        <Form
          form={form}
          autoComplete="off"
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 19 }}
        >
          <EditModuleTitle title={'基础信息'} />

          <Form.Item
            name={'productType'}
            label={'产品类型'}
            rules={[{ required: true, message: '请选择产品类型' }]}
          >
            <Radio.Group
              disabled={type !== PageType.ADD}
              options={productTypeList}
              onChange={(e) => {
                const value = e.target.value;
                getStation(value);
                if (value == ProductType.INTEGRATE) {
                  form.setFieldsValue({
                    vehicleBusinessType: 'COMMON_WAREHOUSE',
                  });
                } else if (value == ProductType.VEHICLE) {
                  form.setFieldsValue({
                    vehicleBusinessType: 'DISPATCH',
                  });
                } else if (value == ProductType.ROBOT) {
                  form.setFieldsValue({
                    vehicleBusinessType: 'JING_LIN',
                  });
                }
              }}
            />
          </Form.Item>
          {form.getFieldsValue().productType === ProductType.VEHICLE ? (
            <Form.Item
              name={'supplier'}
              label={'供应商'}
              rules={[{ required: true, message: '请选择供应商' }]}
            >
              <Radio.Group
                disabled={type !== PageType.ADD}
                options={formatOptions(dropdownData[dropDownListKey.SUPPLIER])}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value == SUPPLIER.NEOLIX || value == SUPPLIER.RINO) {
                    setProductTypeList([
                      {
                        value: ProductType.VEHICLE,
                        label: '无人车',
                      },
                    ]);
                    fetchApi
                      .queryDeviceTypeList({
                        supplier: value,
                        productType: ProductType.VEHICLE,
                        enable: 1,
                      })
                      .then((res) => {
                        if (
                          res.code === HttpStatusCode.Success &&
                          !isEmpty(res.data)
                        ) {
                          setDeviceTypeList(
                            res.data.map((v) => {
                              return {
                                value: v.deviceTypeBaseId,
                                label: v.deviceTypeName,
                              };
                            }),
                          );
                          form.setFieldsValue({
                            deviceTypeBaseId: res.data[0].deviceTypeBaseId,
                          });
                        }
                      });
                    getStation(ProductType.VEHICLE, [
                      STATION_USECASE.OPEN_OPERATION,
                    ]);
                  } else {
                    setProductTypeList(
                      formatOptions(dropdownData[dropDownListKey.PRODUCT_TYPE]),
                    );
                    setDeviceTypeList(
                      formatOptions(dropdownData[dropDownListKey.VEHICLE_TYPE]),
                    );
                    form.setFieldsValue({
                      deviceTypeBaseId: null,
                    });
                    getStation(ProductType.VEHICLE);
                  }
                }}
              />
            </Form.Item>
          ) : null}
          {form.getFieldsValue().productType !== ProductType.INTEGRATE && (
            <Form.Item
              name="vehicleBusinessType"
              label="设备类型"
              rules={[{ required: true, message: '请选择设备类型' }]}
            >
              <Radio.Group
                disabled={type === PageType.READONLY}
                options={
                  form.getFieldsValue().productType === 'vehicle'
                    ? vehicleBusinessType
                    : robotBusinessType
                }
              />
            </Form.Item>
          )}

          {boxformVisible && (
            <Form.Item
              label="车架号"
              name="serialNo"
              rules={[
                { required: true, message: '请输入车架号' },
                { pattern: /[0-9a-zA-Z]+$/, message: '请输入正确的车架号' },
              ]}
            >
              <Input
                disabled={type === PageType.READONLY}
                maxLength={30}
                allowClear
                placeholder="请输入车架号"
              />
            </Form.Item>
          )}
          <Form.Item
            label="车牌号"
            name="name"
            rules={[{ required: true, message: '请输入车牌号' }]}
          >
            <Input
              disabled={type === PageType.READONLY}
              maxLength={20}
              allowClear
              placeholder="请输入车牌号"
              onChange={(e) => {
                let value = e.target.value;
                const reg = /[A-Za-z0-9]+$/;
                if (value && !reg.test(value)) {
                  value = e.target.defaultValue;
                }
                form.setFieldsValue({
                  license: value,
                });
              }}
            />
          </Form.Item>
          {type === PageType.ADD && (
            <Form.Item
              label="所属站点"
              name="station"
              rules={[{ required: true, message: '请选择所属站点' }]}
            >
              <Select
                options={stationList}
                labelInValue
                disabled={type === PageType.READONLY}
                placeholder="请选择所属站点"
                showSearch
                filterOption={(input, option: any) => {
                  const label: any = option?.label || '';
                  return label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                }}
              />
            </Form.Item>
          )}
          {boxformVisible && (
            <>
              <Form.Item
                name="isVirtual"
                label="是否为虚拟车"
                rules={[{ required: true, message: '请选择是否为虚拟车' }]}
              >
                <Radio.Group
                  disabled={type === PageType.READONLY}
                  options={isVirtualList}
                />
              </Form.Item>
              <Form.Item name="tagList" label="系统标签">
                <Checkbox.Group
                  disabled={type === PageType.READONLY}
                  options={formatOptions(
                    dropdownData[dropDownListKey.TAG_LIST],
                  )}
                />
              </Form.Item>
            </>
          )}

          <EditModuleTitle title={'车型信息'} />
          <VehicleModel
            form={form}
            initialValue={
              detailData
                ? {
                    deviceTypeBaseId: detailData?.deviceTypeBaseId,
                    deviceTypeName: detailData?.deviceTypeName,
                    deviceTypeHardwareInfoList:
                      detailData.deviceTypeHardwareInfoList,
                    sensorSchemeDetailList: detailData.sensorSchemeDetailList,
                    sensorSchemeId: detailData.sensorSchemeId,
                    sensorSchemeName: detailData.sensorSchemeName,
                    manufactoryId: detailData.manufactoryId,
                    manufactoryName: detailData.manufactoryName,
                  }
                : null
            }
            productType={form.getFieldsValue().productType}
            disabled={type === PageType.READONLY}
            options={deviceTypeList}
            supplier={form.getFieldsValue().supplier}
          />

          {boxformVisible ? (
            <>
              <EditModuleTitle title={'车辆信息'} />
              <Form.Item
                label="箱体格口模板名称"
                name="boxTemplateName"
                rules={[{ required: true, message: '请输入模板名称' }]}
              >
                <Select
                  options={boxList}
                  labelInValue
                  onChange={async (value: any) => {
                    const response: any = await gridFetchApi.getBoxTemplateInfo(
                      value?.value,
                    );
                    if (response && response.code === HttpStatusCode.Success) {
                      const boxTempInfoResult = response.data;
                      const boxTempInfo = {
                        boxModelName: boxTempInfoResult.hardwareModelName,
                        hardwareModelModel:
                          boxTempInfoResult.hardwareModelModel,
                        gridNum: boxTempInfoResult.gridNum,
                      };
                      form.setFieldsValue({
                        ...boxTempInfo,
                        boxTemplateId: value,
                      });
                    }
                  }}
                  disabled={type === PageType.READONLY}
                  placeholder="请输入模板名称"
                  showSearch
                  filterOption={(input, option: any) => {
                    const label: any = option?.label || '';
                    return (
                      label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    );
                  }}
                />
              </Form.Item>
              <Form.Item label="箱体名称" name="boxModelName">
                <Input disabled placeholder="系统生成" />
              </Form.Item>
              <Form.Item label="箱体型号" name="hardwareModelModel">
                <Input disabled placeholder="系统生成" />
              </Form.Item>
              <Form.Item label="格口数" name="gridNum">
                <Input disabled placeholder="系统生成" />
              </Form.Item>
              <HardwareTemplate
                type="device"
                formRef={form}
                disabled={type === PageType.READONLY}
                productType={form.getFieldsValue().productType}
                initialVal={detailData?.deviceHardwareList ?? []}
              />
              <AddCard
                form={form}
                telecomOperatorList={
                  dropdownData[dropDownListKey.TELECOM_OPERATOR]
                }
                initValues={detailData?.cardNoList}
                disable={type === PageType.READONLY}
              />
            </>
          ) : null}
        </Form>
      </CommonEdit>
    </>
  );
};

export default DeviceInfoEdit;
