import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { DeviceLifeApi } from '@/fetch/business';
import { vehicleRepairColumns } from '../../utils/column';
import CheckStatusModal from '@/views/RepairOrderManagement/components/CheckStatusModal';
import { isEmpty, makeUrlQuery } from '@/utils/utils';
import {
  removeSearchValues,
  saveSearchValues,
} from '@/redux/reducer/searchForm';
import { handleVehicleManage } from '@/redux/reducer/vehicleManage';
import { PageType, TabType } from '../../utils/constant';
import { CommonTable, TableOperateBtn } from '@/components';
import { useTableData } from '@/components/CommonTable/useTableData';
import { DeviceRepairResponse, DeviceRequest } from '@/types/deviceLife';
import DivideVehicle from '../../DivideVehicle';
import { AnyFunc } from '@/global';
interface Props {
  searchForm: any;
  activeTabKey: string;
  click: number;
  setSearchForm: AnyFunc;
  searchRef: any;
  tableKey: string;
  setTableKey: any;
}
const fetchApi = new DeviceLifeApi();
const RepairTab = (props: Props) => {
  const {
    searchForm,
    activeTabKey,
    click,
    setSearchForm,
    searchRef,
    tableKey,
    setTableKey,
  } = props;
  const dispatch = useDispatch();
  const navigator = useNavigate();
  const [pageType, setPageType] = useState<PageType>(PageType.repair);
  const [showDivideModal, setShowDivideModal] = useState(false);
  const [calibrationModal, setCalibrationModal] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]); // 在用车辆、车辆调度池批量选中的数据keys
  const [selectedRows, setSelectedRows] = useState<any>([]); // 去分配存储分配车辆详细信息
  const [vehicleId, setVehicleId] = useState(null);
  useEffect(() => {
    if (activeTabKey === TabType.VEHICLE_REPAIRING) {
      setSelectedRowKeys([]);
      setSelectedRows([]);
      setSearchForm({
        ...searchForm,
        pageNum: 1,
        pageSize: 10,
      });
    }
  }, [activeTabKey, click]);
  const { tableData, loading } = useTableData<
    DeviceRequest,
    DeviceRepairResponse
  >(searchForm, fetchApi.fetchDeviceRepairTable, tableKey);
  useEffect(() => {
    if (tableData) {
      const temp: any = [];
      if (!isEmpty(selectedRows)) {
        for (let item of selectedRows) {
          for (let row of tableData.list) {
            if (row.id === item.id) {
              temp.push(row);
            }
          }
        }
        setSelectedRows(temp);
      }
    }
  }, [tableData]);
  const goToRepairCenter = (record: any) => {
    const selectedRowKeys = [
      {
        id: record.id,
        deviceName: record.deviceName,
      },
    ];
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: {
          searchForm: searchForm,
        },
      }),
    );
    dispatch(handleVehicleManage(selectedRowKeys));
    setShowDivideModal(true);
  };
  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedRows(selectedRows);
    },
  };

  const formatColumns = () => {
    return vehicleRepairColumns.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${(searchForm.pageNum - 1) * searchForm.pageSize + index + 1}`;
          break;
        case 'checkStatusName':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'vehicleBusinessTypeName':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'stationName':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'stationUseCaseName':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'schedule':
          // eslint-disable-next-line react/display-name
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                <TableOperateBtn
                  title="查看维修单"
                  handleClick={() => {
                    dispatch(removeSearchValues(null));
                    const searchForm = {
                      name: record.deviceName,
                    };
                    const query = makeUrlQuery(searchForm);
                    const url = `/app/repairOrderManagement?${query}`;
                    navigator(url);
                  }}
                  show={true}
                />
                <TableOperateBtn
                  title="查看标定检测结果"
                  handleClick={() => {
                    setVehicleId(record.id);
                    setCalibrationModal(true);
                  }}
                  show={record.checkStatus}
                />
                <TableOperateBtn
                  title="转维修中心"
                  handleClick={() => goToRepairCenter(record)}
                  show={true}
                />
              </div>
            );
          };
          break;
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
      return col;
    });
  };
  return (
    <div>
      <CommonTable
        rowKey={'id'}
        searchRef={searchRef}
        rowSelection={{ ...rowSelection }}
        columns={formatColumns()}
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        loading={loading}
        onPageChange={(paginationData: any) => {
          setSearchForm({
            ...searchForm,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      />
      {/* 查看标定结果 */}
      {calibrationModal && (
        <CheckStatusModal
          visible={calibrationModal}
          onOk={() => {
            setCalibrationModal(false);
          }}
          onCancel={() => {
            setCalibrationModal(false);
          }}
          id={vehicleId}
        />
      )}
      {/** 接收/转站/交付弹窗 */}
      {showDivideModal && (
        <DivideVehicle
          pageType={pageType}
          show={showDivideModal}
          setShow={setShowDivideModal}
          setTableKey={setTableKey}
        />
      )}
    </div>
  );
};

export default React.memo(RepairTab);
