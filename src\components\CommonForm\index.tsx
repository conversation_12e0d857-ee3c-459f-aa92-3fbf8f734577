import React, { useEffect, useState, useRef } from 'react';
import {
  DatePicker,
  Select,
  Form,
  Cascader,
  Input,
  Radio,
  Checkbox,
  Switch,
  Row,
  Col,
  Grid,
  Button,
  InputNumber,
} from 'antd';
import 'moment/locale/zh-cn';
import locale from 'antd/es/date-picker/locale/zh_CN';
import { cloneDeep } from 'lodash';
import { filterOption } from './util';
import { FormConfig, FieldItem } from '../index';
import { CommonApi } from '@/fetch/business';
import './index.scss';
import { HttpStatusCode } from '@/fetch/core/constant';
const { RangePicker } = DatePicker;
export interface FormProps {
  formConfig: FormConfig;
  layout?: 'horizontal' | 'vertical' | 'inline';
  defaultValue?: Record<string, any>;
  formType?: 'search' | 'edit'; // search表示是搜索表单会有查询重置按钮
  colon?: boolean;
  className?: string;
  labelAlign?: 'left' | 'right' | undefined;
  updateForm?: any;
  optionsList?: { [key: string]: [{ lable: string; value: any }] | [] };
  onValueChange?: Function;
  getFormInstance?: Function;
  onResetClick?: Function;
  onSearchClick?: Function;
  onFieldFocus?: Function;
}
const CommonForm = (props: FormProps) => {
  const {
    formConfig,
    layout,
    formType = 'edit',
    labelAlign,
    className,
    colon,
    defaultValue = {},
    optionsList,
    onValueChange,
    getFormInstance,
    onResetClick,
    onSearchClick,
    onFieldFocus,
  } = props;
  const [formData, setFormData] = useState<any>(cloneDeep(formConfig));
  const [commonFormRef] = Form.useForm();
  const commonFetch = new CommonApi();
  const [selectList, setSelectList] = useState<Object>({});
  const showCheckedStrategyMap = new Map([
    ['SHOW_PARENT', Cascader.SHOW_PARENT],
    ['SHOW_CHILD', Cascader.SHOW_CHILD],
  ]);

  useEffect(() => {
    setFormData(cloneDeep(formConfig));
  }, [formConfig]);

  useEffect(() => {
    if (defaultValue) {
      commonFormRef.setFieldsValue(defaultValue);
      Object.keys(defaultValue).forEach((v) => {
        onFieldsChange(v, defaultValue[v], true);
      });
    }
  }, [JSON.stringify(defaultValue)]);

  useEffect(() => {
    getFormInstance && getFormInstance(commonFormRef);
  }, []);

  const getDropDownList = async (item: any) => {
    if (selectList[item.fieldName]) {
      return;
    }
    if (item.dropDownKey) {
      const res = await commonFetch.getCommonDropDown({
        keyList: [item.dropDownKey],
      });
      if (res?.code === HttpStatusCode.Success) {
        const updatedList = res.data[item.dropDownListKey].map((item: any) => ({
          label: item.name,
          value: item.code,
        }));
        setSelectList({
          ...selectList,
          [item.fieldName]: updatedList,
        });
      }
    }
  };

  const getDepartmentList = async (item: any, type: string) => {
    if (selectList[item.fieldName]) {
      return;
    }
    let res;
    const fetchFunction =
      type === 'station'
        ? commonFetch.getStationDepartment
        : commonFetch.getCityDepartment;
    res = await fetchFunction(item.departmentParams ?? {});
    if (res.code === HttpStatusCode.Success) {
      setSelectList({
        ...selectList,
        [item.fieldName]: res.data,
      });
    }
  };

  const renderFieldItem = (dataItem: FieldItem) => {
    const {
      fieldName,
      type,
      options,
      placeholder,
      multiple,
      showSearch,
      maxLength,
      allowClear,
      disabled,
      showSelectAll,
      autoSize,
      showCount,
      maxTagCount,
      defaultChecked,
      mapRelation,
      specialFetch,
      renderFunc,
      changeOnSelect,
      labelInValue,
      showCheckedStrategy,
      min,
      max,
      fileListType = 'picture',
      accept,
      bucketName,
      LOPDN,
      getPreSignatureUrl,
      maxFileSize,
      unit = 'MB',
    } = dataItem;
    switch (type) {
      case 'input':
        return (
          <Input
            placeholder={placeholder ?? ''}
            allowClear={typeof allowClear === 'undefined' ? true : allowClear}
            maxLength={maxLength}
            disabled={disabled}
          />
        );
      case 'inputNumber':
        return (
          <InputNumber
            placeholder={placeholder ?? ''}
            maxLength={maxLength}
            disabled={disabled}
            min={min}
            max={max}
          />
        );
      case 'radioGroup':
        return <Radio.Group options={options} disabled={disabled} />;
      case 'textarea':
        return (
          <Input.TextArea
            placeholder={placeholder ?? ''}
            autoSize={autoSize === true ? { minRows: 2, maxRows: 6 } : autoSize}
            maxLength={maxLength}
            disabled={disabled}
            showCount={
              showCount && {
                formatter: ({ value, count, maxLength }) =>
                  `${count}/${maxLength}`,
              }
            }
          />
        );
      case 'select':
        let _options: any[] = [];
        if (optionsList && optionsList[fieldName!]) {
          _options = optionsList[fieldName!];
        } else if (selectList[fieldName!]) {
          _options = selectList[fieldName!];
        } else if (options) {
          _options = options;
        }
        return (
          <Select
            maxTagCount={4}
            maxLength={4}
            placeholder={placeholder ?? ''}
            options={_options}
            showSearch={typeof showSearch === 'undefined' ? true : showSearch}
            labelInValue={
              typeof labelInValue === 'undefined' ? true : labelInValue
            }
            disabled={disabled}
            mode={multiple ? 'multiple' : undefined}
            allowClear={typeof allowClear === 'undefined' ? true : allowClear}
            filterOption={filterOption}
            onDropdownVisibleChange={() => {
              specialFetch === 'commonDown' && getDropDownList(dataItem);
              onFieldFocus &&
                onFieldFocus(
                  dataItem.fieldName,
                  commonFormRef.getFieldsValue(),
                );
            }}
          />
        );
      case 'datePicker':
        return (
          <RangePicker placement={'bottomRight'} style={{ width: '100%' }} />
        );
      case 'rangeTime':
        return (
          <RangePicker
            locale={locale}
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            disabled={disabled}
            // disabledDate={disabledDate}
          />
        );
      case 'dateTime':
        return (
          <DatePicker
            allowClear={typeof allowClear === 'undefined' ? true : allowClear}
            locale={locale}
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            disabled={disabled}
          />
        );
      case 'cascader':
        return (
          <Cascader
            fieldNames={mapRelation}
            options={
              ['city', 'station'].includes(specialFetch!)
                ? selectList[fieldName!]
                : options
            }
            placeholder={placeholder ?? ''}
            maxTagCount={maxTagCount}
            showSearch={typeof showSearch === 'undefined' ? true : showSearch}
            disabled={disabled}
            allowClear={typeof allowClear === 'undefined' ? true : allowClear}
            multiple={multiple}
            showCheckedStrategy={
              typeof showCheckedStrategy == 'undefined'
                ? Cascader.SHOW_CHILD
                : showCheckedStrategyMap.get(showCheckedStrategy)
            }
            changeOnSelect={
              typeof changeOnSelect === 'undefined' ? true : changeOnSelect
            }
            onDropdownVisibleChange={(value) => {
              if (
                value &&
                specialFetch &&
                ['city', 'station'].includes(specialFetch!)
              ) {
                getDepartmentList(dataItem, specialFetch);
              }
              onFieldFocus &&
                onFieldFocus(
                  dataItem.fieldName,
                  commonFormRef.getFieldsValue(),
                );
            }}
          />
        );
      case 'switch':
        return <Switch defaultChecked={defaultChecked} />;
      case 'checkboxGroup':
        return <Checkbox.Group options={options} disabled={disabled} />;
      case 'ReactNode':
        return <>{renderFunc && renderFunc()}</>;
      default:
        return null;
    }
  };

  const onFieldsChange = (changedFieldName: any, val: any, isInit: boolean) => {
    if (!formData?.linkRules || !formData.linkRules[changedFieldName]) {
      return;
    }
    // 找到当前元素改变后需要的联动的表单项
    formData?.linkRules[changedFieldName]?.forEach((item: any) => {
      const { linkFieldName, rule, dependenceData, disabledValue, fetchFunc } =
        item;
      let needChangeField: FieldItem = formData.fields?.find(
        (f: FieldItem) => f.fieldName === linkFieldName,
      );
      if (!needChangeField) {
        return;
      }
      switch (rule) {
        case 'visible':
          needChangeField.hidden = !dependenceData.includes(val);
          break;
        case 'refresh':
          commonFormRef.setFieldsValue({
            [linkFieldName]: defaultValue[linkFieldName],
          });
          break;
        case 'clear':
          if (!isInit) {
            commonFormRef.setFieldsValue({
              [linkFieldName]: null,
            });
          }
          break;
        case 'fetchData':
          fetchFunc(val).then((res: any[]) => {
            setSelectList({
              ...selectList,
              [linkFieldName]: res,
            });
          });
          break;
        case 'valueDisable':
          if (needChangeField.options) {
            needChangeField.options = needChangeField.options.map((v: any) => {
              if (
                disabledValue.includes(v.value) &&
                dependenceData.includes(val)
              ) {
                return {
                  ...v,
                  disabled: true,
                };
              } else {
                return { ...v, disabled: false };
              }
            });
          }
          break;
      }
    });
    setFormData({ ...formData });
  };

  const makeLayout = () => {
    let titleMaxLength = 4;
    formData?.fields.forEach((item: any) => {
      if (item.label?.length && item.label?.length > titleMaxLength) {
        titleMaxLength = item.label.length || item.label.length;
      }
    });
    titleMaxLength = Math.min(titleMaxLength, 9);

    return {
      labelCol: { span: titleMaxLength + 1 },
      wrapperCol:
        layout === 'inline'
          ? { span: 24 - (titleMaxLength + 1) }
          : { span: 23 - (titleMaxLength + 1) },
    };
  };

  return (
    <div
      className={
        formType === 'search' ? 'searchform-conatiner' : 'common-form-container'
      }
    >
      <Form
        {...makeLayout()}
        name="complex-form"
        labelAlign={labelAlign ?? 'right'}
        layout={layout ?? 'horizontal'}
        colon={colon ?? true}
        form={commonFormRef}
        autoComplete="off"
        className={className || ''}
        style={{
          alignItems: 'center',
        }}
        onFieldsChange={(changedFields: any) => {
          const changedFieldName = changedFields[0].name[0];
          const formValues = commonFormRef.getFieldsValue();
          onValueChange && onValueChange(formValues, changedFieldName);
          changedFieldName &&
            onFieldsChange(
              changedFieldName,
              formValues[changedFieldName],
              false,
            );
        }}
      >
        <Row gutter={24} align="middle" style={{ width: '100%' }}>
          {formData?.fields?.map((item: FieldItem, index: number) => {
            if (item.childrenList) {
              const cWidth = Math.floor(100 / item.childrenList.length);
              return (
                <Col
                  xxl={layout === 'inline' ? (item.xxl ? item.xxl : 6) : 23}
                  xl={layout === 'inline' ? (item.xl ? item.xl : 8) : 23}
                  lg={layout === 'inline' ? (item.lg ? item.lg : 12) : 23}
                  md={item.md ? item.md : 23}
                >
                  <Form.Item
                    key={item.fieldName?.toString()}
                    label={item.label}
                    style={{ marginBottom: 0 }}
                    labelCol={item.labelCol && item.labelCol}
                    wrapperCol={item.wrapperCol && item.wrapperCol}
                  >
                    {item.childrenList.map((name: string, index: number) => {
                      const _cField = formData?.fields.find(
                        (v: FieldItem) => v.fieldName === name,
                      );
                      const _marginLeft =
                        _cField?.marginLeft || _cField?.marginLeft === 0
                          ? _cField.marginLeft
                          : 8;
                      const _marginRight =
                        _cField?.marginRight || _cField?.marginRight === 0
                          ? _cField.marginRight
                          : 8;
                      return (
                        <Form.Item
                          key={name?.toString()}
                          labelCol={_cField?.labelCol && _cField.labelCol}
                          wrapperCol={_cField?.wrapperCol && _cField.wrapperCol}
                          name={_cField?.fieldName}
                          label={_cField?.label}
                          help={_cField?.help}
                          rules={_cField?.validatorRules ?? []}
                          className={'field_' + _cField?.fieldName?.toString()}
                          style={{
                            display: 'inline-block',
                            width:
                              _cField?.width ??
                              `calc(${cWidth}% - ${_marginLeft}px - ${_marginRight}px)`,
                            marginLeft: `${_marginLeft}px`,
                            marginRight: `${_marginRight}px`,
                          }}
                        >
                          {renderFieldItem(_cField)}
                        </Form.Item>
                      );
                    })}
                  </Form.Item>
                </Col>
              );
            } else if (!item.isChild) {
              return (
                !item.hidden && (
                  <Col
                    xxl={layout === 'inline' ? (item.xxl ? item.xxl : 6) : 23}
                    xl={layout === 'inline' ? (item.xl ? item.xl : 8) : 23}
                    lg={layout === 'inline' ? (item.lg ? item.lg : 12) : 23}
                    md={item.md ? item.md : 23}
                    key={item.fieldName?.toString()}
                  >
                    <Form.Item
                      key={item.fieldName?.toString()}
                      labelCol={item.labelCol && item.labelCol}
                      wrapperCol={item.wrapperCol && item.wrapperCol}
                      name={item.fieldName}
                      label={item.label}
                      help={item.help}
                      rules={item.hidden ? [] : item.validatorRules ?? []}
                      className={'field_' + item.fieldName?.toString()}
                    >
                      {renderFieldItem(item)}
                    </Form.Item>
                  </Col>
                )
              );
            }
          })}
        </Row>
      </Form>
      {formType === 'search' && (
        <Row justify={'end'} className="searchbtns">
          <Button
            className="reset"
            style={{ marginRight: '12px' }}
            onClick={() => {
              commonFormRef.setFieldsValue(defaultValue);
              onResetClick && onResetClick();
            }}
          >
            重置
          </Button>

          <Button
            type="primary"
            onClick={() => {
              onSearchClick && onSearchClick(commonFormRef.getFieldsValue());
            }}
          >
            查询
          </Button>
        </Row>
      )}
    </div>
  );
};

export default React.memo(CommonForm);
