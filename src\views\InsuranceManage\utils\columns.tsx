import { DeviceInsuredStatus, InsuredStatus } from '@/utils/constant';
import { FormConfig, Tag } from '@jd/x-coreui';
import React from 'react';
export const SearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'policyNumber',
      label: '保单号',
      type: 'input',
      placeholder: '请输入保单号',
      maxLength: 30,
      labelCol: { span: 3 },
      wrapperCol: { span: 21 },
      xxl: 8,
      xl: 12,
      lg: 16,
    },
    {
      fieldName: 'deviceName',
      label: '车牌号',
      type: 'input',
      placeholder: '请输入车牌号',
      maxLength: 15,
      labelCol: { span: 3 },
      wrapperCol: { span: 21 },
      xxl: 8,
      xl: 12,
      lg: 16,
    },
    {
      fieldName: 'insuredStatus',
      label: '投保状态',
      type: 'select',
      placeholder: '请选择',
      labelInValue: false,
      options: [
        { label: '待生效', value: InsuredStatus.PENDING },
        { label: '生效中', value: InsuredStatus.EFFECTIVE },
        { label: '已失效', value: InsuredStatus.EXPIRED },
      ],
      labelCol: { span: 3 },
      wrapperCol: { span: 21 },
      xxl: 8,
      xl: 12,
      lg: 16,
    },
    {
      fieldName: 'effectiveTime',
      label: '生效时间',
      type: 'rangeTime',
      format: 'YYYY-MM-DD',
      placeholder: '请选择生效时间',
      showTime: false,
      labelCol: { span: 3 },
      wrapperCol: { span: 21 },
      xxl: 8,
      xl: 12,
      lg: 16,
    },
  ],
};

export const TableColumns: any[] = [
  {
    title: '保单号',
    dataIndex: 'policyNumber',
  },
  {
    title: '保险公司',
    dataIndex: 'insuranceCompany',
  },
  {
    title: '投保险种',
    dataIndex: 'insuranceType',
  },
  {
    title: '投保公司主体',
    dataIndex: 'insuredEntity',
  },
  {
    title: '投保车数',
    dataIndex: 'deviceCount',
  },
  {
    title: '保单保额',
    dataIndex: 'policyCoverageAmount',
  },
  {
    title: '生效开始日期',
    dataIndex: 'effectiveStartTime',
  },
  {
    title: '生效结束日期',
    dataIndex: 'effectiveEndTime',
  },
  {
    title: '保单状态',
    dataIndex: 'insuredStatusName',
  },
  {
    title: '最后操作人',
    dataIndex: 'modifyUser',
  },
  {
    title: '最后操作时间',
    dataIndex: 'modifyTime',
  },
  {
    title: '操作',
    dataIndex: 'operate',
  },
];

export const VehicleInsuranceColumns: any[] = [
  {
    title: '车牌号',
    dataIndex: 'deviceName',
    width: 150,
    fixed: 'left',
  },
  {
    title: '车架号',
    dataIndex: 'serialNo',
    width: 150,
  },
  {
    title: '省',
    dataIndex: 'provinceName',
    width: 80,
  },
  {
    title: '市',
    dataIndex: 'cityName',
    width: 150,
  },
  {
    title: '投保站点',
    dataIndex: 'stationName',
    width: 150,
  },
  {
    title: '运营半径(km)',
    dataIndex: 'radius',
    width: 120,
  },
  {
    title: '投保状态',
    dataIndex: 'deviceInsuredStatusName',
    width: 100,
    fixed: 'right',
    render: (text: any, record: any) => {
      return (
        <span
          style={{
            color:
              record.deviceInsuredStatus === DeviceInsuredStatus.LAPSED
                ? '#F5222D'
                : '#333',
          }}
        >
          {text}
        </span>
      );
    },
  },
];

export const InsuranceFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'policyNumber',
      label: '保险单号',
      type: 'input',
      placeholder: '请输入保险单号',
      labelCol: { span: 3 },
      wrapperCol: { span: 21 },
      maxLength: 30,
      required: true,
      validatorRules: [
        {
          required: true,
          message: '请输入保险单号',
        },
      ],
    },
    {
      fieldName: 'insuranceCompany',
      label: '保险公司',
      type: 'input',
      placeholder: '请输入保险公司全称',
      labelCol: { span: 3 },
      wrapperCol: { span: 21 },
      maxLength: 30,
      required: true,
      validatorRules: [
        {
          required: true,
          message: '请输入保险公司',
        },
      ],
    },
    {
      fieldName: 'insuranceType',
      label: '投保险种',
      type: 'input',
      placeholder: '请输入投保险种',
      labelCol: { span: 3 },
      wrapperCol: { span: 21 },
      maxLength: 30,
      required: true,
      validatorRules: [
        {
          required: true,
          message: '请输入投保险种',
        },
      ],
    },
    {
      fieldName: 'insuredEntity',
      label: '投保公司主体',
      type: 'input',
      placeholder: '请输入投保公司主体',
      maxLength: 50,
      labelCol: { span: 3 },
      wrapperCol: { span: 21 },
      required: true,
      validatorRules: [
        {
          required: true,
          message: '请输入投保公司主体',
        },
      ],
    },
    {
      fieldName: 'policyCoverageAmount',
      label: '保单保额',
      type: 'inputNumber',
      placeholder: '请输入金额，最多输入2位小数',
      labelCol: { span: 3 },
      wrapperCol: { span: 21 },
      formatter: (value) => {
        if (value === '' || value === null || value === undefined) return '';
        // 保留两位小数，使用正则添加千分位
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      },
      parser: (value) => {
        if (!value) return undefined;
        // 移除千分位分隔符，返回数字
        return value.replace(/,/g, '');
      },
      required: true,
      precision: 2,
      min: 0,
      max: 999999999,
      validatorRules: [
        {
          required: true,
          message: '请输入金额，最多输入2位小数',
        },
      ],
    },
    {
      fieldName: 'effectiveTime',
      label: '生效日期',
      type: 'rangeTime',
      format: 'YYYY-MM-DD',
      showTime: false,
      labelCol: { span: 3 },
      wrapperCol: { span: 21 },
      required: true,
      validatorRules: [
        {
          required: true,
          message: '请选择生效日期',
        },
      ],
    },
    {
      fieldName: 'policyAttachment',
      label: '保单',
      type: 'upload',
      fileListType: 'file',
      accept: 'image/*, .pdf',
      bucketName: 'rover-operation',
      LOPDN: process.env.JDX_APP_REQUEST_HEADER,
      getPreSignatureUrl: process.env.JDX_APP_UPLOAD_PRE_SIGNATURE_URL,
      max: 1,
      uploadedFileList: [],
      labelCol: { span: 3 },
      wrapperCol: { span: 21 },
      required: true,
      validatorRules: [
        {
          required: true,
          message: '请上传保单附件',
        },
      ],
    },
    {
      fieldName: 'otherAttachmentList',
      label: '其他附件',
      type: 'upload',
      accept: '*',
      fileListType: 'file',
      bucketName: 'rover-operation',
      LOPDN: process.env.JDX_APP_REQUEST_HEADER,
      getPreSignatureUrl: process.env.JDX_APP_UPLOAD_PRE_SIGNATURE_URL,
      labelCol: { span: 3 },
      wrapperCol: { span: 21 },
    },
  ],
};

export const BatchUploadColumns: any[] = [
  {
    title: '车牌号',
    dataIndex: 'deviceName',
  },
  {
    title: '车架号',
    dataIndex: 'serialNo',
  },
  {
    title: '省',
    dataIndex: 'provinceName',
  },
  {
    title: '市',
    dataIndex: 'cityName',
  },
  {
    title: '站点',
    dataIndex: 'stationName',
  },
  {
    title: '运营半径(km)',
    dataIndex: 'radius',
  },
  {
    title: '操作',
    dataIndex: 'operate',
  },
];

export const InsuranceExcelTemplateColumns: any[] = [
  {
    key: 'deviceName',
    title: '车牌号',
    required: true,
    type: 'string',
  },
  {
    key: 'serialNo',
    title: '车架号',
    type: 'string',
    required: true,
  },
  {
    key: 'provinceName',
    title: '省份',
    type: 'string',
    required: true,
  },
  {
    key: 'cityName',
    title: '城市',
    type: 'string',
  },
  {
    key: 'stationName',
    title: '站点',
    type: 'string',
  },
  {
    key: 'radius',
    title: '运营半径',
    type: 'number',
    transform: (value: any) => {
      if (!value) {
        return null;
      }
      return Number(value);
    },
  },
];

export const InsuranceDevicesColumns: any[] = [
  {
    title: '车牌号',
    dataIndex: 'deviceName',
  },
  {
    title: '车架号',
    dataIndex: 'serialNo',
  },
  {
    title: '省',
    dataIndex: 'provinceName',
  },
  {
    title: '市',
    dataIndex: 'cityName',
  },
  {
    title: '站点',
    dataIndex: 'stationName',
  },
  {
    title: '运营半径(km)',
    dataIndex: 'radius',
  },
  {
    title: '投保状态',
    dataIndex: 'deviceInsuredStatus',
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
];
