import { request } from '../core';

export const getShelfTypeList = () => {
  return request({
    path: '/k2/management/station_warehouse/shelf_type/get_shelf_type_list',
    method: 'POST',
    newGeteway: true,
  });
};

export const changeDeviceWordMode = (deviceBaseId, workMode) => {
  return request({
    path: '/k2/management/station_warehouse/update_device_work_mode',
    method: 'POST',
    body: {
      deviceBaseId,
      workMode,
    },
    newGeteway: true,
  });
};

export const getShelfPageList = (params: {
  stationBaseId: number;
  pageNum: number;
  pageSize: number;
}) => {
  return request({
    path: '/k2/management/station_warehouse/get_shelf_page_list',
    method: 'POST',
    body: params,
    newGeteway: true,
  });
};

export const deleteShelf = (shelfId: number) => {
  return request({
    path: '/k2/management/station_warehouse/delete_shelf',
    method: 'POST',
    body: {
      shelfId,
    },
    newGeteway: true,
  });
};

export const batchAddShelf = (params: {
  stationBaseId: number | null;
  shelfTypeId: number;
  prefixNo: string;
  suffixStartNo: string;
  suffixEndNo: string;
}) => {
  return request({
    path: '/k2/management/station_warehouse/batch_add_shelf',
    method: 'POST',
    body: params,
    newGeteway: true,
  });
};

export const getShelfTypePageList = (params: {
  pageNum: number;
  pageSize: number;
  shelfTypeName?: string | null;
  doubleSide?: string | null;
}) => {
  return request({
    path: '/k2/management/station_warehouse/shelf_type/get_shelf_type_page_list',
    method: 'POST',
    body: params,
    newGeteway: true,
  });
};

export const addShelfType = (params: {
  shelfTypeName: string;
  length: number;
  width: number;
  height: number;
  doubleSide: number;
  aSideGridCount: number;
  bSideGridCount: number;
}) => {
  return request({
    path: '/k2/management/station_warehouse/shelf_type/add_shelf_type',
    method: 'POST',
    body: params,
    newGeteway: true,
  });
};

export const getShelfTypeInfo = (shelfTypeId: number) => {
  return request({
    path: '/k2/management/station_warehouse/shelf_type/get_shelf_type',
    method: 'POST',
    body: {
      shelfTypeId,
    },
    newGeteway: true,
  });
};

export const editShelfTypeInfo = (params: {
  shelfTypeId: number;
  length: number;
  width: number;
  height: number;
}) => {
  return request({
    path: '/k2/management/station_warehouse/shelf_type/edit_shelf_type',
    method: 'POST',
    body: params,
    newGeteway: true,
  });
};

export const deleteShelfTypeInfo = (shelfTypeId) => {
  return request({
    path: '/k2/management/station_warehouse/shelf_type/delete_shelf_type',
    method: 'POST',
    body: {
      shelfTypeId,
    },
    newGeteway: true,
  });
};
export const getDeviceShelfTypeList = (params: {
  pageNum: number;
  pageSize: number;
  deviceTypeBaseId?: number | null;
  shelfTypeId?: number | null;
}) => {
  return request({
    path: '/k2/management/station_warehouse/shelf_type/get_device_type_shelf_type_page_list',
    method: 'POST',
    body: params,
    newGeteway: true,
  });
};

export const getDeviceTypeShelfType = (deviceTypeBaseId: number) => {
  return request({
    path: '/k2/management/station_warehouse/shelf_type/get_device_type_shelf_type',
    method: 'POST',
    body: {
      deviceTypeBaseId,
    },
    newGeteway: true,
  });
};

export const editDeviceTypeShelfType = (
  deviceTypeBaseId: number,
  shelfTypeIdList: number[],
) => {
  return request({
    path: '/k2/management/station_warehouse/shelf_type/edit_device_type_shelf_type',
    method: 'POST',
    body: {
      deviceTypeBaseId,
      shelfTypeIdList,
    },
    newGeteway: true,
  });
};

export const getStopRangeList = (warehouseTaskType?: string) => {
  return request({
    path: '/k2/management/station_warehouse/stop_range/get_stop_range_list',
    method: 'POST',
    body: {
      warehouseTaskType,
    },
    newGeteway: true,
  });
};

export const editStopRangeList = (
  toleranceList: {
    taskType: string;
    toleranceFrontBack: number;
    toleranceLeftRight: number;
    toleranceYaw: number;
  }[],
) => {
  return request({
    path: '/k2/management/station_warehouse/stop_range/edit_stop_range_list',
    method: 'POST',
    body: {
      stopRangeEditList: toleranceList,
    },
    newGeteway: true,
  });
};

export const getWarehouseStationBusinessInfo = (stationBaseId: number) => {
  return request({
    path: '/k2/management/station_warehouse/get_warehouse_station_business_info',
    method: 'POST',
    body: {
      stationBaseId,
    },
    newGeteway: true,
  });
};

export const resetStopRange = (taskType: string) => {
  return request({
    path: '/k2/management/station_warehouse/stop_range/reset_stop_range',
    method: 'POST',
    body: {
      taskType,
    },
    newGeteway: true,
  });
};
