import { request } from '../core';

export const getJiraDetail = (bugCode: string) => {
  return request({
    path: '/k2/management/bug/getBugInfo',
    method: 'POST',
    body: {
      bugCode,
    },
    newGeteway: true,
  });
};

export const getTabModuleList = () => {
  return request({
    path: '/k2/management/bugCategory/getIssueDownList',
    method: 'POST',
    newGeteway: true,
  });
};

export const submitJiraEdit = (values: any) => {
  return request({
    path: '/k2/management/bug/edit',
    method: 'POST',
    newGeteway: true,
    body: values,
  });
};

export const getXyBugs = () => {
  return request({
    path: '/k2/management/bug/getXyBugs',
    method: 'POST',
    newGeteway: true,
  });
};

export const getDeviceTypeList = (values: any) => {
  return request({
    path: '/k2/management/device_type/query_device_type_list',
    method: 'POST',
    newGeteway: true,
    body: values,
  });
};

export const getXingyunFieldList = () => {
  return request({
    path: '/k2/management/bug/get_xingyun_field_list',
    method: 'POST',
    newGeteway: true,
  });
};

export const getExportUrl = (values: any) => {
  return request({
    path: '/k2/management/bug/export_bug_info',
    method: 'POST',
    newGeteway: true,
    body: values,
  });
};