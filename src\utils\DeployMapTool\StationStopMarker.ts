import VectorLayer from 'ol/layer/Vector';
import <PERSON><PERSON><PERSON>ana<PERSON>, { <PERSON>er, MarkerOptions } from './MarkerManager';
import { Feature, Map as OlMap, Overlay } from 'ol';
import VectorSource from 'ol/source/Vector';
import { Point } from 'ol/geom';
import RBush from 'ol/structs/RBush';
import { MapLayerEnum } from '@/views/DeployMap/utils/enum';

export interface StationStopMarkerOptions extends MarkerOptions {
  label: string;
  labelStyle?: {
    fontSize?: string;
    color?: string;
    backgroundColor?: string;
  };
  popoverContent: HTMLElement | string;
  popoverStyle?: {
    backgroundColor?: string;
    borderColor?: string;
    maxWidth?: string;
  };
  onPopoverClick?: (type: string) => void;
  pointType?: 'STATION' | 'STOP'; // 点位类型：站点或停靠点
  disabled?: boolean; // 是否禁用
}

class StationStopMarker extends Marker {
  private label: string;
  private labelElement: HTMLElement;
  private labelOverlay: Overlay;
  private popoverOverlay: Overlay;
  private popoverContent: HTMLElement | string;
  private isPopoverVisible: boolean = false;
  private isLabelVisible: boolean = false;
  private onPopoverClick: ((type: string) => void) | undefined;
  private pointType: 'STATION' | 'STOP' | undefined;
  private disabled: boolean | undefined;

  constructor(options: StationStopMarkerOptions) {
    super(options);
    this.label = options.label;
    this.popoverContent = options.popoverContent;
    this.createLabel(options.labelStyle);
    this.createPopoverOverlay(options.popoverStyle);
    this.onPopoverClick = options.onPopoverClick;
    this.pointType = options.pointType;
    this.disabled = options.disabled;
  }

  private createLabel(style?: StationStopMarkerOptions['labelStyle']): void {
    this.labelElement = document.createElement('div');
    this.labelElement.innerHTML = this.label;
    this.labelElement.className = 'station-stop-marker-label';
    // Apply default and custom styles
    Object.assign(this.labelElement.style, {
      fontSize: '12px',
      color: 'black',
      padding: '2px 5px',
      ...style,
    });
    // Add label to map
    this.labelOverlay = new Overlay({
      element: this.labelElement,
      position: this.getLngLat(),
      positioning: 'top-center',
      offset: [0, 0],
      insertFirst: false,
    });
  }

  private createPopoverOverlay(
    style?: StationStopMarkerOptions['popoverStyle'],
  ): void {
    const popoverElement = document.createElement('div');
    popoverElement.className = 'station-stop-marker-popover';
    popoverElement.innerHTML =
      typeof this.popoverContent === 'string' ? this.popoverContent : '';
    if (typeof this.popoverContent !== 'string') {
      popoverElement.appendChild(this.popoverContent);
    }
    // Apply default and custom styles
    Object.assign(popoverElement.style, {
      backgroundColor: 'white',
      borderColor: '#ccc',
      borderWidth: '1px',
      borderStyle: 'solid',
      borderRadius: '4px',
      padding: '10px',
      maxWidth: '200px',
      ...style,
    });
    popoverElement
      .querySelector('.start')
      ?.addEventListener('click', (event) => {
        this.onPopoverClick && this.onPopoverClick('start');
      });
    popoverElement.querySelector('.end')?.addEventListener('click', (event) => {
      this.onPopoverClick && this.onPopoverClick('end');
    });
    this.popoverOverlay = new Overlay({
      element: popoverElement,
      position: this.getLngLat(),
      positioning: 'bottom-center',
      offset: [0, -50],
      autoPan: true,
      className: 'start-end-popover',
    });
  }

  showPopover(): void {
    this.map.addOverlay(this.popoverOverlay);
    if (!this.isPopoverVisible) {
      this.isPopoverVisible = true;
    }
  }

  hidePopover(): void {
    this.map.removeOverlay(this.popoverOverlay);
    if (this.isPopoverVisible) {
      this.isPopoverVisible = false;
    }
  }
  updatePopoverContent(content: HTMLElement | string): void {
    this.popoverContent = content;
    const popoverElement = this.popover.getElement();
    if (popoverElement) {
      popoverElement.innerHTML = typeof content === 'string' ? content : '';
      if (typeof content !== 'string') {
        popoverElement.appendChild(content);
      }
    }
  }

  showLabel(): void {
    if (!this.isLabelVisible) {
      this.map.addOverlay(this.labelOverlay);
      this.isLabelVisible = true;
    }
  }

  hideLabel(): void {
    if (this.isLabelVisible) {
      this.map.removeOverlay(this.labelOverlay);
      this.isLabelVisible = false;
    }
  }

  setLabelVisibility(isVisible: boolean): void {
    if (isVisible !== this.isLabelVisible) {
      if (isVisible) {
        this.showLabel();
      } else {
        this.hideLabel();
      }
    }
  }
  getIsLabelVisible(): boolean {
    return this.isLabelVisible;
  }
  // Override the select method to show popover
  select(): void {
    super.select();
    this.showPopover();
  }

  // Override the deselect method to hide popover
  deselect(): void {
    super.deselect();
    this.hidePopover();
  }
}

class StationStopMarkerManager {
  private map: OlMap;
  private markers: Map<string, StationStopMarker> = new Map();
  private selectedMarker: StationStopMarker | null = null;

  // 原始图层（保留向后兼容）
  private markerLayer: any;
  private selectedMarkerLayer: any;

  // 新增的四个图层
  private stationEnableLayer: VectorLayer<VectorSource>;
  private stationDisableLayer: VectorLayer<VectorSource>;
  private stopEnableLayer: VectorLayer<VectorSource>;
  private stopDisableLayer: VectorLayer<VectorSource>;

  private popoverLayer: VectorLayer<VectorSource>;
  private rbush: RBush<Feature<Point>> = new RBush();
  private onMarkerSelect: ((marker: StationStopMarker | null) => void) | null =
    null;

  constructor(
    map: OlMap,
    onMarkerSelect: (marker: StationStopMarker | null) => void,
  ) {
    this.map = map;
    this.setupMarkerLayer();
    this.setupMapClickListener();
    this.setupViewChangeListener();
    this.setupPopoverLayer();
    this.onMarkerSelect = onMarkerSelect;
  }

  private setupMarkerLayer(): void {
    // 保留原始图层（向后兼容）
    this.markerLayer = new VectorLayer({
      source: new VectorSource(),
      zIndex: 1005,
    });
    this.markerLayer.set('layerId', MapLayerEnum.STATION_POINT_LAYER);
    // this.map.addLayer(this.markerLayer);

    this.selectedMarkerLayer = new VectorLayer({
      source: new VectorSource(),
      zIndex: 1006,
    });
    this.selectedMarkerLayer.set('layerId', MapLayerEnum.SELECT_POINTS_LAYER);
    this.map.addLayer(this.selectedMarkerLayer);

    // 创建站点启用图层
    this.stationEnableLayer = new VectorLayer({
      source: new VectorSource(),
      zIndex: 1005,
    });
    this.stationEnableLayer.set(
      'layerId',
      MapLayerEnum.STATION_POINT_ENABLE_LAYER,
    );
    this.map.addLayer(this.stationEnableLayer);

    // 创建站点禁用图层
    this.stationDisableLayer = new VectorLayer({
      source: new VectorSource(),
      zIndex: 1005,
    });
    this.stationDisableLayer.set(
      'layerId',
      MapLayerEnum.STATION_POINT_DISABLE_LAYER,
    );
    this.map.addLayer(this.stationDisableLayer);

    // 创建停靠点启用图层
    this.stopEnableLayer = new VectorLayer({
      source: new VectorSource(),
      zIndex: 1005,
    });
    this.stopEnableLayer.set('layerId', MapLayerEnum.STOP_POINT_ENABLE_LAYER);
    this.map.addLayer(this.stopEnableLayer);

    // 创建停靠点禁用图层
    this.stopDisableLayer = new VectorLayer({
      source: new VectorSource(),
      zIndex: 1005,
    });
    this.stopDisableLayer.set('layerId', MapLayerEnum.STOP_POINT_DISABLE_LAYER);
    this.map.addLayer(this.stopDisableLayer);
  }

  private setupPopoverLayer(): void {
    this.popoverLayer = new VectorLayer({
      source: new VectorSource(),
      zIndex: 1010, // 设置一个很高的z-index值
    });
    this.popoverLayer.set('name', 'popoverLayer');
    this.map.addLayer(this.popoverLayer);
  }

  addMarker(options: StationStopMarkerOptions): StationStopMarker {
    if (this.markers.has(options.id)) {
      const existingMarker = this.markers.get(options.id);
      if (existingMarker) {
        existingMarker
          .getFeature()
          .getGeometry()
          ?.setCoordinates(options.lngLat);
        return existingMarker;
      }
    }

    const marker = new StationStopMarker(options);
    this.markers.set(marker.getId(), marker);

    // 根据点的类型和状态选择图层
    let targetLayer: VectorLayer<VectorSource>;
    if (options.pointType === 'STATION') {
      // 站点
      if (options.disabled) {
        targetLayer = this.stationDisableLayer;
      } else {
        targetLayer = this.stationEnableLayer;
      }
    } else {
      // 停靠点
      if (options.disabled) {
        targetLayer = this.stopDisableLayer;
      } else {
        targetLayer = this.stopEnableLayer;
      }
    }

    // 如果没有指定类型，默认添加到原始图层（向后兼容）
    if (!options.pointType) {
      targetLayer = this.markerLayer;
    }

    targetLayer.getSource()?.addFeature(marker.getFeature());
    this.rbush.insert(
      marker.getFeature().getGeometry()!.getExtent(),
      marker.getFeature(),
    );
    return marker;
  }
  addMarkers(optionsArray: StationStopMarkerOptions[]): StationStopMarker[] {
    const newMarkers: StationStopMarker[] = [];

    // 清空所有图层
    this.stationEnableLayer.getSource()?.clear();
    this.stationDisableLayer.getSource()?.clear();
    this.stopEnableLayer.getSource()?.clear();
    this.stopDisableLayer.getSource()?.clear();

    optionsArray.forEach((options) => {
      if (this.markers.has(options.id)) {
        const existingMarker = this.markers.get(options.id);
        if (existingMarker) {
          existingMarker
            .getFeature()
            .getGeometry()
            ?.setCoordinates(options.lngLat);
        }
        return;
      }

      // 创建新标记
      const marker = new StationStopMarker(options);
      this.markers.set(options.id, marker);
      // 根据点的类型和状态选择图层
      let targetLayer: VectorLayer<VectorSource>;
      if (options.pointType === 'STATION') {
        // 站点
        if (options.disabled) {
          targetLayer = this.stationDisableLayer;
        } else {
          targetLayer = this.stationEnableLayer;
        }
      } else {
        // 停靠点
        if (options.disabled) {
          targetLayer = this.stopDisableLayer;
        } else {
          targetLayer = this.stopEnableLayer;
        }
      }

      // 如果没有指定类型，默认添加到原始图层（向后兼容）
      if (!options.pointType) {
        targetLayer = this.markerLayer;
      }

      targetLayer.getSource()?.addFeature(marker.getFeature());
      this.rbush.insert(
        marker.getFeature().getGeometry()!.getExtent(),
        marker.getFeature(),
      );
      newMarkers.push(marker);
    });

    this.updateVisibleMarkers();
    return newMarkers;
  }
  /**
   * 设置标签可见性
   * @param isVisible 是否可见
   * @param visibleLayerIds 当前可见的图层ID列表，如果提供，则只有在这些图层中的marker才会显示标签
   */
  setAllLabelsVisibility(isVisible: boolean, visibleLayerIds?: string[]): void {
    // 如果没有提供visibleLayerIds，则按照原来的逻辑处理所有marker
    if (!visibleLayerIds || visibleLayerIds.length === 0) {
      this.markers.forEach((marker) => {
        marker.setLabelVisibility(isVisible);
      });
      return;
    }

    // 如果提供了visibleLayerIds，则根据marker所在图层决定是否显示标签
    this.markers.forEach((marker) => {
      const options = marker as unknown as StationStopMarkerOptions;
      let markerLayerId: string | undefined;

      // 根据marker的类型和状态确定其所在的图层ID
      if (options.pointType === 'STATION') {
        markerLayerId = options.disabled
          ? MapLayerEnum.STATION_POINT_DISABLE_LAYER
          : MapLayerEnum.STATION_POINT_ENABLE_LAYER;
      } else if (options.pointType === 'STOP') {
        markerLayerId = options.disabled
          ? MapLayerEnum.STOP_POINT_DISABLE_LAYER
          : MapLayerEnum.STOP_POINT_ENABLE_LAYER;
      } else {
        // 如果没有指定类型，默认使用原始图层（向后兼容）
        markerLayerId = MapLayerEnum.STATION_POINT_LAYER;
      }

      // 如果marker所在图层在可见图层列表中，则根据isVisible设置标签可见性
      // 否则隐藏标签
      if (visibleLayerIds.includes(markerLayerId)) {
        marker.setLabelVisibility(isVisible);
      } else {
        marker.setLabelVisibility(false);
      }
    });
  }
  getAllStationLabelsVisibility(): boolean {
    let allVisible = true;
    this.markers.forEach((marker) => {
      const options = marker as unknown as StationStopMarkerOptions;
      if (options.pointType === 'STATION' && !marker.getIsLabelVisible()) {
        allVisible = false;
      }
    });
    return allVisible;
  }
  getAllStopLabelsVisibility(): boolean {
    let allVisible = true;
    this.markers.forEach((marker) => {
      const options = marker as unknown as StationStopMarkerOptions;
      if (options.pointType === 'STOP' && !marker.getIsLabelVisible()) {
        allVisible = false;
      }
    });
    return allVisible;
  }
  getEnableStationLabelsVisibility(): boolean {
    let allVisible = true;
    this.markers.forEach((marker) => {
      const options = marker as unknown as StationStopMarkerOptions;
      if (
        options.pointType === 'STATION' &&
        !options.disabled &&
        !marker.getIsLabelVisible()
      ) {
        allVisible = false;
      }
    });
    return allVisible;
  }
  getDisableStationLabelsVisibility(): boolean {
    let allVisible = true;
    this.markers.forEach((marker) => {
      const options = marker as unknown as StationStopMarkerOptions;
      if (
        options.pointType === 'STATION' &&
        options.disabled &&
        !marker.getIsLabelVisible()
      ) {
        allVisible = false;
      }
    });
    return allVisible;
  }
  getEnableStopLabelsVisibility(): boolean {
    let allVisible = true;
    this.markers.forEach((marker) => {
      const options = marker as unknown as StationStopMarkerOptions;
      if (
        options.pointType === 'STOP' &&
        !options.disabled &&
        !marker.getIsLabelVisible()
      ) {
        allVisible = false;
      }
    });
    return allVisible;
  }
  getDisableStopLabelsVisibility(): boolean {
    let allVisible = true;
    this.markers.forEach((marker) => {
      const options = marker as unknown as StationStopMarkerOptions;
      if (
        options.pointType === 'STOP' &&
        options.disabled &&
        !marker.getIsLabelVisible()
      ) {
        allVisible = false;
      }
    });
    return allVisible;
  }
  setAllStationLabelsVisibility(isVisible: boolean): void {
    this.markers.forEach((marker) => {
      const options = marker as unknown as StationStopMarkerOptions;
      if (options.pointType === 'STATION') {
        marker.setLabelVisibility(isVisible);
      }
    });
  }
  setAllStopLabelsVisibility(isVisible: boolean): void {
    this.markers.forEach((marker) => {
      const options = marker as unknown as StationStopMarkerOptions;
      if (options.pointType === 'STOP') {
        marker.setLabelVisibility(isVisible);
      }
    });
  }
  setEnableStationLabelsVisibility(isVisible: boolean): void {
    this.markers.forEach((marker) => {
      const options = marker as unknown as StationStopMarkerOptions;
      if (options.pointType === 'STATION' && !options.disabled) {
        marker.setLabelVisibility(isVisible);
      }
    });
  }
  setDisableStationLabelsVisibility(isVisible: boolean): void {
    this.markers.forEach((marker) => {
      const options = marker as unknown as StationStopMarkerOptions;
      if (options.pointType === 'STATION' && options.disabled) {
        marker.setLabelVisibility(isVisible);
      }
    });
  }
  setEnableStopLabelsVisibility(isVisible: boolean): void {
    this.markers.forEach((marker) => {
      const options = marker as unknown as StationStopMarkerOptions;
      if (options.pointType === 'STOP' && !options.disabled) {
        marker.setLabelVisibility(isVisible);
      }
    });
  }
  setDisableStopLabelsVisibility(isVisible: boolean): void {
    this.markers.forEach((marker) => {
      const options = marker as unknown as StationStopMarkerOptions;
      if (options.pointType === 'STOP' && options.disabled) {
        marker.setLabelVisibility(isVisible);
      }
    });
  }
  removeMarker(id: string): boolean {
    const marker = this.markers.get(id);
    if (marker) {
      if (this.selectedMarker === marker) {
        this.deselectAll();
      }

      // 从所有图层中移除该 marker
      this.stationEnableLayer.getSource()?.removeFeature(marker.getFeature());
      this.stationDisableLayer.getSource()?.removeFeature(marker.getFeature());
      this.stopEnableLayer.getSource()?.removeFeature(marker.getFeature());
      this.stopDisableLayer.getSource()?.removeFeature(marker.getFeature());
      this.markerLayer.getSource()?.removeFeature(marker.getFeature());
      this.selectedMarkerLayer.getSource()?.removeFeature(marker.getFeature());

      this.rbush.remove(marker.getFeature());
      return this.markers.delete(id);
    }
    return false;
  }

  selectMarker(id: string): void {
    const marker = this.markers.get(id);
    if (marker) {
      if (this.selectedMarker && this.selectedMarker !== marker) {
        this.deselectAll();
      }
      marker.select();
      this.selectedMarker = marker;

      // 从所有可能的图层中移除该 marker
      this.stationEnableLayer.getSource()?.removeFeature(marker.getFeature());
      this.stationDisableLayer.getSource()?.removeFeature(marker.getFeature());
      this.stopEnableLayer.getSource()?.removeFeature(marker.getFeature());
      this.stopDisableLayer.getSource()?.removeFeature(marker.getFeature());
      this.markerLayer.getSource()?.removeFeature(marker.getFeature());

      // 添加到选中图层
      this.selectedMarkerLayer.getSource()?.addFeature(marker.getFeature());

      // 显示弹出框
      marker.showPopover();

      if (this.onMarkerSelect) {
        this.onMarkerSelect(marker);
      }
    }
  }

  deselectAll(): void {
    if (this.selectedMarker) {
      this.selectedMarker.deselect();

      // 从选中图层移除
      this.selectedMarkerLayer
        .getSource()
        ?.removeFeature(this.selectedMarker.getFeature());

      // 根据 marker 的类型和状态，将其添加回对应的图层
      const options = this
        .selectedMarker as unknown as StationStopMarkerOptions;
      let targetLayer: VectorLayer<VectorSource>;

      if (options.pointType === 'STATION') {
        // 站点
        if (options.disabled) {
          targetLayer = this.stationDisableLayer;
        } else {
          targetLayer = this.stationEnableLayer;
        }
      } else if (options.pointType === 'STOP') {
        // 停靠点
        if (options.disabled) {
          targetLayer = this.stopDisableLayer;
        } else {
          targetLayer = this.stopEnableLayer;
        }
      } else {
        // 如果没有指定类型，默认添加到原始图层（向后兼容）
        targetLayer = this.markerLayer;
      }

      targetLayer.getSource()?.addFeature(this.selectedMarker.getFeature());

      // 隐藏弹出框
      this.selectedMarker.hidePopover();

      this.selectedMarker = null;
      if (this.onMarkerSelect) {
        this.onMarkerSelect(null);
      }
    }
  }

  setMarkerPopoverVisibility(id: string, isVisible: boolean): void {
    const marker = this.markers.get(id);
    if (marker) {
      if (isVisible) {
        marker.showPopover();
      } else {
        marker.hidePopover();
      }
    }
  }
  getSelectedMarker(): StationStopMarker | null {
    return this.selectedMarker;
  }

  getAllMarkers(): StationStopMarker[] {
    return Array.from(this.markers.values());
  }
  private clearAllOverlays() {
    const overlays = this.map.getOverlays().getArray();
    const overlaysCopy = [...overlays];
    overlaysCopy.forEach((overlay) => {
      this.map.removeOverlay(overlay);
    });
  }
  clearAllMarkers(): void {
    this.deselectAll();

    // 清空所有图层
    this.stationEnableLayer.getSource()?.clear();
    this.stationDisableLayer.getSource()?.clear();
    this.stopEnableLayer.getSource()?.clear();
    this.stopDisableLayer.getSource()?.clear();
    this.markerLayer.getSource()?.clear();
    this.selectedMarkerLayer.getSource()?.clear();

    this.clearAllOverlays();
    this.markers.clear();
    this.rbush.clear();
  }

  private setupMapClickListener(): void {
    this.map.on('click', (event) => {
      const clickedFeature = this.map.forEachFeatureAtPixel(
        event.pixel,
        (feature) => feature,
        {
          hitTolerance: 5,
          layerFilter: (layer) =>
            layer === this.stationEnableLayer ||
            layer === this.stationDisableLayer ||
            layer === this.stopEnableLayer ||
            layer === this.stopDisableLayer ||
            layer === this.selectedMarkerLayer,
        },
      );
      if (clickedFeature) {
        const marker = this.markers.get(clickedFeature.getId() as string);
        if (marker) {
          this.selectMarker(marker.getId());
        }
      } else {
        this.deselectAll();
      }
    });
  }

  private updateVisibleMarkers(): void {
    const extent = this.map.getView().calculateExtent(this.map.getSize());
    const visibleFeatures = this.rbush.getInExtent(extent);

    // 清空所有图层
    this.stationEnableLayer.getSource()?.clear();
    this.stationDisableLayer.getSource()?.clear();
    this.stopEnableLayer.getSource()?.clear();
    this.stopDisableLayer.getSource()?.clear();
    this.markerLayer.getSource()?.clear();
    this.selectedMarkerLayer.getSource()?.clear();

    visibleFeatures.forEach((feature) => {
      const marker = this.markers.get(feature.getId() as string);

      if (marker) {
        if (marker === this.selectedMarker) {
          // 选中的 marker 添加到选中图层
          this.selectedMarkerLayer.getSource()?.addFeature(feature);
        } else {
          // 根据 marker 的类型和状态，将其添加到对应的图层
          const options = marker as unknown as StationStopMarkerOptions;

          if (options.pointType === 'STATION') {
            // 站点
            if (options.disabled) {
              this.stationDisableLayer.getSource()?.addFeature(feature);
            } else {
              this.stationEnableLayer.getSource()?.addFeature(feature);
            }
          } else if (options.pointType === 'STOP') {
            // 停靠点
            if (options.disabled) {
              this.stopDisableLayer.getSource()?.addFeature(feature);
            } else {
              this.stopEnableLayer.getSource()?.addFeature(feature);
            }
          } else {
            // 如果没有指定类型，默认添加到原始图层（向后兼容）
            this.markerLayer.getSource()?.addFeature(feature);
          }
        }
      }
    });
  }
  private setupViewChangeListener(): void {
    this.map
      .getView()
      .on('change:center', this.updateVisibleMarkers.bind(this));
    this.map
      .getView()
      .on('change:resolution', this.updateVisibleMarkers.bind(this));
  }

  /**
   * 设置图层可见性
   * @param layerId 图层ID
   * @param visible 是否可见
   */
  setLayerVisibility(layerId: string, visible: boolean): void {
    const layers = this.map.getLayers().getArray();
    for (let layer of layers) {
      if (layer.get('layerId') === layerId) {
        layer.setVisible(visible);
        break;
      }
    }
  }
  getLayerVisibility(layerId: string): boolean {
    const layers = this.map.getLayers().getArray();
    for (let layer of layers) {
      if (layer.get('layerId') === layerId) {
        return layer.getVisible();
      }
    }
    return false;
  }

  /**
   * 获取所有子图层
   * @returns 返回所有子图层
   */
  getAllSubLayers(): { [key: string]: VectorLayer<VectorSource> } {
    return {
      stationEnableLayer: this.stationEnableLayer,
      stationDisableLayer: this.stationDisableLayer,
      stopEnableLayer: this.stopEnableLayer,
      stopDisableLayer: this.stopDisableLayer,
    };
  }
}
export default StationStopMarkerManager;
