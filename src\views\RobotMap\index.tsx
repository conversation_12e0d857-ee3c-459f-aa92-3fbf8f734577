import React, { useState, useEffect } from 'react';
import { CommonTable, TableOperateBtn, CommonForm } from '@/components';
import { tableColumns } from './utils/columns';
import { useTableData } from '@/components/CommonTable/useTableData';
import { RobotMapApi, CommonApi } from '@/fetch/business';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/store';
import { useNavigate } from 'react-router-dom';
import { HttpStatusCode } from '@/fetch/core/constant';
const fetchApi = new RobotMapApi();

const RobotMap = () => {
  const navigator = useNavigate();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const initSearchCondition = {
    searchForm: {
      name: null,
      groupNo: null,
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValues.searchValues
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );
  const [stationList, setStationList] = useState<any[]>([]);
  const { tableData, loading } = useTableData(
    {
      searchForm: {
        ...searchCondition.searchForm,
        groupNo: searchCondition.searchForm.groupNo?.value,
      },
      pageNum: searchCondition.pageNum,
      pageSize: searchCondition.pageSize,
    },
    fetchApi.fetchTableList,
  );

  useEffect(() => {
    fetchApi.fetchStationList().then((res) => {
      if (res.code === HttpStatusCode.Success) {
        setStationList(
          res.data?.map((val) => ({
            label: val.name,
            value: val.groupNo,
          })),
        );
      }
    });
  }, []);

  const middleBtns: any[] = [
    {
      show: true,
      title: '跳转地图组',
      onClick: () => window.open(process.env.JDX_APP_MAP_GROUP, '_blank'),
    },
  ];

  const formateColumns = () => {
    return tableColumns.map((col) => {
      switch (col.dataIndex) {
        case 'operation':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return (
                <div className="operate">
                  <TableOperateBtn
                    title="版本信息"
                    handleClick={() => {
                      navigator(
                        `/app/robotMapManage/versionInfo?mapNumber=${record.number}&version=${record.version}`,
                      );
                    }}
                  />
                </div>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  const onSearchClick = (val) => {
    const data = {
      searchForm: val,
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(data);
  };
  return (
    <>
      <CommonForm
        formConfig={{
          fields: [
            {
              fieldName: 'name',
              label: '地图名称',
              placeholder: '请输入地图名称',
              type: 'input',
              maxLength: 50,
              labelCol: { span: 6 },
              wrapperCol: { span: 18 },
            },
            {
              fieldName: 'groupNo',
              label: '站点',
              placeholder: '请选择站点',
              type: 'select',
              labelCol: { span: 6 },
              wrapperCol: { span: 18 },
              options: stationList,
            },
          ],
        }}
        defaultValue={searchCondition.searchForm}
        layout="inline"
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={() => setSearchCondition({ ...initSearchCondition })}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        columns={formateColumns()}
        loading={loading}
        rowKey={'number'}
        middleBtns={middleBtns}
        searchCondition={searchCondition}
        onPageChange={(value: any) => setSearchCondition(value)}
      />
    </>
  );
};

export default React.memo(RobotMap);
