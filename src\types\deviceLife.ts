import { CommonTableRequest } from './interface';

/** 设备类型 */
export interface DeviceType {
  /** 设备类型id */
  deviceTypeBaseId: number;
  /** 设备类型名称 */
  deviceTypeName: string;
}

/** 设备批量处理请求 */
export interface DeviceBatchRequest {
  /** 设备名称列表 */
  deviceNameList: string[];
  /** 站点ID */
  stationBaseId?: number | null;
  /** 车辆归属方 */
  ownerUseCase?: string | null;
}

/** 设备生命周期搜索表单 */
export interface DeviceRequest extends CommonTableRequest {
  searchForm: {
    /** 所在国家/大区 */
    countryId?: number | null;
    /** 所在省份 */
    stateId?: number | null;
    /** 所在城市 */
    cityId?: number | null;
    /** 站点ID */
    stationBaseId?: number | null;
    /** 公司编号 */
    companyNumber?: string | null;
    /** 设备编号 */
    name?: string | null;
    /** 设备生命周期 */
    hardwareStatusList?: string[] | null;
    /** 标定检测状态 */
    checkStatus?: string | null;
    /** 产品类型 */
    productType?: string | null;
    /** 设备类型 */
    businessType?: string | null;
    /** 车辆归属方 */
    ownerUseCaseList?: string[] | null;
    /** 是否存在维修单 */
    isRequire?: number | null;
    /** 是否为虚拟车 */
    isVirtual?: number | null;
    /** 车型id */
    deviceTypeBaseId?: number | null;
    /** 车架号 */
    serialNo?: string | null;
  };
}

/** 车辆生产表格 */
export interface DeviceProduceResponse {
  /** 设备id */
  id: number;
  /** 设备名称 */
  deviceName: string;
  /** 车型id */
  deviceTypeBaseId: number;
  /** 车型名称 */
  deviceTypeName: string;
  /** 产品类型 */
  productType: string;
  /** 产品类型名称 */
  productTypeName: string;
  /** 设备类型 */
  businessType: string;
  /** 设备类型名称 */
  businessTypeName: string;
  /** 站点ID */
  stationBaseId: number;
  /** 站点名称 */
  stationName: string;
  /** 站点用途 */
  stationUseCase: string;
  /** 站点用途名称 */
  stationUseCaseName: string;
  /** 车辆归属方 */
  ownerUseCase: string;
  /** 车辆归属方名称 */
  ownerUseCaseName: string;
  /** 设备生命周期 */
  hardwareStatus: string;
  /** 设备生命周期名称 */
  hardwareStatusName: string;
  /** 标定检测状态 */
  checkStatus: string;
  /** 标定检测状态名称 */
  checkStatusName: string;
  /** 车架号 */
  serialNo: string;
}

/** 标定结果表格 */
export interface DeviceCheckResult {
  /** 标定结果ID标识 */
  id: number;
  /** 最近维护人 */
  modifyUser: string;
  /** 最近维护时间，yyyy-MM-dd HH:mm:ss */
  modifyTime: string;
}

/** 车辆调度表格 */
export interface DevicePollResponse {
  /** 设备id */
  id: number;
  /** 设备名称 */
  deviceName: string;
  /** 设备类型 */
  businessType: string;
  /** 设备类型名称 */
  businessTypeName: string;
  /** 站点ID */
  stationBaseId: number;
  /** 站点名称 */
  stationName: string;
  /** 站点用途 */
  stationUseCase: string;
  /** 站点用途名称 */
  stationUseCaseName: string;
  /** 车辆归属方 */
  ownerUseCase: string;
  /** 车辆归属方名称 */
  ownerUseCaseName: string;
  /** 设备生命周期 */
  hardwareStatus: string;
  /** 设备生命周期名称 */
  hardwareStatusName: string;
  /** 标定检测状态 */
  checkStatus: string;
  /** 标定检测状态名称 */
  checkStatusName: string;
  /** 车架号 */
  serialNo: string;
  /** 是否存在维修单 */
  isRequire: number;
  /** 是否存在维修单名称 */
  isRequireName: string;
}

/** 车辆交付表格 */
export interface DeviceUseResponse {
  /** 设备id */
  id: number;
  /** 设备名称 */
  deviceName: string;
  /** 设备类型 */
  businessType: string;
  /** 设备类型名称 */
  businessTypeName: string;
  /** 公司编号 */
  companyNumber: string;
  /** 公司名称 */
  companyName: string;
  /** 站点ID */
  stationBaseId: number;
  /** 站点名称 */
  stationName: string;
  /** 站点用途 */
  stationUseCase: string;
  /** 站点用途名称 */
  stationUseCaseName: string;
  /** 车辆归属方 */
  ownerUseCase: string;
  /** 车辆归属方名称 */
  ownerUseCaseName: string;
  /** 设备生命周期 */
  hardwareStatus: string;
  /** 设备生命周期名称 */
  hardwareStatusName: string;
  /** 标定检测状态 */
  checkStatus: string;
  /** 标定检测状态名称 */
  checkStatusName: string;
  /** 车架号 */
  serialNo: string;
  /** 推流模式 */
  videoMode: number;
  /** 推流模式名称 */
  videoModeName: string;
}

/** 车辆维修表格 */
export interface DeviceRepairResponse {
  /** 设备id */
  id: number;
  /** 设备名称 */
  deviceName: string;
  /** 设备类型 */
  businessType: string;
  /** 设备类型名称 */
  businessTypeName: string;
  /** 站点ID */
  stationBaseId: number;
  /** 站点名称 */
  stationName: string;
  /** 站点用途 */
  stationUseCase: string;
  /** 站点用途名称 */
  stationUseCaseName: string;
  /** 车辆归属方 */
  ownerUseCase: string;
  /** 车辆归属方名称 */
  ownerUseCaseName: string;
  /** 设备生命周期 */
  hardwareStatus: string;
  /** 设备生命周期名称 */
  hardwareStatusName: string;
  /** 标定检测状态 */
  checkStatus: string;
  /** 标定检测状态名称 */
  checkStatusName: string;
  /** 车架号 */
  serialNo: string;
  /** 设备类型id */
  deviceTypeBaseId: number;
  /** 设备类型名称 */
  deviceTypeName: string;
}
