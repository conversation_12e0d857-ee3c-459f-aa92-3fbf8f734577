import ModulePart from '@/components/ModulePart';
import { ProductType } from '@/utils/enum';
import { QuestionCircleOutlined } from '@ant-design/icons';
import {
  Form,
  FormInstance,
  Input,
  InputNumber,
  Row,
  Select,
  Tooltip,
} from 'antd';
import { StationFetchApi } from '@/fetch/business';
import React, { useRef, useState } from 'react';
import { HttpStatusCode } from '@/fetch/core/constant';
import { ChargeType, ChargeTypeText } from '@/utils/constant';
import GroupBusinessList from '../GroupBusinessList';
import { StationBusinessInfo } from '@/types/station';
const BusinessInfo = (props: {
  form: FormInstance<any>;
  stationType: ProductType | undefined;
  businessInfo: StationBusinessInfo | null;
}) => {
  const { form, stationType, businessInfo } = props;
  const stationFetch = new StationFetchApi();
  const [validateInfo, setValidateInfo] = useState<{
    validateStatus: 'success' | 'warning' | 'error' | 'validating' | '';
  }>({
    validateStatus: '',
  });
  const bussinessRef = useRef<any>(null);
  return (
    <>
      <ModulePart title="业务配置" otherContentStyle={{ paddingBottom: '0px' }}>
        <Row>
          {stationType !== ProductType.INTEGRATE && (
            <Form.Item
              className="col-item"
              wrapperCol={{ span: 10 }}
              labelCol={{ span: 16 }}
              initialValue={30}
              label={
                <div style={{ display: 'flex' }}>
                  <p>{'揽件接单最小时长设置(min)'}</p>
                  <Tooltip
                    placement="left"
                    title={'车辆停靠时长小于此时长，车端不再接收揽件任务。'}
                    overlayStyle={{ maxWidth: 600 }}
                    autoAdjustOverflow={false}
                  >
                    <QuestionCircleOutlined className="question" />
                  </Tooltip>
                </div>
              }
              name="minDropOffTime"
              rules={[
                { required: true, message: '请输入揽件接单最小时长' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value) {
                      return Promise.reject(new Error(' '));
                    }
                    const reg = /^([3-9]\d{0,}|[1-9]\d{1,})?$/;
                    if (!reg.test(value)) {
                      return Promise.reject(new Error('只能输入≥3的正整数'));
                    }
                    if (value.length > 4) {
                      return Promise.reject(new Error('最多输入4位数字'));
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <Input allowClear placeholder="请输入≥3的正整数" />
            </Form.Item>
          )}

          <Form.Item
            className="col-item"
            label="所在业务系统的站点编号"
            wrapperCol={{ span: 8 }}
            labelCol={{ span: 8 }}
            name="number"
            validateStatus={validateInfo.validateStatus}
            hasFeedback
            rules={[
              { required: true, message: '请输入所在业务系统的站点编号' },
              ({ getFieldValue }) => ({
                async validator(_, value) {
                  if (
                    value === undefined ||
                    (value != undefined && value.length <= 0)
                  ) {
                    return Promise.reject(new Error(' '));
                  }
                  const reg = /^[a-zA-Z0-9._-]+$/;
                  if (!reg.test(value)) {
                    return Promise.reject(
                      new Error('请输入英文、数字、.、-、_'),
                    );
                  }
                  const res = await stationFetch.checkDuplicateStationNumber({
                    number: value,
                  });
                  if (res.code != HttpStatusCode.Success) {
                    setValidateInfo({
                      validateStatus: 'error',
                    });
                    return Promise.reject(new Error(res.message));
                  }
                  setValidateInfo({
                    validateStatus: 'success',
                  });
                  return Promise.resolve();
                },
              }),
            ]}
            extra={
              <div>
                说明：在业务系统的编号，如：青龙基础资料系统，则为“网点ID”。
              </div>
            }
          >
            <Input
              allowClear
              maxLength={20}
              placeholder="请输入所在业务系统的站点编号"
              onChange={(e) => {
                form.setFieldsValue({
                  number: e.target.value.slice(0, 20),
                });
              }}
            />
          </Form.Item>
          {stationType === ProductType.INTEGRATE && (
            <>
              <Form.Item
                label="车端亮灯提醒设置=波次时间(最晚生产时间)-车端当前时间≤"
                className="col-item"
                wrapperCol={{ span: 4 }}
                labelCol={{ span: 20 }}
                name="reminderTime"
                rules={[{ required: true, message: '请输入数字' }]}
                extra={
                  <span
                    style={{ position: 'absolute', top: 0, right: '-30px' }}
                  >
                    min
                  </span>
                }
              >
                <InputNumber max={500} placeholder="请输入数字" />
              </Form.Item>
              <Form.Item
                label="AMR充电方式"
                className="col-item"
                wrapperCol={{ span: 24 }}
                labelCol={{ span: 14 }}
                name="chargeType"
                rules={[{ required: true, message: '请选择' }]}
              >
                <Select
                  placeholder="请选择"
                  options={[
                    {
                      value: ChargeType.CHARGE,
                      label: ChargeTypeText[ChargeType.CHARGE],
                    },
                    {
                      value: ChargeType.SWAP,
                      label: ChargeTypeText[ChargeType.SWAP],
                    },
                  ]}
                />
              </Form.Item>
            </>
          )}
        </Row>
        {stationType === ProductType.INTEGRATE && (
          <Row>
            <Form.Item
              className="col-item"
              wrapperCol={{ span: 15 }}
              labelCol={{ span: 8 }}
              label="可接任务电量(%)"
              name="missionChargeLimit"
              rules={[{ required: true, message: '必填' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                placeholder="请输入1-100数字"
                min={1}
                max={100}
              />
            </Form.Item>
            <Form.Item
              label="自动回充电量(%)"
              className="col-item"
              wrapperCol={{ span: 15 }}
              labelCol={{ span: 8 }}
              name="autoChargeLimit"
              rules={[{ required: true, message: '必填' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                placeholder="请输入1-100数字"
                min={1}
                max={100}
              />
            </Form.Item>
            <Form.Item
              label="强制回充电量(%)"
              className="col-item"
              wrapperCol={{ span: 15 }}
              labelCol={{ span: 8 }}
              name="forceChargeLimit"
              rules={[{ required: true, message: '必填' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                placeholder="请输入1-100数字"
                min={1}
                max={100}
              />
            </Form.Item>
          </Row>
        )}
      </ModulePart>
      {stationType === ProductType.INTEGRATE && (
        <ModulePart title="业务接口配置">
          <Form.Item name="groupBusinessList" wrapperCol={{ span: 24 }}>
            <GroupBusinessList
              isEdit={true}
              ref={bussinessRef}
              onChange={(list: any) => {
                form.setFieldValue('groupBusinessList', list);
              }}
              businessList={businessInfo?.groupBusinessList || []}
            />
          </Form.Item>
        </ModulePart>
      )}
    </>
  );
};
export default React.memo(BusinessInfo);
