import { Form, Modal, message, Row, Col, Select, Input } from 'antd';
import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { CommonEdit } from '@/components';
import lodash, { clone } from 'lodash';
import { GridManageApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { formatLocation } from '@/utils/utils';
import { YESNO } from '@/utils/enum';
import { ProductDropDownList, GlobalEventName } from '@/utils/constant';
import BasicInfo from './component/BasicInfo';
import { GridTitle, PageType } from '@/utils/EditTitle';
import {
  setGridListAction,
  setGridSizeListAction,
  setGridPositionValueAction,
  boxGridSelector,
} from '@/redux/reducer/boxGrid';
import {
  addGlobalEventListener,
  removeGlobalEventListener,
  sendGlobalEvent,
} from '@/utils/emit';
import GridList from './component/GridList';
import { validationGrid } from './utils';
import { ProductType } from '@/utils/enum';

const GridManageEdit = () => {
  const fetchApi = new GridManageApi();
  const [basicForm] = Form.useForm();
  const [gridEditForm] = Form.useForm();
  const gridData = useSelector(boxGridSelector);
  const dispatch = useDispatch();
  const navigator = useNavigate();
  const { id, type, productType } = formatLocation(window.location.search);
  const [gridList, setGridList] = useState<any>([]);
  const [gridPositionValue, setGridPositionValue] = useState<any>({});

  const [initValues, setInitValues] = useState<any>({
    enable: 1,
    productType: 'vehicle',
  });
  let gridCofigArr: any[] =
    gridData.gridSizeList.length > 0
      ? gridData.gridSizeList
      : [{ width: '', length: '', height: '' }];
  const boxGirdCrumbItems = [
    {
      title: '箱体格口模板管理',
      route: '',
    },
    {
      title: GridTitle[type] || '',
      route: '',
    },
  ];
  useEffect(() => {
    if (id) {
      fetchBoxGridData();
    } else {
      dispatch(setGridSizeListAction(gridCofigArr));
    }
    // 监听格口增删改查
    addGlobalEventListener(GlobalEventName.EVENT_ADD_CONFIG, addFromClick);
    addGlobalEventListener(
      GlobalEventName.EVENT_DELETE_CONFIG,
      deleteFromClick,
    );
    addGlobalEventListener(
      GlobalEventName.EVENT_UPDATE_CONFIG,
      updateConfigFnc,
    );
    return () => {
      // 重置数据
      removeGlobalEventListener(GlobalEventName.EVENT_ADD_CONFIG, addFromClick);
      removeGlobalEventListener(
        GlobalEventName.EVENT_DELETE_CONFIG,
        deleteFromClick,
      );
      removeGlobalEventListener(
        GlobalEventName.EVENT_UPDATE_CONFIG,
        updateConfigFnc,
      );
      gridCofigArr = [{ width: '', length: '', height: '' }];
      dispatch(setGridPositionValueAction([]));
      dispatch(setGridSizeListAction([]));
    };
  }, []);

  const fetchBoxGridData = async () => {
    try {
      const response: any = await fetchApi.fetchDetail(id);
      if (response && response.code === HttpStatusCode.Success) {
        if (type == PageType.ADD && id) {
          response.data.id = '';
        }
        gridPositionValue.leftBoxColumnNum = response.data.leftBoxColumnNum;
        gridPositionValue.rightBoxColumnNum = response.data.rightBoxColumnNum;
        if (
          response.data.gridSpecificationList &&
          response.data.gridSpecificationList.length > 0
        ) {
          gridCofigArr = response.data.gridSpecificationList.map((ele: any) => {
            ele.sizeStr = `${ele.width} × ${ele.length} × ${ele.height}`;
            return ele;
          });
        }
        basicForm.setFieldsValue({ productType: response.data.productType });
        dispatch(setGridSizeListAction(gridCofigArr));
        setGridPositionValue({ ...gridPositionValue });
        dispatch(setGridPositionValueAction(gridPositionValue));
        setInitValues(response.data);
        setGridList(
          response.data.gridList?.map((item: any) => {
            return {
              ...item,
              sizeStr: `${item.width} × ${item.length} × ${item.height}`,
            };
          }),
        );
      }
    } catch (e) {
      console.log(e);
    }
  };

  const goBack = () => {
    navigator('/app/gridManage');
  };
  const confirm = async () => {
    for (let i = 0; i < gridCofigArr.length; i++) {
      const item = gridCofigArr[i];
      if (!item.width || !item.length || !item.height) {
        sendGlobalEvent(GlobalEventName.EVENT_VALID_CONFIG);
        return true;
      }
    }
    const editGridList: any[] = gridEditForm.getFieldValue('gridList');
    const valid = validationGrid(
      editGridList,
      gridPositionValue,
      gridCofigArr,
      basicForm.getFieldsValue().productType,
    );
    if (!valid.isValid) {
      Modal.error({
        title: valid.msg,
      });
      return;
    }
    try {
      const formValues = await basicForm.validateFields();
      const params = {
        ...formValues,
        hardwareModelId: formValues.hardwareModelId?.value,
        driverType: formValues.driverType?.value,
        baudRate: parseInt(formValues?.baudRate),
        gridList: handleGirdList(),
      };
      if (id && type === PageType.EDIT) {
        params.id = id;
      }
      const response = await fetchApi.submitGrid({ type, requestBody: params });
      if (response.code === HttpStatusCode.Success) {
        message.success(response.message);
        goBack();
      } else {
        message.error(response.message);
      }
    } catch (e) {
      console.log(e);
    }
  };

  const handleGirdList = () => {
    const editGridList: any[] = gridEditForm.getFieldValue('gridList');
    const gird: any[] = [];
    editGridList.forEach((item: any) => {
      const obj = {
        boardNo: item.boardNo,
        gridNo: item.gridNo,
        height: item.height,
        length: item.length,
        lockNo: item.lockNo,
        side: item.side,
        enable: item.enable === 1 ? 1 : 0,
        width: item.width,
        id: item.newAdd ? null : item.id,
        palletList: item.palletList,
        type: item.type,
      };
      gird.push(obj);
    });
    return gird;
  };
  const updateBaseInfoValue = (value: any, key: any) => {
    gridPositionValue[key] = value;
    setGridPositionValue({ ...gridPositionValue });
    dispatch(setGridPositionValueAction(gridPositionValue));
  };

  const addFromClick = (list) => {
    if (list.length >= 5) {
      message.warning('最多只可添加5行配置');
      return;
    }
    const curProduct = basicForm.getFieldsValue().productType;
    if (curProduct === ProductType.VEHICLE) {
      const firstItem = list[0];
      list = [
        ...list,
        { width: firstItem.width, length: firstItem.length, height: '' },
      ];
    } else if (curProduct === ProductType.ROBOT || ProductType.INTEGRATE) {
      list = [...list, { width: '', length: '', height: '' }];
    }
    dispatch(setGridSizeListAction(list));
  };
  const deleteFromClick = ({ index, list }: { index: number; list: any[] }) => {
    const cur = list[index];
    if (cur) {
      const editGridList: any[] = gridEditForm.getFieldValue('gridList');
      const sizeStrArr = list.filter((grid: any) => {
        return grid.sizeStr === cur.sizeStr;
      });
      if (
        editGridList.some((grid: any) => grid.sizeStr === cur.sizeStr) &&
        sizeStrArr.length == 1
      ) {
        message.error('删除失败，此规格已被引用！');
        return;
      }
      const tempCofigArr = [...list];
      tempCofigArr.splice(index, 1);
      list = lodash.cloneDeep([...tempCofigArr]);
      dispatch(setGridSizeListAction(list));
    }
  };
  const updateConfigFnc = (params: { value: any; index: number; key: any }) => {
    const { value, index, key } = params;
    const cur = Object.assign({}, gridCofigArr[index]);
    if (cur[key] == value) return;
    let editGridList: any[] = gridEditForm.getFieldValue('gridList');
    if (key === 'width' || key === 'length') {
      editGridList = editGridList.map((item: any) => {
        const grid = Object.assign({}, item);
        grid[key] = value;
        grid.sizeStr = `${grid.width} × ${grid.length} × ${grid.height}`;
        return grid;
      });
    } else {
      editGridList = editGridList.map((item: any) => {
        const grid = Object.assign({}, item);
        if (grid.height == cur.height) {
          grid[key] = value;
          grid.sizeStr = `${grid.width} × ${grid.length} × ${grid.height}`;
        }
        return grid;
      });
    }
    setGridList([...editGridList]);
    let tempCofigArr = [...gridCofigArr];
    if (
      index == 0 &&
      gridCofigArr.length > 0 &&
      (key === 'width' || key === 'length')
    ) {
      tempCofigArr = gridCofigArr.map((ele: any) => {
        const item = { ...ele };
        item[key] = value;
        if (item.width && item.length && item.height) {
          item.sizeStr = `${item.width} × ${item.length} × ${item.height}`;
        } else {
          item.sizeStr = null;
        }
        return item;
      });
    } else {
      cur[key] = value;
      if (cur.width && cur.length && cur.height) {
        cur.sizeStr = `${cur.width} × ${cur.length} × ${cur.height}`;
      } else {
        cur.sizeStr = null;
      }
      tempCofigArr.splice(index, 1, cur);
    }
    gridCofigArr = lodash.cloneDeep([...tempCofigArr]);
    dispatch(setGridSizeListAction(gridCofigArr));
  };

  const previewClick = () => {
    const editGridList: any[] = gridEditForm.getFieldValue('gridList');
    const valid = validationGrid(
      editGridList,
      gridPositionValue,
      gridData.gridSizeList,
      basicForm.getFieldsValue().productType,
    );
    if (!valid.isValid) {
      message.warning(valid.msg);
      return false;
    }
    dispatch(setGridListAction(editGridList));
    return true;
  };

  return (
    <div style={{ padding: '10px' }}>
      <CommonEdit
        title={GridTitle[type]}
        breadCrumbConfig={boxGirdCrumbItems}
        onSubmitClick={confirm}
        onCancleClick={goBack}
        hideSubmit={type === PageType.READONLY}
        cancelTitle={type === PageType.READONLY ? '返回' : '取消'}
      >
        <BasicInfo
          form={basicForm}
          pageType={type}
          initValues={initValues}
          updateBaseInfoValue={updateBaseInfoValue}
          changeProductType={(val: any) => {
            setGridList([]);
            dispatch(
              setGridSizeListAction([{ width: '', length: '', height: '' }]),
            );
          }}
        />
        <GridList
          moduleTitle="格口配置"
          formRef={gridEditForm}
          disabled={
            type === PageType.READONLY ||
            (type === PageType.EDIT &&
              initValues.productType !== ProductType.VEHICLE)
          }
          gridList={gridList}
          previewClick={previewClick}
          viewType={type}
          isShowCarImg={type === PageType.READONLY}
          productionType={basicForm.getFieldsValue().productType}
        />
      </CommonEdit>
    </div>
  );
};

export default React.memo(GridManageEdit);
