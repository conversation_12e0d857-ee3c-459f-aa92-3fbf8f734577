/* eslint-disable react/display-name */
/* eslint-disable no-unused-vars */
import React from 'react';
export declare type GridModel = {
  id: number;
  gridNo: number;
  boardNo: number;
  side: string;
  lockNo: number;
  length: number;
  width: number;
  height: number;
  statusId: string;
  statusName: string;
};

// 格口编辑弹窗表单配置
export const gridEditModalFormConfiger: {
  name: string;
  label: string;
  length?: number;
  placeHolder: string;
  type: 'input' | 'select' | 'radio';
  rule?: 'thanOrEqual0' | 'morethan0' | 'withComma' | null;
  disabled?: boolean;
}[] = [
  {
    name: 'gridNo',
    label: '格口号',
    length: 2,
    placeHolder: '请输入格口号',
    type: 'input',
    rule: 'thanOrEqual0',
    disabled: true,
  },
  {
    name: 'side',
    label: '格口方位',
    length: 2,
    placeHolder: '请选择格口方位',
    type: 'select',
  },
  {
    name: 'lockNo',
    label: '锁号',
    placeHolder: '请输入锁号',
    type: 'input',
    rule: 'withComma',
    disabled: false,
  },
];

// 格口table的colums
export const gridTableColums: any[] = [
  {
    title: '格口号',
    className: 'drag-visible',
    align: 'center',
    dataIndex: 'gridNo',
    width: 80,
  },
  {
    title: '板卡号',
    width: 80,
    className: 'drag-visible',
    dataIndex: 'boardNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '方位',
    width: 80,
    dataIndex: 'side',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '锁号',
    width: 80,
    dataIndex: 'lockNo',
    align: 'center',
    ellipsis: true,
  },
  // { title: '格口宽度（mm）', width: 120, dataIndex: 'width', align: 'center', ellipsis: true },
  // { title: '格口高度（mm）', width: 120, dataIndex: 'height', align: 'center', ellipsis: true },
  // { title: '格口深度（mm）', width: 120, dataIndex: 'length', align: 'center', ellipsis: true },
  {
    title: '格口宽 × 深 × 高（mm）',
    width: 200,
    dataIndex: 'sizeStr',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '格口状态',
    width: 80,
    dataIndex: 'enable',
    align: 'center',
    ellipsis: true,
    render: (text: any, record: any) => {
      const status = record.enable === 1 ? true : false;
      return (
        <div style={{ color: status ? '#31C2A6' : '#D9001B' }}>
          {' '}
          {status ? '启用' : '停用'}{' '}
        </div>
      );
    },
  },
  {
    title: '格口内小格口',
    width: 150,
    dataIndex: 'pallet',
    align: 'center',
    ellipsis: true,
    render: (text: any) => text ?? '无',
  },
];

export const robotGridTableColums: any[] = [
  {
    title: '格口号',
    className: 'drag-visible',
    align: 'center',
    dataIndex: 'gridNo',
    width: 80,
  },
  // {
  //   title: '板卡号',
  //   width: 80,
  //   className: 'drag-visible',
  //   dataIndex: 'boardNo',
  //   align: 'center',
  //   ellipsis: true,
  // },
  // {
  //   title: '方位',
  //   width: 80,
  //   dataIndex: 'side',
  //   align: 'center',
  //   ellipsis: true,
  // },
  {
    title: '锁号',
    width: 80,
    dataIndex: 'lockNo',
    align: 'center',
    ellipsis: true,
  },
  // { title: '格口宽度（mm）', width: 120, dataIndex: 'width', align: 'center', ellipsis: true },
  // { title: '格口高度（mm）', width: 120, dataIndex: 'height', align: 'center', ellipsis: true },
  // { title: '格口深度（mm）', width: 120, dataIndex: 'length', align: 'center', ellipsis: true },
  {
    title: '格口宽 × 深 × 高（mm）',
    width: 200,
    dataIndex: 'sizeStr',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '格口状态',
    width: 80,
    dataIndex: 'enable',
    align: 'center',
    ellipsis: true,
    render: (text: any, record: any) => {
      const status = record.enable === 1 ? true : false;
      return (
        <div style={{ color: status ? '#31C2A6' : '#D9001B' }}>
          {' '}
          {status ? '启用' : '停用'}{' '}
        </div>
      );
    },
  },
  // {
  //   title: '格口内小格口',
  //   width: 150,
  //   dataIndex: 'pallet',
  //   align: 'center',
  //   ellipsis: true,
  //   render: (text: any) => text ?? '无',
  // },
];
