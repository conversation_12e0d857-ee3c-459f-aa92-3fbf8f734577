import proj4 from 'proj4';
export function convex(p: any, n: any) {
  var j, k, z;
  var flag = 0;
  if (n < 3) {
    // console.log("不符合要求")
    return 0;
  }
  for (var i = 0; i < n; i++) {
    j = (i + 1) % n;
    k = (i + 2) % n;
    z = (p[j][0] - p[i][0]) * (p[k][1] - p[j][1]);
    z -= (p[j][1] - p[i][1]) * (p[k][0] - p[j][0]);
    if (z < 0) {
      flag |= 1;
    } else if (z > 0) {
      flag |= 2;
    }
    if (flag == 3) {
      // console.log("凹多边形，不符合要求")
      return -1; //CONCAVE
    }
  }
  if (flag != 0) {
    // console.log("凸多边形")
    return 1; //CONVEX
  } else {
    return 0;
  }
}

export const wgs842utm = (lon: any, lat: any, zoneNum: any) => {
  const utm = '+proj=utm +zone=' + zoneNum;
  const wgs84 = '+proj=longlat +ellps=WGS84 +datum=WGS84 +no_defs';
  return (window as any).proj4(wgs84, utm, [lon, lat]);
};

export const getZone = (lat: number, lon: number) => {
  return lon / 6.0 + 31;
};

export const utm2wgs84 = (lon: any, lat: any, zoneNum: number) => {
  const utm = '+proj=utm +zone=' + zoneNum;
  const wgs84 = '+proj=longlat +ellps=WGS84 +datum=WGS84 +no_defs';
  return (window as any).proj4(utm, wgs84, [lon, lat]);
};

/**
 * 已知某一点坐标，旋转角度，长度，求另一点坐标
 * @param originPoint
 * @param degree
 * @param len
 */
export function calculateCoordinatePoint(
  originPoint: { x: number; y: number },
  degree: number,
  len: number
) {
  var rotate = (degree - 90 + 360) % 360; //这里是因为一开始以y轴下方为0度的
  var point = {
    x: len,
    y: 0,
  };
  //计算某一点旋转后的坐标点，这里假设传入的点为原点
  var relativeOriginPoint = calculateRotate(point, rotate);
  //计算相对坐标系的坐标
  var points = calculateCoordinateRelativePoint(
    originPoint,
    relativeOriginPoint
  );
  return points;
}
/**
 * 计算某一点旋转后的坐标点
 * @param point
 * @param degree
 */
export function calculateRotate(
  point: { x: number; y: number },
  degree: number
) {
  var x =
    point.x * Math.cos((degree * Math.PI) / 180) +
    point.y * Math.sin((degree * Math.PI) / 180);
  var y =
    -point.x * Math.sin((degree * Math.PI) / 180) +
    point.y * Math.cos((degree * Math.PI) / 180);
  var relativeOriginPoint = {
    x: Math.round(x * 1000000000) / 1000000000,
    y: Math.round(y * 1000000000) / 1000000000,
  };
  return relativeOriginPoint;
}

/**
 * 计算相对坐标系的坐标
 */
export function calculateCoordinateRelativePoint(
  origin: { x: number; y: number },
  relativeOriginPoint: { x: number; y: number }
) {
  var x = relativeOriginPoint.x + origin.x;
  var y = relativeOriginPoint.y + origin.y;
  var points = {
    x: Math.round(x * 1000000000) / 1000000000,
    y: Math.round(y * 1000000000) / 1000000000,
  };
  return points;
}

export const calculateRectangleCoordinates = ({
  centerPoint,
  head,
  backToCenterLen,
  frontToCenterLen,
  width,
}: any) => {
  const b2cActan = (Math.atan(width / 2 / backToCenterLen) * 180) / Math.PI;
  const b2cLen = Math.sqrt(
    Math.pow(width / 2, 2) + Math.pow(backToCenterLen, 2)
  );
  const f2cActan = (Math.atan(width / 2 / frontToCenterLen) * 180) / Math.PI;
  const f2cLen = Math.sqrt(
    Math.pow(width / 2, 2) + Math.pow(frontToCenterLen, 2)
  );
  const leftTopPoint = getLonAndLat(
    centerPoint.x,
    centerPoint.y,
    head + b2cActan,
    b2cLen
  );

  const rightTopPoint = getLonAndLat(
    centerPoint.x,
    centerPoint.y,
    head - b2cActan,
    b2cLen
  );
  const bottomLeftPoint = getLonAndLat(
    centerPoint.x,
    centerPoint.y,
    180 - f2cActan,
    f2cLen
  );

  const bottomRightPoint = getLonAndLat(
    centerPoint.x,
    centerPoint.y,
    180 + head + f2cActan,
    f2cLen
  );
  return [leftTopPoint, rightTopPoint, bottomLeftPoint, bottomRightPoint];
};

/**
 * 度换成弧度
 * @param  {Float} d  度
 * @return {Float}   弧度
 */
function rad(d) {
  return (d * Math.PI) / 180.0;
}
/**
 * 弧度换成度
 * @param  {Float} x 弧度
 * @return {Float}   度
 */
function deg(x) {
  return (x * 180) / Math.PI;
}
/**
 *
 * @param {*} lng 经度 113.3960698
 * @param {*} lat 纬度 22.941386
 * @param {*} brng 方位角 45   ---- 正北方：000°或360°  正东方：090° 正南方：180°  正西方：270°
 * @param {*} dist 距离 9000
 *
 */
export function getLonAndLat(lng, lat, brng, dist) {
  //大地坐标系资料WGS-84 长半径a=6378137 短半径b=6356752.3142 扁率f=1/298.2572236
  var a = 6378137;
  var b = 6356752.3142;
  var f = 1 / 298.257223563;

  var lon1 = lng * 1;
  var lat1 = lat * 1;
  var s = dist;
  var alpha1 = rad(brng);
  var sinAlpha1 = Math.sin(alpha1);
  var cosAlpha1 = Math.cos(alpha1);

  var tanU1 = (1 - f) * Math.tan(rad(lat1));
  var cosU1 = 1 / Math.sqrt(1 + tanU1 * tanU1),
    sinU1 = tanU1 * cosU1;
  var sigma1 = Math.atan2(tanU1, cosAlpha1);
  var sinAlpha = cosU1 * sinAlpha1;
  var cosSqAlpha = 1 - sinAlpha * sinAlpha;
  var uSq = (cosSqAlpha * (a * a - b * b)) / (b * b);
  var A = 1 + (uSq / 16384) * (4096 + uSq * (-768 + uSq * (320 - 175 * uSq)));
  var B = (uSq / 1024) * (256 + uSq * (-128 + uSq * (74 - 47 * uSq)));

  var sigma = s / (b * A),
    sigmaP = 2 * Math.PI;
  while (Math.abs(sigma - sigmaP) > 1e-12) {
    var cos2SigmaM = Math.cos(2 * sigma1 + sigma);
    var sinSigma = Math.sin(sigma);
    var cosSigma = Math.cos(sigma);
    var deltaSigma =
      B *
      sinSigma *
      (cos2SigmaM +
        (B / 4) *
          (cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM) -
            (B / 6) *
              cos2SigmaM *
              (-3 + 4 * sinSigma * sinSigma) *
              (-3 + 4 * cos2SigmaM * cos2SigmaM)));
    sigmaP = sigma;
    sigma = s / (b * A) + deltaSigma;
  }

  var tmp = sinU1 * sinSigma - cosU1 * cosSigma * cosAlpha1;
  var lat2 = Math.atan2(
    sinU1 * cosSigma + cosU1 * sinSigma * cosAlpha1,
    (1 - f) * Math.sqrt(sinAlpha * sinAlpha + tmp * tmp)
  );
  var lambda = Math.atan2(
    sinSigma * sinAlpha1,
    cosU1 * cosSigma - sinU1 * sinSigma * cosAlpha1
  );
  var C = (f / 16) * cosSqAlpha * (4 + f * (4 - 3 * cosSqAlpha));
  var L =
    lambda -
    (1 - C) *
      f *
      sinAlpha *
      (sigma +
        C *
          sinSigma *
          (cos2SigmaM + C * cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)));

  var revAz = Math.atan2(sinAlpha, -tmp); // final bearing

  var lngLatObj = { lon: lon1 + deg(L), lat: deg(lat2) };
  return lngLatObj;
}
