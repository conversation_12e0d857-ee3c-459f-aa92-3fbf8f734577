import React, { ReactNode } from 'react';
import { Popconfirm, Button, ButtonProps } from 'antd';
import { AnyFunc } from '@/global';
const PopconfirmBtn = (props: {
  btnProps: ButtonProps;
  btnText: ReactNode;
  popMsg: ReactNode;
  onConfirm: AnyFunc;
  okText?: ReactNode;
  cancelText?: ReactNode;
  onCancel?: AnyFunc;
  overlayStyle?: object;
}) => {
  const {
    btnProps,
    btnText,
    popMsg,
    onConfirm,
    okText,
    cancelText,
    overlayStyle,
  } = props;
  return (
    <>
      <Popconfirm
        title={popMsg}
        placement="left"
        onConfirm={onConfirm}
        okText={okText ?? '确定'}
        cancelText={cancelText ?? '取消'}
        overlayStyle={overlayStyle ?? { maxWidth: 800 }}
      >
        <Button {...btnProps}>{btnText}</Button>
      </Popconfirm>
    </>
  );
};
export default React.memo(PopconfirmBtn);
