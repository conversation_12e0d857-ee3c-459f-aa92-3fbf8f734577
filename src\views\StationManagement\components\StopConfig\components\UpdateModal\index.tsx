import { Modal } from 'antd';
import React from 'react';
interface Props {
  statusId: string;
  stopName: string;
  modalVisible: boolean;
  submiting?: boolean;
  handleOk: () => void;
  handleCancel: () => void;
}

const UpdateStopStatusModal = (props: Props) => {
  const { stopName, statusId, modalVisible } = props;
  return (
    <Modal
      maskClosable={false}
      keyboard={false}
      title={`停靠点${statusId === '1' ? '启用' : '停用'}`}
      open={modalVisible}
      onOk={props.handleOk}
      confirmLoading={props.submiting ?? false}
      onCancel={props.handleCancel}
    >
      {statusId === '0' ? (
        <span style={{ textAlign: 'center', fontSize: '15px' }}>
          <p style={{ fontWeight: 'bold' }}>
            确认要停用【{stopName}】停靠点吗？
          </p>
          <p>停用停靠点，会自动解绑该停靠点所关联的车辆。</p>
          <p>请确认该停靠点已真正无法使用后，再进行停用。</p>
        </span>
      ) : (
        <p>
          停靠点{stopName}启用后，该停靠点关联的所有业务数据将应用于业务运营
        </p>
      )}
    </Modal>
  );
};
export default UpdateStopStatusModal;
