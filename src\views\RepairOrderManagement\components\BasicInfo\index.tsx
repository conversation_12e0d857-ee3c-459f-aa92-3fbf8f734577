import { Image, Row, Col } from 'antd';
import React, { useState } from 'react';
import './index.scss';
interface Props {
  title: string;
  basicInfo: any;
}
interface Iconfig {
  [key: string]: any;
}
interface Iinfo {
  [key: string]: any;
}
const BasicInfo = (props: Props) => {
  const { title, basicInfo } = props;
  const [config, setConfig] = useState<Iconfig>({
    number: '维修单号',
    deviceName: '车牌号',
    serialNo: '车架号',
    stationBaseInfo: '所属站点',
    requireHardwareTypeNames: '维修位置',
    // title: '故障标题',
    isInfluenceOperation: '问题影响',
    description: '描述',
    pictureList: '添加图片',
    video: '添加视频',
    reportErp: '联系人',
    reportPhone: '联系人手机号',
    reportEmail: '联系人邮箱',
  });

  return (
    <div className="hardwareBasic-info">
      <div className="hardwareBasicInfo-title">
        <span>{title}</span>
      </div>
      <div className="hardwareBasicInfo-content">
        {Object.keys(basicInfo)?.map((item: string) => {
          switch (item) {
            case 'pictureList':
              return (
                <Row key={item}>
                  <Col span={3}>
                    <div className="pic">{`${config[item]} :`}</div>
                  </Col>
                  {!basicInfo.pictureList ? (
                    <Col>
                      <p>-</p>
                    </Col>
                  ) : (
                    basicInfo.pictureList?.map((img: string) => {
                      return (
                        <Col key={img}>
                          <Image
                            src={img}
                            style={{ height: '200px', width: '200px' }}
                          />
                        </Col>
                      );
                    })
                  )}
                </Row>
              );
              break;
            case 'video':
              return (
                <Row key={item}>
                  <Col span={3}>
                    <div className="video">{`${config[item]} :`}</div>
                  </Col>
                  {!basicInfo.video ? (
                    <Col>
                      <p>-</p>
                    </Col>
                  ) : (
                    <Col>
                      <video
                        width="320"
                        height="240"
                        controls
                        controlsList={'nodownload'}
                      >
                        <source src={basicInfo.video} type="video/mp4" />
                        <source src={basicInfo.video} type="video/ogg" />
                        <source src={basicInfo.video} type="video/flv" />
                        <source src={basicInfo.video} type="video/avi" />
                        <source src={basicInfo.video} type="video/wmv" />
                        <source src={basicInfo.video} type="video/rmvb" />
                        <source src={basicInfo.video} type="video/mov" />
                      </video>
                    </Col>
                  )}
                </Row>
              );
              break;
            default:
              return (
                <Row key={item} style={{ minHeight: '36px' }}>
                  <Col span={3}>
                    <div className="config">{`${config[item]} :`}</div>
                  </Col>
                  <Col span={20}>
                    <div>{basicInfo[item] || '-'}</div>
                  </Col>
                </Row>
              );
          }
        })}
      </div>
    </div>
  );
};

export default React.memo(BasicInfo);
