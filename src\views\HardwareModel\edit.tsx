import { Form, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { CommonEdit, CommonForm, FieldItem, FormConfig } from '@/components';
import { formatLocation } from '@/utils/utils';
import { HardwareModalApi, CommonApi } from '@/fetch/business';
import { useEditPageData } from '@/utils/hooks';
import EditForm from './component/EditForm';
import { HardwareModalTitle, PageType } from '@/utils/EditTitle';
import { HttpStatusCode } from '@/fetch/core/constant';

const Edit = () => {
  const fetchApi = new HardwareModalApi();
  const location = formatLocation(window.location.search);
  const type = location.type;
  const navigator = useNavigate();
  const [editForm] = Form.useForm();

  const detailData = useEditPageData(
    location.id,
    fetchApi.fetchHardwearModalDetail,
  );
  const confirm = async () => {
    let data = await editForm.validateFields();
    if (type === PageType.EDIT) {
      data = {
        ...data,
        id: location.id,
      };
    }
    const res = await fetchApi.submitHardwareModal({
      type: type,
      requestBody: data,
    });
    if (res.code === HttpStatusCode.Success) {
      message.success(res.message);
      navigator(-1);
    } else {
      message.error(res.message);
    }
  };
  const goBack = () => {
    navigator(-1);
  };
  const title = HardwareModalTitle[type] || '';
  const hardWareModelBreadCrumbItems = [
    {
      title: '硬件型号管理',
      route: '',
    },
    {
      title: title,
      route: '',
    },
  ];
  return (
    <>
      <CommonEdit
        title={title}
        breadCrumbConfig={hardWareModelBreadCrumbItems}
        onSubmitClick={confirm}
        onCancleClick={goBack}
        hideSubmit={type === PageType.READONLY}
        cancelTitle={type === PageType.READONLY ? '返回' : '取消'}
      >
        <EditForm
          form={editForm}
          type={type}
          initValues={type === PageType.ADD ? { enable: 1 } : detailData}
        />
      </CommonEdit>
    </>
  );
};

export default Edit;
