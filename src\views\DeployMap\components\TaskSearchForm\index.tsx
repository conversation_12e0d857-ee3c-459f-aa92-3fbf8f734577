import React, { useState, useEffect, useRef, useImperativeHandle } from 'react';
import { CommonForm } from '@jd/x-coreui';
import { message, Pagination, Spin } from 'antd';
import Drawer from '@/components/Drawer';
import './index.scss';
import formConfig from './formConfig';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import TaskCard from '../TaskCard';
import { Map as OlMap } from 'ol';
import { LayerIdEnum } from '@/utils/constant';
import VectorSource from 'ol/source/Vector';
import MapOperate from '../../utils/mapOperate';
import { isEmpty, cloneDeep } from 'lodash';
import DeployMapApi, { LatLng } from '@/fetch/business/deployMap';
import { HttpStatusCode } from '@/fetch/core/constant';
import * as turf from '@turf/turf';
interface SearchParams {
  taskName?: string;
  taskStatus?: string;
  stationId?: number | null;
  creatorUsername?: string;
}

interface TaskSearchFormProps {
  map: OlMap;
  selectedTaskId: number | null;
  setSelectedTaskId: (taskId: number | null) => void;
  selectedTaskIdRef: React.MutableRefObject<number | null>;
  taskFormRef: React.RefObject<any>;
  taskList: any[];
  loading?: boolean;
  onSearch?: (values: SearchParams) => void;
  stationList: any[];
  setIsEditing: (isEditing: boolean) => void;
  isEditingRef: React.MutableRefObject<boolean>;
  isInit: boolean;
  getTaskList: () => void;
  removeTaskDetailOverlay: () => void;
  createTaskDetailOverlay: (taskId: number, latlng: LatLng) => void;
  setLoading: (loading: boolean) => void;
}

export const DrawerBtn = ({
  type,
  onClick,
}: {
  type: 'open' | 'close';
  onClick: () => void;
}) => {
  return (
    <div className="drawer-expand-icon" onClick={onClick}>
      <span className="expand-icon-text">
        {type === 'open' ? (
          <LeftOutlined style={{ color: '#fff', fontSize: '14px' }} {...{}} />
        ) : (
          <RightOutlined style={{ color: '#fff', fontSize: '14px' }} {...{}} />
        )}
      </span>
    </div>
  );
};

const TaskSearchForm: React.FC<TaskSearchFormProps> = ({
  map,
  selectedTaskId,
  setSelectedTaskId,
  selectedTaskIdRef,
  taskFormRef,
  taskList,
  loading = false,
  setLoading,
  onSearch,
  stationList,
  setIsEditing,
  isEditingRef,
  isInit,
  getTaskList,
  removeTaskDetailOverlay,
  createTaskDetailOverlay,
}) => {
  const commonFormRef = useRef<any>(null);
  const mapOperateRef = useRef<MapOperate | null>(null);
  const [searchFormConfig, setSearchFormConfig] = useState<any>(formConfig);
  const initSearchCondition = {
    taskName: '',
    taskStatus: undefined,
    stationId: null,
    creatorUsername: '',
  };

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: taskList?.length || 0,
  });

  // 当前页显示的任务列表
  const [displayList, setDisplayList] = useState<any[]>([]);

  useEffect(() => {
    if (map) {
      mapOperateRef.current = new MapOperate(map);
    }
  }, [map]);

  // 初始化数据
  useEffect(() => {
    getTaskStatusList();
  }, []);

  // 更新分页数据
  useEffect(() => {
    const { current, pageSize } = pagination;
    const start = (current - 1) * pageSize;
    const end = start + pageSize;
    if (!isEmpty(taskList)) {
      setDisplayList(taskList.slice(start, end));
      setPagination((prev) => ({ ...prev, total: taskList.length }));
    } else {
      setDisplayList([]);
      setPagination((prev) => ({ ...prev, total: 0 }));
    }
  }, [pagination.current, taskList]);

  useEffect(() => {
    const newConfig = cloneDeep(searchFormConfig);
    if (!isEmpty(stationList)) {
      newConfig.fields.find(
        (item: any) => item.fieldName === 'stationId',
      ).options = stationList.map((item: any) => ({
        label: item.pointName,
        value: item.pointId,
      }));
    } else {
      newConfig.fields.find(
        (item: any) => item.fieldName === 'stationId',
      ).options = [];
    }
    setSearchFormConfig(newConfig);
  }, [stationList]);

  const handleOpenOrClose = (type: 'open' | 'close') => {
    if (commonFormRef.current) {
      commonFormRef.current.style.display = type === 'open' ? 'block' : 'none';
    }
  };

  // 获取任务状态列表
  const getTaskStatusList = async () => {
    try {
      const res = await DeployMapApi.getEnumMapping();
      if (res.code === HttpStatusCode.Success) {
        if (!isEmpty(res.data)) {
          const taskStatus = res.data.find(
            (item: any) => item.enumType === 'TASK_STATUS',
          );
          if (!isEmpty(taskStatus)) {
            const newConfig = cloneDeep(searchFormConfig);
            newConfig.fields.find(
              (item: any) => item.fieldName === 'taskStatus',
            ).options = taskStatus.enumList.map((item: any) => ({
              label: item.enumName,
              value: item.enumValue,
            }));
            setSearchFormConfig(newConfig);
          }
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  // 处理搜索
  const handleSearch = (values: SearchParams) => {
    if (onSearch) {
      onSearch(values);
    }
    setPagination((prev) => ({
      ...prev,
      current: 1,
    }));
  };

  // 处理重置
  const handleReset = () => {
    if (onSearch) {
      onSearch(initSearchCondition);
    }
    setPagination((prev) => ({
      ...prev,
      current: 1,
    }));
  };

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setPagination((prev) => ({
      ...prev,
      current: page,
    }));
  };

  // 高亮地图上的规划路线
  const highlightRoute = (taskId: number) => {
    const routeLayer = map
      ?.getAllLayers()
      .find(
        (layer) => layer.get('layerId') === LayerIdEnum.PLANNED_ROUTE_LAYER,
      );
    const vectorSource = routeLayer?.getSource() as VectorSource;
    const features = vectorSource?.getFeatures() || [];
    const route = features.find((feature) => feature.get('taskId') === taskId);
    const color = route?.get('color');
    if (route) {
      mapOperateRef.current?.highlightRoute(route, color);
    }
  };

  // 取消高亮
  const cancelHighlight = (taskId: number) => {
    const routeLayer = map
      ?.getAllLayers()
      .find(
        (layer) => layer.get('layerId') === LayerIdEnum.PLANNED_ROUTE_LAYER,
      );
    const vectorSource = routeLayer?.getSource() as VectorSource;
    const features = vectorSource?.getFeatures() || [];
    const route = features.find((feature) => feature.get('taskId') === taskId);
    const color = route?.get('color');
    if (route) {
      mapOperateRef.current?.cancelHighlight(route, color);
    }
  };

  // 选中任务
  const handleSelectTask = (taskId: number) => {
    if (selectedTaskId === taskId) {
      cancelHighlight(taskId);
      setSelectedTaskId(null);
      selectedTaskIdRef.current = null;
      removeTaskDetailOverlay();
    } else {
      if (selectedTaskId) {
        cancelHighlight(selectedTaskId);
        removeTaskDetailOverlay();
      }
      // 检查当前页是否包含选中的任务
      const isTaskInCurrentPage = displayList.some(
        (task) => task.taskId === taskId,
      );

      // 如果当前页面没有这个任务，需要跳转到对应页面
      if (!isTaskInCurrentPage) {
        // 在完整的taskList中找到任务的索引
        const taskIndex = taskList.findIndex((task) => task.taskId === taskId);
        if (taskIndex !== -1) {
          // 计算任务所在的页码
          const targetPage = Math.floor(taskIndex / pagination.pageSize) + 1;
          // 更新分页
          setPagination((prev) => ({
            ...prev,
            current: targetPage,
          }));
        }
      }
      const task = taskList.find((task) => task.taskId === taskId);
      if (!isEmpty(task.taskRouteList)) {
        const coordinates = task.taskRouteList.map((item: LatLng) => [
          item.longitude,
          item.latitude,
        ]);
        const turfFeature = turf.points(coordinates);
        const center = turf.center(turfFeature).geometry.coordinates;
        createTaskDetailOverlay(taskId, {
          latitude: center[1],
          longitude: center[0],
        });
      } else {
        removeTaskDetailOverlay();
      }

      setSelectedTaskId(taskId);
      selectedTaskIdRef.current = taskId;
      highlightRoute(taskId);
    }
  };

  useImperativeHandle(taskFormRef, () => ({
    handleSelectTask,
  }));

  const startRouteTask = async (taskId: number) => {
    try {
      const res = await DeployMapApi.startRouteTask(taskId);
      if (res.code !== HttpStatusCode.Success) {
        message.error(res.message || '开始路线任务失败');
        return;
      } else {
        setSelectedTaskId(taskId);
        setIsEditing(true);
        isEditingRef.current = true;

        // 使用延迟重试机制确保线路图层可见性正确设置
        mapOperateRef.current?.changeLayerVisible(
          LayerIdEnum.PLANNED_ROUTE_LAYER,
          false,
          0, // 初始重试次数
          10, // 增加最大重试次数
          200, // 增加重试延迟
        );

        removeTaskDetailOverlay();
      }
    } catch (err) {
      console.error(err);
      message.error('系统异常，请稍后重试');
    }
  };

  const deleteTask = async (taskId: number, taskStatus: string) => {
    try {
      const res = await DeployMapApi.deleteTask(taskId, taskStatus);
      if (res.code === HttpStatusCode.Success) {
        message.success('删除任务成功');
        getTaskList();
      } else {
        message.error(res.message || '删除任务失败');
      }
    } catch (err) {
      console.error(err);
      message.error('系统异常，请稍后重试');
    }
  };

  const revokeTask = async (taskId: number) => {
    try {
      setLoading(true);
      const res = await DeployMapApi.revokeTask(taskId);
      if (res.code === HttpStatusCode.Success) {
        startRouteTask(taskId);
      } else {
        message.error(res.message || '编辑任务失败');
        setLoading(false);
        getTaskList();
      }
    } catch (err) {
      console.error(err);
      message.error('系统异常，请稍后重试');
      setLoading(false);
      getTaskList();
    }
  };

  return (
    <Drawer
      className="task-search-form"
      defaultOpened={true}
      openWidth="428px"
      openBtn={
        <DrawerBtn type="open" onClick={() => handleOpenOrClose('open')} />
      }
      closeBtn={
        <DrawerBtn type="close" onClick={() => handleOpenOrClose('close')} />
      }
    >
      <div className="drawer-content" ref={commonFormRef}>
        <div className="task-search-form-header">
          <div className="task-search-form-header-title">线路</div>
        </div>

        <div className="task-search-container">
          {isInit && (
            <CommonForm
              className="task-search-form-content"
              formConfig={searchFormConfig}
              formType="search"
              defaultValue={initSearchCondition}
              onSearchClick={handleSearch}
              onResetClick={handleReset}
              labelAlign="left"
              colon={false}
            />
          )}
          {!isEmpty(taskList) ? (
            <>
              <Spin spinning={loading}>
                <div className="task-list-container">
                  {displayList.map((item) => (
                    <TaskCard
                      key={item.taskId}
                      taskData={item}
                      selectedTaskId={selectedTaskId}
                      handleSelectTask={handleSelectTask}
                      onEdit={() => {
                        startRouteTask(item.taskId);
                      }}
                      onDelete={() => {
                        deleteTask(item.taskId, item.taskStatus);
                      }}
                      onRevokeTask={() => {
                        revokeTask(item.taskId);
                      }}
                    />
                  ))}
                </div>
              </Spin>
              {taskList.length > 10 && (
                <div className="pagination-container">
                  <Pagination
                    current={pagination.current}
                    total={pagination.total}
                    pageSize={pagination.pageSize}
                    onChange={handlePageChange}
                    showSizeChanger={false}
                    size="small"
                    simple
                  />
                </div>
              )}
            </>
          ) : (
            <div className="no-task-list">暂无任务</div>
          )}
        </div>
      </div>
    </Drawer>
  );
};

export default TaskSearchForm;
