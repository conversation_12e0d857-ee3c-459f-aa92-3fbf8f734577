/* eslint-disable react/prop-types */
/* eslint-disable react/display-name */
/* eslint-disable no-unused-vars */
import React, { useState, useEffect, useRef } from 'react';
import { ActionType, ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import { Input, message, Form, Space } from 'antd';
import { CustomButton } from '@/components';

type ValidName = 'repeat' | 'empty';

type DataSourceType = {
  id: React.Key;
  name: any;
  newAdd?: boolean;
  invalid?: ValidName;
};

const LittleGridTable = ({
  onChange,
  initValue,
}: {
  onChange: Function;
  initValue: any;
}) => {
  const [editableKeys, setEditableRowKeys] = useState<any>(
    initValue?.map((e: any) => e.id) || [],
  );
  const [dataSource, setDataSource] = useState<DataSourceType[]>(
    initValue || [],
  );
  const actionRef = useRef<ActionType>();
  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '序号',
      align: 'center',
      ellipsis: true,
      width: 140,
      editable: false,
      render: (text: any, record: any, index: any) => index + 1,
    },
    {
      title: '小格口名称',
      align: 'center',
      key: 'name',
      dataIndex: 'name',
      renderFormItem: (_, { recordKey }) => {
        const currentItem = dataSource?.filter(
          (Item) => Item.id === Number(recordKey),
        )[0];
        const { invalid } = currentItem;
        return <RowEditFormItem invalid={invalid} />;
      },
    },
    {
      title: '操作',
      align: 'center',
      valueType: 'option',
      width: 150,
      render: () => {
        return null;
      },
    },
  ];

  useEffect(() => {
    onChange && onChange(dataSource);
  }, [dataSource]);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <CustomButton
        title="新增小格口"
        otherCSSProperties={{
          marginLeft: '20px',
          marginBottom: '20px',
          width: '100px',
        }}
        onSubmitClick={() => {
          if (
            dataSource.some(
              (item: any) =>
                item.invalid === 'repeat' || item.invalid === 'empty',
            )
          )
            return;

          actionRef.current?.addEditRecord?.(
            {
              id: Date.now(),
              name: null,
              newAdd: true,
              invalid: 'empty',
            },
            { newRecordType: 'dataSource' },
          );
        }}
      />
      <EditableProTable<DataSourceType>
        bordered
        actionRef={actionRef}
        columns={columns}
        rowKey={(record) => record.id}
        value={dataSource}
        recordCreatorProps={false}
        editable={{
          type: 'multiple',
          editableKeys,
          deletePopconfirmMessage: <div>是否确认删除此小格口？</div>,
          actionRender: (row, config, defaultDoms) => {
            return [defaultDoms.delete];
          },
          onValuesChange: async (record, recordList) => {
            if (!record) {
              const filteredList = recordList.filter(
                (item) =>
                  item.name === null ||
                  `${item.name}`.length <= 0 ||
                  item.invalid !== undefined,
              );
              if (filteredList.length > 1) {
                return;
              }
              setDataSource([...recordList]);
            } else {
              if (
                record.name !== undefined &&
                record.name !== null &&
                `${record.name}`.length > 0
              ) {
                const repeatList = dataSource.filter(
                  (ele) => ele.id !== record.id && ele.name === record.name,
                );
                if (repeatList.length > 0) {
                  record.invalid = 'repeat';
                } else {
                  record.invalid = undefined;
                  dataSource.forEach((ele) => (ele.invalid = undefined));
                }
              } else {
                record.invalid = 'empty';
              }
              setDataSource([
                ...dataSource.map((ele) => {
                  if (ele.id === record.id) {
                    return record;
                  }
                  return ele;
                }),
              ]);
            }
          },
          onChange: setEditableRowKeys,
        }}
      />
    </div>
  );
};

const RowEditFormItem: React.FC<{
  value?: any;
  onChange?: (value: any) => void;
  invalid?: ValidName;
}> = ({ value, onChange, invalid }) => {
  const [inputValue, setInputValue] = useState<any>(value);
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const reg = /^[\u4E00-\u9FA5A-Za-z0-9]{0,10}$/;
    if (reg.test(value)) {
      setInputValue(value);
    } else {
      setInputValue(null);
    }
  };
  const handleInputConfirm = () => {
    let value: string = `${inputValue || ''}`;
    if (value.length > 1) {
      value = value.substring(0, 1);
    }
    setInputValue(value);
    onChange?.(value);
  };
  const errorMsg = () => {
    let errorMsg;
    if (invalid === 'empty') {
      errorMsg = '请输入小格口名称';
    } else if (invalid === 'repeat') {
      errorMsg = '名称不能重复';
    }
    return errorMsg;
  };
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
      }}
    >
      <Input
        placeholder="请输入小格口名称"
        style={{ border: `1px solid ${invalid ? 'red' : '#ddd'}` }}
        value={inputValue}
        maxLength={1}
        onChange={handleInputChange}
        onBlur={handleInputConfirm}
        onPressEnter={handleInputConfirm}
      />
      <div style={{ color: 'red' }}>{errorMsg()}</div>
    </div>
  );
};

export default LittleGridTable;
