import { getPermissionBtn } from '@/utils/utils';
import { Button } from 'antd';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import './index.scss';

interface Props {
  title: string;
  sourceCode?: string;
  btnType?: 'primary' | 'default' | 'textBtn';
  className?: string;
  otherStyle?: object;
  loading?: boolean;
  clickCallBack: Function;
}
const PerButton = (props: Props) => {
  const {
    title,
    sourceCode,
    btnType,
    className,
    otherStyle,
    clickCallBack,
    loading,
  } = props;
  const state = useSelector((state: any) => {
    return state.commonReducer;
  });

  const [usable, setUsable] = useState<boolean>(true);
  useEffect(() => {
    // setUsable(getPermissionBtn(sourceCode));
  }, [JSON.stringify(state)]);

  return (
    <>
      {btnType !== 'textBtn'
        ? usable && (
            <Button
              type={btnType ?? 'primary'}
              key={sourceCode}
              className={className ?? 'pre-btn'}
              style={{ ...otherStyle }}
              loading={loading}
              onClick={() => clickCallBack()}
            >
              {title}
            </Button>
          )
        : usable && (
            <a
              className={className ?? 'pre-text-btn'}
              onClick={() => clickCallBack()}
              key={sourceCode}
            >
              {title}
            </a>
          )}
    </>
  );
};

export default React.memo(PerButton);
