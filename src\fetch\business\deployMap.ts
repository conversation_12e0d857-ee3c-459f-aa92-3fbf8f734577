import { ProductType } from '@/utils/enum';
import { request } from '../core';

export interface LatLng {
  latitude: number;
  longitude: number;
}

export interface RouteTaskRequest {
  taskName: string;
  stationId: number;
  routePlanType?: string;
  preciseType?: string;
  totalMileage: number;
  taskRouteColor?: string;
  taskRouteList: LatLng[];
  roadNameList?: string[];
}

export type RouteListRequest = {
  elementType?: any;
  elementId?: string | null;
  taskName?: string | null;
  stationId?: number | null;
  creatorUsername?: string | null;
  taskStatus?: string | null;
  latitude?: number | string | null;
  longitude?: number | string | null;
};

export class DeployMap {
  private headers: Record<string, string>;
  private absoluteURL: string;
  constructor() {
    this.headers = {
      'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
    };
    this.absoluteURL = `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}`;
  }

  async getStationList() {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/station_base/get_station_address_list',
      body: { stationProductType: ProductType.VEHICLE, enable: 1 },
    };
    return request(options);
  }

  async getEnumMapping() {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.absoluteURL}/deployment/exploration/getEnumMapping`,
      headers: this.headers,
    };
    return request(options);
  }

  /**
   * 模糊搜索元素
   * @param elementName 元素名称
   * @returns 模糊搜索结果的Promise
   */
  async fuzzySearch(elementName: string) {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.absoluteURL}/deployment/exploration/elementFuzzySearch`,
      body: { elementName },
      headers: this.headers,
    };
    return request(options);
  }

  /**
   * 获取任务路线列表
   * @param params 请求参数
   * @returns 任务路线列表的Promise
   */
  async getTaskRouteList(params: RouteListRequest) {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.absoluteURL}/deployment/exploration/getTaskRouteList`,
      body: params,
      headers: this.headers,
    };
    return request(options);
  }

  /**
   * 获取任务详情
   * @param taskId 任务ID
   * @returns 任务详情的Promise
   */
  async getTaskDetail(taskId: number) {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.absoluteURL}/deployment/exploration/getTaskDetail`,
      body: { taskId },
      headers: this.headers,
    };
    return request(options);
  }

  /**
   * 创建任务路线
   * @param params 请求参数
   * @returns 创建任务路线的Promise
   */
  async createTaskRoute(params: RouteTaskRequest) {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.absoluteURL}/deployment/exploration/createTaskRoute`,
      body: params,
      headers: this.headers,
    };
    return request(options);
  }

  /**
   * 编辑任务路线
   * @param params 请求参数
   * @returns 编辑任务路线的Promise
   */
  async modifyTaskRoute(params: { taskId: number } & RouteTaskRequest) {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.absoluteURL}/deployment/exploration/modifyTaskRoute`,
      body: params,
      headers: this.headers,
    };
    return request(options);
  }

  /**
   * 删除指定任务
   * @param taskId 任务ID
   * @returns 删除请求的Promise
   */
  async deleteTask(taskId: number, taskStatus: string) {
    const requestOptions: RequestOptions = {
      absoluteURL: `${this.absoluteURL}/deployment/exploration/deleteTask`,
      method: 'POST',
      body: { taskId, taskStatus },
      headers: this.headers,
    };
    return request(requestOptions);
  }

  /**
   * 开始路线任务
   * @param taskId 任务ID
   * @returns Promise 请求结果
   */
  public async startRouteTask(taskId: number) {
    const requestOptions: RequestOptions = {
      absoluteURL: `${this.absoluteURL}/deployment/exploration/task/process-start`,
      method: 'POST',
      body: { taskId },
      headers: this.headers,
    };
    return request(requestOptions);
  }

  /**
   * 结束路线任务
   * @param taskId 任务ID
   * @returns Promise 请求结果
   */
  public async endRouteTask(taskId: number) {
    const requestOptions: RequestOptions = {
      absoluteURL: `${this.absoluteURL}/deployment/exploration/task/process-end`,
      method: 'POST',
      body: { taskId },
      headers: this.headers,
    };
    return request(requestOptions);
  }

  /**
   * 编辑任务
   * @param taskId 任务ID
   * @returns Promise 请求结果
   */
  public async revokeTask(taskId: number) {
    const requestOptions: RequestOptions = {
      absoluteURL: `${this.absoluteURL}/deployment/exploration/revokeTask`,
      method: 'POST',
      body: { taskId },
      headers: this.headers,
    };
    return request(requestOptions);
  }

  /**
   * 获取初始点位信息
   * @param params - 包含纬度和经度的参数对象
   * @returns 返回初始点位的Promise
   */
  public getInitialPoints(params: { latitude: number; longitude: number }) {
    const requestOptions: RequestOptions = {
      absoluteURL: `${this.absoluteURL}/deployment/exploration/route/get-initial-points`,
      method: 'POST',
      headers: this.headers,
      body: params,
    };
    return request(requestOptions);
  }

  /**
   * 获取用户图层显隐配置
   * @returns 用户图层显隐配置的Promise
   */
  public getUserLayerConfig(configTypeList: string[]) {
    const requestOptions: RequestOptions = {
      absoluteURL: `${this.absoluteURL}/k2/management/common/get_custom_filter_config`,
      method: 'POST',
      headers: this.headers,
      body: { configTypeList },
    };
    return request(requestOptions);
  }

  /**
   * 更新用户图层显隐配置
   * @param params - 包含图层配置的参数对象
   * @returns 更新用户图层显隐配置的Promise
   */
  public updateUserLayerConfig(params: any) {
    const requestOptions: RequestOptions = {
      absoluteURL: `${this.absoluteURL}/k2/management/common/save_custom_filter_config`,
      method: 'POST',
      headers: this.headers,
      body: params,
    };
    return request(requestOptions);
  }
}

export default new DeployMap();
