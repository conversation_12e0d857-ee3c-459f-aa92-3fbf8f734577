import React, { useRef, useState } from 'react';
import AbnormalDurationForm from './components/AbnormalDurationForm';
import { BreadCrumb } from '@/components';
import './addAbnormalDuration.scss';
import { Button, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { MaintainAbnormalDurationApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';

const AddAbnormalDuration = () => {
  const ref = useRef<any>(null);
  const fetchApi = new MaintainAbnormalDurationApi();
  const navigator = useNavigate();

  const handleSubmit = async () => {
    const formVal = await ref.current?.formRef?.validateFields();
    if (formVal) {
      ref.current?.checkInvalidVal();
      const list = ref.current?.list;
      const invalidData = list.filter(
        (v) => v.notOperationDuration == undefined,
      );
      if (invalidData.length > 0) {
        message.error('请输入影响运营时长');
        return;
      }
      const res = await fetchApi.createRecord({
        disabledRecordItems: list,
        deviceName: formVal.deviceName,
        recordMode: formVal.recordMode,
        breakdown: formVal.breakdown,
        disabledCauseKey: formVal.disabledCauseKey,
        stationBaseId: formVal.stationBaseId,
      });
      if (res.code === HttpStatusCode.Success) {
        message.success('提交成功');
        navigator('/app/maintainAbnormalDuration');
      } else {
        message.error(res.message);
      }
    }
  };

  return (
    <div className="add-abnormal-duration">
      <div className="bread-crumb">
        <BreadCrumb
          items={[
            {
              title: '车辆异常时长维护',
              route: '',
            },
            {
              title: '录入',
              route: '',
            },
          ]}
        />
      </div>
      <div className="content">
        <h3>录入车辆异常时长</h3>
        <AbnormalDurationForm type="add" ref={ref} />
        <div className="btns">
          <Button onClick={() => ref.current?.fillNotOperateTime()}>
            一键填报
          </Button>
          <Button type="primary" onClick={() => handleSubmit()}>
            保存提交
          </Button>
          <Button
            onClick={() => {
              navigator('/app/maintainAbnormalDuration');
            }}
          >
            返回
          </Button>
        </div>
      </div>
    </div>
  );
};

export default React.memo(AddAbnormalDuration);
