import { Form, Input, Select, Modal, Checkbox } from 'antd';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { CommonEdit, EditModuleTitle } from '@/components';
import { VehicleInstructionApi } from '@/fetch/business';
import { basicInfo } from './utils/columns';
import { HttpStatusCode } from '@/fetch/core/constant';
import { useCommonDropDown } from '@/utils/hooks';
import { dropDownListKey, dropDownKey } from '@/utils/constant';
import { formatOptions } from '@/utils/utils';

const selectDataMap = new Map([
  ['eventSource', dropDownListKey.SHADOW_EVENT_SOURCE],
  ['eventGroup', dropDownListKey.EVENT_GROUP],
]);
const VehicleInstructionEdit = () => {
  const fetchApi = new VehicleInstructionApi();
  const [form] = Form.useForm();
  const navigator = useNavigate();
  const checkboxOptions = [
    { label: '车辆实时数据', value: 'extractVehicleInfo' },
    { label: '提取关联视频', value: 'extractVideo' },
    { label: '提取3D数据', value: 'extractThreeDimension' },
  ];
  const [options, setOptions] = useState<any>({});
  const [checkboxItems, setCheckboxItems] = useState<any>({
    extractVehicleInfo: false,
    extractVideo: false,
    extractThreeDimension: false,
  });
  const layout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 16 },
  };

  const dropDownData = useCommonDropDown([
    dropDownKey.SHADOW_EVENT_SOURCE,
    dropDownKey.EVENT_GROUP,
  ]);

  const setSatelliteInfo = (value: any[]) => {
    setCheckboxItems({
      extractVehicleInfo:
        value.indexOf('extractVehicleInfo') > -1 ? true : false,
      extractVideo: value.indexOf('extractVideo') > -1 ? true : false,
      extractThreeDimension:
        value.indexOf('extractThreeDimension') > -1 ? true : false,
    });
  };

  const onSubmitClick = async () => {
    const data = await form.validateFields();
    const requestParams = {
      ...data,
      ...checkboxItems,
    };
    const res = await fetchApi.submitEditInfo(requestParams);
    if (res.code === HttpStatusCode.Success) {
      Modal.success({
        title: `模板${res.data}创建成功!`,
        onOk() {
          history.go(-1);
        },
      });
    } else {
      Modal.error({
        title: res.message,
      });
    }
  };

  const renderFieldItem = (item: any) => {
    let bar: any;
    switch (item.type) {
      case 'select':
        bar = (
          <Select
            placeholder={item.placeHolder}
            options={formatOptions(dropDownData[selectDataMap.get(item.name)!])}
            filterOption={(input, option) => {
              const label: any = option?.label || '';
              return label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            }}
            showSearch
            allowClear
          />
        );
        break;
      case 'input':
        bar = (
          <Input
            allowClear
            placeholder={item.placeHolder}
            maxLength={item.maxLength}
          />
        );
        break;
      default:
        return null;
    }
    return bar;
  };
  return (
    <CommonEdit
      title="新建车辆指令"
      breadCrumbConfig={[
        {
          title: '车辆指令管理',
          route: '',
        },
        {
          title: '新建车辆指令',
          route: '',
        },
      ]}
      onSubmitClick={onSubmitClick}
      onCancleClick={() => {
        navigator('/app/vehicleInstruction');
      }}
    >
      <Form form={form} {...layout}>
        <EditModuleTitle title="基本信息" />
        {basicInfo.map((item: any, index: any) => {
          return (
            <Form.Item
              key={index}
              name={item.name}
              label={item.label}
              rules={[{ required: item.required, message: item.placeHolder }]}
            >
              {renderFieldItem(item)}
            </Form.Item>
          );
        })}

        <EditModuleTitle title="附属信息" />
        <Form.Item label={'附属信息'}>
          <Checkbox.Group
            options={checkboxOptions}
            defaultValue={['Pear']}
            onChange={setSatelliteInfo}
          />
        </Form.Item>

        <EditModuleTitle title="指令模板" />
        <Form.Item
          name={'content'}
          label={'指令模板'}
          rules={[{ required: true, message: '请输入指令模板' }]}
        >
          <Input.TextArea
            placeholder="请输入模板内容"
            maxLength={500}
            autoSize={true}
          />
        </Form.Item>
        <EditModuleTitle title="接收人员" />
        <Form.Item
          name={'maintainUser'}
          label={'指令负责人ERP'}
          rules={[{ required: true, message: '请输入指令负责人ERP' }]}
        >
          <Input
            maxLength={50}
            allowClear
            placeholder="请输入指令负责人ERP,多人用英文“;“隔开"
          />
        </Form.Item>
        <Form.Item name={'receiveUser'} label={'指令接收人ERP'}>
          <Input
            maxLength={50}
            allowClear
            placeholder="请输入指令接收人ERP,多人用英文“;“隔开"
          />
        </Form.Item>
      </Form>
    </CommonEdit>
  );
};

export default React.memo(VehicleInstructionEdit);
