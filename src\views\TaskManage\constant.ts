import { FormConfig } from '@/components/CommonForm/formConfig';
import { dropDownListKey, dropDownKey } from '@/utils/constant';
export const SearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'stationBaseId',
      label: '站点',
      placeholder: '请选择站点',
      type: 'select',
    },
    {
      fieldName: 'taskType',
      label: '任务类型',
      placeholder: '请选择任务类型',
      type: 'select',
      dropDownKey: dropDownKey.WAREHOUSE_TASK_TYPE,
      dropDownListKey: dropDownListKey.WAREHOUSE_TASK_TYPE,
      specialFetch: 'commonDown',
    },
    {
      fieldName: 'deviceName',
      label: '车牌号',
      placeholder: '请输入车牌号',
      type: 'input',
      maxLength: 50,
    },
    {
      fieldName: 'orderNo',
      label: '关联WCS运输单ID',
      placeholder: '请输入关联WCS运输单ID',
      type: 'input',
      maxLength: 50,
    },
    {
      fieldName: 'taskStatus',
      label: '任务状态',
      placeholder: '请选择任务状态',
      type: 'select',
      multiple: true,
      dropDownKey: dropDownKey.WAREHOUSE_TASK_STATUS,
      dropDownListKey: dropDownListKey.WAREHOUSE_TASK_STATUS,
      specialFetch: 'commonDown',
    },
    {
      fieldName: 'rangeTime',
      label: '任务创建时间',
      type: 'rangeTime',
      wrapperCol: { span: 24 },
    },
  ],
};

export const tableColumns: any[] = [
  {
    title: '任务ID',
    dataIndex: 'taskId',
    align: 'center',
    width: 170,
    ellipsis: true,
  },
  {
    title: '任务类型',
    dataIndex: 'taskTypeName',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '执行车牌号',
    dataIndex: 'deviceName',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '车架号',
    dataIndex: 'deviceSerialNo',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '上装编号',
    dataIndex: 'shelfNo',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '设备类型',
    dataIndex: 'businessTypeName',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '站点',
    dataIndex: 'stationName',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '任务状态',
    dataIndex: 'taskStatusName',
    align: 'center',
    width: 80,
    ellipsis: true,
  },
  {
    title: '关联WCS运输单ID',
    dataIndex: 'orderNo',
    align: 'center',
    width: 190,
    ellipsis: true,
  },
  {
    title: '目的地',
    dataIndex: 'destination',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '任务创建时间',
    dataIndex: 'createTime',
    align: 'center',
    width: 170,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    align: 'center',
    width: 150,
    ellipsis: true,
    fixed: 'right',
  },
];

export const detailTableColumns: any[] = [
  {
    title: '任务状态',
    dataIndex: 'taskStatusName',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '起始点',
    dataIndex: 'startPointName',
    align: 'center',
    width: 70,
    ellipsis: true,
  },
  {
    title: '停靠站',
    dataIndex: 'stopStationName',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '停靠点',
    dataIndex: 'mapPointName',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '耗时时间',
    dataIndex: 'costTime',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '结束原因',
    dataIndex: 'reasonName',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
];
