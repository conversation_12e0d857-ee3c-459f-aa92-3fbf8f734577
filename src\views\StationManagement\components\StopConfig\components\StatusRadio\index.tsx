/* eslint-disable no-unused-vars */

import { Form, FormInstance, Radio } from 'antd';
import React, { useEffect, useState } from 'react';


const StatusItem = (
  {
    disable,
    name,
    label,
    form,
    items,
    defaultValue,
    initValue,
    onValueChanged
  }: {
    disable?: boolean,
    name: string,
    label: string,
    form: FormInstance,
    items: any[] | null,
    defaultValue?: any,
    initValue?: any,
    onValueChanged?: Function
  }
) => {
  const [value, setValue] = useState(defaultValue);
  useEffect(() => {
    if (initValue) {
      setValue(initValue)
      form.setFieldsValue({
        [name]: initValue
      })
    } else {
      form.setFieldsValue({
        [name]: defaultValue
      })
    }
  }, [initValue]);

  const useStyle: React.CSSProperties = {
    color: "#31C2A6", marginLeft: 20
  }
  const unuseStyle: React.CSSProperties = {
    color: "red"
  }

  const makeRadioOptions = () => {
    return items?.map((item: any) => {
      return {
        label: item.name,
        value: item.code,
        style: `${item.code}` === '1' ? useStyle : unuseStyle
      }
    })
  }
  return (
    <Form.Item
      name={name}
      label={label}
      rules={[{ required: true }]}
    >
      <Radio.Group
        disabled={disable}
        onChange={(e: any) => {
          setValue(e.target.value)
          form.setFieldsValue({
            [name]: e.target.value
          })
          onValueChanged && onValueChanged(e.target.value)
        }}
        value={value}
        options={makeRadioOptions()}
      />
    </Form.Item>
  );
}

export default React.memo(StatusItem)
