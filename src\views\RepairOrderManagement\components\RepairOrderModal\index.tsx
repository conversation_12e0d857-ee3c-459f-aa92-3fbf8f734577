import { message, Modal, Radio, Form, Row, Input, Checkbox } from 'antd';
import React, { useEffect, useState } from 'react';
import BasicInfo from '../BasicInfo';
import HardwareInfo from '../HardwareInfo';
import OperationLog from '../OperationLog';
import RepairRecord from '../RepairRecord';
import { RepairOrderFetchApi } from '@/fetch/business';
import './index.scss';
import { CustomButton, ButtonType } from '@/components';
import { HttpStatusCode } from '@/fetch/core/constant';
import { repairOrderStatus } from '@/types';
const { TextArea } = Input;
const CheckboxGroup = Checkbox.Group;
interface Props {
  visible: boolean;
  repairOrderInfo: {
    number: any;
    status: any;
  };
  handleCloseRepairModal: () => void;
  refreshTable: (timeStr: string) => void;
}
enum IsHandleRadioKey {
  handle = 1, // 受理
  notHandle = 0, // 不受理
}
const ModalTitleMap = new Map([
  [repairOrderStatus.TO_BE_ACCEPTED, '维修申请'],
  [repairOrderStatus.REPAIRING, '维修记录'],
  [repairOrderStatus.TO_BE_CONFIRMED, '确认维修单'],
  [repairOrderStatus.NOT_ACCEPTED, '查看维修单'],
  [repairOrderStatus.COMPLETED, '查看维修单'],
]);
const CancelBtnTitleMap = new Map([
  [repairOrderStatus.TO_BE_ACCEPTED, '取消'],
  [repairOrderStatus.REPAIRING, '取消'],
  [repairOrderStatus.TO_BE_CONFIRMED, '取消'],
  [repairOrderStatus.NOT_ACCEPTED, '关闭'],
  [repairOrderStatus.COMPLETED, '关闭'],
]);
const RepairOrderModal = (props: Props) => {
  const fetchApi = new RepairOrderFetchApi();
  const { visible, handleCloseRepairModal, refreshTable, repairOrderInfo } =
    props;
  const [ishandle, setIshandle] = useState<number>(); // 是否受理
  const [handleResultForm] = Form.useForm();
  const [confirmCompleteForm] = Form.useForm();
  const [repairRecordForm] = Form.useForm();
  const [requireLog, setRequireLog] = useState([]); // 操作日志
  const [hardwareType, setHardwareType] = useState([]); // 硬件类型
  const [basicInfo, setBasicInfo] = useState({});
  const [vehicleId, setVehicleId] = useState(0);
  const [oldVehicleStatus, setOldVehicleStatus] = useState(''); // 原始车辆生命周期
  const [requireHardwareTypeIds, setRequireHardwareTypeIds] = useState<any>();
  const [hardwareInfo, setHardwareInfo] = useState([
    {
      id: 0,
      oldHardwareModelId: null,
      oldHardwareModelName: null,
      oldHardwareModelModel: '系统带入型号',
      newHardwareModelId: null,
      newHardwareModelName: null,
      newHardwareModelModel: '系统带入型号',
      newHardwareModelNumber: null,
    },
  ]);
  const [priceInfo, setPriceInfo] = useState({
    laborCost: '-',
    serviceCost: '-',
    otherCost: '-',
    costVerifyNumbers: '-',
    otherCostRemark: '-',
  });
  const [repairRecordInfo, setRepairRecordInfo] = useState({});
  useEffect(() => {
    if (repairOrderInfo?.number) {
      getRepairOrderInfo();
    }
  }, [repairOrderInfo]);
  const getHardwareType = async () => {
    const res = await fetchApi.getHardwareType();
    if (res.code === HttpStatusCode.Success) {
      setHardwareType(
        res.data.hardwareTypeList.map((v: any) => ({
          label: v.name,
          value: v.code,
        })),
      );
    }
  };
  /**
   * 获取维修单详情信息
   */
  const getRepairOrderInfo = async () => {
    try {
      const res: any = await fetchApi.getRepairOrderDetail(
        repairOrderInfo.number,
      );
      if (res.code === HttpStatusCode.Success && res.data) {
        if (res.data.status) {
          getHardwareType();
        }
        if (res.data.requireHardwareModelInfoList) {
          setHardwareInfo(res.data.requireHardwareModelInfoList);
        }
        setRequireLog(res.data.requireLog);
        setVehicleId(res.data.deviceBaseId);
        setOldVehicleStatus(res.data.oldHardwareStatusName);
        setBasicInfo({
          number: res.data?.number,
          deviceName: res.data?.deviceName,
          serialNo: res.data?.serialNo,
          stationBaseInfo: res.data?.stationBaseInfo,
          requireHardwareTypeNames: res.data?.requireHardwareTypeNames,
          // title: res.data?.title,
          isInfluenceOperation:
            res.data?.isInfluenceOperation === 0 ? '不影响运营' : '影响运营',
          description: res.data?.description,
          pictureList: res.data?.pictureList,
          video: res.data?.video,
          reportErp: res.data?.reportErp,
          reportPhone: res.data?.reportPhone,
          reportEmail: res.data?.reportEmail,
        });
        setPriceInfo({
          laborCost: res.data?.laborCost,
          serviceCost: res.data?.serviceCost,
          otherCost: res.data?.otherCost,
          costVerifyNumbers: res.data?.costVerifyNumbers,
          otherCostRemark: res.data?.otherCostRemark,
        });
        const hardwareList = res.data.requireHardwareModelInfoList
          ? res.data.requireHardwareModelInfoList.map((item: any) => {
              return {
                ...item,
                oldHardwareModelModel:
                  item.oldHardwareModelModel || '系统带入型号',
                newHardwareModelModel:
                  item.newHardwareModelModel || '系统带入型号',
              };
            })
          : hardwareInfo;
        setRepairRecordInfo({
          remark: res.data.requireCompleteRemark,
          laborCost: res.data.laborCost,
          serviceCost: res.data.serviceCost,
          otherCost: res.data.otherCost,
          otherCostRemark: res.data.otherCostRemark,
          costVerifyNumbers: res.data.costVerifyNumbers,
          isCheck: res.data.isCheck,
          checkErp: res.data.checkErp,
          isVerifyAutoDrive: res.data.isVerifyAutoDrive,
          verifyAutoDriveErp: res.data.verifyAutoDriveErp,
          notVerifyAutoDriveRemark: res.data.notVerifyAutoDriveRemark,
          hardwareModelInfoList: hardwareList,
        });
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    } finally {
      //
    }
  };

  /**
   * 点击取消按钮
   */
  const handleCancelRepairModal = () => {
    handleCloseRepairModal();
    handleResultForm.resetFields();
    confirmCompleteForm.resetFields();
    repairRecordForm.resetFields();
  };

  // type === 1受理或不受理维修单的提交按钮点击
  const handleRepairOrder = async () => {
    const handleResult = await handleResultForm.validateFields();
    const params = {
      ...handleResult,
      number: repairOrderInfo.number,
      requireHardwareTypeIds:
        handleResult.isHandle === 1 ? requireHardwareTypeIds : null,
    };
    const result: any = await fetchApi.handleRepairOrder(params);
    if (result.code === HttpStatusCode.Success) {
      message.success(result.message);
      refreshTable(new Date().getMilliseconds().toString());
    } else {
      message.error(result.message);
    }
    handleCloseRepairModal();
  };

  /**
   * 维修完成时填写维修单，提交或暂存
   * @param {number} isCommit  暂存还是提交 1提交；0暂存
   */
  const completeRepairOrder = async (isCommit: number) => {
    const completeResult =
      isCommit === 1
        ? await repairRecordForm.validateFields()
        : await repairRecordForm.getFieldsValue();
    // 提交时维修硬件信息要么不填，如果填了就必须得把维修硬件名称和更换后硬件名称也填了
    if (isCommit === 1) {
      const hardwareInfo = repairRecordForm.getFieldsValue([
        'requireHardwareModelInfoList',
      ]);

      // filterList-有效数据
      const filterList = hardwareInfo?.requireHardwareModelInfoList?.filter(
        (item: any) => {
          if (
            (item.newHardwareModelId && item.oldHardwareModelId) ||
            (!item.newHardwareModelId && !item.oldHardwareModelId)
          ) {
            return item;
          }
        },
      );
      if (
        hardwareInfo?.requireHardwareModelInfoList?.length !== filterList.length
      ) {
        message.error('请完善维修硬件信息');
        return;
      }
    }

    const params = {
      ...completeResult,
      laborCost: completeResult.laborCost
        ? parseFloat(completeResult.laborCost)
        : null,
      serviceCost: completeResult.serviceCost
        ? parseFloat(completeResult.serviceCost)
        : null,
      otherCost: completeResult.otherCost
        ? parseFloat(completeResult.otherCost)
        : null,
      isCheck: completeResult.isCheck,
      isVerifyAutoDrive: completeResult.isVerifyAutoDrive,
      number: repairOrderInfo.number,
      isCommit: isCommit,
      checkErp: completeResult.checkErp,
      verifyAutoDriveErp: completeResult.verifyAutoDriveErp,
      notVerifyAutoDriveRemark: completeResult.notVerifyAutoDriveRemark,
    };
    const result: any = await fetchApi.completeRepair(params);
    if (result.code === HttpStatusCode.Success) {
      message.success(result.message);
      refreshTable(new Date().getMilliseconds().toString());
    } else {
      message.error(result.message);
    }
    handleCloseRepairModal();
  };
  // type === 5确认维修完成
  const confirmComplete = async () => {
    const confirmCompleteInfo = await confirmCompleteForm.validateFields();
    if (
      confirmCompleteInfo.remark === '' ||
      confirmCompleteInfo.remark === undefined
    ) {
      message.error('请完善信息');
      return;
    }
    const params = {
      ...confirmCompleteInfo,
      number: repairOrderInfo.number,
    };
    const result: any = await fetchApi.checkRepair(params);
    if (result.code === HttpStatusCode.Success) {
      message.success(result.message);
      refreshTable(new Date().getMilliseconds().toString());
    } else {
      message.error(result.message);
    }
    handleCloseRepairModal();
  };

  return (
    <Modal
      className="repair-model"
      open={visible}
      onCancel={handleCancelRepairModal}
      destroyOnClose={true}
      wrapClassName={'repair-model-out'}
      footer={
        <div className="repair-btn">
          {repairOrderInfo.status === repairOrderStatus.TO_BE_ACCEPTED && (
            <CustomButton
              buttonType={ButtonType.PrimaryButton}
              title={'提交'}
              onSubmitClick={() => {
                handleRepairOrder();
              }}
              otherCSSProperties={{ marginLeft: 15, marginRight: 15 }}
            />
          )}
          {repairOrderInfo.status === repairOrderStatus.REPAIRING && (
            <div>
              <CustomButton
                buttonType={ButtonType.PrimaryButton}
                title={'提交'}
                onSubmitClick={() => {
                  completeRepairOrder(1);
                }}
                otherCSSProperties={{ marginLeft: 15, marginRight: 15 }}
              />
              <CustomButton
                buttonType={ButtonType.DefaultButton}
                title={'暂存'}
                onSubmitClick={() => {
                  completeRepairOrder(0);
                }}
                otherCSSProperties={{ marginLeft: 15, marginRight: 15 }}
              />
            </div>
          )}
          {repairOrderInfo.status === repairOrderStatus.TO_BE_CONFIRMED && (
            <div>
              <CustomButton
                buttonType={ButtonType.PrimaryButton}
                title={'提交'}
                onSubmitClick={() => {
                  confirmComplete();
                }}
                otherCSSProperties={{ marginLeft: 15, marginRight: 15 }}
              />
            </div>
          )}
          {/* 关闭或取消按钮 */}
          <CustomButton
            buttonType={ButtonType.DefaultButton}
            title={CancelBtnTitleMap.get(repairOrderInfo.status)!}
            onSubmitClick={() => {
              handleCloseRepairModal();
            }}
            otherCSSProperties={{ marginLeft: 15, marginRight: 15 }}
          />
        </div>
      }
      width={1400}
    >
      <div className="repair-order">
        <BasicInfo
          title={ModalTitleMap.get(repairOrderInfo.status)!}
          basicInfo={basicInfo}
        />

        {/* 维修中 */}
        {repairOrderInfo.status === repairOrderStatus.REPAIRING && (
          <div>
            <OperationLog requireLog={requireLog} />
            <RepairRecord
              repairRecordInfo={repairRecordInfo}
              vehicleId={vehicleId}
              form={repairRecordForm}
            />
          </div>
        )}

        {/* 待受理 */}
        {repairOrderInfo.status === repairOrderStatus.TO_BE_ACCEPTED && (
          <div>
            <OperationLog requireLog={requireLog} />
            <div className="handle-result">
              <p className="module-title">{'处理结果'}</p>
              <Form
                labelCol={{ span: 3 }}
                wrapperCol={{ span: 18 }}
                form={handleResultForm}
              >
                <Form.Item
                  label="结果"
                  name="isHandle"
                  rules={[{ required: true, message: '请选择是否受理' }]}
                >
                  <Radio.Group
                    value={ishandle}
                    onChange={(e) => {
                      setIshandle(e.target.value);
                      handleResultForm.resetFields([
                        'remark',
                        'isInfluenceOperation',
                      ]);
                    }}
                  >
                    <Radio value={IsHandleRadioKey.handle}>受理</Radio>
                    <Radio value={IsHandleRadioKey.notHandle}>不受理</Radio>
                  </Radio.Group>
                </Form.Item>
                {/* 不受理备注为必填，受理为非必填 */}
                {ishandle === 1 && (
                  <Form.Item
                    label="故障硬件类型"
                    name="requireHardwareTypeIds"
                    rules={[
                      ishandle === 1
                        ? { required: true, message: '请选择故障硬件类型' }
                        : { required: false },
                    ]}
                  >
                    <CheckboxGroup
                      options={hardwareType}
                      onChange={(val: any[]) => {
                        setRequireHardwareTypeIds(val.join(';'));
                        handleResultForm.resetFields([
                          'remark',
                          'isInfluenceOperation',
                        ]);
                      }}
                    ></CheckboxGroup>
                  </Form.Item>
                )}
                <Form.Item
                  label="备注"
                  name="remark"
                  rules={[
                    ishandle === 0
                      ? { required: true, message: '请输入备注' }
                      : { required: false },
                  ]}
                >
                  <TextArea
                    placeholder="请输入备注"
                    autoSize={{ minRows: 3, maxRows: 3 }}
                    maxLength={200}
                  />
                </Form.Item>
              </Form>
            </div>
          </div>
        )}

        {/* 不受理 */}
        {repairOrderInfo.status === repairOrderStatus.NOT_ACCEPTED && (
          <OperationLog requireLog={requireLog} />
        )}

        {/* 已完成 */}
        {repairOrderInfo.status === repairOrderStatus.COMPLETED && (
          <div>
            <OperationLog requireLog={requireLog} />
            <HardwareInfo hardwareInfo={hardwareInfo} priceInfo={priceInfo} />
          </div>
        )}

        {/* 待确认 */}
        {repairOrderInfo.status === repairOrderStatus.TO_BE_CONFIRMED && (
          <div>
            <OperationLog requireLog={requireLog} />
            <HardwareInfo hardwareInfo={hardwareInfo} priceInfo={priceInfo} />
            <div className="confirm-complete">
              <p className="module-title">{'确认完成维修单'}</p>
              <Form
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 18 }}
                form={confirmCompleteForm}
              >
                <Form.Item label="原始车辆生命周期">
                  {oldVehicleStatus}
                </Form.Item>
                <Form.Item
                  label="备注"
                  name="remark"
                  rules={[{ required: true, message: '请输入备注' }]}
                >
                  <TextArea
                    placeholder="请输入备注"
                    autoSize={{ minRows: 3, maxRows: 3 }}
                    maxLength={200}
                  />
                </Form.Item>
              </Form>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default React.memo(RepairOrderModal);
