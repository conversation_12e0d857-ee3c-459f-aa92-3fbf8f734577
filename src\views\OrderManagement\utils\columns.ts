import { FormConfig } from '@/components';
import { ClstagKey } from './clstagKey';
export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'originalOrderId',
      label: '订单编号',
      placeholder: '请输入订单编号',
      type: 'input',
      // clstagKey: ClstagKey.originalOrderId,
    },
    {
      fieldName: 'collectionUserContact',
      label: '取件人手机号',
      placeholder: '请输入手机号',
      type: 'input',
      // clstagKey: ClstagKey.collectionUserContact,
    },
    {
      fieldName: 'deviceName',
      label: '车牌号',
      placeholder: '请输入车牌号',
      type: 'input',
      // clstagKey: ClstagKey.orderVehicleName,
    },
    {
      fieldName: 'gridNo',
      placeholder: '请选择车牌号，再输入格口号',
      type: 'input',
      label: '格口号',
      // inputType: 'number',
      // clstagKey: ClstagKey.gridNo,
    },
    {
      fieldName: 'orderStatusList',
      label: '订单状态',
      placeholder: '请选择',
      type: 'select',
      multiple: true,
      // clstagKey: ClstagKey.orderStatusList,
    },
    {
      fieldName: 'scheduleName',
      label: '调度单号',
      placeholder: '请输入调度单号',
      type: 'input',
      // clstagKey: ClstagKey.scheduleName,
    },
    {
      fieldName: 'scheduleTime',
      label: '订单装载时间 ',
      placeholder: '请输入车牌号，支持关键字联想全称',
      type: 'rangeTime',
      xxl: 12,
      xl: 16,
      lg: 24,
      labelCol: { span: 3.5 },
      wrapperCol: { span: 20.5 },
      // clstagKey: ClstagKey.scheduleTime,
    },
    {
      fieldName: 'stationInfo',
      label: '省市站',
      placeholder: '请选择省市站信息',
      type: 'cascader',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      multiple: true,
      mapRelation: {
        label: 'name',
        value: 'id',
        children: 'children',
      },
      specialFetch: 'station',
    },
    {
      fieldName: 'stopId',
      label: '停靠点名称',
      placeholder: '请选择停靠点名称',
      type: 'select',
      showSearch: true,
      // clstagKey: ClstagKey.orderStop,
    },
    {
      fieldName: 'orderSourceCodeList',
      label: '订单来源',
      placeholder: '请选择',
      type: 'select',
      multiple: true,
      // clstagKey: ClstagKey.orderSourceCodeList,
    },
    {
      fieldName: 'deliveryMode',
      label: '配送方式',
      placeholder: '请选择',
      type: 'select',
      showSearch: true,
      // clstagKey: ClstagKey.deliveryMode,
    },
    {
      fieldName: 'loadMethod',
      label: '投递方式',
      placeholder: '请选择',
      type: 'select',
      // clstagKey: ClstagKey.loadMethod,
    },
  ],
};

export const columns: any[] = [
  {
    title: '订单编号',
    width: 190,
    dataIndex: 'originalOrderId',
    align: 'center',
  },
  {
    title: '包裹号',
    width: 220,
    dataIndex: 'orderId',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '姓名',
    width: 110,
    dataIndex: 'collectionUserName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '手机号',
    dataIndex: 'collectionUserContact',
    align: 'center',
    width: 140,
    ellipsis: true,
  },
  {
    title: '订单来源',
    width: 150,
    dataIndex: 'orderSourceCodeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '配送方式',
    width: 100,
    dataIndex: 'deliveryModeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '调度单号',
    width: 170,
    dataIndex: 'scheduleName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车牌号',
    width: 120,
    dataIndex: 'deviceName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '格口号',
    width: 80,
    dataIndex: 'gridNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '订单状态',
    width: 120,
    dataIndex: 'orderStatusName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '箱号',
    width: 120,
    dataIndex: 'boxCode',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '订单装载时间',
    width: 190,
    dataIndex: 'createTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    width: 150,
    dataIndex: 'operation',
    align: 'center',
    fixed: 'right',
  },
];

export const subOrderColumns: any[] = [
  {
    title: '序号',
    width: 50,
    dataIndex: 'order',
    align: 'center',
  },
  {
    title: '子订单编号',
    width: 100,
    dataIndex: 'originalId',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '包裹数',
    width: 50,
    dataIndex: 'packageCount',
    align: 'center',
    ellipsis: true,
  },
];
