import { message, Form, Radio, Tag } from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  removeSearchValues,
  saveSearchValues,
} from '@/redux/reducer/searchForm';
import { useCommonDropDown } from '@/utils/hooks';
import { handleVehicleManage } from '@/redux/reducer/vehicleManage';
import { DeviceLifeApi } from '@/fetch/business';
import { vehicleUseColumns } from '../../utils/column';
import CheckStatusModal from '@/views/RepairOrderManagement/components/CheckStatusModal';
import { HttpStatusCode } from '@/fetch/core/constant';
import { TabType, PageType } from '../../utils/constant';
import { CommonTable, TableOperateBtn, showModal } from '@/components';
import { isEmpty } from '@/utils/utils';
import { RootState } from '@/redux/store';
import { useTableData } from '@/components/CommonTable/useTableData';
import { DeviceUseResponse, DeviceRequest } from '@/types/deviceLife';
import DivideVehicle from '../../DivideVehicle';
import { AnyFunc } from '@/global';
interface Props {
  searchForm: any;
  activeTabKey: string;
  click: number;
  setSearchForm: AnyFunc;
  searchRef: any;
  tableKey: string;
  setTableKey: any;
}
const UsingTab = (props: Props) => {
  const {
    searchForm,
    activeTabKey,
    click,
    setSearchForm,
    searchRef,
    tableKey,
    setTableKey,
  } = props;
  const fetchApi = new DeviceLifeApi();
  const [pageType, setPageType] = useState<PageType>(
    PageType.usingChangeStation,
  );
  const [showDivideModal, setShowDivideModal] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]); // 在用车辆、车辆调度池批量选中的数据keys
  const [selectedRows, setSelectedRows] = useState<any>([]); // 去分配存储分配车辆详细信息
  const [calibrationModal, setCalibrationModal] = useState(false);
  const [formInstance] = Form.useForm();
  const [vehicleId, setVehicleId] = useState(null);
  const dropdownList: any = useCommonDropDown(['VIDEO_MODE']);
  const dispatch = useDispatch();
  const { tableData, loading } = useTableData<DeviceRequest, DeviceUseResponse>(
    searchForm,
    fetchApi.fetchDeviceUseTable,
    tableKey,
  );
  useEffect(() => {
    if (tableData) {
      const temp: any = [];
      if (!isEmpty(selectedRows)) {
        for (let item of selectedRows) {
          for (let row of tableData.list) {
            if (row.id === item.id) {
              temp.push(row);
            }
          }
        }
        setSelectedRows(temp);
      }
    }
  }, [tableData]);
  useEffect(() => {
    if (activeTabKey === TabType.VEHICLE_USING) {
      setSelectedRowKeys([]);
      setSelectedRows([]);
      setSearchForm({
        ...searchForm,
        pageNum: 1,
        pageSize: 10,
      });
    }
  }, [activeTabKey, click]);

  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedRows(selectedRows);
    },
  };

  const formatColumns = () => {
    return vehicleUseColumns.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${(searchForm.pageNum - 1) * searchForm.pageSize + index + 1}`;
          break;
        case 'checkStatusName':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'deviceBusinessTypeName':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'isRequireName':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => `${text || '-'}`;
          break;
        case 'operate':
          // eslint-disable-next-line react/display-name
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                <TableOperateBtn
                  title="转站"
                  handleClick={() => changeStation(record)}
                  show={true}
                />
                <TableOperateBtn
                  title="查看标定检测结果"
                  handleClick={() => {
                    setVehicleId(record.id);
                    setCalibrationModal(true);
                  }}
                  show={record.checkStatus}
                />
              </div>
            );
          };
          break;
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
      return col;
    });
  };

  // 校验车端是否存在未完成调度单
  const checkVehicleSchedule = async (list: string[]) => {
    const res = await fetchApi.checkDeviceSchedule(list);
    if (res && res.code === HttpStatusCode.Success) {
      return res.data;
    } else {
      return [];
    }
  };

  // 转站
  const changeStation = async (record: any) => {
    const list = await checkVehicleSchedule([record.deviceName]);
    if (list.length > 0) {
      message.error(
        `车牌号${list.toString()}车端存在未完成调度单，不允许转站！`,
      );
      return;
    }
    const selectedRowKeys = [
      {
        id: record.id,
        deviceName: record.deviceName,
      },
    ];
    dispatch(handleVehicleManage(selectedRowKeys));
    setShowDivideModal(true);
    setPageType(PageType.usingChangeStation);
  };

  // 批量转站
  const changeStationBatch = async () => {
    if (selectedRows.length <= 0) {
      message.error('请至少选中一辆车进行批量转站操作！');
      return;
    }
    const vehicleNameList = selectedRows.map((item: any) => {
      return item.deviceName;
    });
    const list = await checkVehicleSchedule(vehicleNameList);
    if (list.length > 0) {
      message.error(`车牌号${list.toString()}存在未完成调度单，不允许转站！`);
      return;
    }
    dispatch(handleVehicleManage(selectedRows));
    setShowDivideModal(true);
    setPageType(PageType.usingChangeStation);
  };

  // 批量修改车辆推流模式
  const chageStreamModeBatch = () => {
    if (isEmpty(selectedRows)) {
      message.error('请至少选择一条数据进行批量设置推流模式!');
      return;
    }
    const deviceNameList = selectedRows.map((item: any) => {
      return item.deviceName;
    });

    showModal({
      title: '批量设置推流模式',
      width: 800,
      content: (
        <>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                width: '17%',
              }}
            >
              当前选中车辆：
            </div>
            <div className="vehicle-name-tag">
              {deviceNameList.map((item: string) => {
                return <Tag key={item}>{item}</Tag>;
              })}
            </div>
          </div>
          <Form form={formInstance}>
            <Form.Item name={'videoMode'} label="推流模式" initialValue={2}>
              <Radio.Group>
                {dropdownList?.videoModeList &&
                  dropdownList?.videoModeList.map((item: any) => {
                    return (
                      <Radio value={item.code} key={item.code + item.name}>
                        {item.name}
                      </Radio>
                    );
                  })}
              </Radio.Group>
            </Form.Item>
          </Form>
        </>
      ),
      footer: {
        showOk: true,
        showCancel: true,
        okFunc: async (cb: any) => {
          const res = await fetchApi.deviceChangeVideoMode({
            deviceNameList: deviceNameList,
            videoMode: formInstance.getFieldValue('videoMode'),
          });
          if (res.code === HttpStatusCode.Success) {
            message.success('批量设置车辆推流模式成功！');
            setTableKey(new Date().getMilliseconds().toString());
          } else {
            message.error('批量设置车辆推流模式失败！');
          }
          cb();
          formInstance.setFieldValue('videoMode', 2);
        },
        cancelFunc: (cb: any) => {
          cb();
          formInstance.setFieldValue('videoMode', 2);
        },
      },
    });
  };
  const middleBtns = [
    {
      show: true,
      title: '批量转站',
      key: 'changeStationBatch',
      onClick: () => changeStationBatch(),
    },
    {
      show: true,
      title: '批量设置推流模式',
      key: 'chageStreamModeBatch',
      onClick: () => chageStreamModeBatch(),
    },
  ];
  return (
    <div>
      <CommonTable
        middleBtns={middleBtns}
        searchRef={searchRef}
        rowKey={'id'}
        rowSelection={{ ...rowSelection }}
        columns={formatColumns()}
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        loading={loading}
        onPageChange={(paginationData: any) => {
          setSearchForm({
            ...searchForm,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      />
      {calibrationModal && (
        <CheckStatusModal
          visible={calibrationModal}
          onOk={() => {
            setCalibrationModal(false);
          }}
          onCancel={() => {
            setCalibrationModal(false);
          }}
          id={vehicleId}
        />
      )}
      {/** 接收/转站/交付弹窗 */}
      {showDivideModal && (
        <DivideVehicle
          pageType={pageType}
          show={showDivideModal}
          setShow={setShowDivideModal}
          setTableKey={setTableKey}
        />
      )}
    </div>
  );
};

export default React.memo(UsingTab);
