import React, { useEffect, useState } from "react";
import { CommonTable, CommonForm, useTableData, FormConfig, FieldItem, ExcelUploader } from '@jd/x-coreui';
import { ExcelColumnConfig, getFormConfig, SearchConfig, TableConfig } from './config';
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { TransportModelApi } from "@/fetch/business/transportModel";
import { showModal, TableOperateBtn } from "@/components";
import { HttpStatusCode } from "@/fetch/core/constant";
import { message, Modal } from "antd";

const fetchApi = new TransportModelApi();
const TransportModelSiteConfig = () => {

    const [searchConfig, setSearchConfig] = useState<FormConfig>(SearchConfig);
    const [visible, setVisible] = useState(false);
    const [templateUrl, setTemplateUrl] = useState('');

    const initSearchCondition = {
        searchForm: {
            siteId: null,
            siteName: null,
        },
        pageNum: 1,
        pageSize: 10,
    };

    const historySearchValues = useSelector(
        (state: RootState) => state.searchForm,
    );

    const [searchCondition, setSearchCondition] = useState<SearchCondition>(
        () => {
            return historySearchValues.searchValues
                ? historySearchValues.searchValues
                : initSearchCondition;
        },
    );

    const middleBtns: any[] = [
        {
            show: true,
            title: '添加站点',
            key: 'addSiteConfig',
            clstag: 'app_transportModelSiteConfig|addSiteConfig',
            onClick: () => {
                let formRef: any = null;
                showModal({
                    title: '添加站点',
                    content: <CommonForm formConfig={getFormConfig('add')} layout="vertical" formType="edit" colon={false} labelAlign="left" getFormInstance={(ref) => {
                        formRef = ref;
                    }} />,
                    footer: {
                        showOk: true,
                        showCancel: true,
                        okFunc: async (cb) => {
                            try {
                                const values = await formRef.validateFields();
                                const siteConfigList: any[] = [];
                                siteConfigList.push(values);
                                const res = await fetchApi.addSite(siteConfigList);
                                if (res.code === HttpStatusCode.Success) {
                                    message.success("添加成功");
                                    cb();
                                    reloadTable();
                                } else {
                                    message.error(res.message || '接口请求错误');
                                }
                            } catch (e) { }
                        },
                        cancelFunc: (cb) => {
                            cb();
                        }
                    }
                })
            },
        },
        {
            show: true,
            title: '+批量添加站点',
            key: 'batchAddSiteConfig',
            clstag: 'app_transportModelSiteConfig|batchAddSiteConfig',
            onClick: () => {
                setVisible(true);
            },
        }
    ];

    const onSearchClick = (val) => {
        if (JSON.stringify(val) === JSON.stringify(searchCondition.searchForm)) {
            return;
        }
        const data = {
            ...searchCondition,
            searchForm: val,
        };
        setSearchCondition(data);
    };

    const { tableData, reloadTable, loading }: any = useTableData(
        {
            searchForm: {
                ...searchCondition.searchForm,
                siteId: searchCondition.searchForm.siteId,
                siteName: searchCondition.searchForm.siteName?.value,
            },
            pageNum: searchCondition.pageNum,
            pageSize: searchCondition.pageSize,
        },
        fetchApi.fetchTableList,
    );

    const deleteSite = async (id: number) => {
        try {
            const res = await fetchApi.deleteSite(id);
            if (res.code === HttpStatusCode.Success) {
                message.success('删除成功');
                reloadTable();
            } else {
                message.error(res.message || '接口请求错误');
            }
        } catch (err: any) {
            message.error(err);
        }
    };

    const batchAddSiteConfig = async (dataList: any[]) => {
        try {
            const res = await fetchApi.addSite(dataList);
            if (res.code === HttpStatusCode.Success) {
                reloadTable();
            } else {
                message.error(res.message || '接口请求错误');
            }
        } catch (err: any) {
            message.error(err);
        }
    }

    const formatColumns = () => {
        return TableConfig.map((col) => {
            switch (col.dataIndex) {
                case 'operation':
                    return {
                        ...col,
                        render: (text: any, record: any, index: number) => {
                            return (
                                <div className="operate">
                                    <TableOperateBtn
                                        title="编辑"
                                        handleClick={() => {
                                            let formRef: any = null;
                                            showModal({
                                                title: '编辑站点',
                                                content: <CommonForm formConfig={getFormConfig('edit')} layout="vertical" formType="edit" colon={false} labelAlign="left" getFormInstance={(ref) => {
                                                    formRef = ref;
                                                    formRef.setFieldsValue({ ...record })
                                                }} />,
                                                footer: {
                                                    showOk: true,
                                                    showCancel: true,
                                                    okFunc: async (cb) => {
                                                        try {
                                                            const values = await formRef.validateFields();
                                                            values.id = record.id;
                                                            const res = await fetchApi.updateSite(values);
                                                            if (res.code === HttpStatusCode.Success) {
                                                                message.success("编辑成功");
                                                                cb();
                                                                reloadTable();
                                                            } else {
                                                                message.error(res.message || '接口请求错误');
                                                            }
                                                        } catch (e) { }
                                                    },
                                                    cancelFunc: (cb) => {
                                                        cb();
                                                    }
                                                }
                                            })
                                        }}
                                    />
                                    <TableOperateBtn
                                        title="删除"
                                        styleName="unenable"
                                        handleClick={() => {
                                            Modal.confirm({
                                                title: '确定删除站点[' + record.siteName + ']吗？',
                                                onOk: () => deleteSite(record.id)
                                            })
                                        }}
                                    />
                                </div>
                            );
                        },
                    };
                default:
                    return {
                        ...col,
                        render: (text: any) => `${text || '-'}`,
                    };
            }
        });
    };

    useEffect(() => {
        fetchApi.fetchSiteNameList().then((res) => {
            if (res.code === HttpStatusCode.Success) {
                const field = searchConfig.fields.find((item: FieldItem) => item?.fieldName === 'siteName');
                field!.options = res.data?.map((val) => ({
                    label: val,
                    value: val,
                }));
                setSearchConfig({ ...searchConfig });
            }
        })
        setTemplateUrl('https://rover-operation.s3.cn-north-1.jdcloud-oss.com/TRANSPORT_SITE_CONFIG.xlsx?AWSAccessKeyId=JDC_86715195E93739AC1616AA00A9DF&Expires=1773394541&Signature=Jge4MLWYlwdN%2Bi6Flsx0h19UUaA%3D');
    }, []);

    return (
        <>
            <CommonForm
                formConfig={searchConfig}
                defaultValue={searchCondition.searchForm}
                layout="inline"
                formType="search"
                colon={false}
                onSearchClick={onSearchClick}
                onResetClick={() => setSearchCondition({ ...initSearchCondition })}
            />
            <CommonTable
                tableListData={{
                    list: tableData?.list ?? [],
                    totalNumber: tableData?.total,
                    totalPage: tableData?.pages,
                }}
                middleBtns={middleBtns}
                columns={formatColumns()}
                loading={loading}
                rowKey={'id'}
                searchCondition={searchCondition}
                onPageChange={(value: any) => setSearchCondition(value)}
            />
            <ExcelUploader
                title="批量添加站点"
                downLoadTemplate={() => {
                    window.open(templateUrl, '_self');
                }}
                columnConfig={ExcelColumnConfig}
                visible={visible}
                onCancel={() => setVisible(false)}
                onSuccess={(value) => {
                    if (Array.isArray(value)) {
                        batchAddSiteConfig(value);
                    }
                }}
                onError={(value) => {
                    console.log(value);
                }}
                postProcessor={async (value) => {
                    if (Array.isArray(value)) {
                        const uniqueMap = new Map();
                        const invalidData: any = [];
                        const uniqueValue = value.filter((item) => {
                            const key = `${item.siteId}`;
                            if (!uniqueMap.has(key)) {
                                uniqueMap.set(key, true);
                                return true;
                            }
                            invalidData.push(item);
                            return false;
                        });
                        return {
                            validData: uniqueValue,
                            invalidData: invalidData,
                        };
                    }
                    return {
                        validData: [],
                        invalidData: value,
                    };
                }}
            />
        </>
    )
}

export default TransportModelSiteConfig;