import React, { useMemo, useState } from 'react';
import { OccupyStatus } from '@/utils/constant';
import { Popconfirm, message } from 'antd';
import {
  CommonForm,
  CommonTable,
  FormConfig,
  useTableData,
} from '@jd/x-coreui';
import {
  batchAddShelf,
  deleteShelf,
  getShelfPageList,
  getShelfTypeList,
} from '@/fetch/business/integrate';
import { HttpStatusCode } from '@/fetch/core/constant';
import { showModal } from '@/components';

const ShelfMagnagement = (props: { stationBaseId: number }) => {
  const [searchCondition, setSearchCondition] = useState<any>({
    stationBaseId: Number(props.stationBaseId),
    pageNum: 1,
    pageSize: 10,
  });
  const { tableData, loading, reloadTable } = useTableData(
    searchCondition,
    getShelfPageList,
  );
  const handleDeleteShelf = (record: any) => {
    deleteShelf(record.shelfId).then((res) => {
      if (res?.code === HttpStatusCode.Success) {
        message.success('删除成功！');
        reloadTable();
      } else {
        message.error(res?.message || '删除失败！');
      }
    });
  };

  const middleBtns: any[] = [
    {
      show: true,
      title: '新建',
      key: 'addShelf',
      onClick: async () => {
        let formRef: any = null;
        const res = await getShelfTypeList();
        if (res?.code === HttpStatusCode.Success) {
          const config: FormConfig = {
            fields: [
              {
                fieldName: 'shelfTypeId',
                type: 'select',
                label: '上装类型',
                placeholder: '请选择',
                options: res?.data?.map((item) => ({
                  label: item.shelfTypeName,
                  value: item.shelfTypeId,
                })),
                labelInValue: false,
                validatorRules: [
                  {
                    required: true,
                    message: '请选择',
                  },
                ],
              },
              {
                fieldName: 'prefixNo',
                type: 'input',
                label: '固定号段',
                placeholder: '请输入固定号段，用于区分业务类型',
                validatorRules: [
                  {
                    required: true,
                    message: '请输入固定号段，用于区分业务类型',
                  },
                  {
                    max: 5,
                    message: '固定号段最大位数不能超过5位数字',
                  },
                  {
                    pattern: /^\d+$/,
                    message: '固定段号中不能输入非数字的内容',
                  },
                ],
              },
              {
                fieldName: 'suffixStartNo',
                type: 'inputNumber',
                label: '起始号段',
                min: 0,
                placeholder: '请输入初始号段',
                validatorRules: [
                  {
                    required: true,
                    message: '请输入初始号段',
                  },
                  {
                    validator(_, value) {
                      if (value?.toString().length > 5) {
                        return Promise.reject(
                          new Error('起始号段最大位数不能超过5位数字'),
                        );
                      }
                      return Promise.resolve();
                    },
                  },
                ],
              },
              {
                fieldName: 'suffixEndNo',
                type: 'inputNumber',
                label: '结束号段',
                min: 0,
                placeholder: '请输入结束号段',
                validatorRules: [
                  {
                    required: true,
                    message: '请输入结束号段',
                  },
                ],
              },
            ],
          };
          showModal({
            title: '新建上装设置',
            content: (
              <CommonForm
                formConfig={config}
                getFormInstance={(ref) => {
                  formRef = ref;
                }}
              />
            ),
            footer: {
              showCancel: true,
              cancelText: '取消',
              cancelFunc: (cb) => {
                cb();
              },
              showOk: true,
              okFunc: async (cb) => {
                try {
                  const values = await formRef.validateFields();
                  batchAddShelf({
                    stationBaseId: Number(props.stationBaseId) || null,
                    shelfTypeId: values?.shelfTypeId,
                    prefixNo: values?.prefixNo,
                    suffixStartNo: values?.suffixStartNo,
                    suffixEndNo: values?.suffixEndNo,
                  }).then((res) => {
                    if (res?.code === HttpStatusCode.Success) {
                      message.success('新建成功！');
                      reloadTable();
                      cb();
                    } else {
                      message.error(res?.message || '新建失败！');
                    }
                  });
                } catch (e) {}
              },
            },
          });
        }
      },
    },
  ];

  const formatColumns = useMemo(() => {
    return [
      {
        title: '设备编号',
        dataIndex: 'shelfNo',
        align: 'center',
        width: 150,
      },
      {
        title: '上装类型',
        dataIndex: 'shelfTypeName',
        align: 'center',
        width: 150,
      },
      {
        title: '关联车辆',
        dataIndex: 'deviceName',
        align: 'center',
        width: 150,
      },
      {
        title: '上装与容器状态',
        dataIndex: 'occupyStatusName',
        align: 'center',
        width: 150,
      },
      {
        title: '操作',
        dataIndex: 'operate',
        align: 'center',
        width: 150,
        render: (text, record) => {
          return record.occupyStatus &&
            record.occupyStatus != OccupyStatus.EMPTY ? (
            <a
              onClick={() => {
                if (
                  record.occupyStatus &&
                  record.occupyStatus != OccupyStatus.EMPTY
                ) {
                  message.error('已绑定容器/车辆不可删除！');
                  return;
                }
              }}
            >
              删除
            </a>
          ) : (
            <Popconfirm
              title="确认删除吗？"
              trigger={'click'}
              onConfirm={handleDeleteShelf.bind(null, record)}
            >
              <a>删除</a>
            </Popconfirm>
          );
        },
      },
    ];
  }, [open]);
  return (
    <CommonTable
      columns={formatColumns}
      tableListData={{
        list: tableData?.list || [],
        totalPage: tableData?.pages,
        totalNumber: tableData?.total,
      }}
      rowKey="shelfId"
      middleBtns={middleBtns}
      onPageChange={(paginationData: any) => {
        setSearchCondition({
          ...searchCondition,
          pageNum: paginationData.pageNum,
          pageSize: paginationData.pageSize,
        });
      }}
    />
  );
};

export default ShelfMagnagement;
