import { TransportDataApi } from "@/fetch/business/transportDataStop";
import { HttpStatusCode } from "@/fetch/core/constant";
import { FormConfig } from "@jd/x-coreui";
import dayjs from "dayjs";

const fetchApi = new TransportDataApi();
export const SearchConfig: FormConfig = {
    fields: [
        {
            fieldName: 'cityIdList',
            label: '城市',
            placeholder: '请选择城市,可多选',
            type: 'select',
            multiple: true,
            showSearch: true,
            labelInValue: false,
        },
        {
            fieldName: 'stationIdList',
            label: '站点',
            placeholder: '请选择站点,可多选',
            type: 'select',
            multiple: true,
            showSearch: true,
            labelInValue: false,
        },
        {
            fieldName: 'vehicleNameList',
            label: '车号',
            placeholder: '请选择车号,可多选',
            type: 'select',
            multiple: true,
            showSearch: true,
            labelInValue: false,
        },
        {
            fieldName: 'dt',
            label: '日期范围',
            placeholder: '请选择日期范围',
            type: 'rangeTime',
            format: 'YYYY-MM-DD',
            showTime: false,
            disabledDate: (current) => {
                return current > dayjs().subtract(1, 'day');
            },
            xl: 12,
            xxl: 8,
            lg: 16,
            labelCol: {
                span: 4
            },
            wrapperCol: {
                span: 20
            },
        },
    ],
    linkRules: {
        cityIdList: [
            {
                linkFieldName: 'stationIdList',
                rule: 'clear'
            },
            {
                linkFieldName: 'stationIdList',
                rule: 'fetchData',
                fetchFunc: async (val) => {
                    const params = {
                        dimType: 'STATION',
                        filterIds: val,
                    };
                    const res = await fetchApi.getDimSelect(params);
                    if (res.code === HttpStatusCode.Success) {
                        return res.data.map((item) => {
                            return {
                                value: item.dimValue,
                                label: item.dimName,
                            };
                        });
                    } else {
                        return [];
                    }
                },
            },
            {
                linkFieldName: 'vehicleNameList',
                rule: 'clear'
            },
        ],
        stationIdList: [
            {
                linkFieldName: 'vehicleNameList',
                rule: 'clear'
            },
            {
                linkFieldName: 'vehicleNameList',
                rule: 'fetchData',
                fetchFunc: async (val) => {
                    const params = {
                        dimType: 'VEHICLE',
                        filterIds: val,
                    };
                    const res = await fetchApi.getDimSelect(params);
                    if (res.code === HttpStatusCode.Success) {
                        return res.data.map((item) => {
                            return {
                                value: item.dimName,
                                label: item.dimName,
                            };
                        });
                    } else {
                        return [];
                    }
                },
            },
        ],
    }
}

export const TableConfig: any[] = [
    {
        title: '日期',
        dataIndex: 'dt',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '调度单号',
        dataIndex: 'scheduleName',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '城市',
        dataIndex: 'cityName',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '站点',
        dataIndex: 'stationName',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '车号',
        dataIndex: 'vehicleName',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '任务总耗时(分钟)',
        dataIndex: 'totalDuration',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '任务总里程(公里)',
        dataIndex: 'totalMileage',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '出发停靠点名称',
        dataIndex: 'startStopName',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '到达停靠点名称',
        dataIndex: 'arrivalStopName',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '出发时间',
        dataIndex: 'startTime',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '到达时间',
        dataIndex: 'arrivalTime',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '行驶里程(公里)',
        dataIndex: 'mileage',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '行驶耗时(分钟)',
        dataIndex: 'duration',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
]