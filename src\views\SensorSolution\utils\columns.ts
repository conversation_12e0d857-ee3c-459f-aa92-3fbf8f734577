import { dropDownListKey, dropDownKey } from '@/utils/constant';
import { FieldItem, FormConfig } from '@/components';
export const tableColumns = [
  {
    title: '传感器方案名称',
    dataIndex: 'name',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '方案id',
    dataIndex: 'id',
    align: 'center',
    width: 60,
    ellipsis: true,
  },
  {
    title: '所属产品',
    dataIndex: 'productTypeName',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '方案状态',
    dataIndex: 'enableName',
    align: 'center',
    width: 60,
    ellipsis: true,
  },
  {
    title: '操作人',
    dataIndex: 'modifyUser',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
];
export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'name',
      label: '方案名称',
      placeholder: '请输入方案名称',
      type: 'input',
    },
    {
      fieldName: 'productType',
      label: '所属产品',
      placeholder: '请选择所属产品',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.PRODUCT_TYPE,
      dropDownListKey: dropDownListKey.PRODUCT_TYPE,
    },
    {
      fieldName: 'enable',
      label: '方案状态',
      placeholder: '请选择方案状态',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.ENABLE,
      dropDownListKey: dropDownListKey.ENABLE,
    },
  ],
};
