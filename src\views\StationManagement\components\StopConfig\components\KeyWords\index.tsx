/* eslint-disable no-unused-vars */
/* eslint-disable require-jsdoc */
/* eslint-disable no-invalid-this */
import React from 'react';
import './index.scss';
import { Tag, Input, Tooltip } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

const tagColors = [
  "#f50",
  "#2db7f5",
  "#87d068",
  "#108ee9",
  "red",
  "magenta",
  "volcano",
  "orange",
  "gold",
  "lime",
  "green",
  "cyan",
  "blue",
  "geekblue",
  "purple",
];

export default class KeyWords extends React.Component<any> {
  input: any;
  initialized = false
  state: {
    tags: any
    inputVisible: boolean,
    inputValue: string,
    editInputIndex: number,
    editInputValue: string,
  } = {
      tags: [],// ['Unremovable', 'Tag 2', 'Tag 3'],
      inputVisible: false,
      inputValue: '',
      editInputIndex: -1,
      editInputValue: '',
    };

  handleClose = (removedTag: any) => {
    const { tags } = this.state;
    if (tags) {
      const stateTags = tags.filter((tag: any) => tag !== removedTag);
      this.setState({ tags: stateTags });
      this.props.keywordsChanged ? this.props.keywordsChanged(stateTags) : null
    }
  };

  showInput = () => {
    this.setState({ inputVisible: true }, () => this.input.focus());
  };

  handleInputChange = (e: any) => {
    this.setState({ inputValue: e.target.value.slice(0, 20) });
  };

  handleInputConfirm = () => {
    const { inputValue } = this.state;
    let { tags } = this.state;
    if (inputValue && tags.indexOf(inputValue) === -1) {
      tags = [...tags, inputValue];
    }
    this.setState({
      tags,
      inputVisible: false,
      inputValue: '',
    });
    this.props.keywordsChanged ? this.props.keywordsChanged(tags) : null
  };

  saveInputRef = (input: any) => {
    this.input = input;
  };

  componentDidUpdate() {
    if (this.props.keywords && !this.initialized) {
      this.setState({
        tags: this.props.keywords
      })
      this.initialized = true
    }
  }

  render() {
    const { tags, inputVisible, inputValue } = this.state;
    return (
      <>
        {tags?.map((tag: any, index: number) => {
          const isLongTag = tag.length > 20;
          const tagElem = (
            <Tag
              style={{ marginTop: "8px" }}
              className="edit-tag"
              key={tag}
              closable={true}
              color={tagColors[index]}
              onClose={() => this.handleClose(tag)}
            >
              <span>
                {isLongTag ? `${tag.slice(0, 20)}...` : tag}
              </span>
            </Tag>
          );
          return isLongTag ? (
            <Tooltip title={tag} key={tag}>
              {tagElem}
            </Tooltip>
          ) : (
            tagElem
          );
        })}
        {inputVisible && (
          <Input
            ref={this.saveInputRef}
            type="text"
            size="small"
            className="tag-input"
            value={inputValue}
            onChange={this.handleInputChange}
            onBlur={this.handleInputConfirm}
            onPressEnter={this.handleInputConfirm}
          />
        )}
        {!inputVisible && (
          <Tag className="site-tag-plus" onClick={this.showInput}>
            <PlusOutlined /> 标签
          </Tag>
        )}
      </>
    );
  }
}
