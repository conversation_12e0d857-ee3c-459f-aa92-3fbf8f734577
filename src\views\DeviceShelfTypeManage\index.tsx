import { showModal } from '@/components';
import { DeviceInfoApi } from '@/fetch/business/deviceInfo';
import {
  editDeviceTypeShelfType,
  getDeviceShelfTypeList,
  getDeviceTypeShelfType,
  getShelfTypeList,
} from '@/fetch/business/integrate';
import { HttpStatusCode } from '@/fetch/core/constant';
import { ProductType } from '@/utils/enum';
import { CommonForm, CommonTable, useTableData } from '@jd/x-coreui';
import { message } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
const deviceApi = new DeviceInfoApi();
const DeviceShelfTypeManage = () => {
  const initSearch = {
    pageNum: 1,
    pageSize: 10,
    deviceTypeBaseId: null,
    shelfTypeId: null,
  };
  const formRef = useRef<any>(null);
  const [searchCondition, setSearchCondition] = useState<any>(initSearch);
  const { tableData, reloadTable } = useTableData(
    searchCondition,
    getDeviceShelfTypeList,
  );
  const [shelfTypeList, setShelfTypeList] = useState<any>([]);
  const [deviceTypeList, setDeviceTypeList] = useState<any>([]);
  const showEditModal = (record: any) => {
    getDeviceTypeShelfType(record.deviceTypeBaseId).then((res) => {
      if (res?.code === HttpStatusCode.Success) {
        let formRef: any = null;
        showModal({
          title: '上装类型设置',
          content: (
            <CommonForm
              defaultValue={{
                deviceTypeName: res?.data?.deviceTypeName,
                shelfTypeIdList: res?.data?.shelfTypeList?.map(
                  (i) => i.shelfTypeId,
                ),
              }}
              getFormInstance={(ref: any) => {
                formRef = ref;
              }}
              formConfig={{
                fields: [
                  {
                    fieldName: 'deviceTypeName',
                    type: 'text',
                    label: '车型名称',
                  },
                  {
                    fieldName: 'shelfTypeIdList',
                    type: 'select',
                    multiple: true,
                    options: shelfTypeList,
                    label: '可用上装类型',
                    placeholder: '请选择',
                    validatorRules: [{ required: true, message: '请选择' }],
                  },
                ],
              }}
            />
          ),
          footer: {
            showCancel: true,
            cancelFunc: (cb: any) => cb(),
            showOk: true,
            okFunc: async (cb) => {
              try {
                const values = await formRef.validateFields();
                editDeviceTypeShelfType(
                  record.deviceTypeBaseId,
                  values?.shelfTypeIdList?.map((i) => i.value),
                ).then((res) => {
                  if (res?.code === HttpStatusCode.Success) {
                    message.success('编辑成功');
                    reloadTable();
                    cb();
                  } else {
                    message.error(res?.message || '编辑失败');
                  }
                });
              } catch (e) {}
            },
          },
        });
      }
    });
  };

  const formatColumns = useMemo(() => {
    return [
      {
        title: '车型',
        dataIndex: 'deviceTypeName',
        align: 'center',
      },
      {
        title: '车型ID',
        dataIndex: 'deviceTypeBaseId',
        align: 'center',
      },
      {
        title: '已绑定上装类型',
        dataIndex: 'shelfTypeNames',
        align: 'center',
      },
      {
        title: '操作',
        dataIndex: 'operate',
        align: 'center',
        render: (value, record) => {
          return <a onClick={showEditModal.bind(null, record)}>编辑</a>;
        },
      },
    ];
  }, [JSON.stringify(shelfTypeList)]);

  useEffect(() => {
    getShelfTypeList().then((res) => {
      if (res?.code === HttpStatusCode.Success) {
        setShelfTypeList(
          res?.data?.map((item: any) => ({
            label: item.shelfTypeName,
            value: item.shelfTypeId,
          })),
        );
      }
    });

    deviceApi
      .queryDeviceTypeList({
        productType: ProductType.INTEGRATE,
        enable: 1,
      })
      .then((res) => {
        if (res?.code === HttpStatusCode.Success) {
          setDeviceTypeList(
            res?.data?.map((i) => ({
              label: i.deviceTypeName,
              value: i.deviceTypeBaseId,
            })),
          );
        }
      });
  }, []);

  const onSearchClick = (values: any) => {
    setSearchCondition({
      ...searchCondition,
      ...(values || {}),
    });
   
  };
  const onResetClick = (values: any) => {
    setSearchCondition({
      ...initSearch,
    });
    formRef.current.setFieldsValue({
      deviceTypeBaseId: null,
      shelfTypeId: null,
    });
  };
  return (
    <div className="device-shelf-manage" style={{ marginTop: '10px' }}>
      <CommonForm
        formConfig={{
          fields: [
            {
              fieldName: 'deviceTypeBaseId',
              label: '车型',
              type: 'select',
              options: deviceTypeList,
              placeholder: '请选择',
              labelInValue: false,
              showSearch: true,
            },
            {
              fieldName: 'shelfTypeId',
              label: '上装类型',
              type: 'select',
              options: shelfTypeList,
              placeholder: '请选择',
              labelCol: { span: 8 },
              labelInValue: false,
            },
          ],
        }}
        layout={'inline'}
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={onResetClick}
        getFormInstance={(ref: any) => {
          formRef.current = ref;
        }}
      />
      <CommonTable
        rowKey="deviceTypeBaseId"
        columns={formatColumns}
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        onPageChange={(paginationData: any) => {
          setSearchCondition({
            ...searchCondition,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      />
    </div>
  );
};

export default DeviceShelfTypeManage;
