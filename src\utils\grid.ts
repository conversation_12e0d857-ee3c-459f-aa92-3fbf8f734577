// 货箱格口配置规则校验 
const validationCaseData = (boxColumnNum:number,colHeigh:any,minHeight:any,gridsArr:any[],key:string) => { 
    let tempHeight= 0
    let colIdx= 0
    const valid = {
      isValid:true,
      msg:''
    }
    gridsArr.map((item:any,idx:number) => { 
      const grid = Object.assign({},item)
      if(colIdx == boxColumnNum){
        valid.isValid = false
        valid.msg = `生成失败，格口配置有误，无法生成${key}的列数【${boxColumnNum}】`
        return
      } else if(parseInt(grid.height) > colHeigh){
        valid.isValid = false
        valid.msg = `生成失败，【${key}】格口号【${grid.gridNo}】的高度大于货箱高度【${colHeigh}】mm`
        return
      }
      tempHeight += parseInt(grid.height)
      // 判断是否是最后一个格子:a.根据下标判断 b.累计格子高度+下一个格子高度大于最小列高的1/3,下一个格子是下一列的
      if(gridsArr.length === (idx + 1)){ 
        grid.isLast = true
      } else {
        const lastGrid = gridsArr[idx+1]
        const nextSumHeight = tempHeight + parseInt(lastGrid.height)
        if( nextSumHeight - colHeigh > minHeight/3){  
          grid.isLast = true
        }
      }
    
      if(grid.isLast && gridsArr.length > idx + 1){ 
        colIdx++
        tempHeight =  0
      }
    })
    return valid
}
export const validationGrid = (editGridList: any[],gridPositionValue:any,gridCofigArr:any) => {
    let valid = {
        isValid:true,
        msg:''
      }
      // 每一侧的列数
      const leftColumnNum =  gridPositionValue.leftBoxColumnNum || 0
      const rightColumnNum =  gridPositionValue.rightBoxColumnNum || 0
      // 每一侧的格口总高度
      let leftColSumHeight = 0        
      let rightColSumHeight = 0
  
      let isValidatSize = true
      let leftGridNum = 0
      let rightGridNum = 0
      const leftGrids:any[] = []
      const rightGrids:any[] = []
      let minLeftHeight = 0
      let minRightHeight = 0
      editGridList.map((grid:any) => { 
        if(grid.side === '左侧') { 
          leftGridNum++
          leftColSumHeight += parseInt(grid.height)
          leftGrids.push(grid)
          if(parseInt(grid.height) < minLeftHeight || minLeftHeight == 0){ 
            minLeftHeight = parseInt(grid.height)
          }
        }
        if(grid.side === '右侧') { 
          rightGridNum++
          rightColSumHeight += parseInt(grid.height)
          rightGrids.push(grid)
          if(parseInt(grid.height) < minRightHeight || minRightHeight == 0){ 
            minRightHeight = parseInt(grid.height)
          }
        }
        if(gridCofigArr.findIndex( (size:any) => size.sizeStr ===  grid.sizeStr) === -1  && isValidatSize) { 
          // 判断是否出现当前数据规格跟配置的不一致的情况
          isValidatSize = false
        }
      })
      
      if(leftGridNum < leftColumnNum){ 
        valid.isValid = false
        valid.msg = '生成失败，左侧格口数不足！'
      } else if(rightGridNum < rightColumnNum){ 
        valid.isValid = false
        valid.msg = '生成失败，右侧格口数不足！'
      } else if(leftGridNum == 0 && rightGridNum == 0){ 
        valid.isValid = false
        valid.msg ='操作失败，请维护详细格口信息！'
      }else if(leftGridNum && leftColumnNum == 0){ 
        valid.isValid = false
        valid.msg ='生成失败，左侧按照实际显示列数为0，不应该存在格口数据！'
      } else if(rightGridNum && rightColumnNum == 0){ 
        valid.isValid = false
        valid.msg ='生成失败，右侧按照实际显示列数为0，不应该存在格口数据！'
      } else if(!isValidatSize){ 
        valid.isValid = false
        valid.msg ='生成失败，格口规格参数不一致，请检查！'
      } else {
        // 每一侧的列高度
        let leftColHeigh:any = 0
        let rightColHeigh:any = 0
        if(leftColumnNum) { 
          leftColHeigh =(leftColSumHeight/leftColumnNum).toFixed(2)
        }
        if(rightGridNum) { 
          rightColHeigh = (rightColSumHeight/rightColumnNum).toFixed(2)
        }
        const leftValidData =  validationCaseData(leftColumnNum,leftColHeigh,minLeftHeight,leftGrids,'左侧')
        const rightValidData =  validationCaseData(rightColumnNum,rightColHeigh,minRightHeight,rightGrids,'右侧')
        if(!leftValidData.isValid){ 
          valid = leftValidData
        } else  if(!rightValidData.isValid){ 
          valid = rightValidData
        }
      }
      return valid
  }
  