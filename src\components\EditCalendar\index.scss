.edit-calendar-container{
  margin-bottom: 20px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  .edit-item{
    height: 80%;
    width: 80%;
  }
  .common-title{
    height: 50px;
    line-height: 50px;
    font-size: 20px;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.02);
    color: #000;
  }
  .footer-btn{
    text-align: center;
    margin-top: 20px;
    padding-bottom: 20px;
  }
  .form-group_item{
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    padding: 10px;
  }
  .label-text{
    margin: 0 4px;
  }
  .#{$ant-prefix}-input{
    width: 80px;
  }
  .form-group_item{
    margin-top: 10px;
  }
  .#{$ant-prefix}-btn{
    margin-right: 6px;
    margin-left: 10px;
  }
  .#{$ant-prefix}-form-item{
    margin-bottom: 0px!important;
  }
  .#{$ant-prefix}-space{
    margin-right: 10px;
  }
  .tips{
    padding: 10px;
    color: #333;
    padding-bottom: 0;
    font-size: 14px;
  }
}


input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none;
  margin: 0; 
}
input[type=number] {
    -moz-appearance:textfield;
}
.#{$ant-prefix}-picker-calendar-header{
  margin-right: 8px;
}