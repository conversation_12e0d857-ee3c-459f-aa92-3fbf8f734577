import React, { useState, useEffect } from 'react';
import { CommonTable, CommonForm, TableOperateBtn } from '@/components';
import { searchConfig, tableColumns } from './utils/columns';
import { useTableData } from '@/components/CommonTable/useTableData';
import { RoleManageApi, ResourceManageApi } from '@/fetch/business';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/store';
import { useNavigate } from 'react-router-dom';
import { ProductType, YESNO } from '@/utils/enum';
import { saveSearchValues } from '@/redux/reducer/searchForm';
import { HttpStatusCode } from '@/fetch/core/constant';
import { message, Form, Table, Space, Popconfirm, Modal, Tree } from 'antd';
import { PageType } from '@/utils/EditTitle';
import { TableListType } from '@/utils/constant';

interface DataNode {
  title: string;
  key: string;
  isLeaf?: boolean;
  children?: DataNode[];
}

const RoleManage = () => {
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const fetchApi = new RoleManageApi();
  const resourceApi = new ResourceManageApi();
  const { historySearchValues } = useSelector((state: RootState) => ({
    historySearchValues: state.searchForm,
  }));
  const initSearchCondition = {
    searchForm: {
      name: '',
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValues.searchValues
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [tableData, setTableData] = useState<TableListType>({
    list: [],
    total: 0,
    pages: 0,
  });
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>(['3']);
  const [editItem, setEditItem] = useState<any>();
  const [isReaourceModalVisible, setIsReaourceModalVisible] = useState(false);
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
  const [treeData, setTreeData] = useState<DataNode[]>([]);

  useEffect(() => {
    fetchTableData();
  }, [searchCondition]);
  useEffect(() => {
    getTreeReaource();
  }, []);

  const middleBtns: any[] = [
    {
      show: true,
      title: '新建角色',
      onClick: () => handleEdit(PageType.ADD),
    },
  ];
  const formateColumns = () => {
    return tableColumns.map((col) => {
      switch (col.dataIndex) {
        case 'appNumber':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              const { pageSize, pageNum } = searchCondition;
              return <div>{(pageNum - 1) * pageSize + index + 1}</div>;
            },
          };
        case 'operation':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return (
                <div className="operate">
                  <TableOperateBtn
                    title="编辑"
                    handleClick={() => handleEdit(PageType.EDIT, record.number)}
                  />
                  <Popconfirm
                    placement="left"
                    title={'确定删除'}
                    onConfirm={() => {
                      deleteClick(record.number);
                    }}
                    okText="确定"
                    cancelText="取消"
                    overlayStyle={{ maxWidth: 800 }}
                  >
                    <a style={{ marginLeft: '5px', marginRight: '5px' }}>
                      删除
                    </a>
                  </Popconfirm>
                  <TableOperateBtn
                    title="关联资源"
                    handleClick={() => {
                      getResourceOfRole(record.number);
                      setEditItem(record);
                      setIsReaourceModalVisible(true);
                    }}
                  />
                </div>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  const fetchTableData = async () => {
    try {
      setLoading(true);
      const params = {
        searchForm: {
          name: searchCondition.searchForm.name,
        },
        pageNum: searchCondition.pageNum,
        pageSize: searchCondition.pageSize,
      };
      const res: any = await fetchApi.fetchTableList(params);
      if (res.code === HttpStatusCode.Success) {
        setTableData({
          list: res.data.list,
          total: res.data.total,
          pages: res.data.pages,
        });
      }
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };
  /**
   * 根据资源编号查询子资源，编号为空则查询所有顶级资源
   * @param {string | null}reaourceNumber
   * @param {Function}callback
   */
  const getTreeReaource = async () => {
    const response: any = await resourceApi.fetchTableList({
      pageNum: 1,
      pageSize: 1000,
      searchForm: {},
    });
    if (response.code === HttpStatusCode.Success && response.data) {
      const arr = recursionTree(response.data.list);
      setTreeData(arr);
    }
  };
  const recursionTree = (data: any) => {
    const arr =
      data &&
      data.map((item: any) => {
        let children = [];
        if (item.hasChildren) {
          children = recursionTree(item.children);
        }
        return {
          title: item.name,
          key: item.number,
          children: children,
        };
      });
    return arr;
  };

  /**
   * 根据角色编号查询已绑定的资源
   * @param {string}roleNumber
   */
  const getResourceOfRole = async (roleNumber: string) => {
    const response: any = await fetchApi.getResource(roleNumber);
    if (response.code === HttpStatusCode.Success) {
      const arr =
        response.data &&
        response.data.map((item: any) => {
          return item.number;
        });
      setCheckedKeys(arr);
    } else {
      message.error(response.message);
    }
  };

  const deleteClick = async (number: any) => {
    const response: any = await fetchApi.delRole(number);
    if (response.code === HttpStatusCode.Success) {
      message.success(response.message);
      fetchTableData();
    } else {
      message.error(response.message);
    }
  };

  const handleEdit = (type: PageType, id?: number) => {
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: searchCondition,
      }),
    );
    navigator(
      id
        ? `/app/roleManage/edit?type=${type}&id=${id}`
        : `/app/roleManage/edit?type=${type}`,
    );
  };

  const onSearchClick = (val) => {
    const data = {
      searchForm: val,
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(data);
  };

  /**
   * 关联资源
   */
  const handleOkClick = async () => {
    const response: any = await fetchApi.bindResource(
      editItem.number,
      checkedKeys,
    );
    if (response.code === HttpStatusCode.Success) {
      message.success(response.message);
      handleCancelClick();
    } else {
      message.error(response.message);
    }
  };

  const handleCancelClick = () => {
    setIsReaourceModalVisible(false);
  };
  const onCheck = (checkedKeysValue: any, info: any) => {
    setCheckedKeys(checkedKeysValue);
  };
  return (
    <>
      <CommonForm
        formConfig={searchConfig}
        defaultValue={searchCondition.searchForm}
        layout="inline"
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={() => setSearchCondition({ ...initSearchCondition })}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        columns={formateColumns()}
        loading={loading}
        rowKey={'id'}
        middleBtns={middleBtns}
        searchCondition={searchCondition}
        onPageChange={(value: any) => setSearchCondition(value)}
      />
      <Modal
        title="关联资源"
        visible={isReaourceModalVisible}
        onOk={handleOkClick}
        onCancel={handleCancelClick}
        width={650}
      >
        <Tree
          checkable
          autoExpandParent={autoExpandParent}
          onCheck={onCheck}
          checkedKeys={checkedKeys}
          treeData={treeData}
        />
      </Modal>
    </>
  );
};

export default React.memo(RoleManage);
