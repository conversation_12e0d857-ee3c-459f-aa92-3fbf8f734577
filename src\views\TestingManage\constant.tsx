import { YESNO } from '@/utils/enum';
import { FormConfig, FormProConfig } from '@jd/x-coreui';
import { Tooltip } from 'antd';
import React from 'react';
export const BugStatus = [
  {
    label: '待确认',
    value: 'NEW',
  },
  {
    label: '处理中',
    value: 'HANDLE',
  },
  {
    label: '已解决',
    value: 'FIX',
  },
  {
    label: '关闭',
    value: 'CLOSE',
  },
  {
    label: '重新开启',
    value: 'REOPEN',
  },
  {
    label: '挂起',
    value: 'HANG',
  },
  {
    label: '已驳回',
    value: 'REJECT',
  },
  {
    label: '取消',
    value: 'CANCEL',
  },
];

export const InvalidReason = [
  {
    label: '数据收集',
    value: 'DATA_COLLECTION',
  },
  {
    label: '重复缺陷',
    value: 'REPEAT_DEFECT',
  },
  {
    label: '驳回缺陷',
    value: 'REJECTION_DEFECT',
  },
];

export const SearchForm: FormConfig = {
  fields: [
    {
      label: '亮灯颜色',
      fieldName: 'alarmColors',
      type: 'select',
      multiple: true,
      labelInValue: false,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      placeholder: '请选择',
      xl: 4,
      xxl: 4,
      options: [
        {
          label: '红灯',
          value: 'red',
        },
        {
          label: '黄灯',
          value: 'yellow',
        },
      ],
    },
    {
      label: '缺陷编号',
      fieldName: 'bugCode',
      type: 'input',
      placeholder: '请输入缺陷编号',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 4,
      xxl: 4,
    },
    {
      label: '优先级',
      fieldName: 'bugPriority',
      labelInValue: false,
      type: 'select',
      options: ['P0', 'P1', 'P2', 'P3'].map((item: string) => ({
        label: item,
        value: item,
      })),
      placeholder: '请选择',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 4,
      xxl: 4,
    },
    {
      label: '缺陷状态',
      fieldName: 'bugStatusList',
      type: 'select',
      multiple: true,
      labelInValue: false,
      options: BugStatus,
      placeholder: '请选择',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 4,
      xxl: 4,
    },
    {
      label: '版本号',
      fieldName: 'roverVersion',
      type: 'input',
      placeholder: '请输入Rover版本号',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 4,
      xxl: 4,
    },
    {
      label: '提交人',
      fieldName: 'bugProposer',
      type: 'input',
      placeholder: '请输入ERP',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 4,
      xxl: 4,
    },
    {
      label: '受理人',
      fieldName: 'bugAssignee',
      type: 'input',
      placeholder: '请输入ERP',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 4,
      xxl: 4,
    },
    {
      label: '功能场景',
      fieldName: 'functionSceneList',
      type: 'select',
      multiple: true,
      labelInValue: false,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      placeholder: '请选择',
      xl: 4,
      xxl: 4,
      options: [],
    },
    {
      label: '障碍物信息',
      fieldName: 'obstacleInfoList',
      type: 'select',
      multiple: true,
      labelInValue: false,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      placeholder: '请选择',
      xl: 4,
      xxl: 4,
      options: [],
    },
    {
      label: '障碍物方位',
      fieldName: 'obstaclePositionList',
      type: 'select',
      multiple: true,
      labelInValue: false,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      placeholder: '请选择',
      xl: 4,
      xxl: 4,
      options: [],
    },
    {
      label: '障碍物意图',
      fieldName: 'obstacleIntentList',
      type: 'select',
      multiple: true,
      labelInValue: false,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      placeholder: '请选择',
      xl: 4,
      xxl: 4,
      options: [],
    },
    {
      label: '道路结构',
      fieldName: 'roadStructureList',
      type: 'select',
      multiple: true,
      labelInValue: false,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      placeholder: '请选择',
      xl: 4,
      xxl: 4,
      options: [],
    },
    {
      label: '车型',
      fieldName: 'deviceTypeBaseIdList',
      type: 'select',
      multiple: true,
      labelInValue: false,
      showSearch: true,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      placeholder: '请选择',
      xl: 6,
      xxl: 4,
      options: [],
    },
    {
      label: '标签模块/分类/标签',
      fieldName: 'labelIds',
      type: 'cascader',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      placeholder: '请选择',
      multiple: true,
      maxTagCount: 2,
      xl: 8,
      xxl: 8,
      mapRelation: {
        label: 'name',
        value: 'value',
        children: 'child',
      },
    },
    {
      label: '缺陷创建时间',
      fieldName: 'rangeTime',
      type: 'rangeTime',
      // showTime: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xl: 8,
      xxl: 8,
    },
    {
      fieldName: 'isQaGroupDefectSubmitted',
      type: 'checkboxGroup',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xl: 8,
      xxl: 4,
      options: [
        {
          label: '质控组提交缺陷',
          value: true,
        },
      ],
    },
  ],
};

export const columns = [
  {
    title: (tableProps: any) => {
      return (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ fontSize: '14px' }}>亮灯颜色</div>
          <Tooltip title="红灯：P0红灯=预计修复日期-当前日期+2＜0 ；P1红色=预计修复日期-当前日期+8＜0；黄灯：P1黄灯=预计修复日期-当前日期+3＜0 ；P2黄灯=预计修复日期-当前日期+4＜0">
            <svg
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="2601"
              width="24"
              height="24"
            >
              <path
                d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z"
                fill="#CCCCCC"
                p-id="2602"
              ></path>
              <path
                d="M471.04 759.488a40.96 40.96 0 0 0 81.92 0 40.96 40.96 0 0 0-81.92 0zM512 230.4c-113.088 0-204.8 84.032-204.8 187.648v0.064c0 20.736 18.368 37.568 40.96 37.568 22.592 0 40.96-16.832 40.96-37.568 0-62.208 55.04-112.64 122.88-112.64s122.88 50.432 122.88 112.64-55.04 112.64-122.88 112.64c-22.592 0-40.96 16.832-40.96 37.504V643.456c0 20.736 18.368 37.504 40.96 37.504 22.592 0 40.96-16.768 40.96-37.504v-41.344c93.44-17.408 163.84-93.184 163.84-184C716.8 314.432 625.088 230.4 512 230.4z"
                fill="#FFFFFF"
                p-id="2603"
              ></path>
            </svg>
          </Tooltip>
        </div>
      );
    },
    dataIndex: 'alarmColor',
    align: 'center',
    width: 80,
    fixed: 'left',
  },
  {
    title: '缺陷编号',
    dataIndex: 'bugCode',
    align: 'center',
    width: 180,
    fixed: 'left',
  },
  {
    title: '缺陷标题',
    dataIndex: 'bugTitle',
    align: 'center',
    width: 220,
  },
  {
    title: '优先级',
    dataIndex: 'bugPriorityName',
    align: 'center',
    width: 60,
  },
  {
    title: '车型',
    dataIndex: 'deviceTypeName',
    align: 'center',
    width: 180,
    ellipsis: true,
  },
  {
    title: '提交人',
    dataIndex: 'bugProposer',
    align: 'center',
    width: 100,
  },
  {
    title: '受理人',
    dataIndex: 'bugAssignee',
    align: 'center',
    width: 100,
  },
  {
    title: '缺陷状态',
    dataIndex: 'bugStatusName',
    align: 'center',
    width: 60,
  },
  {
    title: '缺陷创建日期',
    dataIndex: 'bugCreateDate',
    align: 'center',
    width: 120,
  },
  {
    title: '预计修复完成日期',
    dataIndex: 'bugEstimatedFixDate',
    align: 'center',
    width: 120,
  },
  {
    title: '车辆Rover版本号',
    dataIndex: 'roverVersion',
    align: 'center',
    width: 100,
  },
  {
    title: '排查结论',
    dataIndex: 'bugConclusion',
    align: 'center',
    width: 180,
  },
  {
    title: '解决方案',
    dataIndex: 'bugResolution',
    align: 'center',
    width: 80,
  },
  {
    title: '修复此缺陷的MR_ID',
    dataIndex: 'fixMrIdList',
    align: 'center',
    width: 100,
  },
  {
    title: '关联缺陷',
    dataIndex: 'associatedBugList',
    align: 'center',
    width: 100,
  },
  {
    title: '标签模块',
    dataIndex: 'bugIssueModuleName',
    align: 'center',
    width: 100,
  },
  {
    title: 'QA跟进人',
    dataIndex: 'qaPerson',
    align: 'center',
    width: 100,
  },
  {
    title: '标签分类',
    dataIndex: 'bugIssueCategoryName',
    align: 'center',
    width: 100,
  },
  {
    title: '问题标签',
    dataIndex: 'bugIssueLabelName',
    align: 'center',
    width: 100,
  },
  {
    title: '功能场景',
    dataIndex: 'functionScene',
    align: 'center',
    width: 100,
  },
  {
    title: '障碍物信息',
    dataIndex: 'obstacleInfo',
    align: 'center',
    width: 100,
  },
  {
    title: '障碍物方位',
    dataIndex: 'obstaclePosition',
    align: 'center',
    width: 100,
  },
  {
    title: '障碍物意图',
    dataIndex: 'obstacleIntent',
    align: 'center',
    width: 100,
  },
  {
    title: '道路结构',
    dataIndex: 'roadStructure',
    align: 'center',
    width: 100,
  },
  {
    title: '仿真任务包名',
    dataIndex: 'simulationRecordInfoList',
    align: 'center',
    width: 160,
  },
  {
    title: '是否为有效缺陷',
    dataIndex: 'enableName',
    align: 'center',
    width: 100,
  },
  {
    title: '无效缺陷原因',
    dataIndex: 'invalidReason',
    align: 'center',
    width: 100,
  },
  {
    title: '是否需要仿真回归',
    dataIndex: 'isSimulationName',
    align: 'center',
    width: 100,
  },
  {
    title: '仿真复现任务名称',
    dataIndex: 'simulationTaskDTOList',
    align: 'center',
    width: 100,
  },
  {
    title: '是否可复现',
    dataIndex: 'isReproducibleName',
    align: 'center',
    width: 100,
  },
  {
    title: '不可复现原因',
    dataIndex: 'nonReproducibleReason',
    align: 'center',
    width: 100,
  },
  {
    title: '是否可召回',
    dataIndex: 'isRecallableName',
    align: 'center',
    width: 100,
  },
  {
    title: '不可召回原因',
    dataIndex: 'nonRecallableReason',
    align: 'center',
    width: 100,
  },
  {
    title: '仿真回退任务名称',
    dataIndex: 'rollbackTaskDTOList',
    align: 'center',
    width: 100,
  },
  {
    title: '回退结果',
    dataIndex: 'rollbackResultName',
    align: 'center',
    width: 60,
  },
  {
    title: '回退结果备注',
    dataIndex: 'rollbackRemark',
    align: 'center',
    width: 60,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    align: 'center',
    width: 60,
  },
  {
    title: '亮灯天数',
    dataIndex: 'alarmDays',
    align: 'center',
    width: 60,
  },
  {
    title: '研发解决天数',
    dataIndex: 'developerResolutionDays',
    align: 'center',
    width: 60,
    fixed: 'right',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 100,
    fixed: 'right',
  },
];

export const XingyunEditForm: FormConfig = {
  fields: [
    {
      fieldName: 'bugCode',
      label: '缺陷编号',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'bugTitle',
      label: '缺陷标题',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'bugDescription',
      label: '问题描述',
      type: 'ReactNode',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
      renderFunc: (field: any, text: any) => {
        return <div dangerouslySetInnerHTML={{ __html: text }}></div>;
      },
    },
    {
      fieldName: 'bugPriority',
      label: '优先级',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'bugProposer',
      label: '提交人',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'bugAssignee',
      label: '受理人',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'bugStatusName',
      label: '缺陷状态',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'bugCreateDate',
      label: '缺陷创建日期',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'bugEstimatedFixDate',
      label: '预计修复完成日期',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'roverVersion',
      label: '车辆Rover版本号',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'bugConclusion',
      label: '排查结论',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'bugResolution',
      label: '解决方案',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'fixMrIdList',
      label: '修复此缺陷的MR_ID',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'associatedBugList',
      label: '关联缺陷',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'bugIssueModuleName',
      label: '问题模块',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'qaPerson',
      label: 'QA跟进人',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'bugIssueCategoryName',
      label: '问题分类',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'bugIssueLabelName',
      label: '问题标签',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'functionScene',
      label: '功能场景',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'obstacleInfo',
      label: '障碍物信息',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'obstaclePosition',
      label: '障碍区方位',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'obstacleIntent',
      label: '障碍物意图',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'roadStructure',
      label: '道路结构',
      type: 'text',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
    {
      fieldName: 'simulationRecordInfoList',
      label: '仿真任务包名',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      lg: 12,
    },
  ],
};

export const JiraTestProgress: FormProConfig = {
  fields: [
    {
      label: '是否为有效缺陷',
      fieldName: 'isValid',
      type: 'radioGroup',
      options: [
        {
          label: '无效',
          value: YESNO.NO,
        },
        {
          label: '有效',
          value: YESNO.YES,
        },
      ],
      validatorRules: [
        {
          required: true,
        },
      ],
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      xl: 12,
      xxl: 12,
    },
    {
      label: '无效缺陷原因',
      fieldName: 'invalidReason',
      type: 'radioGroup',
      options: InvalidReason,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      xl: 12,
      xxl: 12,
      hidden: true,
      validatorRules: [
        {
          required: true,
          message: '请选择无效缺陷原因',
        },
      ],
    },
    {
      label: '是否需要仿真回归',
      fieldName: 'isSimulation',
      type: 'radioGroup',
      options: [
        {
          label: '需要',
          value: YESNO.YES,
        },
        {
          label: '不需要',
          value: YESNO.NO,
        },
      ],
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      xl: 12,
      xxl: 12,
      validatorRules: [
        {
          required: true,
          message: '请选择是否需要仿真回归',
        },
      ],
    },
    {
      label: '仿真复现任务名称',
      fieldName: 'simulationTaskName',
      type: 'input',
      placeholder: '请输入仿真复现任务名称，多用英文分号“;”隔开',
      maxLength: 200,
      showCount: true,
      hidden: true,
      validatorRules: [
        {
          required: true,
          message: '请输入仿真复现任务名称，多个用英文分号“;”隔开',
        },
      ],
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      xl: 12,
      xxl: 12,
    },
    {
      label: '是否可复现',
      fieldName: 'isReproducible',
      type: 'radioGroup',
      hidden: true,
      options: [
        {
          label: '是',
          value: YESNO.YES,
        },
        {
          label: '否',
          value: YESNO.NO,
        },
      ],
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      xl: 12,
      xxl: 12,
      validatorRules: [
        {
          required: true,
          message: '请选择是否可复现',
        },
      ],
    },
    {
      label: '不可复现原因',
      fieldName: 'nonReproducibleReason',
      type: 'textarea',
      placeholder: '请输入不可复现原因',
      maxLength: 500,
      showCount: true,
      hidden: true,
      validatorRules: [
        {
          required: true,
          message: '请输入不可复现原因',
        },
      ],
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      xl: 12,
      xxl: 12,
    },
    {
      label: '是否可召回',
      fieldName: 'isRecallable',
      type: 'radioGroup',
      options: [
        {
          label: '可以',
          value: YESNO.YES,
        },
        {
          label: '不可以',
          value: YESNO.NO,
        },
      ],
      hidden: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      xl: 12,
      xxl: 12,
      validatorRules: [
        {
          required: true,
          message: '请选择是否可召回',
        },
      ],
    },
    {
      label: '不可召回原因',
      fieldName: 'nonRecallableReason',
      type: 'textarea',
      placeholder: '请输入不可召回原因',
      maxLength: 500,
      showCount: true,
      hidden: true,
      validatorRules: [
        {
          required: true,
          message: '请输入不可召回原因',
        },
      ],
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      xl: 12,
      xxl: 12,
    },
    {
      label: '仿真回退任务名称',
      fieldName: 'rollbackTaskName',
      type: 'input',
      placeholder: '请输入仿真回退任务名称，多个用英文分号“;”隔开',
      maxLength: 200,
      showCount: true,
      hidden: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      xl: 12,
      xxl: 12,
    },
    {
      label: '回退结果',
      fieldName: 'rollbackResult',
      type: 'radioGroup',
      hidden: true,
      options: [
        {
          label: '通过',
          value: 'PASS',
        },
        {
          label: '不通过',
          value: 'NO_PASS',
        },
      ],
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      xl: 12,
      xxl: 12,
    },
    {
      label: '回退结果备注',
      fieldName: 'rollbackRemark',
      type: 'textarea',
      placeholder: '请输入回退仿真结果备注',
      maxLength: 500,
      showCount: true,
      hidden: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      xl: 12,
      xxl: 12,
    },
    {
      label: '上传图片（0/3）',
      fieldName: 'imageList',
      type: 'upload',
      fileListType: 'picture',
      accept: '.jpg, .jpeg, .png, .gif',
      LOPDN: process.env.JDX_APP_REQUEST_HEADER!,
      bucketName: 'rover-operation',
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      max: 3,
      getPreSignatureUrl:
        location.protocol +
        '//' +
        process.env.JDX_APP_CLOUD_FETCH_DOMAIN +
        '/infrastructure/oss/getPreUrl',
    },
    {
      label: '备注',
      fieldName: 'remark',
      type: 'textarea',
      placeholder: '请输入备注描述',
      maxLength: 500,
      showCount: true,
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
      xl: 12,
      xxl: 12,
    },
  ],
  linkRules: {
    isValid: [
      {
        targetFields: ['isSimulation', 'invalidReason'],
        rule: 'clear',
      },
      {
        targetFields: ['isSimulation'],
        rule: 'visible',
        dependenceData: [YESNO.YES],
      },
      {
        targetFields: ['invalidReason'],
        rule: 'visible',
        dependenceData: [YESNO.NO],
      },
      {
        targetFields: [
          'simulationTaskName',
          'isReproducible',
          'nonReproducibleReason',
          'isRecallable',
          'nonRecallableReason',
          'rollbackTaskName',
          'rollbackResult',
          'rollbackRemark',
        ],
        rule: 'hidden',
        dependenceData: [YESNO.YES, YESNO.NO],
      },
    ],
    isSimulation: [
      {
        targetFields: ['simulationTaskName', 'isReproducible', 'imageList'],
        rule: 'clear',
      },
      {
        targetFields: ['simulationTaskName', 'isReproducible', 'imageList'],
        rule: 'visible',
        dependenceData: [YESNO.YES],
      },
      {
        targetFields: [
          'nonReproducibleReason',
          'isRecallable',
          'nonRecallableReason',
          'rollbackTaskName',
          'rollbackResult',
          'rollbackRemark',
        ],
        rule: 'hidden',
        dependenceData: [YESNO.YES, YESNO.NO],
      },
    ],
    isReproducible: [
      {
        targetFields: [
          'nonReproducibleReason',
          'isRecallable',
          'rollbackTaskName',
          'rollbackResult',
          'rollbackRemark',
          '',
        ],
        rule: 'clear',
      },
      {
        targetFields: ['nonReproducibleReason'],
        rule: 'visible',
        dependenceData: [YESNO.NO],
      },
      {
        targetFields: ['nonRecallableReason'],
        rule: 'hidden',
        dependenceData: [YESNO.NO],
      },
      {
        targetFields: [
          'isRecallable',
          'rollbackTaskName',
          'rollbackResult',
          'rollbackRemark',
        ],
        rule: 'visible',
        dependenceData: [YESNO.YES],
      },
    ],

    isRecallable: [
      {
        targetFields: ['nonRecallableReason'],
        rule: 'visible',
        dependenceData: [YESNO.NO],
      },
    ],
  },
};
