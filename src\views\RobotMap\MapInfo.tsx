import React, { useState, useEffect, useRef } from 'react';
import { CommonTable, TableOperateBtn, CommonForm } from '@/components';
import { versionTableColumns } from './utils/columns';
import { useTableData } from '@/components/CommonTable/useTableData';
import { RobotMapApi } from '@/fetch/business';
import { useSelector, useDispatch } from 'react-redux';
import { formatLocation } from '@/utils/utils';
import { useLocation, useNavigate } from 'react-router-dom';
import { HttpStatusCode } from '@/fetch/core/constant';
import { pageSizeOptions } from '@/utils/constant';
import { Switch, Breadcrumb, message } from 'antd';
import { RobotMapTool, generateProjection } from '@/utils/RobotMapTool';
import './mapInfo.scss';
import { getCenter } from 'ol/extent.js';
import MapUI from './components/MapUI';

enum LayerId {
  BASE_IMAGE_MAP = 'BASE_IMAGE_MAP',
  ROAD_MAP = 'ROAD_MAP',
  POINT_MAP = 'POINT_MAP',
  AREA_MAP = 'AREA_MAP',
  POINT_NAME = 'POINT_NAME',
}

enum MapBusinessType {
  POINT = 'POINT',
  LINE = 'LINE',
  AREA = 'AREA',
  POINT_NAME = 'POINT_NAME',
}
const fetchApi = new RobotMapApi();

const MapInfo = () => {
  const navigator = useNavigate();
  const { number, version, mapName } = formatLocation(window.location.search);
  const mapRef = useRef<any>(null);
  const mapContainer = useRef<any>();
  const [allMapInfo, setAllMapInfo] = useState<any>();
  const [showBusiness, setShowBusiness] = useState<MapBusinessType[]>([]);
  const mapBusiness = [
    {
      value: MapBusinessType.POINT,
      label: '点',
      layerId: LayerId.POINT_MAP,
    },
    {
      value: MapBusinessType.LINE,
      label: '线',
      layerId: LayerId.ROAD_MAP,
    },
    {
      value: MapBusinessType.AREA,
      label: '区域',
      layerId: LayerId.AREA_MAP,
    },
    {
      value: MapBusinessType.POINT_NAME,
      label: '点名称',
      layerId: LayerId.POINT_NAME,
    },
  ];
  const BreadcrumbItems = [
    {
      title: (
        <a
          onClick={() => {
            window.history.go(-2);
          }}
        >
          地图管理
        </a>
      ),
    },
    {
      title: (
        <a
          onClick={() => {
            window.history.go(-1);
          }}
        >
          版本信息
        </a>
      ),
    },
    {
      title: '地图信息',
    },
  ];

  useEffect(() => {
    fetchMapElement();
  }, [number, version]);

  const fetchMapElement = async () => {
    const res = await fetchApi.fetchMapElement({ version, number });
    if (res?.code === HttpStatusCode.Success) {
      setAllMapInfo(res?.data);
      if (!mapRef.current) {
        drawMap(res.data.mapInfo);
      }
    } else {
      message.error(res?.message);
    }
  };

  const drawMap = (mapInfo) => {
    const {
      width,
      height,
      offsetBottomX,
      offsetBottomY,
      offsetTopX,
      offsetTopY,
      origin,
      resolution,
    } = mapInfo;
    if (!origin) {
      return;
    }
    const originX = origin?.coordinates[0];
    const originY = origin?.coordinates[1];
    const MAP_ROTATION = (176 * Math.PI) / 180;

    // 默认缩放级别设置
    const DEFAULT_ZOOM = 10; // 可以根据需要调整：1-17，数值越大越放大
    // 使用偏移量来计算正确的地图边界
    // offsetBottomX, offsetBottomY 是左下角的偏移
    // offsetTopX, offsetTopY 是右上角的偏移
    const extent: [number, number, number, number] = [
      originX + (offsetBottomX || 0), // minX (左下角X)
      originY + (offsetBottomY || 0), // minY (左下角Y)
      originX + (offsetTopX || width * resolution), // maxX (右上角X)
      originY + (offsetTopY || height * resolution), // maxY (右上角Y)
    ];

    const projection = generateProjection(extent);

    // 计算地图中心点，使图片居中显示
    const centerX = (extent[0] + extent[2]) / 2; // (minX + maxX) / 2
    const centerY = (extent[1] + extent[3]) / 2; // (minY + maxY) / 2
    const mapCenter = [centerX, centerY];

    console.log('地图中心点:', mapCenter, 'extent:', extent);

    mapRef.current = new RobotMapTool({
      container: mapContainer.current,
      projection: projection,
      center: mapCenter, // 使用计算出的中心点
      zoom: DEFAULT_ZOOM, // 设置默认缩放级别
    });

    // 设置地图旋转
    mapRef.current.map.getView().setRotation(MAP_ROTATION);
    mapRef.current.createImgLayer(
      mapInfo.jpgFileUrl,
      projection,
      extent,
      LayerId.BASE_IMAGE_MAP,
    );

    // 让地图自动适应容器大小并居中显示
    // 延迟执行以确保地图容器已完全渲染
    setTimeout(() => {
      if (mapRef.current) {
        mapRef.current.fitToExtent(extent, [50, 50, 50, 50]); // 50像素内边距
      }
    }, 100);

    // 添加鼠标移动事件监听
    mapRef.current.map.on('pointermove', (event) => {
      const coordinates = event.coordinate;
      const node = document.querySelector('.mouse-coordinates');
      if (node) {
        node.innerHTML = `X: ${coordinates[0]}, Y: ${coordinates[1]}`;
      }
    });
  };

  const createLayer = (layerId: LayerId) => {
    let dataList;
    switch (layerId) {
      case LayerId.AREA_MAP:
        dataList = allMapInfo.areaInfoList.map((val) => {
          return {
            type: 'Polygon',
            coordinates: val.points.coordinates,
            id: val.id,
          };
        });
        break;
      case LayerId.POINT_MAP:
        dataList = allMapInfo.pointInfoList.map((val) => {
          return {
            type: 'Point',
            coordinates: [val.pointX, val.pointY],
            id: val.id,
            iconUrl: require('../../assets/image/common/map_marker.png'),
          };
        });
        break;
      case LayerId.ROAD_MAP:
        dataList = allMapInfo.voronoiInfoList.map((val) => {
          return {
            type: 'LineString',
            coordinates: val.edge.coordinates,
            id: val.id,
          };
        });
        break;
      case LayerId.POINT_NAME:
        dataList = allMapInfo.pointInfoList.map((val) => {
          return {
            type: 'Point',
            coordinates: [val.pointX, val.pointY],
            id: val.id + 'name',
            name: val.name,
          };
        });
        break;
    }
    mapRef.current.createVectorLayer(
      {
        zIndex: 120,
      },
      layerId,
      dataList,
    );
  };

  const handleChangeBusiness = (item: any, checked: boolean) => {
    if (!mapRef.current.getLayer(item.layerId)) {
      createLayer(item.layerId);
      setShowBusiness(showBusiness.concat([item.type]));
      return;
    }

    if (checked) {
      setShowBusiness(showBusiness.concat([item.type]));
    } else {
      setShowBusiness(showBusiness.filter((v) => v !== item.type));
    }
    mapRef.current.changeLayerVisible(item.layerId, checked);
  };

  return (
    <div className="map-info">
      <Breadcrumb items={BreadcrumbItems} />
      <MapUI
        ref={mapContainer}
        mapName={mapName}
        mapBusiness={mapBusiness}
        showBusiness={showBusiness}
        switchDisable={mapRef.current ? false : true}
        handleChangeBusiness={(item: any, checked: boolean) =>
          handleChangeBusiness(item, checked)
        }
      />
      <div
        className="mouse-coordinates"
        style={{
          color: '#fff',
          marginLeft: '20px',
          position: 'absolute',
          top: '200px',
        }}
      ></div>
    </div>
  );
};

export default React.memo(MapInfo);
