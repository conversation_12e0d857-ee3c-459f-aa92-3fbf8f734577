import { request } from '@/fetch/core';

export async function fetchBlackList(params: any) {
  const options: RequestOptions = {
    method: 'POST',
    path: `/k2/management/black_list/black_get_page_list`,
    urlParams: {
      pageNum: params.current,
      pageSize: params.pageSize,
    },
    body: params.searchForm,
  };
  return request(options);
}

export async function fetchUpdateStatus(blackIdList: string[], enable: string) {
  const options: RequestOptions = {
    method: `PUT`,
    path: '/k2/management/black_list/black_update_status',
    body: {
      blackIdList,
      enable,
    },
  };
  return request(options);
}

export async function editAndAddBlackList(params: any) {
  const options: RequestOptions = {
    method: `${params.id ? 'PUT' : 'POST'}`,
    path: ` ${
      params.id
        ? '/k2/management/black_list/black_edit'
        : '/k2/management/black_list/black_add'
    }`,
    body: params,
  };
  return request(options);
}

export async function checkDuplicateStationName(params: {
  id: string | null;
  customerPhone: string;
}) {
  const options: RequestOptions = {
    method: `POST`,
    path: '/k2/management/black_list/black_check_duplicate_phone',
    body: params,
  };
  return request(options);
}

export async function deleteBlackUser(blackIdList: string[]) {
  const options: RequestOptions = {
    method: `DELETE`,
    path: '/k2/management/black_list/black_delete',
    body: {
      blackIdList: blackIdList,
    },
  };
  return request(options);
}
