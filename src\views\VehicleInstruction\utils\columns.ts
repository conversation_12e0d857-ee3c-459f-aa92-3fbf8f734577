import { dropDownListKey, dropDownKey } from '@/utils/constant';
import { FormConfig } from '@/components';
export const tableColumns: any[] = [
  {
    title: '序号',
    dataIndex: 'order',
    width: 70,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '指令唯一主键',
    dataIndex: 'eventNo',
    width: 215,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '指令名称',
    dataIndex: 'name',
    width: 160,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '指令类别',
    dataIndex: 'eventGroup',
    width: 90,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '指令来源系统',
    dataIndex: 'eventSource',
    width: 120,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '是否抽取车辆实时数据',
    dataIndex: 'extractVehicleInfo',
    width: 180,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '是否抽取关联视频',
    dataIndex: 'extractVideo',
    width: 150,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '是否抽取3D数据',
    dataIndex: 'extractThreeDimension',
    width: 140,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '指令负责人ERP',
    dataIndex: 'maintainUser',
    width: 170,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '指令接收人ERP',
    dataIndex: 'receiveUser',
    width: 170,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 160,
    align: 'center',
    ellipsis: true,
    fixed: 'right',
  },
];

export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'name',
      label: '指令名称',
      placeholder: '请输入指令名称',
      type: 'input',
    },
    {
      fieldName: 'eventSource',
      label: '指令来源系统',
      placeholder: '请选择指令来源系统',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.SHADOW_EVENT_SOURCE,
      dropDownListKey: dropDownListKey.SHADOW_EVENT_SOURCE,
    },
    {
      fieldName: 'maintainUser',
      label: '指令负责人ERP',
      placeholder: '请输入ERP',
      type: 'input',
    },
  ],
};

export const basicInfo = [
  {
    name: 'name',
    label: '指令名称',
    placeHolder: '请输入指令名称',
    type: 'input',
    required: true,
    maxLength: 50,
  },
  {
    name: 'eventSource',
    label: '来源系统',
    placeHolder: '请选择来源系统',
    type: 'select',
    required: true,
  },
  {
    name: 'eventGroup',
    label: '指令类别',
    placeHolder: '请选择指令类别',
    type: 'select',
    required: true,
  },
];
