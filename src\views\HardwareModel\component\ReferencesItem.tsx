/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { Row, Col, Input, Button } from 'antd';
import { PageType } from '@/utils/enum';

const ReferencesItem = ({
  material,
  changeValue,
  type,
}: {
  material: any[];
  changeValue: Function;
  type: string;
}) => {
  const [arrData, setArrData] = useState<any[]>([]);

  useEffect(() => {
    if (material && material.length) {
      setArrData(material);
    }
    if (
      type === PageType.ADD ||
      (type === PageType.EDIT && material && material.length == 0)
    ) {
      addClick();
    }
  }, [material]);
  useEffect(() => {
    changeValue(arrData);
  }, [arrData]);

  const addClick = () => {
    setArrData([
      ...arrData,
      {
        name: '',
        address: '',
      },
    ]);
  };

  const reduceClick = (idx: number) => {
    const tempArr = [...arrData];
    tempArr.splice(idx, 1);
    setArrData([...tempArr]);
  };

  const inputBlur = (idx: number, value: any, key: string) => {
    const tempArr = [...arrData];
    const curItem = tempArr[idx];
    curItem[key] = value;
    tempArr.splice(idx, 1, curItem);
    setArrData([...tempArr]);
  };
  return (
    <>
      {arrData &&
        arrData.map((item: any, idx: number) => {
          return (
            <Row key={`material_${idx}`} style={{ marginBottom: 10 }}>
              <Col span={6}>
                <Input.TextArea
                  disabled={type === PageType.READONLY}
                  autoSize
                  placeholder="请输入链接名称"
                  maxLength={250}
                  defaultValue={item.name}
                  onBlur={(e) => inputBlur(idx, e.target.value, 'name')}
                ></Input.TextArea>
              </Col>
              <Col span={type === PageType.READONLY ? 18 : 17}>
                <Input.TextArea
                  disabled={type === PageType.READONLY}
                  autoSize
                  placeholder="请输入链接地址"
                  maxLength={500}
                  defaultValue={item.address}
                  onBlur={(e) => inputBlur(idx, e.target.value, 'address')}
                ></Input.TextArea>
              </Col>
              {type === PageType.READONLY ? null : (
                <Col span={1} className="flex flex_pack_right">
                  {idx === 0 ? (
                    <Button style={{ width: '90%' }} onClick={addClick}>
                      +
                    </Button>
                  ) : (
                    <Button
                      style={{ width: '90%' }}
                      onClick={() => reduceClick(idx)}
                    >
                      -
                    </Button>
                  )}
                </Col>
              )}
            </Row>
          );
        })}
    </>
  );
};
export default React.memo(ReferencesItem);
