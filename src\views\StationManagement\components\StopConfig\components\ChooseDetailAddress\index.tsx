import { Button, Input, message, Modal } from 'antd';
import React, { Component } from 'react';
import { TencentMap } from '@/utils/tencentMap';
import CustomButton, { ButtonType } from '@/components/CustomButton';
import DebounceAsyncSelect from '../../../DebounceSelect';
import {
  fetchPoiList,
  fetchGeocoder,
} from '@/views/StationManagement/utils/dataSearch';
import { isEmpty } from '@/utils/utils';

export default class ChooseDetailAddress extends Component<any> {
  tMap: TencentMap | null = null;
  zone: number = 0;
  state: {
    address: string | null;
    searchText: string | null;
  } = {
    address: null,
    searchText: null,
  };

  _initTencentMap() {
    this.tMap = new TencentMap({
      target: 'tencentmap-container',
      initLocation: null,
      onMarked: (info: any) => {
        const detailAddress = info ? info.detailAddress : {};
        this.setState({
          address: `${detailAddress?.country}_${detailAddress?.province}_${detailAddress?.city}_${info.detailAddress.name}`,
          searchText: `${detailAddress?.country}${detailAddress?.province}${detailAddress?.city}${info.detailAddress.name}`,
        });
      },
    });
  }

  _initPoi() {
    fetchPoiList(this.props.department?.cityObjc?.name).then((res) => {
      if (!isEmpty(res)) {
        const pos = res[0] || {};
        const [lon, lat] = pos?.value?.split('-');
        this.tMap?.setMapCenter({
          position: {
            lon: Number(lon),
            lat: Number(lat),
          },
          addMarker: true,
        });
      }
    });
  }
  componentDidMount() {
    if (this.tMap === null) {
      this._initTencentMap();
    }
    if (this.tMap) {
      this._initPoi();
    }
  }

  render() {
    return (
      <Modal
        styles={{ body: { padding: 15 } }}
        maskClosable={false}
        open={this.props.visiable}
        closable={false}
        footer={null}
        forceRender={true}
        width={1000}
      >
        <div className="select-detail-address">
          <div className="head">
            <div style={{ width: 150, fontSize: 14 }}>停靠点位置选择</div>
            <DebounceAsyncSelect
              placeholder={this.props.department?.cityObjc?.name || '请搜索'}
              onChange={(data: any) => {
                const pos = data.value
                  ?.split('-')
                  ?.reverse()
                  ?.map((i: string) => parseFloat(i));
                this.tMap?._configMarker(
                  data?.label,
                  { lat: pos[0], lng: pos[1] },
                  data?.label,
                );
                this.tMap?.setMapCenter({
                  addMarker: false,
                  position: { lat: pos[0], lon: pos[1] },
                });
                this.setState({
                  address: data.label,
                });
              }}
            />
            <div className="function">
              <CustomButton
                title="确定"
                otherCSSProperties={{ marginRight: 10 }}
                onSubmitClick={() => {
                  if (this.state.address) {
                    this.props.onSubmit
                      ? this.props.onSubmit(this.state.address)
                      : null;
                  } else {
                    message.error('请在地图上标记一个地址');
                  }
                }}
              />
              <CustomButton
                title="取消"
                buttonType={ButtonType.DefaultButton}
                onSubmitClick={() => {
                  this.props.onCancle ? this.props.onCancle() : null;
                }}
              />
            </div>
          </div>
          <div className="tencent-map-container" id="tencentmap-container" />
        </div>
      </Modal>
    );
  }
}
