import { FormConfig } from "@jd/x-coreui";

export const SearchConfig: FormConfig = {
    fields: [
        {
            fieldName: 'siteName',
            label: '站点名称',
            placeholder: '请选择站点',
            type: 'select',
            showSearch: true,
        },
        {
            fieldName: 'siteId',
            label: '站点ID',
            placeholder: '请输入站点ID',
            type: 'input',
            maxLength: 10,
        }
    ]
}

export const TableConfig: any[] = [
    {
        title: '站点名称',
        dataIndex: 'siteName',
        align: 'center',
        width: 170,
        ellipsis: true,
    },
    {
        title: '站点ID',
        dataIndex: 'siteId',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '无人车可跑行速度(KM/H)',
        dataIndex: 'runSpeed',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '无人车负载单量',
        dataIndex: 'loadLimit',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '无人车体积容载量(CM3)',
        dataIndex: 'volumeLimit',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '无人车重量容载量(KG)',
        dataIndex: 'weightLimit',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '编辑时间',
        dataIndex: 'modifyTime',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '操作',
        dataIndex: 'operation',
        align: 'center',
        width: 150,
        ellipsis: true,
        fixed: 'right',
    },
];

export const getFormConfig = (type: 'add' | 'edit'): FormConfig => {
    return {
        fields: [
            {
                fieldName: 'siteName',
                label: '站点名称',
                placeholder: '请输入站点名称',
                type: 'input',
                maxLength: 20,
                wrapperCol: { span: 24 },
                required: true,
                disabled: type === 'edit',
                validatorRules: [{ required: true, message: '请输入站点名称' }],
            },
            {
                fieldName: 'siteId',
                label: '站点ID',
                placeholder: '请输入站点ID',
                type: 'input',
                maxLength: 20,
                wrapperCol: { span: 24 },
                required: true,
                disabled: type === 'edit',
                validatorRules: [{ required: true, message: '请输入站点ID' }],
            },
            {
                fieldName: 'runSpeed',
                label: '无人车可跑行速度(KM/H)',
                placeholder: '请输入无人车可跑行速度(KM/H)',
                type: 'inputNumber',
                min: 1,
                max: 100,
                wrapperCol: { span: 24 },
                required: true,
                validatorRules: [{ required: true, message: '请输入无人车可跑行速度(KM/H)' }],
            },
            {
                fieldName: 'loadLimit',
                label: '无人车负载单量',
                placeholder: '请输入无人车负载单量',
                type: 'inputNumber',
                min: 1,
                max: 99999,
                wrapperCol: { span: 24 },
                required: true,
                validatorRules: [{ required: true, message: '请输入无人车负载单量' }],
            },
            {
                fieldName: 'volumeLimit',
                label: '无人车体积容载量(CM3)',
                placeholder: '请输入无人车体积容载量(CM3)',
                type: 'inputNumber',
                min: 1,
                max: 99999,
                wrapperCol: { span: 24 },
            },
            {
                fieldName: 'weightLimit',
                label: '无人车重量容载量(KG)',
                placeholder: '请输入无人车重量容载量(KG)',
                type: 'inputNumber',
                min: 1,
                max: 99999,
                wrapperCol: { span: 24 },
            },
        ]
    }
}

export const ExcelColumnConfig: any[] = [
    {
        key: 'siteName',
        title: '站点名称',
        required: true,
        type: 'string',
    },
    {
        key: 'siteId',
        title: '站点id',
        type: 'string',
        required: true,
    },
    {
        key: 'runSpeed',
        title: '无人车可跑行速度(KM/H)',
        type: 'number',
        required: true,
        transform: (value: any) => {
            if (!value) {
                return null;
            }
            return Number(value);
        },
    },
    {
        key: 'loadLimit',
        title: '无人车负载单量',
        type: 'number',
        required: true,
        transform: (value: any) => {
            if (!value) {
                return null;
            }
            return Number(value);
        },
    },
    {
        key: 'volumeLimit',
        title: '无人车体积容载量(CM3)',
        type: 'number',
        required: false,
        transform: (value: any) => {
            if (!value) {
                return null;
            }
            return Number(value);
        },
    },
    {
        key: 'weightLimit',
        title: '无人车重量容载量(KG)',
        type: 'number',
        required: false,
        transform: (value: any) => {
            if (!value) {
                return null;
            }
            return Number(value);
        },
    },
]