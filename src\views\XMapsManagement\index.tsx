import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { DEFAULT_PAGINATION, xMapInfoColumns } from './utils/column';
import { Select, Table } from 'antd';
import './index.scss';
import { useInfoData, useMapIdList } from './utils/utils';

const XMapInfoManegement = () => {
  const navigator = useNavigate();
  const mapIdList = useMapIdList();
  const [searchConditions, setSearchConditions] = useState<{
    currentPage: number;
    pageSize: number;
    mapId?: number | null;
  }>({
    currentPage: DEFAULT_PAGINATION.currentPage,
    pageSize: DEFAULT_PAGINATION.pageSize,
  });
  const formatColumns = () => {
    return xMapInfoColumns.map((column: any) => {
      if (column.dataIndex === 'operation') {
        column.render = (text: any, record: any) => {
          return (
            <>
              <span
                style={{ color: '#1890ff', cursor: 'pointer' }}
                onClick={() => {
                  navigator(`/app/xMapVersionManagement?id=${record.id}`);
                }}
              >
                查看已发版地图
              </span>
            </>
          );
        };
      }
      return column;
    });
  };
  const { infoData, total } = useInfoData(searchConditions);
  const onChangeSearchCondition = (
    mapId: number | null,
    currentPage = DEFAULT_PAGINATION.currentPage,
    pageSize = DEFAULT_PAGINATION.pageSize
  ) => {
    setSearchConditions({
      mapId,
      currentPage,
      pageSize,
    });
    return;
  };
  return (
    <div className="form-table-container">
      <span className="search-zone">
        地图名称：
        <Select
          showSearch
          placeholder="请选择要查看的地图"
          allowClear
          options={mapIdList}
          onChange={(value) => {
            onChangeSearchCondition(value);
          }}
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
          style={{ flexGrow: 1 }}
        ></Select>
      </span>
      <Table
        bordered
        columns={formatColumns()}
        dataSource={infoData}
        onChange={(pagination) => {
          setSearchConditions({
            ...searchConditions,
            currentPage: pagination.current as number,
            pageSize: pagination.pageSize as number,
          });
        }}
        pagination={{
          current: searchConditions.currentPage,
          pageSize: searchConditions.pageSize,
          total: total,
          pageSizeOptions: [100, 200, 300],
        }}
        rowKey={'id'}
        scroll={{ y: 750 }}
      ></Table>
    </div>
  );
};
export default React.memo(XMapInfoManegement);
