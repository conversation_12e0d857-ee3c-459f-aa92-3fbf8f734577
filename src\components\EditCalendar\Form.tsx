import { Button, Form, Input, InputNumber, Space } from 'antd';
import React from 'react';

export interface ComplexFormProps {
  onOk: (value: any) => void;
  onClear?: () => void;
}

const ComplexForm: React.FC<ComplexFormProps> = (props) => {
  const [form] = Form.useForm();
  const { onOk, onClear } = props;
  return (
    <div className="complex-form-wrapper">
      <Form name="complexForm" form={form}>
        <div className="form-group_item">
          <Space>
            <Form.Item name="order1" label="目标车效：周一至周四：">
              <InputNumber
                placeholder="请输入数字"
                min={0}
                keyboard={false}
                controls={false}
                parser={(displayValue: any) =>
                  displayValue ? Math.abs(displayValue) : displayValue
                }
              />
            </Form.Item>
            <span>单</span>
          </Space>
          <Space>
            <Form.Item name="order2" label="周五：">
              <InputNumber
                placeholder="请输入数字"
                min={0}
                keyboard={false}
                controls={false}
                parser={(displayValue: any) =>
                  displayValue ? Math.abs(displayValue) : displayValue
                }
              />
            </Form.Item>
            <span>单</span>
          </Space>
          <Space>
            <Form.Item name="order3" label="周六：">
              <InputNumber
                placeholder="请输入数字"
                min={0}
                keyboard={false}
                controls={false}
                parser={(displayValue: any) =>
                  displayValue ? Math.abs(displayValue) : displayValue
                }
              />
            </Form.Item>
            <span>单</span>
          </Space>
          <Space>
            <Form.Item name="order4" label="周日：">
              <InputNumber
                placeholder="请输入数字"
                min={0}
                keyboard={false}
                controls={false}
                parser={(displayValue: any) =>
                  displayValue ? Math.abs(displayValue) : displayValue
                }
              />
            </Form.Item>
            <span>单</span>
          </Space>
        </div>
        <div className="form-group_item">
          <Space>
            <Form.Item name="efficiency1" label="跑行车数：工作日：">
              <InputNumber
                placeholder="请输入数字"
                min={0}
                keyboard={false}
                controls={false}
                parser={(displayValue: any) =>
                  displayValue ? Math.abs(displayValue) : displayValue
                }
              />
            </Form.Item>
            <span>辆</span>
          </Space>
          <Space>
            <Form.Item name="efficiency2" label="周末：">
              <InputNumber
                placeholder="请输入数字"
                min={0}
                keyboard={false}
                controls={false}
                parser={(displayValue: any) =>
                  displayValue ? Math.abs(displayValue) : displayValue
                }
              />
            </Form.Item>
            <span>辆</span>
          </Space>
          <Form.Item>
            <Button
              type="primary"
              style={{
                backgroundColor: 'rgba(60,110,240,1)',
              }}
              onClick={() => {
                const values = form.getFieldsValue();
                onOk(values);
              }}
            >
              应用
            </Button>
          </Form.Item>

          <Button
            onClick={() => {
              form.resetFields();
            }}
          >
            清空快捷录入
          </Button>
          <Button
            onClick={() => {
              onClear && onClear();
            }}
          >
            清空该月目标
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default ComplexForm;
