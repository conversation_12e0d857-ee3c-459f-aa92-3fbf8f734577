/* eslint-disable no-unused-vars */
/* eslint-disable new-cap */
import { Button, Form, message, Modal } from 'antd';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Breadcrumb from '@/components/BreadCrumb';
import Loading from '@/components/Loading';
import FormTitle from '@/components/FormTitle';
import BasicInfo from '../components/BasicInfo';
import LocationInfo from '../components/LocationInfo';
import BusinessInfo from '../components/BusinessInfo';
import { StationFetchApi } from '@/fetch/business';
import './index.scss';
import { useCommonDropDown } from '@/utils/hooks';
import { HttpStatusCode } from '@/fetch/core/constant';
import { formatLocation } from '@/utils/utils';
import { StationDetailTabKeys } from '../../StationDetail';
import { set } from 'lodash';

const win = window as any;
const { rad } = win;
const breadCrumbItems = [
  {
    title: '停靠点列表',
    route: '',
  },
  {
    title: '停靠点配置',
    route: '',
  },
];

const StopPointEdit = () => {
  const urlData = formatLocation(window.location.search);
  const fetchApi = new StationFetchApi();
  const navigator = useNavigate();
  const [detailData, setDetailData] = useState<any>();
  const [transpotPointlist, setTranspotPointlist] = useState<any>();

  const [submiting, setSubmiting] = useState(false);
  const [currentDepartment, setCurrentDepartment] = useState<any>();
  const [currentStopType, setCurrentStopType] = useState<any>(null);
  const [basicInfoForm] = Form.useForm();
  const [locationInfoForm] = Form.useForm();
  const [bussinessInfoForm] = Form.useForm();
  const dropDownData = useCommonDropDown(['STOP_TYPE']);
  const fetchStopDetail = async () => {
    if (urlData.stopId) {
      const response: any = await fetchApi.getStopDetail(urlData.stopId);
      if (response.code === HttpStatusCode.Success) {
        setDetailData({
          ...response.data,
          number: urlData.type === 'add' ? null : response.data.number,
          id: urlData.type === 'add' ? null : response.data.id,
        });
        setCurrentStopType(response.data.type);
      }
    }

    //获取终端可用接驳点列表
    const transportRes: any = await fetchApi.getTransportPointList(
      urlData.stationBaseId,
    );
    if (transportRes.code === HttpStatusCode.Success) {
      setTranspotPointlist(transportRes.data);
    }

    // 新建时获取站点的基础信息
    const res = await fetchApi.fetchStationBasicInfo(urlData.stationBaseId);
    if (res.code === HttpStatusCode.Success) {
      setCurrentDepartment({
        cityObjc: {
          code: res.data.cityId,
          name: res.data.cityName,
          parent: null,
        },
        stationObjc: {
          code: res.data.stationBaseId,
          name: res.data.name,
          parent: null,
        },
      });
    } else {
      message.error(res.message);
    }
  };
  useEffect(() => {
    fetchStopDetail();
  }, []);

  useEffect(() => {
    detailData && basicInfoForm.setFieldValue('type', detailData.type);
  }, [dropDownData, detailData]);
  const onSubmit = async () => {
    try {
      const hasDobble = new Set();
      const basic = await basicInfoForm.validateFields();
      const location = await locationInfoForm.validateFields();
      const business = await bussinessInfoForm.validateFields();
      const collectionUserlist =
        business.businessUserList?.stopCollectionUserList || [];
      const connectionUserlist =
        business.businessUserList?.stopConnectionUserList || [];
      const stopCollectionUserList: any[] = [];
      const stopConnectionUserList: any[] = [];
      const defaultUserList: any[] = [];
      if (collectionUserlist) {
        collectionUserlist.forEach((item: any) => {
          if (item.isDefault === 1) {
            defaultUserList.push(item);
          }
          stopCollectionUserList.push({
            id: item.newAdd ? null : item.id,
            name: item.name,
            contact: item.contact,
            enable: parseInt(item.enable),
            isDefault: item.isDefault,
          });
        });
      }
      if (stopCollectionUserList?.length > 0 && defaultUserList?.length <= 0) {
        message.error('请配置默认接驳人');
        return;
      }
      if (connectionUserlist) {
        connectionUserlist.forEach((item: any) => {
          if (item.isDefault === 1) {
            defaultUserList.push(item);
          }
          stopConnectionUserList.push({
            id: item.newAdd ? null : item.id,
            name: item.name,
            contact: item.contact,
            enable: parseInt(item.enable),
            isDefault: item.isDefault,
          });
        });
      }
      if (stopConnectionUserList?.length > 0 && defaultUserList?.length <= 0) {
        message.error('请配置默认代收人');
        return;
      }
      let parkingSpotList = [];
      let hasUnComplete = false;
      if (location.parkingSpotList.length > 0) {
        parkingSpotList = location.parkingSpotList.map(
          (item: any, index: number) => {
            if (
              item.name === null ||
              item.name === undefined ||
              item.name?.length <= 0 ||
              item.longitude === null
            ) {
              hasUnComplete = true;
            }
            hasDobble.add(item.name);
            return {
              id: item.newAdd ? null : item.id,
              name: item.name,
              latitude: item.latitude,
              longitude: item.longitude,
              heading: rad(item.heading),
              level: index + 1,
            };
          },
        );
      }
      if (hasUnComplete) {
        message.error('请完善停车点信息', 3);
        return;
      }
      if (
        parkingSpotList.length !== 0 &&
        hasDobble.size !== parkingSpotList.length
      ) {
        message.error('操作失败，存在重复的停车点名称！', 3);
        return;
      }
      const formValue =
        urlData.type === 'add'
          ? {
              name: basic.name,
              type: basic.type,
              thirdId: basic.thirdId,
              thirdName: basic.thirdName,
              stationBaseId: Number(urlData.stationBaseId),
              parkingSpotList,
              ...business,
              waitingTime: Number(business.waitingTime),
              keyWordList: business.keyWordList ?? [],
              stopCollectionUserList,
              stopConnectionUserList,
              addressName: basic.addressName,
              parkPictureList: basic.parkPictureList,
            }
          : {
              id: basic.id,
              number: basic.number,
              name: basic.name,
              thirdId: basic.thirdId,
              thirdName: basic.thirdName,
              type: basic.type,
              stationBaseId: Number(urlData.stationBaseId),
              parkingSpotList,
              ...business,
              waitingTime: Number(business.waitingTime),
              keyWordList: business.keyWordList ?? [],
              stopCollectionUserList,
              stopConnectionUserList,
              addressName: basic.addressName,
              parkPictureList: basic.parkPictureList,
            };
      const result: any =
        urlData.type === 'add'
          ? await fetchApi.addStationStop(formValue)
          : await fetchApi.editStationStop(formValue);
      if (result.code === HttpStatusCode.Success) {
        navigator(
          `/app/stationManagement/detail?stationBaseId=${urlData.stationBaseId}&tabKey=${StationDetailTabKeys.STOP_CONFIG}`,
        );
        message.success(result.message);
      } else {
        Modal.warning({
          title: '提示',
          content: result.message,
        });
      }
      setSubmiting(false);
    } catch (e) {
      console.log(e);
    } finally {
      //
    }
  };
  return (
    <div className="stoppoint-edit">
      <div className="bread-crub">
        <Breadcrumb items={breadCrumbItems} />
      </div>
      <div className="content">
        <FormTitle title="停靠点配置" />
        <BasicInfo
          type={urlData.type}
          form={basicInfoForm}
          configData={dropDownData.stopTypeList}
          transportData={transpotPointlist}
          editInfo={detailData}
          cityStation={currentDepartment}
          stationBaseId={urlData.stationBaseId}
          onStationTypeChanged={(stopType: any) => {
            setCurrentStopType(stopType);
          }}
        />
        <LocationInfo
          form={locationInfoForm}
          editInfo={detailData}
          department={currentDepartment}
          onValueChanged={() => {}}
        />
        <BusinessInfo
          form={bussinessInfoForm}
          editInfo={detailData}
          stopType={currentStopType}
        />
        <div className="function">
          <Button className="btn" type="primary" onClick={onSubmit}>
            确定
          </Button>
          <Button
            className="cancel btn"
            onMouseDown={() => {
              navigator(
                `/app/stationManagement/detail?stationBaseId=${urlData.stationBaseId}&tabKey=${StationDetailTabKeys.STOP_CONFIG}`,
              );
            }}
          >
            取消
          </Button>
        </div>
      </div>
      <Loading fetching={submiting} />
    </div>
  );
};
export default React.memo(StopPointEdit);
