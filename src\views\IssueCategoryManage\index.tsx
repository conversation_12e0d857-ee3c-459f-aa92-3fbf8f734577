import React, { useState, useEffect, useRef } from 'react';
import { CommonTable, CommonForm, useTableData } from '@jd/x-coreui';
import { searchConfig, tableColumns } from './utils/columns';
import { IssueCateGoryManageApi } from '@/fetch/business';
import { useSelector, useDispatch } from 'react-redux';
import { saveSearchValues } from '@/redux/reducer/searchForm';
import { RootState } from '@/redux/store';
import { TableOperateBtn } from '@/components';
import { useNavigate } from 'react-router-dom';
import './index.scss';
import { PageType } from '@/utils/EditTitle';
import { Popconfirm, message } from 'antd';

import { HttpStatusCode } from '@/fetch/core/constant';

const IssueCategoryManage = () => {
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const searchFormRef = useRef<any>(null);
  const fetchApi = new IssueCateGoryManageApi();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const initSearchCondition = {
    searchForm: {
      bugCategoryName: null,
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValues.searchValues
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );

  const { tableData, loading, reloadTable } = useTableData(
    {
      searchForm: {
        ...searchCondition.searchForm,
      },
      pageNum: searchCondition.pageNum,
      pageSize: searchCondition.pageSize,
    },
    fetchApi.fetchTableList,
  );
  useEffect(() => {}, []);

  const middleBtns: any[] = [
    {
      show: true,
      title: '新建',
      key: 'getNewMR',
      onClick: () => handleEdit(PageType.ADD),
    },
  ];
  const handleEdit = (type: PageType, id?: number) => {
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: searchCondition,
      }),
    );
    navigator(
      id
        ? `/app/issueCategoryManage/edit?type=${type}&id=${id}`
        : `/app/issueCategoryManage/edit?type=${type}`,
    );
  };

  const formateColumns = () => {
    return tableColumns.map((col) => {
      switch (col.dataIndex) {
        case 'labelCount':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return (
                <a
                  key={record.labelCount}
                  onClick={() => {
                    navigator(
                      `/app/issueLabelManage?bugCategoryId=${record.bugCategoryId}&bugCategoryName=${record.bugCategoryName}`,
                    );
                  }}
                >
                  {record.labelCount}
                </a>
              );
            },
          };

        case 'operation':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return (
                <div className="operate">
                  <TableOperateBtn
                    title="编辑"
                    handleClick={() => {
                      handleEdit(PageType.EDIT, record.bugCategoryId);
                    }}
                  />
                  <Popconfirm
                    placement="left"
                    title={`请确认删除该【${record.bugCategoryName}】分类吗？`}
                    onConfirm={() => {
                      fetchApi
                        .deleteCateGoryInfo({
                          bugCategoryId: record.bugCategoryId,
                        })
                        .then((res) => {
                          if (res.code === HttpStatusCode.Success) {
                            message.success('删除成功');
                            reloadTable();
                          } else {
                            message.error(res.message);
                          }
                        });
                    }}
                    okText="确定"
                    cancelText="取消"
                    overlayStyle={{ maxWidth: 800 }}
                  >
                    <a
                      style={{
                        marginRight: '5px',
                        marginLeft: '5px',
                        color: 'red',
                        cursor: 'pointer',
                      }}
                    >
                      删除
                    </a>
                  </Popconfirm>
                </div>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };
  const onSearchClick = (val) => {
    const data = {
      searchForm: {
        ...val,
      },
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(data);
  };

  return (
    <div className="issue-category-manage">
      <CommonForm
        formConfig={searchConfig}
        layout="inline"
        formType="search"
        colon={false}
        getFormInstance={(ref: any) => {
          searchFormRef.current = ref;
        }}
        onSearchClick={onSearchClick}
        onResetClick={() => {
          setSearchCondition({ ...initSearchCondition });
          searchFormRef.current.resetFields();
        }}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        columns={formateColumns()}
        loading={loading}
        middleBtns={middleBtns}
        onPageChange={(value: any) => setSearchCondition(value)}
      />
    </div>
  );
};

export default React.memo(IssueCategoryManage);
