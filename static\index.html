<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script type="text/javascript" src="/utils/geotools.js"></script>
    <script type="text/javascript" src="/utils/proj4.js"></script>

    <script
      charset="utf-8"
      src="https://map.qq.com/api/js?v=2.exp&key=KRBBZ-VJEWS-Q3IOZ-67LGG-RVU2E-UEF6X&libraries=convertor"
    ></script>
    <title>无人车基础数据管理平台</title>
  </head>

  <body>
    <div id="app"></div>
    <script
      crossorigin
      src="https://sgm-static.jd.com/sgm-web-3.3.0.js"
      name="SGMH5"
      sid="d919921f9d354a8c8825e8b2983e3826"
      pid="9HwAEg@Ux9SXXVyhQ0XAxbt"
    ></script>
    <script
      src="https://storage.360buyimg.com/jdx-autopilot-ota/moment.js?Expires=3811729725&AccessKey=n828WHAXD584pTvi&Signature=1UdTdYNvR2aiI2DWljTvLc87Ddg%3D"
      defer
    ></script>
    <script src="//storage.360buyimg.com/fatal-solution/jquery-3.5.1.min.js?Expires=3794216731&AccessKey=n828WHAXD584pTvi&Signature=ez379PR4BFr2HvMfTKiukBeoidU%3D"></script>
    <!--奇点 JS SDK 接入代码-->
    <script>
      (function () {
        var _ = window;
        _.__qd__ ||
          (_.__qd__ = {
            config: {
              sid:
                window.location.host === 'jdxvehicle-ui.jdl.cn'
                  ? 'UB408'
                  : 'U9DIT',
              pageId: function () {
                // key 为路由名称，值为页面ID
                const pathnameList = window.location.pathname.split('/');
                pathnameList.shift();
                if (window.location.host === 'jdxvehicle-ui.jdl.cn') {
                  return pathnameList.join('_');
                }
                return pathnameList.join('');
              },
            },
          });
      })();
    </script>
    <script
      async
      name="qidian-sdk"
      crossorigin="anonymous"
      src="//qdsdk.jd.com/qd.js"
    ></script>
  </body>
</html>
