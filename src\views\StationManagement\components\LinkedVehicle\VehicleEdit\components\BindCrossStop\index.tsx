import React, { useEffect, useState } from 'react';
import CrossStopCard from '../CrossStopCard';
import './index.scss';
import { Row, Col, message, Button, Modal } from 'antd';
import { HttpStatusCode } from '@/fetch/core/constant';
import { BindCrossStopPageType } from '@/utils/constant';
import { StationFetchApi } from '@/fetch/business';
interface Props {
  cityId: any;
  stationId: any;
  type: string;
  crossStopInfo: any[];
  onHandleStop: Function;
}
const fetchApi = new StationFetchApi();
const BindCrossStop = (props: Props) => {
  const { cityId, stationId, type, crossStopInfo, onHandleStop } = props;
  const [stationOptions, setStationOptions] = useState<any[]>([]);

  useEffect(() => {
    getStationOptions();
  }, [stationId, cityId]);

  const getStationOptions = () => {
    if (!stationId && type === BindCrossStopPageType.SINGLE) {
      message.error('请先选择本站站点！');
    } else {
      fetchApi
        .getStationInfoListOfCity({
          cityId: cityId,
          exclusionStationBaseIdList: stationId ? [stationId] : [],
          type: type,
        })
        .then((res: any) => {
          if (res && res.code === HttpStatusCode.Success) {
            setStationOptions(
              res.data?.map((value: any) => {
                return {
                  label: value.name,
                  value: value.id,
                };
              }),
            );
          }
        });
    }
  };

  const onAddCard = async () => {
    if (!stationId && type === BindCrossStopPageType.SINGLE) {
      message.error('请先选择本站站点！');
      return;
    }

    const invalidValue = crossStopInfo?.filter(function (item) {
      if (!item.stationInfo || item.homeList?.length <= 0) {
        return item;
      }
    });
    if (invalidValue.length > 0) {
      message.error('请先完善当前未完成站点信息！');
      return;
    }
    crossStopInfo.push({
      sort: Date.now(),
      stationInfo: null,
      homeList: [],
      stopList: [],
    });
    onHandleStop([...crossStopInfo]);
  };

  return (
    <div className="bind-cross-stop">
      <Row justify="center">
        <Col span={19}>
          {crossStopInfo?.map((item: any, index: any) => {
            return (
              <CrossStopCard
                key={index}
                stationOptions={stationOptions}
                info={item}
                saveInfo={(values: any) => {
                  onHandleStop(values);
                }}
                crossStopInfo={crossStopInfo}
              />
            );
          })}
          <Button className="add-btn" onClick={onAddCard}>
            +添加一个站点
          </Button>
        </Col>
      </Row>
    </div>
  );
};

export default React.memo(BindCrossStop);
