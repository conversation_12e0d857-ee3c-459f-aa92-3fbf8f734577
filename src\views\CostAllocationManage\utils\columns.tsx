import { FormConfig } from '@jd/x-coreui';

export const CostAllocationColumns = [
  {
    title: '划拨年月',
    dataIndex: 'recordDate',
    fixed: 'left',
    width: 100,
  },
  {
    title: '省份',
    dataIndex: 'provinceName',
    width: 100,
  },
  {
    title: '城市',
    dataIndex: 'cityName',
    width: 100,
  },
  {
    title: '站点',
    dataIndex: 'stationName',
    width: 150,
  },
  {
    title: '站点状态',
    dataIndex: 'stationStatusName',
    width: 100,
  },
  {
    title: '部署完成日期',
    dataIndex: 'deploymentCompletionDate',
    width: 150,
  },
  {
    title: '运营车数',
    dataIndex: 'deviceCount',
    width: 100,
  },
  {
    title: '预计收入',
    dataIndex: 'expectedRevenue',
    width: 100,
  },
  {
    title: '实际收入',
    dataIndex: 'actualRevenue',
    width: 110,
  },
  {
    title: '最后操作人',
    dataIndex: 'modifyUser',
    width: 150,
  },
  {
    title: '最后操作时间',
    dataIndex: 'modifyTime',
    width: 220,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    fixed: 'right',
    ellipsis: true,
    width: 220,
  },
];

export const CostAllocationFormConfig: FormConfig = {
  fields: [
    {
      label: '划拨年月',
      fieldName: 'timeRange',
      type: 'rangeTime',
      format: 'YYYY-MM',
      showTime: false,
      picker: 'month',
      labelCol: { span: 3 },
      wrapperCol: { span: 21 },
      xl: 12,
      lg: 12,
      xxl: 12,
    },
    {
      label: '省市站',
      fieldName: 'stationInfo',
      type: 'cascader',
      options: [],
      mapRelation: {
        label: 'name',
        value: 'id',
        children: 'children',
      },
      labelCol: { span: 3 },
      wrapperCol: { span: 21 },
      xl: 12,
      lg: 12,
      xxl: 12,
    },
  ],
};
