/* eslint-disable no-unused-vars */

import { Col, Form, Input, Modal, Row, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { SensorSolutionApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';

const oldConfigData: any = {
  sensorName: null,
  usageName: null,
};
const SensorEditModal = ({
  visable,
  itemValue,
  onSubmit,
  onCancel,
}: {
  visable: boolean;
  itemValue?: any;
  onSubmit: Function;
  onCancel: Function;
}) => {
  const fetchApi = new SensorSolutionApi();
  const [editForm] = Form.useForm();
  const [configValues, setConfigValues] = useState<{
    sensorList: { code?: string; name: string; parent?: string }[] | null;
    usageList: { code?: string; name: string; parent?: string }[] | null;
    hardwearList: { code?: string; name: string; parent?: string }[] | null;
  }>({
    sensorList: null,
    usageList: null,
    hardwearList: null,
  });
  const makeSelectOptions = (list: any) => {
    if (list) {
      return list.map((item: any) => {
        return {
          label: item.name,
          value: JSON.stringify(item),
          key: item.name,
        };
      });
    }
    return [];
  };

  useEffect(() => {
    avaiableCongier();
  }, []);

  const fetchConfigList = async (
    type: 'sensorList' | 'usageList' | 'hardwearList',
    id: any,
  ) => {
    const response: any = await fetchApi.fetchLinkedListData(type, id);
    if (response && response.code === HttpStatusCode.Success) {
      const list: any = response.data?.filter((e: any) => e.enable === 1);
      if (type === 'sensorList') {
        setConfigValues({
          ...configValues,
          sensorList: list,
        });
      } else if (type === 'usageList') {
        setConfigValues({
          ...configValues,
          usageList: list,
        });
      } else if (type === 'hardwearList') {
        setConfigValues({
          ...configValues,
          hardwearList: list,
        });
      }
    }
  };

  useEffect(() => {
    if (itemValue) {
      editForm.setFieldsValue({
        ...itemValue,
      });
    }
  }, [itemValue]);

  const avaiableCongier = async () => {
    if (itemValue) {
      const result: any = {};
      const sensor: any = await fetchApi.fetchLinkedListData(
        'sensorList',
        null,
      );
      if (sensor && sensor.code === HttpStatusCode.Success) {
        result.sensorList = sensor.data;
      }
      const usage: any = await fetchApi.fetchLinkedListData(
        'usageList',
        itemValue.hardwareTypeId,
      );
      if (usage && usage.code === HttpStatusCode.Success) {
        result.usageList = usage.data;
      }
      const hardware: any = await fetchApi.fetchLinkedListData(
        'hardwearList',
        itemValue.hardwareTypeUsageId,
      );
      if (hardware && hardware.code === HttpStatusCode.Success) {
        result.hardwearList = hardware.data;
      }
      setConfigValues(result);
    } else {
      fetchConfigList('sensorList', null);
    }
  };

  useEffect(() => {
    return () => {
      oldConfigData.sensorName = null;
      oldConfigData.usageName = null;
    };
  }, []);

  return (
    <Modal
      width="50vw"
      maskClosable={false}
      title={itemValue != null ? '编辑方案' : '新建方案'}
      closable={false}
      visible={visable}
      okText="确定"
      cancelText="取消"
      onOk={async () => {
        const editValue = await editForm.validateFields();
        if (!itemValue) {
          editValue.id = Date.now();
          editValue.newAdd = true;
          editValue.enable = 1;
        }
        onSubmit ? onSubmit({ ...itemValue, ...editValue }) : null;
      }}
      onCancel={() => {
        onCancel ? onCancel() : null;
      }}
    >
      <Form form={editForm}>
        <Row gutter={20}>
          <Col span={12}>
            <Form.Item name="hardwareTypeId" style={{ margin: 0, height: 0 }}>
              <div></div>
            </Form.Item>
            <Form.Item
              wrapperCol={{ span: 16 }}
              name="hardwareTypeName"
              label="传感器硬件类型"
              rules={[{ required: true, message: '请选择传感器硬件类型' }]}
            >
              <Select
                options={makeSelectOptions(configValues.sensorList)}
                placeholder="请选择传感器硬件类型"
                onChange={(value: any, ops: any) => {
                  const item = JSON.parse(value);
                  let reloadFormValue: any = {
                    hardwareTypeId: item.code,
                    hardwareTypeName: item.name,
                  };
                  if (item.name !== oldConfigData.sensorName) {
                    oldConfigData.sensorName = item.name;
                    reloadFormValue = {
                      hardwareTypeId: item.code,
                      hardwareTypeName: item.name,
                      hardwareTypeUsageName: null,
                      hardwareTypeUsageId: null,
                      hardwareModelName: null,
                      hardwareModelId: null,
                      hardwareModelModel: null,
                    };
                    fetchConfigList('usageList', item.code);
                  }
                  editForm.setFieldsValue(reloadFormValue);
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="hardwareTypeUsageId"
              style={{ margin: 0, height: 0 }}
            >
              <div></div>
            </Form.Item>
            <Form.Item
              wrapperCol={{ span: 16 }}
              name="hardwareTypeUsageName"
              label="用途"
              rules={[{ required: true, message: '请选择用途' }]}
            >
              <Select
                options={makeSelectOptions(configValues.usageList)}
                placeholder="请选择用途"
                onChange={(value: any) => {
                  const item = JSON.parse(value);
                  let reloadFormValue: any = {
                    hardwareTypeUsageName: item.name,
                    hardwareTypeUsageId: item.code,
                  };
                  if (item.name !== oldConfigData.usageName) {
                    oldConfigData.usageName = item.name;
                    reloadFormValue = {
                      hardwareTypeUsageName: item.name,
                      hardwareTypeUsageId: item.code,
                      hardwareModelName: null,
                      hardwareModelId: null,
                      hardwareModelModel: null,
                    };
                    fetchConfigList('hardwearList', item.code);
                  }
                  editForm.setFieldsValue(reloadFormValue);
                }}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={20}>
          <Col span={12}>
            <Form.Item name="hardwareModelId" style={{ margin: 0, height: 0 }}>
              <div></div>
            </Form.Item>
            <Form.Item
              wrapperCol={{ span: 16 }}
              name="hardwareModelName"
              label="传感器硬件型号"
              rules={[{ required: true, message: '请选择传感器硬件型号' }]}
            >
              <Select
                options={makeSelectOptions(configValues.hardwearList)}
                placeholder="请选择硬件名称"
                onChange={(value: any) => {
                  const item = JSON.parse(value);
                  editForm.setFieldsValue({
                    hardwareModelName: item.name,
                    hardwareModelId: item.code,
                    hardwareModelModel: item.parent,
                  });
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="hardwareModelModel" label="型号" required>
              <Input disabled placeholder="系统生成" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={20}>
          <Col span={8}>
            <Form.Item
              name="useNumber"
              label="使用个数"
              rules={[
                {
                  required: true,
                  message: <div style={{ color: 'red' }}>请输入使用个数</div>,
                },
              ]}
            >
              <Input
                placeholder="请输入使用个数"
                maxLength={3}
                onChange={(e) => {
                  let value = e.target.value;
                  const reg = isNaN(parseInt(value)); // 验证this.value是否为数字
                  if (reg) {
                    // 如果this.value不为数字，则reg为true
                    value = value.replace(/[^1-9]*$/g, '');
                  }
                  if (`${value}`.indexOf('0') === 0) {
                    // 此步想“ 阻止 ”用户输入的首个数字为0
                    value = '0';
                  }
                  if (value && `${value}`.length <= 0) {
                    editForm.setFieldsValue({
                      useNumber: null,
                    });
                  } else {
                    editForm.setFieldsValue({
                      useNumber: value,
                    });
                  }
                }}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default React.memo(SensorEditModal);
