import React, { useEffect, useRef, useState } from 'react';
import {
  Button,
  Calendar,
  Col,
  ConfigProvider,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
  Space,
  message,
} from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs, { Dayjs } from 'dayjs';
import 'dayjs/locale/zh-cn';
import './index.scss';
import ComplexForm from './Form';
import { AnyFunc } from '@/global';
dayjs.locale('zh-cn');

export interface EditCalendarProps {
  title?: string;
  id: number;
  onSubmit?: AnyFunc;
  onChange?: AnyFunc;
  defaultDate?: Dayjs;
  targetOrderList?: any[];
  vehicleEffect?: any;
}
const EditCalendar: React.FC<EditCalendarProps> = (props) => {
  const {
    title,
    onSubmit,
    onChange,
    id,
    targetOrderList,
    vehicleEffect,
    defaultDate,
  } = props;
  const [form] = Form.useForm();
  const [selectDate, setSelectDate] = useState<any>(dayjs());
  const dateValueRef = useRef<any>(new Map());
  const [readOnly, setReadOnly] = useState<boolean>(true);
  const [orderSetting, setOrderSetting] = useState<any>({});
  const handleEdit = () => {
    setReadOnly(false);
    form.setFieldsValue(orderSetting);
  };

  const formatData = (obj: any) => {
    for (const key in obj) {
      if (obj[key] === undefined) {
        delete obj[key];
      }
    }

    return obj;
  };
  const onOk = (values: any) => {
    const newValues = formatData(values);
    const { order1, order2, order3, order4, efficiency1, efficiency2 } =
      newValues;
    const monToThurs = order1 * efficiency1;
    const friday = order2 * efficiency1;
    const saturday = order3 * efficiency2;
    const sunday = order4 * efficiency2;
    for (const [key, value] of dateValueRef.current) {
      if ([1, 2, 3, 4].includes(value) && !isNaN(monToThurs)) {
        form.setFieldValue(key, monToThurs);
      } else if (value === 5 && !isNaN(friday)) {
        form.setFieldValue(key, friday);
      } else if (value === 6 && !isNaN(saturday)) {
        form.setFieldValue(key, saturday);
      } else if (value === 0 && !isNaN(sunday)) {
        form.setFieldValue(key, sunday);
      }
    }
  };

  const onSelect = (
    value: Dayjs,
    info: { source: 'year' | 'month' | 'date' | 'customize' },
  ) => {
    const date = value.format('YYYY-MM');
    onChange && onChange(date, id);
    setSelectDate(value);
  };

  const onFinish = () => {
    const values = form.getFieldsValue();
    const newValues = formatData(values);
    setOrderSetting(newValues);
    onSubmit &&
      onSubmit(newValues, () => {
        setReadOnly(true);
      });
  };

  const dateCellRender = (current: any, today: any) => {
    // day	d	星期几 (星期天0，星期六6)
    const day = current.get('day');
    const key = current.format('YYYY-MM-DD');
    if (
      !readOnly &&
      selectDate.format('YYYY-MM') == current.format('YYYY-MM')
    ) {
      dateValueRef?.current?.set(key, day);
      return (
        <Space>
          <Form.Item name={key}>
            <InputNumber
              placeholder="请输入数字"
              min={0}
              keyboard={false}
              controls={false}
              parser={(displayValue: any) => Math.abs(displayValue || 0)}
            />
          </Form.Item>
          <span>单</span>
        </Space>
      );
    }

    return (
      <div className="edit-item">
        {orderSetting && typeof orderSetting[key] === 'number' ? (
          <>{orderSetting[key]}单</>
        ) : null}
      </div>
    );
  };

  useEffect(() => {
    const setting: any = {};
    targetOrderList?.forEach((i) => {
      const { day, month, target, year } = i;
      const key = `${year}-${month < 10 ? `0${month}` : month}-${
        day < 10 ? `0${day}` : day
      }`;
      setting[key] = target;
    });
    setOrderSetting({
      ...orderSetting,
      ...setting,
    });
  }, [targetOrderList]);

  return (
    <ConfigProvider locale={zhCN}>
      <div className="edit-calendar-container">
        {!readOnly && (
          <>
            <div className="tips">
              <p>快捷录入：</p>
              <p>
                参考值：近30天工作日车效{vehicleEffect?.workDay}，周末车效
                {vehicleEffect?.weekend}（剔除停运日）
              </p>
            </div>
            <ComplexForm
              onOk={onOk}
              onClear={() => {
                form.resetFields();
              }}
            />
          </>
        )}
        <div className="common-title">{title}</div>
        <Form name="calendarForm" form={form}>
          <Calendar
            cellRender={dateCellRender}
            onSelect={onSelect}
            headerRender={({ value, type, onChange, onTypeChange }) => {
              const start = 0;
              const end = 12;
              const monthOptions = [];

              let current = value.clone();
              const localeData = value.localeData();
              const months = [];
              for (let i = 0; i < 12; i++) {
                current = current.month(i);
                months.push(localeData.monthsShort(current));
              }

              for (let i = start; i < end; i++) {
                monthOptions.push(
                  <Select.Option key={i} value={i} className="month-item">
                    {months[i]}
                  </Select.Option>,
                );
              }

              const year = value.year();
              const month = value.month();
              const options = [];
              for (let i = year - 10; i < year + 10; i += 1) {
                options.push(
                  <Select.Option key={i} value={i} className="year-item">
                    {i}
                  </Select.Option>,
                );
              }
              return (
                <div
                  style={{
                    padding: 8,
                    display: 'flex',
                    justifyContent: 'right',
                  }}
                >
                  <Row gutter={8}>
                    <Col>
                      <Select
                        size="small"
                        dropdownMatchSelectWidth={false}
                        className="my-year-select"
                        value={year}
                        onChange={(newYear) => {
                          const now = value.clone().year(newYear);
                          onChange(now);
                        }}
                      >
                        {options}
                      </Select>
                    </Col>
                    <Col>
                      <Select
                        size="small"
                        dropdownMatchSelectWidth={false}
                        value={month}
                        onChange={(newMonth) => {
                          const now = value.clone().month(newMonth);
                          onChange(now);
                        }}
                      >
                        {monthOptions}
                      </Select>
                    </Col>
                  </Row>
                </div>
              );
            }}
          />
        </Form>

        <div className="footer-btn">
          {readOnly ? (
            <Button
              type="primary"
              onClick={handleEdit}
              style={{
                backgroundColor: 'rgba(60,110,240,1)',
              }}
            >
              编辑
            </Button>
          ) : (
            <>
              <Button
                style={{
                  backgroundColor: 'rgba(60,110,240,1)',
                }}
                type="primary"
                onClick={onFinish}
              >
                保存
              </Button>
              <Button
                onClick={() => {
                  setReadOnly(true);
                  form.resetFields();
                }}
              >
                取消
              </Button>
            </>
          )}
        </div>
      </div>
    </ConfigProvider>
  );
};

export default EditCalendar;
