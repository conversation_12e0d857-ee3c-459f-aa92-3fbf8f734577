import React, { useEffect, useRef, useState } from 'react';
import {
  CommonForm,
  CommonTable,
  FieldItem,
  FormConfig,
  useTableData,
} from '@jd/x-coreui';
import { SearchForm, columns } from './constant';
import { Tabs, message } from 'antd';
import { getTableList } from '@/fetch/business/testingManage';
import dayjs from 'dayjs';
import {
  getExportUrl,
  getXingyunFieldList,
  getDeviceTypeList,
  getTabModuleList,
  getXyBugs,
} from '@/fetch/business/jiraManage';
import { HttpStatusCode } from '@/fetch/core/constant';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/store';
import { saveSearchValues } from '@/redux/reducer/searchForm';
import { formatLocation, isEmpty } from '@/utils/utils';
import './index.scss';
import { AnyFunc } from '@/global';
import wrapVisibleCommonForm from '@/components/CommonForm/wrapVisibleForm';
const isoWeek = require('dayjs/plugin/isoWeek');
dayjs.extend(isoWeek);
const RedLight = require('@/assets/image/common/red-light.png');
const YellowLight = require('@/assets/image/common/yellow-light.png');
const _format = 'YYYY-MM-DD HH:mm:ss';
const WrapCommonForm = wrapVisibleCommonForm(CommonForm);
const TestingManage = () => {
  const navigator = useNavigate();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const dispatch = useDispatch();
  const { moduleCode, labelId, bugCategoryId } = formatLocation(
    window.location.search,
  );
  const initSearchCondition = {
    alarmColors: null,
    bugCode: null,
    bugPriority: null,
    bugStatusList: [],
    bugCreateStartDate: null,
    bugCreateEndDate: null,
    enable: null,
    roverVersion: null,
    labelIds: labelId ? [labelId] : null,
    rangeTime: null,
    isQaGroupDefectSubmitted: false,
    deviceTypeBaseIdList: [],
    functionSceneList: [],
    obstacleInfoList: [],
    obstaclePositionList: [],
    obstacleIntentList: [],
    roadStructureList: [],
    pageNum: 1,
    pageSize: 50,
  };
  const [searchCondition, setSearchCondition] = useState<any>(() => {
    const params = formatLocation(window.location.href);
    const alarmColorsList = params?.alarmColors?.split('_');
    const initialCondition = historySearchValues.searchValues
      ? historySearchValues.searchValues
      : initSearchCondition;
    if (alarmColorsList?.length > 0) {
      return {
        ...initialCondition,
        alarmColors: alarmColorsList,
      };
    }
    return initialCondition;
  });
  const [searchForm, setSearchForm] = useState<FormConfig>(SearchForm);
  const searchFormRef = useRef<any>(null);
  const searchFormDomRef = useRef<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [, forceUpdate] = useState<number>(0);
  const [activeTabKey, setActiveTabKey] = useState<string>(() => {
    return historySearchValues.searchValues?.activeTabKey
      ? historySearchValues.searchValues?.activeTabKey
      : 'all';
  });
  const [selectDefectInfo, setSelectDefectInfo] = useState<{
    selectedRowKeys: any[];
    selectedRows: any[];
    clearFunc: AnyFunc;
  }>({
    selectedRowKeys: [],
    selectedRows: [],
    clearFunc: () => {},
  });
  const { tableData, reloadTable } = useTableData(
    searchCondition,
    getTableList,
  );
  const getCurrentWeek = (opt: 'current' | 'pre') => {
    if (opt == 'current') {
      const lastFriday = dayjs().startOf('isoWeek').subtract(3, 'day');
      const thisThursday = dayjs().startOf('isoWeek').add(3, 'day');
      return {
        label: `本周(${lastFriday.format('MM/DD')}-${thisThursday.format(
          'MM/DD',
        )})`,
        key: `${lastFriday.startOf('day').format(_format)}/${thisThursday
          .endOf('day')
          .format(_format)}`,
      };
    } else {
      const twoWeeksAgoFriday = dayjs().startOf('isoWeek').subtract(10, 'day');
      const lastThursday = dayjs().startOf('isoWeek').subtract(4, 'day');
      return {
        label: `上周(${twoWeeksAgoFriday.format('MM/DD')}-${lastThursday.format(
          'MM/DD',
        )})`,
        key: `${twoWeeksAgoFriday.startOf('day').format(_format)}/${lastThursday
          .endOf('day')
          .format(_format)}`,
      };
    }
  };

  const getPreDay = (preDay: number, prefix: string = '') => {
    const pre = dayjs().subtract(preDay, 'day');
    return {
      label: `${prefix}(${pre.format('MM/DD')})`,
      key: `${pre.startOf('day').format(_format)}/${pre
        .endOf('day')
        .format(_format)}`,
    };
  };

  const tabs = [
    {
      label: '全部',
      key: 'all',
    },
    getCurrentWeek('current'),
    getCurrentWeek('pre'),
    getPreDay(1, '昨天'),
    getPreDay(2, '前天'),
    getPreDay(3),
    getPreDay(4),
    getPreDay(5),
    getPreDay(6),
    {
      label: '无效缺陷',
      key: 'enable',
    },
  ];

  const onSearchClick = (values: any) => {
    let tabRangeTime: string[] | undefined = [];
    if (activeTabKey != 'all' && activeTabKey != 'enable') {
      tabRangeTime = activeTabKey?.split('/');
    }
    const { rangeTime, labelIds, isQaGroupDefectSubmitted, ...other } = values;
    const normalLabels = labelIds
      ?.filter((item) => item?.length === 3)
      ?.map((item) => item[2]);
    const searchValue = {
      ...other,
      bugCreateStartDate: !isEmpty(tabRangeTime)
        ? tabRangeTime[0]
        : rangeTime && rangeTime[0]?.format(_format),
      bugCreateEndDate: !isEmpty(tabRangeTime)
        ? tabRangeTime[1]
        : rangeTime && rangeTime[1]?.format(_format),
      pageNum: 1,
      pageSize: 50,
      labelIds: normalLabels,
      enable: activeTabKey == 'enable' ? 0 : null,
      isQaGroupDefectSubmitted:
        isQaGroupDefectSubmitted?.length > 0 ? true : false,
    };
    setSearchCondition(searchValue);
  };

  const onResetClick = () => {
    let tabRangeTime: string[] | undefined = [];
    if (activeTabKey != 'all' && activeTabKey != 'enable') {
      tabRangeTime = activeTabKey?.split('/');
    }
    setSearchCondition({
      ...initSearchCondition,
      bugCreateStartDate: !isEmpty(tabRangeTime) ? tabRangeTime[0] : null,
      bugCreateEndDate: !isEmpty(tabRangeTime) ? tabRangeTime[1] : null,
      labelIds: null,
      enable: activeTabKey == 'enable' ? 0 : null,
    });
    searchFormRef.current?.setFieldsValue({
      ...initSearchCondition,
      labelIds: null,
    });
  };

  const handleTabChange = (value: any) => {
    selectDefectInfo.clearFunc();
    setActiveTabKey(value);
    const rangeTimeField = searchForm.fields.find(
      (field) => field.fieldName === 'rangeTime',
    );
    rangeTimeField!.hidden = value != 'all' && value != 'enable';
    setSearchForm({ ...searchForm });
    const formValues = searchFormRef.current?.getFieldsValue();
    const { labelIds, ...other } = formValues;
    const normalLabels = labelIds
      ?.filter((item) => item?.length === 3)
      ?.map((item) => item[2]);
    if (value != 'enable' && value != 'all') {
      const [startTime, endTime] = value?.split('/');
      const searchData = {
        ...other,
        bugCreateStartDate: startTime,
        bugCreateEndDate: endTime,
        labelIds: normalLabels,
        pageSize: 50,
        pageNum: 1,
      };
      setSearchCondition(searchData);
    } else if (value == 'enable') {
      setSearchCondition({
        ...other,
        enable: 0,
        pageSize: 50,
        pageNum: 1,
        labelIds: normalLabels,
      });
    } else if (value === 'all') {
      setSearchCondition({
        ...other,
        pageSize: 50,
        pageNum: 1,
        labelIds: normalLabels,
      });
    }
  };

  const getBugFromXingyun = () => {
    setLoading(true);
    getXyBugs()
      .then((res) => {
        if (res?.code === HttpStatusCode.Success) {
          reloadTable();
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const handleExportDefect = () => {
    const { selectedRowKeys, clearFunc } = selectDefectInfo;
    if (selectedRowKeys?.length <= 0) {
      message.error('请至少选择一条数据进行导出');
      return;
    }
    getExportUrl({ bugCodeList: selectedRowKeys }).then((res) => {
      let downloadUrl = res?.data?.url;
      if (downloadUrl && downloadUrl.startsWith('http:')) {
        downloadUrl = 'https:' + downloadUrl.substring(5);
      }
      if (downloadUrl) {
        window.open(downloadUrl, '_self');
      }
    });
  };
  const formatColumns = () => {
    return columns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'alarmColor':
          col.render = (value: string) => {
            return value == 'red' ? (
              <img width={20} height={20} src={RedLight} />
            ) : value == 'yellow' ? (
              <img width={20} height={20} src={YellowLight} />
            ) : null;
          };
          break;
        case 'operate':
          col.render = (text: any, record: any, index: number) => {
            return (
              <a
                onClick={() => {
                  dispatch(
                    saveSearchValues({
                      routeName: location.pathname,
                      searchValues: {
                        ...searchCondition,
                        activeTabKey,
                      },
                    }),
                  );
                  navigator(`/app/testing/edit?bugCode=${record.bugCode}`);
                }}
              >
                编辑
              </a>
            );
          };
          break;
        case 'fixMrIdList':
          col.render = (value: number[]) => {
            return (
              value?.map((id: number) => (
                <a
                  onClick={() => {
                    navigator(`/app/mrManage?mrNumber=${id}`);
                  }}
                  style={{ whiteSpace: 'nowrap' }}
                  key={id}
                >
                  {id}；
                </a>
              )) || '-'
            );
          };
          break;
        case 'bugCode':
          col.render = (value: any, record: any) => {
            return (
              <a href={record?.bugUrl} target="_blank">
                {value}
              </a>
            );
          };
          break;
        case 'associatedBugList':
          col.render = (value: any, record: any) => {
            return (
              <>
                {value?.map((item: any) => {
                  return (
                    <a
                      style={{ display: 'block' }}
                      key={item.code}
                      href={item.url}
                      target="_blank"
                    >
                      {item.code}；
                    </a>
                  );
                }) || '-'}
              </>
            );
          };
          break;
        case 'simulationTaskDTOList':
          col.render = (value: any, record: any) => {
            if (isEmpty(value)) {
              return '-';
            }
            return (
              <>
                {value?.map((item: any) => {
                  return (
                    <a
                      style={{ display: 'block' }}
                      key={item.simulationTaskName}
                      href={item.simulationTaskUrl}
                      target="_blank"
                    >
                      {item.simulationTaskName}；
                    </a>
                  );
                })}
              </>
            );
          };
          break;
        case 'rollbackTaskDTOList':
          col.render = (value: any, record: any) => {
            if (isEmpty(value)) {
              return '-';
            }
            return (
              <>
                {value?.map((item: any) => {
                  return (
                    <a
                      style={{ display: 'block' }}
                      key={item.simulationTaskName}
                      href={item.simulationTaskUrl}
                      target="_blank"
                    >
                      {item.simulationTaskName}；
                    </a>
                  );
                })}
              </>
            );
          };
          break;
        case 'simulationRecordInfoList':
          col.render = (value: any, record: any) => {
            return (
              <>
                {value?.map((item) => (
                  <a
                    href={item?.simulationRecordUrl}
                    target="_blank"
                    key={item?.simulationRecordName}
                  >
                    {item?.simulationRecordName}；
                  </a>
                ))}
              </>
            );
          };
          break;
        default:
          col.render = (text: any) => `${text ?? '-'}`;
          break;
      }
      return col;
    });
  };

  useEffect(() => {
    const promiseAll = [
      getTabModuleList(),
      getDeviceTypeList({
        productType: 'vehicle',
        enable: 1,
      }),
      getXingyunFieldList(),
    ];
    Promise.all(promiseAll).then((res) => {
      const [issueDownList, deviceTypeList, xingyunFieldList] = res;
      if (issueDownList?.code === HttpStatusCode.Success) {
        const field = searchForm.fields.find(
          (item: FieldItem) => item.fieldName === 'labelIds',
        );
        if (field) {
          field.options = issueDownList?.data || [];
        }
      }
      if (deviceTypeList?.code === HttpStatusCode.Success) {
        const field = searchForm.fields.find(
          (item: FieldItem) => item.fieldName === 'deviceTypeBaseIdList',
        );
        if (field) {
          field.options =
            deviceTypeList?.data?.map((item) => ({
              label: item?.deviceTypeName,
              value: item?.deviceTypeBaseId,
            })) || [];
        }
      }
      if (xingyunFieldList?.code === HttpStatusCode.Success) {
        const xingyunFieldEnum = [
          'functionSceneList',
          'obstacleInfoList',
          'obstaclePositionList',
          'obstacleIntentList',
          'roadStructureList',
        ];
        searchForm.fields.forEach((item: FieldItem) => {
          if (xingyunFieldEnum.includes(item?.fieldName ?? '')) {
            item.options = xingyunFieldList?.data?.[item?.fieldName ?? '']?.map(
              (item) => ({ label: item?.name, value: item?.code }),
            );
          }
        });
      }
      setSearchForm({ ...searchForm });
    });
  }, []);

  useEffect(() => {
    if (searchFormRef.current && searchCondition) {
      searchFormRef.current?.setFieldsValue(searchCondition);
    }
  }, []);

  useEffect(() => {
    const params = formatLocation(window.location.href);
    const alarmColorsList = params?.alarmColors?.split('_');
    if (alarmColorsList?.length && searchFormRef.current) {
      searchFormRef.current.setFieldValue('alarmColors', alarmColorsList);
    }
  }, []);

  return (
    <div className="testing-container">
      <div ref={searchFormDomRef}>
        <WrapCommonForm
          layout="inline"
          defaultValue={{
            labelIds:
              moduleCode && bugCategoryId && labelId
                ? [moduleCode, bugCategoryId, labelId]
                : null,
          }}
          formConfig={searchForm}
          formType="search"
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
          getFormInstance={(ref: any) => {
            searchFormRef.current = ref;
          }}
          onVisibilityChange={() => {
            forceUpdate((prev) => prev + 1);
          }}
        />
      </div>
      <Tabs
        activeKey={activeTabKey}
        onChange={handleTabChange}
        type="card"
        items={tabs}
      />
      <CommonTable
        middleBtns={[
          {
            title: '获取行云缺陷',
            onClick: getBugFromXingyun,
          },
          {
            title: '导出',
            onClick: handleExportDefect,
          },
        ]}
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        columns={formatColumns()}
        loading={loading}
        onPageChange={(paginationData: any) => {
          const val = {
            ...searchCondition,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          };
          setSearchCondition(val);
        }}
        searchCondition={searchCondition}
        rowKey={'bugCode'}
        crossPageSelect={(keys, rows, clearFunc) => {
          setSelectDefectInfo({
            selectedRowKeys: keys,
            selectedRows: rows,
            clearFunc: clearFunc,
          });
        }}
        searchRef={searchFormDomRef}
        pageSizeOptions={['20', '50', '100', '200', '500']}
      />
    </div>
  );
};

export default TestingManage;
