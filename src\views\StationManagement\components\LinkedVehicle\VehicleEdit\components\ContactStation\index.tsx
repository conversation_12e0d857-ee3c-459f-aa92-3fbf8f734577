import { Button, Form, Modal, Table, Col, Row, message } from 'antd';
import React, { useEffect, useState } from 'react';

import './index.scss';
import PointCard from '../PointCard';
import { StopPointType } from '@/utils/constant';
import TransferWithTable from '@/components/TransferWithTable';

const layout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 21 },
};
interface Props {
  allPoint: any;
  selectedPoint: any;
  onChangePoint: (params: any) => void;
  noHome?: boolean;
  type?: string;
  stationId: any;
}
const ModalTitleMap = new Map([
  [StopPointType.PICKUP, '取货点'],
  [StopPointType.LOAD, '装载点'],
  [StopPointType.VENDING, '售卖点'],
]);
const ContactStation = (props: Props) => {
  const [form] = Form.useForm();
  const [homePointForm] = Form.useForm();
  const { noHome, allPoint, selectedPoint, onChangePoint, stationId } = props;
  const [selectHomeModal, setSelectHomeModal] = useState<boolean>(false);
  const [selectStopModal, setSelectStopModal] = useState<boolean>(false);
  const [pointType, setPointType] = useState<any>('');
  const [selectedHomeRowKeys, setSelectedHomeRowKeys] = useState<any[]>([]);
  const [selectedHomeRows, setSelectHomeRows] = useState<any[]>([]);
  const columns: any[] = [
    {
      title: '序号',
      dataIndex: 'order',
      align: 'center',
      width: '10%',
      render: (text: any, record: any, index: number) => `${index + 1}`,
    },
    {
      title: '停靠点名称',
      width: '90%',
      dataIndex: 'name',
      align: 'center',
      ellipsis: true,
      render: (text: any, record: any, index: number) =>
        `${record.name} ${record.waitingTime}分钟`,
    },
  ];
  const [targetKeys, setTargetKeys] = useState<any[]>([]);
  // 点击添加或者更换按钮去选择停靠点
  const onClickSelect = async (type: string) => {
    if (!stationId) {
      Modal.warning({
        title: '提示',
        content: '请先选择站点！',
      });
      return;
    }
    if (type === StopPointType.HOME) {
      setSelectHomeModal(true);
    } else {
      setTargetKeys(selectedPoint[type].map((item: any) => item.id));
      setPointType(type);
      setSelectStopModal(true);
    }
  };

  // 选中home点
  const onSubmitSelectedHomePoint = () => {
    if (selectedHomeRows.length <= 0) {
      message.error('本站home点不能为空!');
      return;
    }
    onChangePoint &&
      onChangePoint({
        ...selectedPoint,
        homeList: selectedHomeRows,
      });
    setSelectHomeModal(false);
  };

  // 选中其他类型停靠点
  const onSubmitSelectedStopPoint = () => {
    const stopPointList: any[] = [];
    allPoint[pointType].forEach((item: any) => {
      if (targetKeys.indexOf(item.id) > -1) {
        stopPointList.push(item);
      }
    });
    for (const key in selectedPoint) {
      if (key === pointType) {
        selectedPoint[key] = stopPointList;
      }
    }
    onChangePoint && onChangePoint(selectedPoint);
    setSelectStopModal(false);
  };

  return (
    <>
      <div className="bind-current-stop">
        <div className="notice">
          <p>
            注意：车辆各停靠点的停靠时长，取【停靠点管理】中所配置的默认时长；车端可根据具体需要灵活调整该时长。
          </p>
        </div>
        <Form {...layout} className="reform" form={form}>
          {noHome ? null : (
            <Form.Item
              label="home点"
              name="homeList"
              required={props.type !== 'batch' && true}
            >
              <PointCard
                type={StopPointType.HOME}
                data={selectedPoint[StopPointType.HOME]}
                onClickSelect={(type: any) => onClickSelect(type)}
              />
            </Form.Item>
          )}
          <Form.Item label="装载点" name="loadList">
            <PointCard
              type={StopPointType.LOAD}
              data={selectedPoint[StopPointType.LOAD]}
              onClickSelect={(type: any) => onClickSelect(type)}
            />
          </Form.Item>
          <Form.Item label="取货点" name="pickList">
            <PointCard
              type={StopPointType.PICKUP}
              data={selectedPoint[StopPointType.PICKUP]}
              onClickSelect={(type: any) => onClickSelect(type)}
            />
          </Form.Item>
          <Form.Item label="售卖点" name="vendingList">
            <PointCard
              type={StopPointType.VENDING}
              data={selectedPoint[StopPointType.VENDING]}
              onClickSelect={(type: any) => onClickSelect(type)}
            />
          </Form.Item>
        </Form>

        <Modal
          className="cross-stop-modal"
          width={1000}
          open={selectHomeModal}
          onCancel={() => {
            setSelectHomeModal(false);
            setSelectHomeRows([]);
            setSelectedHomeRowKeys([]);
          }}
          onOk={onSubmitSelectedHomePoint}
          title={`更新home点停靠点`}
        >
          <Form
            form={homePointForm}
            wrapperCol={{ span: 20 }}
            labelCol={{ span: 3 }}
          >
            <Form.Item label="当前home点" name="currentHome">
              <div>
                {selectedPoint.homeList?.length > 0
                  ? `${selectedPoint.homeList[0].name} ${selectedPoint.homeList[0].waitingTime}分钟`
                  : '-'}
              </div>
            </Form.Item>
            <Form.Item label="home点更换为">
              <Table
                rowKey={(record: any) => record.id}
                columns={columns}
                dataSource={allPoint.homeList}
                pagination={false}
                bordered
                rowSelection={{
                  columnTitle: <div>单选</div>,
                  columnWidth: '50px',
                  type: 'radio',
                  selectedRowKeys: selectedHomeRowKeys,
                  onChange: (selectedRowKeys: any, selectedRows: any) => {
                    setSelectedHomeRowKeys(selectedRowKeys);
                    setSelectHomeRows(selectedRows);
                  },
                }}
              />
            </Form.Item>
          </Form>
        </Modal>

        <Modal
          className="cross-stop-modal"
          width={1200}
          visible={selectStopModal}
          onCancel={() => {
            setSelectStopModal(false);
          }}
          onOk={onSubmitSelectedStopPoint}
          title={`更新${ModalTitleMap.get(pointType)}停靠点`}
        >
          <TransferWithTable
            leftColumns={columns}
            rightColumns={columns}
            onSelectChange={(list: any) => setTargetKeys(list)}
            titles={[
              <div className="transfer-title" key={'left'}>
                可选停靠点
              </div>,
              <div className="transfer-title" key={'right'}>
                已选停靠点
              </div>,
            ]}
            dataSource={allPoint[pointType]}
            targetKeys={targetKeys}
            showSearch={true}
            showSelectAll={false}
            tableSelectAll={true}
            scrollY={350}
            rowKey={'id'}
            searchColumn={'name'}
          />
        </Modal>
      </div>
    </>
  );
};
export default React.memo(ContactStation);
