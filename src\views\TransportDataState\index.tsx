import { CommonForm, FieldItem, FormConfig } from "@jd/x-coreui";
import React, { useEffect, useState } from "react";
import { SearchConfig, TableConfig } from "./config";
import { RootState } from "@/redux/store";
import { useSelector } from "react-redux";
import { TransportDataApi } from "@/fetch/business/transportDataStop";
import { HttpStatusCode } from "@/fetch/core/constant";
import dayjs from "dayjs";
import { YESNO } from "@/utils/enum";
import CommonTable from "@/components/CommonTable";
import { useTableData } from "@/components/CommonTable/useTableData";
import { Table } from "antd";

const fetchApi = new TransportDataApi();
const TransportDataState = () => {

    const [searchConfig, setSearchConfig] = useState<FormConfig>(SearchConfig);
    const initSearchCondition = {
        searchForm: {
            stateIdList: null,
            range: { value: YESNO.YES },
            dt: [dayjs().subtract(2, 'day'), dayjs().subtract(2, 'day')],
        },
        pageNum: 1,
        pageSize: 10,
    };

    const historySearchValues = useSelector(
        (state: RootState) => state.searchForm,
    );

    const [searchCondition, setSearchCondition] = useState<SearchCondition>(
        () => {
            return historySearchValues.searchValues
                ? historySearchValues.searchValues
                : initSearchCondition;
        },
    );

    const onSearchClick = (val) => {
        if (JSON.stringify(val) === JSON.stringify(searchCondition.searchForm)) {
            return;
        }
        const data = {
            ...searchCondition,
            searchForm: val,
        };
        setSearchCondition(data);
    };

    const { tableData, loading }: any = useTableData(
        {
            searchForm: {
                stateIdList: searchCondition.searchForm.stateIdList,
                range: searchCondition.searchForm.range?.value,
                startDay: searchCondition.searchForm.dt[0].format('YYYY-MM-DD'),
                endDay: searchCondition.searchForm.dt[1].format('YYYY-MM-DD'),
            },
            pageNum: searchCondition.pageNum,
            pageSize: searchCondition.pageSize,
        },
        fetchApi.fetchStateTableList,
    );

    const formatColumns = () => {
        return TableConfig.map((col) => {
            switch (col.dataIndex) {
                default:
                    return {
                        ...col,
                        render: (text: any) => (text === 0 ? 0 : text || '-'),
                    };
            }
        });
    }

    useEffect(() => {
        const params = {
            dimType: 'PROVINCE',
            filterIds: []
        };
        fetchApi.getDimSelect(params).then((res) => {
            if (res.code === HttpStatusCode.Success) {
                const field = searchConfig.fields.find((item: FieldItem) => item?.fieldName === 'stateIdList');
                field!.options = res.data?.map((val) => ({
                    value: val.dimValue,
                    label: val.dimName,
                }));
                setSearchConfig({ ...searchConfig });
            }
        });
    }, []);

    const [summaryData, setSummaryData] = useState<any>({
        stationNum: '-',
        deployVehicleNum: '-',
        transportTimes: '-',
        vehicleDayAvgTransportTimes: '-',
        avgTransportTimesRangeOne: '-',
        avgTransportTimesRangeTwo: '-',
        avgTransportTimesRangeThree: '-',
        avgTransportTimesRangeFour: '-',
        avgTransportTimesRangeFive: '-',
        stationBusinessRate: '-',
        vehicleBusinessRate: '-',
    });

    useEffect(() => {
        fetchApi.fetchStateTableSummary({
            searchForm: {
                stateIdList: searchCondition.searchForm.stateIdList,
                range: searchCondition.searchForm.range?.value,
                startDay: searchCondition.searchForm.dt[0].format('YYYY-MM-DD'),
                endDay: searchCondition.searchForm.dt[1].format('YYYY-MM-DD'),
            },
            pageNum: searchCondition.pageNum,
            pageSize: searchCondition.pageSize,
        }).then((res) => {
            if (res.code === HttpStatusCode.Success) {
                setSummaryData(res.data);
            }
        });
    }, [searchCondition.searchForm]);

    return (
        <>
            <CommonForm
                formConfig={searchConfig}
                defaultValue={searchCondition.searchForm}
                layout="inline"
                formType="search"
                colon={false}
                onSearchClick={onSearchClick}
                onResetClick={() => setSearchCondition({ ...initSearchCondition })}
            />
            <CommonTable
                tableListData={{
                    list: tableData?.list ?? [],
                    totalNumber: tableData?.total,
                    totalPage: tableData?.pages,
                }}
                columns={formatColumns()}
                loading={loading}
                rowKey={'stateId'}
                searchCondition={searchCondition}
                onPageChange={(value: any) => setSearchCondition(value)}
                summary={() => {
                    return <Table.Summary.Row>
                        <Table.Summary.Cell index={0} colSpan={1}>总计</Table.Summary.Cell>
                        <Table.Summary.Cell index={1}>{summaryData?.stationNum}</Table.Summary.Cell>
                        <Table.Summary.Cell index={2}>{summaryData?.deployVehicleNum}</Table.Summary.Cell>
                        <Table.Summary.Cell index={3}>{summaryData?.transportTimes}</Table.Summary.Cell>
                        <Table.Summary.Cell index={4}>{summaryData?.vehicleDayAvgTransportTimes}</Table.Summary.Cell>
                        <Table.Summary.Cell index={5}>{summaryData?.avgTransportTimesRangeOne}</Table.Summary.Cell>
                        <Table.Summary.Cell index={6}>{summaryData?.avgTransportTimesRangeTwo}</Table.Summary.Cell>
                        <Table.Summary.Cell index={7}>{summaryData?.avgTransportTimesRangeThree}</Table.Summary.Cell>
                        <Table.Summary.Cell index={8}>{summaryData?.avgTransportTimesRangeFour}</Table.Summary.Cell>
                        <Table.Summary.Cell index={9}>{summaryData?.avgTransportTimesRangeFive}</Table.Summary.Cell>
                        <Table.Summary.Cell index={10}>{summaryData?.stationBusinessRate}</Table.Summary.Cell>
                        <Table.Summary.Cell index={11}>{summaryData?.vehicleBusinessRate}</Table.Summary.Cell>
                    </Table.Summary.Row>
                }}
            />
        </>
    )
}

export default TransportDataState;