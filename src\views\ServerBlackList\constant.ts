import { FormConfig } from '@/components';

export const BlackListFormData: FormConfig = {
  fields: [
    {
      fieldName: 'customerPhone',
      label: '用户手机号',
      type: 'input',
      placeholder: '请输入用户手机号',
    },
    {
      fieldName: 'customerName',
      label: '用户姓名',
      type: 'input',
      placeholder: '请输入用户姓名',
    },
    {
      fieldName: 'enable',
      label: '黑名单状态',
      type: 'select',
      placeholder: '请选择黑名单状态',
      options: [
        {
          label: '启用',
          value: 1,
        },
        {
          label: '停用',
          value: 0,
        },
      ],
    },
    {
      fieldName: 'operationTime',
      label: '添加时间',
      type: 'rangeTime',
      placeholder: '请选择维护时间',
    },
    {
      fieldName: 'jdErp',
      label: '添加人',
      type: 'input',
      placeholder: '请输入添加人',
    },
  ],
};
