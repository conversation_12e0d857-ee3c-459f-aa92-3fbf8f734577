import type { ColumnsType } from 'antd/es/table';
import type { XMapInfoDataType, XMapVersionDataType } from './type';
export const DEFAULT_PAGINATION = {
  currentPage: 1,
  pageSize: 100,
};
export enum MapStatus {
  COMPILE = '已编译',
  TEST = '测试发布',
  RELEASE = '正式发布',
  ROLLBACK = '取消',
  CANCEL = '取消',
}
const getMapStatus = (text: string) => {
  switch (text) {
    case 'COMPILE':
      return MapStatus.COMPILE;
    case 'RELEASE':
      return MapStatus.RELEASE;
    case 'CANCEL':
      return MapStatus.CANCEL;
    case 'ROLLBACK':
      return MapStatus.ROLLBACK;
    case 'TEST':
      return MapStatus.TEST;
  }
};
export const xMapVersionColumns: ColumnsType<XMapVersionDataType> = [
  {
    title: '地图ID',
    dataIndex: 'mapId',
    align: 'center',
    width: 100,
    ellipsis: true,
    fixed: 'left',
  },
  {
    title: '地图名称',
    dataIndex: 'mapBlockName',
    align: 'center',
    width: 150,
    fixed: 'left',
  },
  {
    title: '地图编码',
    dataIndex: 'mapCode',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '内业版本',
    dataIndex: 'rawDataVersion',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '地图版本',
    dataIndex: 'mapVersion',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '地图状态',
    dataIndex: 'mapState',
    align: 'center',
    width: 100,
    ellipsis: true,
    render: (text, record) => getMapStatus(text),
  },
  {
    title: '最小经度',
    dataIndex: 'minX',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '最小纬度',
    dataIndex: 'minY',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '最大经度',
    dataIndex: 'maxX',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '最大纬度',
    dataIndex: 'maxY',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: 'OffsetX',
    dataIndex: 'offsetX',
    align: 'center',
    width: 100,
  },
  {
    title: 'OffsetY',
    dataIndex: 'offsetY',
    align: 'center',
    width: 100,
  },
  {
    title: 'Zone',
    dataIndex: 'utmZone',
    align: 'center',
    width: 100,
  },
  {
    title: '更新日期',
    dataIndex: 'modifyTime',
    align: 'center',
    width: 150,
  },
  {
    title: '更新描述',
    dataIndex: 'rawDataDescription',
    align: 'center',
    width: 200,
    ellipsis: true,
    fixed: 'right',
  },
];

export const xMapInfoColumns: ColumnsType<XMapInfoDataType> = [
  {
    title: '地图ID',
    dataIndex: 'id',
    align: 'center',
    width: 100,
    ellipsis: true,
    fixed: 'left',
  },
  {
    title: '地图名称',
    dataIndex: 'mapBlockName',
    align: 'center',
    width: 150,
    fixed: 'left',
  },
  {
    title: '地图编码',
    dataIndex: 'mapCode',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '内业版本',
    dataIndex: 'rawDataVersion',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '地图版本',
    dataIndex: 'mapVersion',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '最小经度',
    dataIndex: 'minX',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '最小纬度',
    dataIndex: 'minY',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '最大经度',
    dataIndex: 'maxX',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '最大纬度',
    dataIndex: 'maxY',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: 'OffsetX',
    dataIndex: 'offsetX',
    align: 'center',
    width: 100,
  },
  {
    title: 'OffsetY',
    dataIndex: 'offsetY',
    align: 'center',
    width: 100,
  },
  {
    title: 'Zone',
    dataIndex: 'utmZone',
    align: 'center',
    width: 100,
  },
  {
    title: '更新日期',
    dataIndex: 'modifyTime',
    align: 'center',
    width: 150,
  },
  {
    title: '更新描述',
    dataIndex: 'rawDataDescription',
    align: 'center',
    ellipsis: true,
    width: 200,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    align: 'center',
    width: 150,
    fixed: 'right',
  },
];
