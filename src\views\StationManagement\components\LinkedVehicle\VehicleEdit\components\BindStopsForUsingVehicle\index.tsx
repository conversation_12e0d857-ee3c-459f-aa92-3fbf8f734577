/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { Col, Form, Input, message, Row, Tabs, Tag } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import CommonEdit from '@/components/CommonEdit';
import EditModuleTitle from '@/components/EditModuleTitle';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  clearVehicleManage,
  vehicleManageSelector,
} from '@/redux/reducer/vehicleManage';
import { formatLocation, isEmpty } from '@/utils/utils';
import ContactStation from '../../components/ContactStation';
import { StationFetchApi } from '@/fetch/business';
import BindCrossStop from '../BindCrossStop';
import { BindCrossStopPageType, BindStoppointTab } from '@/utils/constant';
import { StationDetailTabKeys } from '@/views/StationManagement/components/StationDetail';
const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};
const breadCrumbConfig = [
  {
    title: '车辆管理',
    route: '',
  },
  {
    title: '批量绑定停靠点',
    route: '',
  },
];
const BindStops = () => {
  const fetchApi = new StationFetchApi();
  const location = formatLocation(window.location.search);
  const navigator = useNavigate();
  const [selectVehicleList, setSelectVehicleList] = useState([]);
  const selector = useSelector(vehicleManageSelector);
  const tagList = selector.selectVehicle;
  const dispatch = useDispatch();
  const [stationInfo, setStationInfo] = useState<any>({});
  const [crossStopInfo, setCrossStopInfo] = useState<any[]>([]);
  const [form] = Form.useForm();
  const [selectedPoint, setSelectedPoint] = useState<any>({
    homeList: [],
    loadList: [],
    pickList: [],
    vendingList: [],
  });
  const [bindStopTab, setBindStopTab] = useState<string>(
    BindStoppointTab.BINDCURRENTSTOPPOINT,
  );
  const [allPoint, setAllPoint] = useState<any>({
    homeList: [],
    loadList: [],
    pickList: [],
    vendingList: [],
  });
  const tabMenu = [
    {
      key: BindStoppointTab.BINDCURRENTSTOPPOINT,
      name: '本站停靠点',
    },
    {
      key: BindStoppointTab.BINDCROSSSTOPPOINT,
      name: '跨站停靠点',
    },
  ];

  useEffect(() => {
    getStationInfo(location.stationBaseId);
    Promise.all([
      getStopList('HOME'),
      getStopList('LOADING'),
      getStopList('PICKUP'),
      getStopList('VENDING'),
    ]).then((res) => {
      setAllPoint({
        homeList: res[0],
        loadList: res[1],
        pickList: res[2],
        vendingList: res[3],
      });
    });

    if (tagList && tagList.length > 0) {
      setSelectVehicleList(tagList);
    } else {
      message.error('请重新选择车辆');
    }
    return () => {
      dispatch(clearVehicleManage({}));
    };
  }, []);

  const goBack = () => {
    navigator(
      `/app/stationManagement/detail?stationBaseId=${location.stationBaseId}&tabKey=${StationDetailTabKeys.LINKED_VEHICLE}`,
    );
  };
  const getStationInfo = async (stationBaseId: number) => {
    try {
      const res = await fetchApi.fetchStationBasicInfo(stationBaseId);

      if (res.code === HttpStatusCode.Success) {
        form.setFieldsValue({
          station: res.data.name,
        });
        setStationInfo({
          cityId: res.data.cityId,
          stationId: res.data.stationBaseId,
        });
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    }
  };
  const getStopList = async (type: string) => {
    try {
      const res = await fetchApi.getStopListByTypeOfStation({
        stationBaseId: location.stationBaseId,
        type,
      });
      if (res.code === HttpStatusCode.Success) {
        return res.data;
      }
    } catch (e) {
      console.log(e);
    }
  };

  const checkPoint = () => {
    let invalid = 0;
    let crossStopEmptyNum = 0;
    Object.values(selectedPoint).forEach((item: any) => {
      if (item?.length <= 0) {
        invalid++;
      }
    });
    // 1. 只选本站停靠点
    if (
      invalid < Object.values(selectedPoint).length &&
      isEmpty(crossStopInfo)
    ) {
      return true;
    }
    // 2. 只选跨站停靠点 + 3. 本站 + 跨站
    if (
      invalid <= Object.values(selectedPoint).length &&
      !isEmpty(crossStopInfo)
    ) {
      Object.values(crossStopInfo).forEach((item: any) => {
        if (item?.homeList.length <= 0) {
          crossStopEmptyNum++;
        }
      });
      if (crossStopEmptyNum > 0) {
        message.error('跨站停靠点必选home点！');
        return false;
      } else {
        return true;
      }
    }
    // 4. 什么都不选
    if (
      invalid === Object.values(selectedPoint).length &&
      isEmpty(crossStopInfo)
    ) {
      message.error('至少选择一个停靠点！');
      return false;
    }
  };
  const confirm = async () => {
    if (!checkPoint()) {
      return;
    }
    try {
      const vehicleNumberList: string[] = [];
      selectVehicleList.forEach((item: any) =>
        vehicleNumberList.push(item.deviceBaseId),
      );
      const linkedStopList: number[] = [];
      const homeStopId =
        !isEmpty(selectedPoint.homeList) && selectedPoint.homeList[0].id;
      crossStopInfo.length > 0 &&
        crossStopInfo.forEach((item: any) => {
          item.homeList.forEach((homePoint: any) => {
            linkedStopList.push(homePoint.id);
          });
          item.stopList.forEach((stopPoint: any) => {
            linkedStopList.push(stopPoint.id);
          });
        });
      !isEmpty(selectedPoint.loadList) &&
        selectedPoint.loadList.forEach((item) => {
          linkedStopList.push(item.id);
        });
      !isEmpty(selectedPoint.pickList) &&
        selectedPoint.pickList.forEach((item) => {
          linkedStopList.push(item.id);
        });
      !isEmpty(selectedPoint.vendingList) &&
        selectedPoint.vendingList.forEach((item) => {
          linkedStopList.push(item.id);
        });
      const para: any = {
        deviceBaseIdList: vehicleNumberList,
        homeStopId: homeStopId ? homeStopId : null,
        stationBaseId: stationInfo.stationId,
        linkedStopList: linkedStopList,
      };
      const response: any = await fetchApi.stationVehicleBindStopBatch(para);
      if (response.code === HttpStatusCode.Success) {
        navigator(
          `/app/stationManagement/detail?stationBaseId=${location.stationBaseId}&tabKey=${StationDetailTabKeys.LINKED_VEHICLE}`,
        );
        message.success(response.message);
      } else {
        message.error(response.message);
      }
    } catch (e) {
      console.log(e);
    }
  };

  const onClose = (e: any, id: any) => {
    const newTag: any = [];
    selectVehicleList.map((item: any) => {
      if (item.deviceBaseId !== id) {
        newTag.push(item);
      }
    });
    setSelectVehicleList(newTag);
  };

  const onChangePoint = (values: any) => {
    setSelectedPoint(values);
  };

  return (
    <div>
      <CommonEdit
        title="批量绑定停靠点"
        breadCrumbConfig={breadCrumbConfig}
        onSubmitClick={confirm}
        onCancleClick={goBack}
      >
        <EditModuleTitle title="基础信息" />
        <Form {...layout} form={form} style={{ paddingRight: 20 }}>
          <Form.Item
            label={`当前选择车辆(${
              selectVehicleList && selectVehicleList.length
            })`}
            rules={[{ required: true, message: '当前选择车辆不能为空' }]}
          >
            {selectVehicleList &&
              selectVehicleList.map((item: any) => {
                return (
                  <Tag
                    key={item.deviceBaseId}
                    closable={
                      selectVehicleList && selectVehicleList.length > 1
                        ? true
                        : false
                    }
                    onClose={(e) => onClose(e, item.deviceBaseId)}
                  >
                    {item.deviceName}
                  </Tag>
                );
              })}
          </Form.Item>
          <Form.Item label="所属站点" name="station">
            <Input disabled />
          </Form.Item>
        </Form>
        <EditModuleTitle title={'关联点位'} />
        <Row justify="center">
          <Col span={19}>
            <Tabs
              size="small"
              activeKey={bindStopTab}
              onChange={(activeKey: string) => setBindStopTab(activeKey)}
              type="line"
            >
              {tabMenu.map((item) => {
                return (
                  <Tabs.TabPane tab={item.name} key={item.key}></Tabs.TabPane>
                );
              })}
            </Tabs>
          </Col>
        </Row>
        {bindStopTab === BindStoppointTab.BINDCURRENTSTOPPOINT && (
          <ContactStation
            type={'batch'}
            allPoint={allPoint}
            selectedPoint={selectedPoint}
            onChangePoint={onChangePoint}
            stationId={location.stationBaseId}
          />
        )}
        {bindStopTab === BindStoppointTab.BINDCROSSSTOPPOINT && (
          <BindCrossStop
            type={BindCrossStopPageType.BATCH}
            cityId={stationInfo.cityId}
            stationId={location.stationBaseId}
            crossStopInfo={crossStopInfo}
            onHandleStop={(values: any) => setCrossStopInfo(values)}
          />
        )}
      </CommonEdit>
    </div>
  );
};

export default BindStops;
