import { StationResponse, StationVehicleResponse, StopResponse } from '@/types';
import {
  ProductType,
  DeployPlanSession,
  MapVehicleSource,
  VehicleSupplier,
} from '@/utils/enum';
import { ColumnsType } from 'antd/es/table';
import { FormConfig } from '@jd/x-coreui';
import { StationFetchApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import React from 'react';

const requestApi = new StationFetchApi();
export const stationColumns: ColumnsType<StationResponse> = [
  {
    title: '序号',
    dataIndex: 'rowIndex',
    align: 'center',
    width: 60,
  },
  {
    title: '站点ID',
    dataIndex: 'stationBaseId',
    align: 'center',
    width: 70,
  },
  {
    title: '站点名称',
    dataIndex: 'name',
    align: 'center',
    width: 180,
    ellipsis: true,
  },
  {
    title: '站点类型',
    dataIndex: 'productTypeName',
    align: 'center',
    width: 100,
  },
  {
    title: '用途',
    dataIndex: 'useCaseName',
    align: 'center',
    width: 90,
  },
  {
    title: '运营方类型',
    dataIndex: 'typeName',
    align: 'center',
    width: 80,
  },
  {
    title: '站点状态',
    dataIndex: 'enableName',
    align: 'center',
    width: 70,
  },
  {
    title: '车辆数目',
    dataIndex: 'deviceCount',
    align: 'center',
    width: 70,
  },
  {
    title: '停靠点数目',
    dataIndex: 'stopCount',
    align: 'center',
    width: 80,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 170,
    align: 'center',
    fixed: 'right',
  },
];

export const getLinkedVehicleColumns = (stationType: ProductType) => {
  const LinkedVehicleColumns: ColumnsType<StationVehicleResponse> = [
    {
      title: '序号',
      dataIndex: 'rowIndex',
      align: 'center',
      width: 60,
    },
    {
      title: '车架号',
      dataIndex: 'serialNo',
      align: 'center',
      width: 100,
    },
    {
      title: '车牌号',
      dataIndex: 'deviceName',
      align: 'center',
      width: 100,
    },
    {
      title: '产品类型',
      dataIndex: 'productTypeName',
      align: 'center',
      width: 100,
    },
    {
      title: '车型名称',
      dataIndex: 'deviceTypeName',
      align: 'center',
      width: 100,
    },
    {
      title: '设备类型',
      dataIndex: 'businessTypeName',
      align: 'center',
      width: 100,
    },
    {
      title: '车辆归属方',
      dataIndex: 'ownerUseCaseName',
      align: 'center',
      width: 100,
    },
    {
      title: '设备生命周期',
      dataIndex: 'hardwareStatusName',
      align: 'center',
      width: 100,
    },
  ];
  let result: ColumnsType<StationVehicleResponse> =
    stationType === ProductType.INTEGRATE
      ? [
          {
            title: '任务模式',
            dataIndex: 'workMode',
            align: 'center',
            width: 150,
          },
          {
            title: '使用状态',
            dataIndex: 'enableName',
            align: 'center',
            width: 100,
          },
          {
            title: '操作',
            dataIndex: 'operations',
            align: 'center',
            width: 180,
            fixed: 'right',
          },
        ]
      : [
          {
            title: '推流模式',
            dataIndex: 'videoModeName',
            align: 'center',
            width: 100,
          },
          {
            title: '存在未完成维修单',
            dataIndex: 'isRequireName',
            align: 'center',
            width: 100,
          },
          {
            title: '绑定本站点位数',
            dataIndex: 'linkLocalStationStopCount',
            align: 'center',
            width: 100,
          },
          {
            title: '绑定跨站点位数',
            dataIndex: 'linkJumpStationStopCount',
            align: 'center',
            width: 100,
          },
          {
            title: '操作',
            dataIndex: 'operations',
            align: 'center',
            width: 180,
            fixed: 'right',
          },
        ];
  return LinkedVehicleColumns.concat(result);
};

export const StopConfigColumns: ColumnsType<StopResponse> = [
  {
    title: '序号',
    dataIndex: 'rowIndex',
    align: 'center',
    width: 60,
  },
  {
    title: '点位编号',
    dataIndex: 'number',
    align: 'center',
    width: 100,
  },
  {
    title: '点位名称',
    dataIndex: 'name',
    align: 'center',
    width: 100,
  },
  {
    title: '点位类型',
    dataIndex: 'typeName',
    align: 'center',
    width: 100,
  },
  {
    title: '等候时长(min)',
    dataIndex: 'waitingTime',
    align: 'center',
    width: 100,
  },
  {
    title: '点位状态',
    dataIndex: 'enableName',
    align: 'center',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    align: 'center',
    width: 180,
    fixed: 'right',
  },
];
