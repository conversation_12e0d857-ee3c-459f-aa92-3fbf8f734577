/* eslint-disable no-unused-vars */

import { Form, FormInstance, Radio,Space } from 'antd';
import React, { useEffect, useState } from 'react';


const FromRadio = (
  {
    disable,
    name,
    label,
    form,
    items,
    defaultValue,
    initValue,
    onValueChanged,
    errMsg,
    direction,
  }: {
    disable?: boolean,
    name: string,
    label: string,
    form: FormInstance,
    items: any[] | null,
    defaultValue?: any,
    initValue?: any,
    onValueChanged?: Function,
    errMsg?:string,
    direction?:any
  }
) => {
  const [value, setValue] = useState(defaultValue);
  useEffect(() => {
    if (initValue) {
      setValue(initValue)
      form.setFieldsValue({
        [name]: initValue
      })
    } else {
      form.setFieldsValue({
        [name]: defaultValue
      })
    }
  }, [initValue]);
  return (
    <Form.Item
      name={name}
      label={label}
      rules={[{ required: true , message: errMsg || `请选择${label}`}]}
    >
      <Radio.Group
        disabled={disable}
        onChange={(e: any) => {
          setValue(e.target.value)
          form.setFieldsValue({
            [name]: e.target.value
          })
          onValueChanged && onValueChanged(e.target.value)
        }}
        value={value}
        style={ {marginTop: 4,marginLeft: 20}}
      >
        <Space direction={direction}>
          {items?.map((item: any,idx:number) => {
              return (
                <Radio key={`From_Radio_${idx}`} value={item.code}>{item.name}</Radio>
              )
          })}
        </Space>
      </Radio.Group>
    </Form.Item>
  );
}

export default React.memo(FromRadio)
