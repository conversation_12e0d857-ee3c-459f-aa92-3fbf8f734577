import { YESNO } from '@/utils/enum';
import { request } from '../core';
import { PageType } from '@/utils/enum';

export class FactoryApi {
  // 分页查询厂商数据列表
  fetchTableList({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/manufacturer/manufacturer_get_page_list',
      urlParams: {
        pageNum,
        pageSize,
      },
      body: searchForm,
    };
    return request(options);
  }
  //  新增编辑厂商
  submitFactory({
    type,
    requestBody,
  }: {
    type: PageType.EDIT | PageType.ADD;
    requestBody: Object;
  }) {
    const options: RequestOptions = {
      method: type === PageType.EDIT ? 'PUT' : 'POST',
      path:
        type === PageType.EDIT
          ? '/k2/management/manufacturer/manufacturer_edit'
          : '/k2/management/manufacturer/manufacturer_add',
      body: requestBody,
    };
    return request(options);
  }
  // 查看厂商详情数据
  fetchFactoryDetail(id: number | string) {
    const options: RequestOptions = {
      method: 'GET',
      path: '/k2/management/manufacturer/manufacturer_get_detail',
      urlParams: {
        id,
      },
    };
    return request(options);
  }
  // 变更厂商状态
  updateFactoryStatus({ id, enable }: { id: number; enable: YESNO }) {
    const options: RequestOptions = {
      method: 'PUT',
      path: '/k2/management/manufacturer/manufacturer_update_enable',
      body: {
        id,
        enable,
      },
    };
    return request(options);
  }
}
