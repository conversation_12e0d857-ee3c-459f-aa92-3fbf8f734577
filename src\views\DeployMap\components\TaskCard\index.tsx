import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>confirm } from 'antd';
import { TaskStatusEnum, TaskStatusTextMap } from '@/utils/constant';
import { LatLng } from '@/fetch/business/deployMap';
import './index.scss';
import classNames from 'classnames';
import { getStatusColor } from '../../utils/constant';
// 声明静态资源文件
const stationIcon = require('@/assets/image/mapCollect/taskcard/task-station-icon.png');
const createTimeIcon = require('@/assets/image/mapCollect/taskcard/task-create-time-icon.png');
const creatorIcon = require('@/assets/image/mapCollect/taskcard/task-creator-icon.png');
const expandIcon = require('@/assets/image/mapCollect/taskcard/task-card-expand-icon.png');
const collapseIcon = require('@/assets/image/mapCollect/taskcard/task-card-collapse-icon.png');

interface TaskCardData {
  taskId: number;
  vehicleName?: string;
  taskName: string;
  taskCreator: string;
  taskStatus: string;
  stationId: number;
  stationName: string;
  createTime: string;
  taskRouteColor: string;
  totalMileage: number;
  taskRouteList: LatLng[];
  roadNameList: string[];
}

interface TaskCardProps {
  taskData: TaskCardData;
  selectedTaskId: number | null;
  handleSelectTask: (taskId: number) => void;
  onDelete?: (taskId: number) => void;
  onEdit?: (taskId: number) => void;
  onRevokeTask?: (taskId: number) => void;
}

const TaskCard: React.FC<TaskCardProps> = ({
  taskData,
  selectedTaskId,
  handleSelectTask,
  onDelete,
  onEdit,
  onRevokeTask,
}) => {
  const [expanded, setExpanded] = useState(true);

  // 渲染操作按钮
  const renderActionButtons = () => {
    const { taskId, taskStatus, taskRouteList } = taskData;

    // 将字符串转换为枚举类型
    const status = taskStatus as TaskStatusEnum;

    switch (status) {
      case TaskStatusEnum.PENDING_CLAIM:
        return (
          <>
            {onDelete && (
              <Popconfirm
                title="确定删除任务吗？"
                onConfirm={() => onDelete(taskId)}
              >
                <Button
                  type="default"
                  style={{
                    marginRight: '12px',
                    background: '#F5F5F6',
                  }}
                >
                  删除
                </Button>
              </Popconfirm>
            )}
            {onRevokeTask && (
              <Button
                type="primary"
                onClick={(e) => {
                  e.stopPropagation();
                  onRevokeTask(taskId);
                }}
              >
                编辑
              </Button>
            )}
          </>
        );
      case TaskStatusEnum.UNDER_EXPLORATION:
        return (
          <>
            {onDelete && (
              <Popconfirm
                title="确定删除任务吗？"
                onConfirm={() => onDelete(taskId)}
              >
                <Button
                  type="default"
                  style={{
                    marginRight: '12px',
                    background: '#F5F5F6',
                  }}
                >
                  删除
                </Button>
              </Popconfirm>
            )}
            {onEdit && (
              <Button
                type="primary"
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(taskId);
                }}
              >
                编辑
              </Button>
            )}
          </>
        );
      default:
        return null;
    }
  };
  return (
    <div
      className={classNames('task-card', {
        'task-card-expanded': expanded,
        'task-card-selected': selectedTaskId === taskData.taskId,
      })}
      onClick={() => handleSelectTask(taskData.taskId)}
    >
      {expanded && (
        <div
          className="status-badge"
          style={{ backgroundColor: getStatusColor(taskData.taskStatus) }}
        >
          {TaskStatusTextMap.get(taskData.taskStatus as TaskStatusEnum) ||
            '未知状态'}
        </div>
      )}
      <div
        className={classNames('card-content', {
          'card-content-unexpanded': !expanded,
        })}
      >
        <div
          className={classNames('task-header', {
            'task-header-expanded': expanded,
          })}
        >
          <div className="task-name">{taskData.taskName}</div>
        </div>
        <div
          className={classNames('expand-icon', {
            'expand-icon-expanded': expanded,
          })}
          onClick={(e) => {
            e.stopPropagation();
            setExpanded(!expanded);
          }}
        >
          <img
            src={expanded ? collapseIcon : expandIcon}
            alt={expanded ? '收起' : '展开'}
          />
        </div>
      </div>
      {expanded && (
        <div className="task-details">
          <div className="task-info-item">
            <img src={stationIcon} alt="营业部" className="info-icon" />
            <span className="value">{taskData.stationName}</span>
          </div>
          <div className="task-info-item">
            <img src={createTimeIcon} alt="创建时间" className="info-icon" />{' '}
            <span className="value">{taskData.createTime}</span>
          </div>
          <div className="task-info-item">
            <img src={creatorIcon} alt="创建人" className="info-icon" />
            <span className="value">{taskData.taskCreator}</span>
          </div>
        </div>
      )}
      {expanded && <div className="action-area">{renderActionButtons()}</div>}
    </div>
  );
};

export default TaskCard;
