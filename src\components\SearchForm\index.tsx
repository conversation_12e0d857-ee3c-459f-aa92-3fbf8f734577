import React, { useState, useEffect } from 'react';
import {
  Col,
  Form,
  Input,
  Row,
  Select,
  DatePicker,
  FormInstance,
  Button,
} from 'antd';
import { SearchFormFetch } from './fetch';
import { HttpStatusCode } from '@/fetch/core/constant';
import { dropDownKey, dropDownListKey, DropDownType } from '@/utils/constant';
const { RangePicker } = DatePicker;
import locale from 'antd/es/date-picker/locale/zh_CN';
import './index.scss';
import MultiSelectComp from '../MultiSelectComp';

export interface SearchFormConfigData {
  name: string;
  title: string;
  placeHolder?: string;
  type: DropDownType;
  showSearch?: boolean;
  linkedDepartment?: boolean; // 是否是四级地址联动
  dropDownKey?: dropDownKey; // 通过getCommonDrownList接口获取下拉框内容传dropDownKey
  dropDownListKey?: dropDownListKey; // 通过getCommonDrownList接口获取下拉框内容传dropDownListKey
  childrenList?: ('station' | 'province' | 'city' | 'country')[];
  parentList?: ('station' | 'province' | 'city' | 'country')[];
  showParent?: boolean;
  clearChild?: boolean;
  onFocus?: Function;
  dataLevel?: 'COMPNAY' | 'PERSON'; // 获取公司或者账户下站点COMPNAY-公司下,PERSON-账户下
  xxl?: number;
  xl?: number;
  lg?: number;
  md?: number;
}

// key与对应下拉框配置项configData里的name保持一致
export interface DropDownMapType {
  [key: string]: { label: any; value: any }[];
}
interface Props {
  configData: SearchFormConfigData[]; // 搜索框表单的配置项
  onResetClick?: Function; // 点击重置
  onSearchClick?: Function; // 点击查询
  initValues?: any; // 搜索条件的初始值
  formRef?: FormInstance;
  dropDownMap?: DropDownMapType; // getCommonDrownList接口无法获取到下拉框列表，从页面中传过来
  noResetBtn?: boolean; // 是否没有重置按钮
  noSearchBtn?: boolean; // 是否没有查询按钮
  colon?: boolean;
  title?: string;
  children?: any;
}

interface Options {
  label: any;
  value: any;
}

const Searchform = ({
  title,
  colon,
  noResetBtn,
  dropDownMap,
  formRef,
  configData,
  onSearchClick,
  onResetClick,
  initValues,
  noSearchBtn,
  children,
}: Props) => {
  const [form] = Form.useForm();
  const fetchApi = new SearchFormFetch();
  const [currentList, setCurrentList] = useState<Options[]>([]);
  let searchFormRef = formRef ? formRef : form;
  useEffect(() => {
    if (initValues) {
      searchFormRef.setFieldsValue(initValues);
    }
  }, [JSON.stringify(initValues)]);

  const makeLayout = () => {
    let titleMaxLength = 4;
    configData.forEach((item: any) => {
      if (
        (item.title?.length && item.title?.length > titleMaxLength) ||
        (item.label?.length && item.label?.length > titleMaxLength)
      ) {
        titleMaxLength = item.title.length || item.label.length;
      }
    });
    titleMaxLength = titleMaxLength >= 9 ? 9 : titleMaxLength;
    return {
      labelCol: { span: titleMaxLength + 2 },
      wrapperCol: { span: 24 - (titleMaxLength + 2) },
    };
  };

  const getDropDownList = async (item: any) => {
    if (item.dropDownKey) {
      const res = await fetchApi.getCommonDropDown([item.dropDownKey]);
      if (res && res.code === HttpStatusCode.Success) {
        setCurrentList(
          res.data[item.dropDownListKey].map((item: any) => {
            return {
              label: item.name,
              value: item.code,
            };
          }),
        );
      }
    }
  };

  // 当点击大区、省份、城市、站点、停靠点下拉框时
  const getDepartmentList = async (item: any) => {
    const data = item.parentList
      ? searchFormRef.getFieldsValue(item.parentList)
      : null;
    // 获取当前项的下拉框内容
    const res = await fetchApi.getCurrentDownList({
      level: item.name,
      countryIdList: data?.country?.value ? [data?.country?.value] : [],
      provinceIdList: data?.province?.value ? [data?.province?.value] : [],
      cityIdList: data?.city?.value ? [data?.city?.value] : [],
      enbale: 1,
      dataLevel: item?.dataLevel,
    });
    if (res && res.code === HttpStatusCode.Success) {
      setCurrentList(
        res.data.map((item: any) => {
          return {
            label: item.name,
            value: item.code,
          };
        }),
      );
    }
  };

  // 当选中值变化
  const onValueChange = async (item: any, value: any) => {
    // 清空子级
    if (item.childrenList && item.clearChild) {
      searchFormRef.resetFields(item.childrenList);
    }
    // 获取父辈节点
    if (item.showParent && item.parentList) {
      const res = await fetchApi.getParentLinked({
        id: value?.value,
        level: item.name,
      });
      if (res && res.code === HttpStatusCode.Success) {
        item.parentList.forEach((item: any) => {
          const data = res.data.filter((value: any) => value.level === item)[0];
          searchFormRef.setFieldsValue({
            [item]: {
              key: data.code,
              value: data.code,
              label: data.name,
            },
          });
        });
      }
    }
  };

  const makeSelectOptions = (item: any) => {
    let list: any[] = [];
    if (!item.dropDownKey && dropDownMap) {
      list = item.name && dropDownMap[item.name];
    }
    return list;
  };

  const renderFieldItem = (item: any) => {
    let bar: JSX.Element;
    switch (item.type) {
      case DropDownType.SELECT:
        bar = (
          <Select
            style={{ textAlign: 'left' }}
            labelInValue
            options={
              item.dropDownKey || item.linkedDepartment
                ? currentList
                : makeSelectOptions(item)
            }
            filterOption={(input: any, option: any) => {
              const label: any = option?.label || '';
              return (
                label.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
              );
            }}
            allowClear
            showSearch={item.showSearch}
            placeholder={item.placeHolder}
            loading={item.loading ?? false}
            onFocus={() => {
              item.onFocus && item.onFocus(); // 自定义下拉框列表
              item.linkedDepartment && getDepartmentList(item); // 四级联动
              item.dropDownKey && getDropDownList(item); // 公共下来列表
            }}
            onBlur={() => {
              (item.dropDownKey || item.linkedDepartment) && setCurrentList([]);
            }}
            onChange={(value: any) => {
              item.linkedDepartment && onValueChange(item, value);
            }}
            onClear={() => item.onClear && item.onClear()}
          />
        );
        break;
      case DropDownType.DATEPICKER:
        bar = (
          <RangePicker
            showTime={item.isShowTime}
            locale={locale}
            style={{ width: '100%', minWidth: item.isShowTime && '360px' }}
          />
        );
        break;
      case DropDownType.INPUT:
        bar = (
          <Input
            allowClear
            placeholder={item.placeHolder ?? ''}
            onChange={(e: any) => {
              searchFormRef?.setFieldsValue({
                [item.name]: e.target.value.trim(),
              });
              if (item.inputType === 'number') {
                const value = e.target.value;
                const newValue = value.replace(/[^\-?\d.]/g, '');
                searchFormRef?.setFieldsValue({
                  [item.name]: newValue,
                });
              }
              item.onChange && item.onChange(e.target.value);
            }}
            maxLength={item.maxLength ? item.maxLength : 50}
          />
        );
        break;
      case DropDownType.MULTIPLESELECT:
        bar = (
          <MultiSelectComp
            options={
              item.dropDownKey || item.linkedDepartment
                ? currentList
                : makeSelectOptions(item)
            }
            onChange={() => {}}
          />
        );
        break;
      default:
        return null;
    }
    return bar;
  };

  return (
    <div>
      {title && (
        <div className="list-page-title">
          <span>{title}</span>
        </div>
      )}
      <div className="searchform-container">
        <Form
          {...makeLayout()}
          form={searchFormRef}
          colon={colon ? colon : false}
          size="middle"
        >
          <Row gutter={24} align="middle" style={{ width: '100%' }}>
            {configData.map((item: any, subIndex: any) => {
              return (
                <Col
                  xxl={item.xxl ? item.xxl : 4}
                  xl={item.xl ? item.xl : 6}
                  lg={item.lg ? item.lg : 8}
                  md={item.md ? item.md : 24}
                  key={subIndex}
                >
                  <Form.Item
                    key={subIndex}
                    name={item.name}
                    label={<div className="label-name">{item.title}</div>}
                    wrapperCol={item.wrapperCol && item.wrapperCol}
                    labelCol={item.labelCol && item.labelCol}
                  >
                    {renderFieldItem(item)}
                  </Form.Item>
                </Col>
              );
            })}
            <Col
              xxl={24 - 4 * (configData.length % 6)}
              xl={24 - 6 * (configData.length % 4)}
              lg={24 - 8 * (configData.length % 3)}
              md={24}
              className={'operation-btn'}
            >
              <Row justify={'end'}>
                {noResetBtn ? null : (
                  <Button
                    className="reset"
                    onClick={() => {
                      searchFormRef.setFieldsValue(initValues);
                      onResetClick && onResetClick();
                    }}
                  >
                    重置
                  </Button>
                )}
                {noSearchBtn ? null : (
                  <Button
                    type="primary"
                    onClick={() => {
                      onSearchClick &&
                        onSearchClick(searchFormRef.getFieldsValue());
                    }}
                  >
                    查询
                  </Button>
                )}
                {children}
              </Row>
            </Col>
          </Row>
        </Form>
      </div>
    </div>
  );
};

export default React.memo(Searchform);
