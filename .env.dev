NODE_ENV=development
PUBLIC_URL=/
port=3000
#变量以JDX_APP_ 开头定义


JDX_APP_FETCH_DATA_DOMAIN = 'jdxgateway-beta.jdl.cn'
JDX_APP_LOCATION_HOST = 'jdxvehicle-ui-beta.jdl.cn'

JDX_APP_PASSPORT_LOGIN_HOST = 'http://sso.jdl.cn/sso/login?ReturnUrl='
JDX_APP_JDL_LOGOUT_HOST = 'https://sso.jdl.com/exit?callback=?'
JDX_APP_OTA_URL=https://jdxota-beta-ui.jdl.cn
JDX_APP_MAP_EDITOR=jdxgateway-beta.jdl.cn
JDX_APP_BI_URL=https://bi-beta.jdl.cn
JDX_APP_MAP_DATA=beta

#跳转地图组链接
JDX_APP_MAP_GROUP=https://out-test-data-task.jd.com/#/warehouseBlueprint
JDX_APP_CLOUD_FETCH_DOMAIN=uat-api-cloud.jdl.cn
JDX_APP_REQUEST_HEADER=jdx.management.public.jsf.beta
JDX_APP_UPLOAD_PRE_SIGNATURE_URL=https://uat-api-cloud.jdl.cn/infrastructure/oss/getPreUrl
JDX_APP_MAP_EDITOR=jdxgateway-beta.jdl.cn
JDX_APP_MAP_DATA=beta
