import React, { useEffect, useState } from 'react';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import baseRoutes from './routes';
import { useRoutes, useNavigate, useLocation } from 'react-router-dom';
import { RootState } from '@/redux/store';
import { useSelector, useDispatch } from 'react-redux';
import AllActionTypes from './redux/actionType';
import { actionSendHttpRequest } from './redux/action';
import { HttpStatusCode } from '@/fetch/core/constant';
import { message } from 'antd';
import { LoginState } from './utils/enum';
import '@/assets/css/index.scss';
import '@jd/x-coreui/lib/index.css';

// 根据menus来筛选路由
// const filterRoute = (menus: any[], routes: any[]) => {
//   menus.forEach((menuItem, index) => {
//     if (menuItem.path) {
//       const Module = AsyncComponent(
//         () =>
//           import(
//             /* webpackChunkName: "[request]" */ `./views/${menuItem.elementName}`
//           ),
//       );
//       routes.push({
//         path: menuItem.path.split('/').at(-1),
//         element: <Module />,
//       });
//     }
//     if (menuItem.children) {
//       filterRoute(menuItem.children, routes);
//     }
//   });
//   return routes;
// };

const App = () => {
  const dispatch = useDispatch();
  const navigator = useNavigate();
  const location = useLocation();
  // const [routes, setRoutes] = useState(baseRoutes);
  const { menuData, hasLogin, userName } = useSelector(
    (state: RootState) => state.common,
  );
  const loginState = localStorage.getItem('loginState');

  useEffect(() => {
    if (location.pathname === '/') {
      return;
    }
    if (location.pathname === '/login') {
      authenticateLogin();
    } else if (
      (location.pathname.includes('/app/') ||
        location.pathname.includes('/ota/')) &&
      hasLogin !== LoginState.LOGIN_SUCCESS
    ) {
      getUserInfo();
    }
  }, [location.pathname]);

  const router: any = useRoutes(baseRoutes);

  const authenticateLogin = () => {
    const requestData: HTTPFetchActionParams = {
      method: 'GET',
      path: '/authentication/server/login/passport_login',
      urlParams: {
        appCode: 'operation-platform-ui',
      },
      actionType: AllActionTypes.AUTHENTICATE_LOGIN,
      nextActionFunc: (res: any) => {
        if (res.code === HttpStatusCode.Success) {
          getUserInfo((res: any) => {
            const { appResourceInfoList, menuData } = res.data;

            if (!menuData) {
              // 当前账号没有权限系统任何权限
              navigator('/app/noPermissionForLogin');
              return;
            }
            const firstPath: any = menuData[0]?.children[0]?.path;
            if (firstPath) {
              navigator(firstPath);
            } else {
              navigator('/app/noPermissionForLogin');
            }
          });
        } else {
          message.error(res.message);
        }
      },
    };
    dispatch(actionSendHttpRequest(requestData));
  };

  const getUserInfo = (cb?: Function) => {
    const requestData: HTTPFetchActionParams = {
      method: 'GET',
      path: '/permission/server/basic/user/get_user_info_after_login',
      urlParams: {
        appCode: 'operation-platform-ui',
      },
      actionType: AllActionTypes.USER_INFO,
      nextActionFunc: (res: any) => {
        try {
          (window as any).__qd__ &&
            (window as any).__qd__.config('userId', () => {
              return res?.data?.userName || '登录失败';
            });
        } catch (e) {
          console.log(e);
        }
        cb && cb(res);
      },
    };
    dispatch(actionSendHttpRequest(requestData));
  };

  return (
    <ConfigProvider locale={zhCN} prefixCls="main-app">
      {router}
    </ConfigProvider>
  );
};

export default App;
