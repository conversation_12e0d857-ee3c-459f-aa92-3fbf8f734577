/* eslint-disable react/display-name */



export const remakeTableItemData = (itemList: any) => {
  if (itemList == null) {
    return [];
  }
  const newList = itemList.map((item: any) => {
    const enableName = item.enableName === '停用' ? '启用' : '停用';
    return {
      ...item,
      key: item.id,
      phone: { "customerPhone": item.customerPhone, "secret": true },
      operate: { "status": enableName, "edit": '编辑', "lookFor": "查看代收点" },
    };
  });
  return newList;
}
