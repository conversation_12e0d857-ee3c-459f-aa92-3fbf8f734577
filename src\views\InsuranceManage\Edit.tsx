import React, { useEffect, useState, useRef } from 'react';
import { InsuranceDevicesColumns, InsuranceFormConfig } from './utils/columns';
import { CommonForm } from '@jd/x-coreui';
import { useNavigate, useParams } from 'react-router-dom';
import InsuranceManage, {
  InsuranceDetail,
} from '@/fetch/business/insuranceManage';
import { Col, Table, FormInstance, Row, Radio, message } from 'antd';
import { CommonEdit } from '@/components';
import { InsuranceTitle, PageType } from '@/utils/EditTitle';
import TextArea from 'antd/es/input/TextArea';
import dayjs from 'dayjs';
import { DeviceInsuredStatus } from '@/utils/constant';
import { cloneDeep, isEmpty } from 'lodash';
import { HttpStatusCode } from '@/fetch/core/constant';

const Edit = () => {
  const navigate = useNavigate();
  const { insuranceId } = useParams();
  const [insuranceDetail, setInsuranceDetail] =
    useState<InsuranceDetail | null>(null);
  const [batchUploadList, setBatchUploadList] = useState<any[]>([]);
  const [remark, setRemark] = useState('');
  const formRef = useRef<any>(null);
  const [formConfig, setFormConfig] = useState<any>({
    fields: [
      ...InsuranceFormConfig.fields,
      {
        fieldName: 'deviceInsuranceInfoList',
        label: '投保车辆',
        required: true,
        type: 'ReactNode',
        labelCol: { span: 3 },
        wrapperCol: { span: 21 },
      },
    ],
  });
  const [validationErrors, setValidationErrors] = useState<Set<string>>(
    new Set(),
  );

  const formatColumns = () => {
    return InsuranceDevicesColumns.map((item) => {
      switch (item.dataIndex) {
        case 'deviceInsuredStatus':
          return {
            ...item,
            render: (text, record) => {
              return (
                <Radio.Group
                  value={record.deviceInsuredStatus}
                  options={[
                    {
                      label: '正常',
                      value: DeviceInsuredStatus.NORMAL,
                    },
                    {
                      label: '退保',
                      value: DeviceInsuredStatus.LAPSED,
                    },
                  ]}
                  onChange={(e) => {
                    setBatchUploadList(
                      batchUploadList.map((item) => {
                        if (item.deviceName === record.deviceName) {
                          if (e.target.value === DeviceInsuredStatus.NORMAL) {
                            setValidationErrors((prev) => {
                              const next = new Set(prev);
                              next.delete(item.deviceName);
                              return next;
                            });
                          }
                          return {
                            ...item,
                            deviceInsuredStatus: e.target.value,
                          };
                        }
                        return item;
                      }),
                    );
                  }}
                />
              );
            },
          };
        case 'remark':
          return {
            ...item,
            render: (text, record) => {
              const hasError = validationErrors.has(record.deviceName);
              return (
                <div>
                  <TextArea
                    value={record.remark}
                    rows={2}
                    maxLength={50}
                    placeholder="请输入备注"
                    status={hasError ? 'error' : undefined}
                    onChange={(e) => {
                      const newValue = e.target.value;
                      setBatchUploadList(
                        batchUploadList.map((item) => {
                          if (item.deviceName === record.deviceName) {
                            if (
                              newValue &&
                              item.deviceInsuredStatus ===
                                DeviceInsuredStatus.LAPSED
                            ) {
                              setValidationErrors((prev) => {
                                const next = new Set(prev);
                                next.delete(item.deviceName);
                                return next;
                              });
                            }
                            return { ...item, remark: newValue };
                          }
                          return item;
                        }),
                      );
                    }}
                  />
                </div>
              );
            },
          };
        default:
          return {
            ...item,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  useEffect(() => {
    if (insuranceId) {
      InsuranceManage.getInsuranceDetail(Number(insuranceId)).then((res) => {
        setInsuranceDetail(res.data);
        formRef.current?.setFieldsValue({
          policyNumber: res.data.policyNumber,
          insuranceCompany: res.data.insuranceCompany,
          insuranceType: res.data.insuranceType,
          insuredEntity: res.data.insuredEntity,
          policyCoverageAmount: res.data.policyCoverageAmount,
          policyAttachment: [res.data.policyAttachment.fileKey],
          otherAttachmentList:
            res.data.otherAttachmentList?.map((item: any) => item.fileKey) ||
            [],
          effectiveTime: [
            dayjs(res.data.effectiveStartTime),
            dayjs(res.data.effectiveEndTime),
          ],
        });
        setBatchUploadList(res.data.deviceInsuranceInfoList);
        const newFormConfig = cloneDeep(formConfig);

        newFormConfig.fields
          .find((item: any) => item.fieldName === 'policyAttachment')
          .uploadedFileList.push({
            fileKey: res.data.policyAttachment.fileKey,
            url: res.data.policyAttachment.url,
            uid: res.data.policyAttachment.fileKey,
          });
        if (
          res.data.otherAttachmentList &&
          !isEmpty(res.data.otherAttachmentList)
        ) {
          newFormConfig.fields.find(
            (item: any) => item.fieldName === 'otherAttachmentList',
          ).uploadedFileList = res.data.otherAttachmentList.map((item: any) => {
            return {
              fileKey: item.fileKey,
              url: item.url,
              uid: item.fileKey,
            };
          });
        }
        setFormConfig(newFormConfig);
        setRemark(res.data.remark);
      });
    }
  }, [insuranceId]);
  // 生成唯一key的函数
  const generateKey = (item: any) => `${item.deviceName}_${item.serialNo}`;
  const validateDeviceList = () => {
    const errors = new Set<string>();

    batchUploadList.forEach((item) => {
      if (
        item.deviceInsuredStatus === DeviceInsuredStatus.LAPSED &&
        !item.remark?.trim()
      ) {
        errors.add(item.deviceName);
      }
    });

    setValidationErrors(errors);
    return errors.size === 0;
  };

  const onSubmitClick = async () => {
    try {
      if (!validateDeviceList()) {
        message.error('存在退保车辆未填写备注');
        return;
      }
      const formValues = await formRef.current?.validateFields();
      const {
        otherAttachmentList,
        policyAttachment,
        effectiveTime,
        insuranceCompany,
        insuranceType,
        insuredEntity,
        policyCoverageAmount,
        policyNumber,
      } = formValues;
      const effectiveStartTime = effectiveTime[0].format('YYYY-MM-DD');
      const effectiveEndTime = effectiveTime[1].format('YYYY-MM-DD');
      const params = {
        insuranceId: Number(insuranceId),
        policyAttachment: {
          type: 'file',
          bucketName: 'rover-operation',
          fileKey: policyAttachment[0],
        },
        otherAttachmentList:
          otherAttachmentList?.map((item: any) => {
            return {
              type: 'file',
              bucketName: 'rover-operation',
              fileKey: item,
            };
          }) || [],
        insuranceCompany,
        insuranceType,
        insuredEntity,
        policyCoverageAmount,
        policyNumber,
        effectiveStartTime,
        effectiveEndTime,
        remark,
        deviceInsuranceInfoList: batchUploadList.map((item) => {
          return {
            deviceInsuranceId: item.deviceInsuranceId,
            deviceName: item.deviceName,
            serialNo: item.serialNo,
            provinceName: item.provinceName,
            cityName: item.cityName,
            stationName: item.stationName,
            radius: item.radius,
            deviceInsuredStatus: item.deviceInsuredStatus,
            remark: item.remark,
          };
        }),
      };
      InsuranceManage.editInsurance(params).then((res) => {
        if (res.code === HttpStatusCode.Success) {
          message.success('编辑成功');
          navigate('/app/insuranceManage');
        } else {
          message.error(res.message);
        }
      });
    } catch (err) {
      console.error(err);
    }
  };
  const onCancleClick = () => {
    navigate('/app/insuranceManage');
  };
  return (
    <CommonEdit
      title={InsuranceTitle[PageType.EDIT]}
      breadCrumbConfig={[
        {
          title: '车辆保险管理',
          route: '',
        },
        {
          title: '编辑',
          route: '',
        },
      ]}
      onSubmitClick={onSubmitClick}
      onCancleClick={onCancleClick}
      cancelTitle={'取消'}
    >
      <CommonForm
        formConfig={formConfig}
        formType="edit"
        layout="horizontal"
        colon={false}
        getFormInstance={(form: FormInstance) => {
          formRef.current = form;
        }}
      />
      <Row justify="space-around" style={{ marginBottom: 16 }}>
        <Col span={18}>
          <Table
            rowKey={(record) => generateKey(record)}
            columns={formatColumns()}
            dataSource={batchUploadList}
            pagination={false}
            scroll={{ y: 300 }}
          ></Table>
        </Col>
      </Row>
      <Row justify="center">
        <Col span={1}>
          <label>备注</label>
        </Col>
        <Col span={18}>
          <TextArea
            rows={5}
            maxLength={200}
            placeholder="请输入备注信息"
            value={remark}
            onChange={(e) => {
              setRemark(e.target.value);
            }}
          />
        </Col>
      </Row>
    </CommonEdit>
  );
};

export default Edit;
