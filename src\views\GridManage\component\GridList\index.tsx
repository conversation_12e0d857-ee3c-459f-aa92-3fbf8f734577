/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import GridTableList from '../GridTableList';
import { EditModuleTitle, CustomButton, ButtonType } from '@/components';
import './index.scss';
import { Form, FormInstance, Col, Row, message } from 'antd';
import Config from './Config';
import PreviewGrid from '../PreviewGrid';
import { setGridListAction } from '@/redux/reducer/boxGrid';
import { PageType, ProductType } from '@/utils/enum';

const layout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 20 },
};
const GridList = ({
  gridList,
  formRef,
  disabled,
  moduleTitle,
  previewClick,
  viewType,
  isShowCarImg,
  productionType,
}: {
  gridList: any;
  formRef: FormInstance;
  disabled?: boolean;
  moduleTitle: string;
  previewClick?: Function;
  viewType?: any;
  isShowCarImg?: boolean;
  productionType: ProductType;
}) => {
  const dispatch = useDispatch();
  const [isShowPreBtn, setIsShowPreBtn] = useState(false);
  const [isShowImg, setIsShowImg] = useState<any>(false);
  useEffect(() => {
    setIsShowPreBtn(
      gridList && gridList.length !== 0 && viewType != PageType.READONLY,
    );
    formRef.setFieldsValue({
      gridList: gridList,
    });
    if (viewType == PageType.READONLY) {
      dispatch(setGridListAction(gridList));
    }
  }, [gridList]);

  useEffect(() => {
    setIsShowImg(isShowCarImg);
  }, [isShowCarImg]);

  return (
    <div>
      <EditModuleTitle title={moduleTitle} />
      <Config
        disabled={viewType === PageType.READONLY}
        productionType={productionType}
      ></Config>
      <Form {...layout} form={formRef}>
        <Form.Item name="gridList" label=" " colon={false}>
          <GridTableList
            productionType={productionType}
            viewType={viewType}
            disabled={disabled}
            initValues={gridList}
            onGridChanged={(values: any) => {
              formRef.setFieldsValue({
                gridList: values,
              });
              setIsShowPreBtn(
                values.length !== 0 && viewType != PageType.READONLY,
              );
            }}
          />
        </Form.Item>
      </Form>
      {/* 生成效果图按钮 */}
      {isShowPreBtn && productionType === 'vehicle' ? (
        <Row>
          <Col span={20} offset={3}>
            <CustomButton
              title="生成货箱效果图"
              buttonType={ButtonType.DefaultButton}
              otherCSSProperties={{
                marginBottom: '20px',
                border: '1px solid #31C2A6',
              }}
              onSubmitClick={() => {
                if (previewClick && previewClick()) {
                  let msg = '货箱效果图已更新';
                  if (!isShowImg) {
                    setIsShowImg(true);
                    msg = '货箱效果图已生成';
                  }
                  message.success(msg);
                }
              }}
            />
          </Col>
        </Row>
      ) : null}
      {/* 效果图展示部分 */}
      {isShowImg && productionType === 'vehicle' ? (
        <Row>
          <Col span={20} offset={3}>
            <PreviewGrid></PreviewGrid>
          </Col>
        </Row>
      ) : null}
    </div>
  );
};

export default React.memo(GridList);
