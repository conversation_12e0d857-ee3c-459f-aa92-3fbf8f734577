/* eslint-disable no-unused-vars */

import {
  Form,
  FormInstance,
  Input,
  Table,
  Modal,
  message,
  Popconfirm,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { PerButton } from '@/components';
import { HardwareTypeApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';

interface UsageModel {
  id: number | null;
  usageName: string | null;
  enable?: number;
  newAdd?: boolean;
}

const AddUsage = ({
  disable,
  form,
  initValues,
}: {
  disable?: boolean;
  form: FormInstance;
  initValues?: any;
}) => {
  const fetchApi = new HardwareTypeApi();
  const usageNameColums: any[] = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
      width: 100,
      ellipsis: true,
      render: (text: any, record: any, index: number) => index + 1,
    },
    {
      title: '用途名称',
      dataIndex: 'usageName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'operate',
      align: 'center',
      width: 150,
      render: (params: any) => {
        return (
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-around',
            }}
          >
            <a
              onClick={() => {
                setShowModal({
                  show: true,
                  usage: params,
                });
                editForm.setFieldsValue({
                  id: params.id,
                  name: params.usageName,
                });
              }}
            >
              编辑
            </a>
            <Popconfirm
              title="是否确认删除此条用途名称"
              onConfirm={async () => {
                if (params.newAdd) {
                  setUsageList([
                    ...usageList.filter((item) => item.id != params.id),
                  ]);
                } else {
                  const canDelete: any = await fetchApi.canDeleteUsage(
                    params.id,
                  );
                  if (canDelete.code === HttpStatusCode.Success) {
                    setUsageList([
                      ...usageList.map((item) => {
                        if (item.id === params.id) {
                          item.enable = 0;
                        }
                        return item;
                      }),
                    ]);
                  } else {
                    message.error(canDelete.message);
                  }
                }
              }}
            >
              <a style={{ color: 'red' }}>删除</a>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  const [usageList, setUsageList] = useState<UsageModel[]>([]);

  const [showUsageModal, setShowModal] = useState<{
    show: boolean;
    usage: UsageModel | null;
  }>({
    show: false,
    usage: null,
  });
  const [editForm] = Form.useForm();

  const makeTableDatasource = () => {
    return usageList
      .map((item, index) => {
        return { ...item, key: item.id, operate: item };
      })
      .filter(
        (item) =>
          (item.newAdd === undefined && item.enable === 1) || item.newAdd,
      );
  };

  const checkUsageNameRepeat = (value: any) => {
    let repeated = false;
    const filteredList = usageList.filter(
      (item) => item.usageName === `${value}`,
    );
    const { usage } = showUsageModal;
    if (usage && usage.usageName === value) {
      repeated = false;
    } else if (filteredList.length >= 1) {
      const deletedItem = filteredList[0];
      if (!deletedItem.newAdd && deletedItem.enable === 0) {
        repeated = false;
      } else {
        repeated = true;
      }
    }
    return repeated;
  };

  const checkUsageNameList = (_: any, value: any) => {
    if (usageList.length > 0) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('请添加用途'));
  };

  useEffect(() => {
    if (initValues) {
      setUsageList(initValues);
    }
  }, [initValues]);

  useEffect(() => {
    form.setFieldsValue({
      hardwareTypeUsageList: usageList?.map((item) => {
        return {
          id: item.newAdd ? null : item.id,
          usageName: item.usageName,
          enable: item.enable,
        };
      }),
    });
  }, [JSON.stringify(usageList)]);

  return (
    <>
      <Form.Item
        name="hardwareTypeUsageList"
        label="添加用途"
        required
        rules={[{ validator: checkUsageNameList }]}
      >
        <div>
          {disable ? null : (
            <div>
              <PerButton
                title="新建用途"
                clickCallBack={() => {
                  setShowModal({
                    usage: null,
                    show: true,
                  });
                }}
              />
            </div>
          )}
          <div style={{ marginTop: '10px' }}>
            <Table
              pagination={false}
              size="small"
              bordered
              columns={usageNameColums.filter((item) =>
                disable ? item.dataIndex != 'operate' : true,
              )}
              dataSource={makeTableDatasource()}
            />
          </div>
        </div>
      </Form.Item>
      {showUsageModal.show ? (
        <Modal
          maskClosable={false}
          title={showUsageModal.usage != null ? '编辑用途' : '新建用途'}
          closable={false}
          visible={showUsageModal.show}
          okText="确定"
          cancelText="取消"
          onOk={async () => {
            try {
              const editValue = await editForm.validateFields();
              const { usage } = showUsageModal;
              if (usage) {
                // edit
                usageList.forEach((item) => {
                  if (item.id === usage.id) {
                    item.usageName = editValue.name;
                  }
                });
                setShowModal({
                  usage: null,
                  show: false,
                });
                editForm.resetFields();
              } else {
                const filteredList = usageList.filter(
                  (item) => item.usageName === `${editValue.name}`,
                );
                if (filteredList.length >= 1) {
                  const deletedItem = filteredList[0];
                  if (!deletedItem.newAdd && deletedItem.enable === 0) {
                    deletedItem.enable = 1;
                  }
                } else {
                  setUsageList(
                    usageList.concat([
                      {
                        id: Date.now(),
                        enable: 1,
                        usageName: editValue.name,
                        newAdd: true,
                      },
                    ]),
                  );
                }
                setShowModal({
                  usage: null,
                  show: false,
                });
                form.validateFields(['usageList']);
                editForm.resetFields();
              }
            } catch (e) {
              //
            }
          }}
          onCancel={() => {
            setShowModal({
              ...showUsageModal,
              show: false,
            });
            editForm.resetFields();
          }}
        >
          <Form labelCol={{ span: 4 }} form={editForm}>
            <Form.Item
              name="name"
              label="用途名称"
              required
              rules={[
                {
                  validator: (_: any, value: any) => {
                    if (!value || value.length < 1) {
                      return Promise.reject(new Error('请输入用途名称'));
                    }
                    if (checkUsageNameRepeat(value)) {
                      return Promise.reject(
                        new Error('用途名称已存在，请重新输入'),
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Input
                autoComplete="off"
                autoFocus
                placeholder="请输入用途名称"
                onBlur={(e) => {
                  const value = `${e.target.value || ''}`;
                  editForm.setFieldsValue({
                    name: value.trim(),
                  });
                }}
                maxLength={20}
              />
            </Form.Item>
          </Form>
        </Modal>
      ) : null}
    </>
  );
};

export default React.memo(AddUsage);
