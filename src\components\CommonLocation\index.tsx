import React, { useState } from 'react';
import { FormInstance, Form, Select, Row, Col } from 'antd';
import { SearchFormFetch } from '../SearchForm/fetch';
import { HttpStatusCode } from '@/fetch/core/constant';
import './index.scss';

const CommonLocation = ({
  showList,
  formRef,
  onValueChange,
  stationOrigin,
}: {
  onValueChange?: Function;
  showList: ('station' | 'province' | 'city' | 'country')[];
  formRef: FormInstance;
  stationOrigin?: 'COMPNAY' | 'PERSON'; // 获取公司或者账户下站点COMPNAY-公司下,PERSON-账户下
}) => {
  const fetchApi = new SearchFormFetch();
  const [currentList, setCurrentList] = useState<
    {
      label: any;
      value: any;
    }[]
  >([]);

  const configData: {
    name: 'station' | 'province' | 'city' | 'country';
    placeholder: string;
    childrenList?: string[];
    fatherList?: string[];
  }[] = [
    {
      name: 'country',
      placeholder: '请选择所在国家',
      childrenList: ['province', 'city', 'station'],
    },
    {
      name: 'province',
      placeholder: '请选择所在省份',
      childrenList: ['city', 'station'],
      fatherList: ['country'],
    },
    {
      name: 'city',
      placeholder: '请选择所在城市',
      childrenList: ['station'],
      fatherList: ['country', 'province'],
    },
    {
      name: 'station',
      placeholder: '请选择站点名称',
      fatherList: ['country', 'province', 'city'],
    },
  ];

  // 当点击大区、省份、城市、站点、停靠点下拉框时
  const getDepartmentList = async (item: any) => {
    const data = item.fatherList
      ? formRef.getFieldsValue(item.fatherList)
      : null;
    // 获取当前项的下拉框内容
    const res = await fetchApi.getCurrentDownList({
      level: item.name,
      countryIdList:
        showList.indexOf('country') > 0 && data?.country?.value
          ? [data?.country?.value]
          : [],
      provinceIdList:
        showList.indexOf('province') > 0 && data?.province?.value
          ? [data?.province?.value]
          : [],
      cityIdList:
        showList.indexOf('city') > 0 && data?.city?.value
          ? [data?.city?.value]
          : [],
      enbale: 1,
      dataLevel: stationOrigin ?? 'PERSON',
    });
    if (res && res.code === HttpStatusCode.Success) {
      setCurrentList(
        res.data.map((item: any) => {
          return {
            label: item.name,
            value: item.code,
          };
        })
      );
    }
  };

  // 当选中值变化
  const onSelectValueChange = async (item: any, value: any) => {
    // 清空子级
    if (item.childrenList?.length > 0) {
      formRef.resetFields(item.childrenList);
    }
    // 获取父辈节点
    const changedValue = formRef.getFieldsValue(showList);
    if (item.fatherList?.length > 0) {
      const res = await fetchApi.getParentLinked({
        id: value?.value,
        level: item?.name,
      });
      if (res && res.code === HttpStatusCode.Success) {
        item.fatherList.forEach((item: any) => {
          const data = res.data.filter((value: any) => value.level === item)[0];
          formRef.setFieldsValue({
            [item]: {
              key: data.code,
              value: data.code,
              label: data.name,
            },
          });
        });
      }
      if (res.data) {
        res.data.forEach(
          (item: { level: string; name: string; code: number }) => {
            changedValue[item.level] = { label: item.name, value: item.code };
          }
        );
      }
    }
    onValueChange && onValueChange(changedValue);
  };

  return (
    <div className="common-location-container">
      <Form form={formRef} colon={false}>
        <Form.Item
          label={
            <div>
              <span style={{ color: 'red' }}>*</span>
              {` 所在地域`}
            </div>
          }
        >
          <Row gutter={20}>
            {configData.map((item, index) => {
              return showList.indexOf(item.name) > -1 ? (
                <Col
                  key={item.name}
                  xxl={Math.floor(24 / showList.length)}
                  xl={Math.floor(24 / showList.length)}
                  lg={24}
                  md={24}
                >
                  <Form.Item
                    name={item.name}
                    rules={[
                      { required: true, message: item.placeholder ?? '' },
                    ]}
                  >
                    <Select
                      style={{ textAlign: 'left' }}
                      labelInValue
                      options={currentList}
                      filterOption={(input: any, option: any) => {
                        const label: any = option?.label || '';
                        return (
                          label
                            .toString()
                            .toLowerCase()
                            .indexOf(input.toLowerCase()) >= 0
                        );
                      }}
                      allowClear
                      showSearch={true}
                      placeholder={item.placeholder}
                      onFocus={() => {
                        getDepartmentList(item);
                      }}
                      onBlur={() => setCurrentList([])}
                      onChange={(value: any) => {
                        onSelectValueChange(item, value);
                      }}
                    />
                  </Form.Item>
                </Col>
              ) : null;
            })}
          </Row>
        </Form.Item>
      </Form>
    </div>
  );
};

export default React.memo(CommonLocation);
