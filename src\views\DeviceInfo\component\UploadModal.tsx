/* eslint-disable react/no-unescaped-entities */
/* eslint-disable no-unused-vars */

import { Col, Form, Input, message, Modal, Row, Upload } from 'antd';
import React, { useEffect, useState } from 'react';
import { CustomButton, ButtonType } from '@/components';
import { CommonApi, downLoadUrlType } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';

const modalConfig: any = {
  addVehicle: {
    type: downLoadUrlType.VEHICLE,
    modalTitle: '批量新增车辆',
    downloadBtnTitle: '批量新增车辆模板',
    downloadPath: '',
    uploadPath: '',
    tips: (
      <>
        <div>
          1、导入模板“<b style={{ color: 'red' }}>*</b>”为必填项；
        </div>
        <div>2、非必填项系统不校验准确性。</div>
      </>
    ),
  },
  addCardNo: {
    type: downLoadUrlType.CARD,
    modalTitle: '批量导入更多设备号',
    downloadBtnTitle: '导入模板',
    downloadPath: '',
    uploadPath: '',
    tips: (
      <>
        <div>
          1、导入模板“<b style={{ color: 'red' }}>*</b>”为必填项；
        </div>
        <div>2、系统不校验非必填内容。</div>
      </>
    ),
  },
};

const UploadModal = ({
  visable,
  module,
  onComplete,
}: {
  visable: boolean;
  module: 'addVehicle' | 'addCardNo';
  onComplete: Function;
}) => {
  const commonFetchApi = new CommonApi();
  const [editForm] = Form.useForm();
  const basicConfig = modalConfig[module];
  const [uploadStatus, setUploadStatus] = useState<
    'uploading' | 'finished' | 'normal'
  >('normal');
  const [choosedFile, setChoosedFile] = useState<any>(null);
  const [uploadResult, setUploadResult] = useState<{
    id: any;
    name: any;
    totalCount: any;
    successCount: any;
    failCount: any;
    resultDescription: any;
    enable: any;
    buttonStyle: React.CSSProperties;
    downloadUrl: any;
  }>({
    id: null,
    name: null,
    totalCount: 0,
    successCount: 0,
    failCount: 0,
    resultDescription: null,
    enable: 0,
    buttonStyle: {
      color: '#666',
      backgroundColor: 'white',
    },
    downloadUrl: null,
  });
  const [downloadUrl, setDownloadUrl] = useState<any>();

  useEffect(() => {
    getdownloadTemplate();
  }, []);

  const uploadClick = async () => {
    if (!choosedFile) {
      message.error('请先选择要上传的文件');
      return;
    }
    setUploadStatus('uploading');
    let reqType = '';
    if (module === 'addCardNo') {
      reqType = 'VEHICLE_CARD_NO_BATCH_ADD';
    } else if (module === 'addVehicle') {
      reqType = 'VEHICLE_BATCH_ADD';
    }
    const resp: any = await commonFetchApi.upload([choosedFile], reqType);
    if (resp.code === HttpStatusCode.Success) {
      setUploadStatus('finished');
      const { failCount, enable } = resp.data;
      let downloadUrl;
      let buttonStyle: React.CSSProperties = {};
      if (failCount && failCount > 0 && enable === 1) {
        buttonStyle = {
          color: 'white',
          backgroundColor: '#31C2A6',
        };
        const host = process.env.JDX_APP_FETCH_DATA_DOMAIN;
        const path = '/k2/management/upload/upload_result_down';
        if (resp.data.successCount > 0 && enable === 1) {
          downloadUrl = `//${host}${path}?id=${resp.data.id}&success=${1}`;
        }
        if (resp.data.failCount > 0 && enable === 1) {
          downloadUrl = `//${host}${path}?id=${resp.data.id}&success=${0}`;
        }
      } else {
        buttonStyle = {
          color: '#333',
          backgroundColor: 'white',
        };
      }
      setUploadResult({
        ...uploadResult,
        ...resp.data,
        buttonStyle,
        downloadUrl,
      });
    } else {
      setUploadStatus('normal');
      message.error(resp.message);
    }
  };

  // 获取批量新增车辆模板及导入更多设备号模板
  const getdownloadTemplate = async () => {
    const res = await commonFetchApi.getDownloadURL(basicConfig.type);
    if (res.code === HttpStatusCode.Success) {
      const newUrl = res.data.url.replace('http', 'https');
      setDownloadUrl(newUrl);
    }
  };

  const makeUploadContent = () => {
    return (
      <Form form={editForm}>
        <Row>
          <Col span={24}>
            <Form.Item label="下载模板">
              <CustomButton
                title={basicConfig.downloadBtnTitle}
                buttonType={ButtonType.DefaultButton}
                otherCSSProperties={{
                  color: '#1890ff',
                  fontSize: 11,
                  height: 30,
                }}
                onSubmitClick={() => {
                  window.location.href = downloadUrl;
                }}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={20}>
          <Col span={18}>
            <Form.Item name="filePath" label="上传文件">
              <Input disabled placeholder="选择文件" />
            </Form.Item>
          </Col>
          <Col>
            <Upload
              accept=".xlsx,.xls"
              name="file"
              showUploadList={false}
              beforeUpload={(file) => {
                setChoosedFile(file);
                editForm.setFieldsValue({
                  filePath: file.name,
                });
                return false;
              }}
            >
              <CustomButton
                title="添加文件"
                buttonType={ButtonType.DefaultButton}
                height={30}
                otherCSSProperties={{ fontSize: 11 }}
                onSubmitClick={() => {}}
              />
            </Upload>
          </Col>
        </Row>
        <Row>
          <Col span={18}>
            <Form.Item label={`\xa0\xa0\xa0\xa0\xa0\xa0说明`}>
              <div style={{ color: '#808080' }}>
                <br />
                {basicConfig.tips}
              </div>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    );
  };

  const makeUploadFinishedContent = () => {
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          fontSize: '15px',
        }}
      >
        <div>{uploadResult.resultDescription ?? ''}</div>
        <div
          style={{
            marginTop: 20,
          }}
        >
          <CustomButton
            title="下载错误列表"
            buttonType={
              uploadResult.failCount > 0 && uploadResult.enable === 1
                ? ButtonType.PrimaryButton
                : ButtonType.DefaultButton
            }
            onSubmitClick={() => {
              if (uploadResult.failCount > 0 && uploadResult.enable === 1) {
                window.location.href = uploadResult.downloadUrl;
              }
            }}
          />
        </div>
      </div>
    );
  };

  return (
    <Modal
      width="50vw"
      maskClosable={false}
      title={
        uploadStatus === 'finished'
          ? `【${basicConfig.modalTitle}】导入结果`
          : basicConfig.modalTitle
      }
      footer={uploadStatus === 'finished' ? null : undefined}
      closable={uploadStatus === 'finished'}
      visible={visable}
      okText="上传"
      confirmLoading={uploadStatus === 'uploading'}
      cancelText="取消"
      onOk={uploadClick}
      onCancel={() => {
        onComplete ? onComplete(uploadResult.id !== null) : null;
      }}
    >
      {uploadStatus === 'finished'
        ? makeUploadFinishedContent()
        : makeUploadContent()}
    </Modal>
  );
};

export default React.memo(UploadModal);
