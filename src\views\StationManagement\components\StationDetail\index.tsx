import React, { useEffect, useRef, useState } from 'react';
import { BasicStationInfo, StationBusinessInfo } from '@/types';
import { message, Tabs, TabsProps } from 'antd';
import DetailHeader from './components/DetailHeader';
import './index.scss';
import EditableStationInfo from './components/StationBasicInfo/EditableStationInfo';
import EditableBusinessInfo from './components/BusinessInfo/EditableBusinessInfo';
import LinkedVehicle from '../LinkedVehicle';
import StopConfig from '../StopConfig';
import { StationFetchApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { formatLocation } from '@/utils/utils';
import { ProductType } from '@/utils/enum';
import ResourceManagement from '../ResourceManagement';
import ShelfMagnagement from '../ShelfManagement';
import { getWarehouseStationBusinessInfo } from '@/fetch/business/integrate';
export enum StationDetailTabKeys {
  STATION_INFO = 'STATION_INFO',
  BUSINESS_CONFIG = 'BUSINESS_CONFIG',
  LINKED_VEHICLE = 'LINKED_VEHICLE',
  STOP_CONFIG = 'STOP_CONFIG',
  RESOURCE_MANAGEMENT = 'RESOURCE_MANAGEMENT',
  SHELF_MANAGEMENT = 'SHELF_MANAGEMENT',
}

const fetchApi = new StationFetchApi();
const StationDetail = () => {
  const [activeKey, setActiveKey] = useState(
    StationDetailTabKeys.STATION_INFO as string,
  );
  const urlData = formatLocation(window.location.search);
  const stationBaseId = urlData.stationBaseId;
  const tabKey = urlData.tabKey;
  const [basicStationInfo, setBasicStationInfo] =
    useState<BasicStationInfo | null>(null);
  const [businessInfo, setBusinessInfo] = useState<StationBusinessInfo | null>(
    null,
  );
  const [vehicleTableKey, setVehicleTableKey] = useState('');
  const [stopTableKey, setStopTableKey] = useState('');
  const [dropDownMap, setDropDownMap] = useState<any>({
    stationUseCase: [],
    productTypeList: [],
    stationTypeList: [],
    userList: [],
  });
  useEffect(() => {
    getAllDropDown();
  }, []);

  useEffect(() => {
    if (tabKey) {
      setActiveKey(tabKey as string);
    }
  }, []);
  const getCommonDropDownList = async () => {
    const res = await fetchApi.getCommonDropDown([
      'STATION_USE_CASE',
      'STATION_TYPE',
      'PRODUCT_TYPE',
    ]);
    if (res && res.code === HttpStatusCode.Success) {
      return res.data;
    }
  };

  const getUserInfoList = async () => {
    const res = await fetchApi.getUserInfoList();
    if (res.code === HttpStatusCode.Success && res.data) {
      return res.data;
    }
  };

  const getAllDropDown = () => {
    Promise.all([getCommonDropDownList(), getUserInfoList()]).then((res) => {
      setDropDownMap({
        stationUseCase:
          res[0]?.stationUseCaseList &&
          res[0]?.stationUseCaseList?.map((item: any) => {
            return {
              label: item.name,
              value: item.code,
            };
          }),
        productTypeList:
          res[0]?.productTypeList &&
          res[0]?.productTypeList?.map((item: any) => {
            return {
              label: item.name,
              value: item.code,
            };
          }),
        stationTypeList:
          res[0]?.stationTypeList &&
          res[0]?.stationTypeList?.map((item: any) => {
            return {
              label: item.name,
              value: item.code,
            };
          }),
        userList:
          res[1] &&
          res[1]?.map((item: any) => {
            return {
              label: `${item.realName}/${item.erp}`,
              value: `${item.userName}/${item.phone}`,
            };
          }),
      });
    });
  };
  const getStationBasicInfo = async () => {
    try {
      const res = await fetchApi.fetchStationBasicInfo(stationBaseId);
      if (res.code === HttpStatusCode.Success) {
        setBasicStationInfo(res.data);
      } else {
        message.error(res.message);
      }
    } catch (error) {
      console.log(error);
    }
  };
  const getStationBusinessInfo = async () => {
    try {
      const request =
        basicStationInfo?.productType == ProductType.INTEGRATE
          ? getWarehouseStationBusinessInfo
          : fetchApi.getVehicleStationBusinessInfo;
      const res = await request(stationBaseId);
      if (res.code === HttpStatusCode.Success) {
        setBusinessInfo(res.data);
      } else {
        message.error(res.message);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    switch (activeKey) {
      case StationDetailTabKeys.STATION_INFO:
        getStationBasicInfo();
        break;
      case StationDetailTabKeys.BUSINESS_CONFIG:
        getStationBusinessInfo();
        break;
      default:
        break;
    }
  }, [activeKey, basicStationInfo?.productType]);
  let items: any = [
    {
      key: StationDetailTabKeys.STATION_INFO,
      label: '站点信息',
      children: (
        <EditableStationInfo
          stationDetail={basicStationInfo}
          dropDownMap={dropDownMap}
          updateDataCb={getStationBasicInfo}
          setDropDownMap={setDropDownMap}
        />
      ),
    },
    {
      key: StationDetailTabKeys.BUSINESS_CONFIG,
      label: '业务配置',
      children: (
        <EditableBusinessInfo
          stationType={basicStationInfo?.productType}
          stationBaseId={stationBaseId}
          businessInfo={businessInfo}
          updateDataCb={getStationBusinessInfo}
        ></EditableBusinessInfo>
      ),
    },
    {
      key: StationDetailTabKeys.LINKED_VEHICLE,
      label: `关联车辆（${basicStationInfo?.deviceCount}）`,
      children: (
        <LinkedVehicle
          stationType={basicStationInfo?.productType}
          stationBaseId={stationBaseId}
          tableKey={vehicleTableKey}
          setTableKey={setVehicleTableKey}
          updateDataCb={getStationBasicInfo}
        />
      ),
    },
    basicStationInfo?.productType !== ProductType.INTEGRATE && {
      key: StationDetailTabKeys.STOP_CONFIG,
      label: '停靠点配置',
      children: (
        <StopConfig
          stationBaseId={stationBaseId}
          tableKey={stopTableKey}
          setTableKey={setStopTableKey}
        />
      ),
    },
    basicStationInfo?.productType === ProductType.INTEGRATE && {
      key: StationDetailTabKeys.RESOURCE_MANAGEMENT,
      label: '资源管理',
      children: (
        <ResourceManagement
          stationBaseId={stationBaseId}
          warehouseNo={basicStationInfo?.number}
        />
      ),
    },
    basicStationInfo?.productType === ProductType.INTEGRATE && {
      key: StationDetailTabKeys.SHELF_MANAGEMENT,
      label: '上装管理',
      children: <ShelfMagnagement stationBaseId={stationBaseId} />,
    },
  ].filter(Boolean);
  return (
    <div className="station-detail">
      <DetailHeader
        stationDetail={basicStationInfo}
        updateDataCb={getStationBasicInfo}
      />
      <Tabs
        items={items}
        activeKey={activeKey}
        onChange={(activeKey) => {
          setActiveKey(activeKey);
          if (activeKey === StationDetailTabKeys.LINKED_VEHICLE) {
            setVehicleTableKey(new Date().getMilliseconds().toString());
          } else if (activeKey === StationDetailTabKeys.STOP_CONFIG) {
            setStopTableKey(new Date().getMilliseconds().toString());
          }
        }}
      ></Tabs>
    </div>
  );
};
export default React.memo(StationDetail);
