import React, { useEffect, useState } from 'react';
import { HttpStatusCode } from '@/fetch/core/constant';
import { OrderManageFetchApi } from '../../fetch';
import DescriptionTable from '../DescriptionTable';
import { Tooltip, Modal, Watermark } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import CustomButton, { ButtonType } from '@/components/CustomButton';
import { RootState } from '@/redux/store';
// import './index.scss';

const CollectOrderDetail = (props: any) => {
  const fetchApi = new OrderManageFetchApi();
  const { visible, id, onCancel } = props;
  const erp: any = useSelector((state: RootState) => state.common).userName;
  const [orderInfo, setOrderInfo] = useState<any>();
  const [scheduleInfo, setScheduleInfo] = useState<any>();
  const [deliveryInfo, setDeliveryInfo] = useState<any>();
  const orderInfoColumn: any[] = [
    { label: '订单编号', key: 'originalOrderId', span: 1 },
    {
      key: 'orderId',
      span: 1,
      label: (
        <div className="packageNo">
          <p>包裹号</p>
          <Tooltip
            placement="right"
            title={'包含“单号-包裹号-包裹数-配送次数”。'}
            overlayStyle={{ maxWidth: 600 }}
            autoAdjustOverflow={false}
          >
            <QuestionCircleOutlined className="question" />
          </Tooltip>
        </div>
      ),
    },
    { label: '订单来源', key: 'orderSourceName', span: 1 },
    { label: '寄件人姓名', key: 'collectionUserName', span: 1 },
    { label: '寄件人手机号', key: 'collectionUserContact', span: 1 },
    { label: '寄件码', key: 'verifyCode', span: 1 },
    { label: '分配人手机号', key: 'loadUserContact', span: 1 },
    { label: '装载格口号', key: 'gridNo', span: 1 },
    { label: '停靠点名称', key: 'address', span: 1 },
    { label: '投递方式', key: 'loadMethodName', span: 1 },
    { label: '配送方式', key: 'deliveryModeName', span: 1 },
    { label: '订单状态', key: 'orderStatusName', span: 1 },
    { label: '取件人姓名', key: 'loadUserName', span: 1 },
    { label: '取件人手机号', key: 'loadUserContactA', span: 1 },
    { label: '取件码', key: 'courierFetchCode', span: 1 },
  ];
  const scheduleColumn: any[] = [
    { label: '调度单号', key: 'scheduleName', span: 1 },
    { label: '车牌号', key: 'vehicleName', span: 1 },
    { label: '所属站点', key: 'stationName', span: 1 },
  ];
  const deliveryColumn: any[] = [
    { label: '调度生成时间', key: 'scheduleCreateTime', span: 1 },
    { label: '订单分配时间', key: 'createTime', span: 1 },
    { label: '调度出发时间', key: 'scheduleStartTime', span: 1 },
    { label: '到达停靠点时间', key: 'arrivalTime', span: 1 },
    { label: '寄件完成时间', key: 'senderDroppedTime', span: 1 },
    {
      key: 'gridOpenTime',
      span: 1,
      label: (
        <div className="packageNo">
          <p>开箱取件时间</p>
          <Tooltip
            placement="right"
            title={'实际货箱格口打开时间'}
            overlayStyle={{ maxWidth: 600 }}
            autoAdjustOverflow={false}
          >
            <QuestionCircleOutlined className="question" />
          </Tooltip>
        </div>
      ),
    },
    {
      key: 'pickupTime',
      span: 1,
      label: (
        <div className="packageNo">
          <p>取件上报时间</p>
          <Tooltip
            placement="right"
            title={'系统之间数据对接或更新的取件时间'}
            overlayStyle={{ maxWidth: 600 }}
            autoAdjustOverflow={false}
          >
            <QuestionCircleOutlined className="question" />
          </Tooltip>
        </div>
      ),
    },
    { label: '调度结束时间', key: 'scheduleEndTime', span: 1 },
  ];

  useEffect(() => {
    getOrderDetail(id);
  }, [id]);

  const getOrderDetail = async (id: any) => {
    const res: any = await fetchApi.fetchOrderDetail(id);
    if (res && res.code === HttpStatusCode.Success) {
      setOrderInfo({
        originalOrderId: res.data.originalOrderId,
        orderId: res.data.orderId,
        orderSourceName: res.data.orderSourceName,
        collectionUserName: res.data.collectionUserName,
        collectionUserContact: res.data.collectionUserContact,
        verifyCode: res.data.verifyCode,
        loadUserContact: res.data.loadUserContact,
        loadUserContactA: res.data.loadUserContact,
        gridNo: res.data.gridNo,
        address: res.data.address,
        deliveryModeName: res.data.deliveryModeName,
        loadMethodName: res.data.loadMethodName,
        orderStatusName: res.data.orderStatusName,
        loadUserName: res.data.loadUserName,
        courierFetchCode: res.data.courierFetchCode,
        contactSecret: true,
        loadUserContactSecret: true,
        loadUserContactASecret: true,
        collectionUserContactSecret: true,
      });
      setScheduleInfo({
        scheduleName: res.data.scheduleName,
        vehicleName: res.data.vehicleName,
        stationName: res.data.stationName,
      });
      setDeliveryInfo({
        scheduleStartTime: res.data.scheduleStartTime,
        createTime: res.data.createTime,
        deliveryStartTime: res.data.deliveryStartTime,
        arrivalTime: res.data.arrivalTime,
        senderDroppedTime: res.data.senderDroppedTime,
        pickupTime: res.data.pickupTime,
        gridOpenTime: res.data.gridOpenTime,
        scheduleCreateTime: res.data.scheduleCreateTime,
        scheduleEndTime: res.data.scheduleEndTime,
      });
    }
  };

  return (
    <div className="order-detail">
      <Modal
        visible={visible}
        width={'90%'}
        title={'揽收详情'}
        footer={
          <CustomButton
            title="关闭"
            onSubmitClick={onCancel}
            buttonType={ButtonType.DefaultButton}
          />
        }
        onCancel={onCancel}
      >
        <Watermark
          content={erp}
          font={{ color: 'rgba(0,0,0,.07)' }}
          gap={[50, 90]}
        >
          {orderInfo && (
            <DescriptionTable
              key={'order'}
              tableColumns={orderInfoColumn}
              title="订单信息"
              info={orderInfo}
              column={3}
              id={id}
            />
          )}
          {scheduleInfo && (
            <DescriptionTable
              key={'schedule'}
              tableColumns={scheduleColumn}
              title="调度单信息"
              info={scheduleInfo}
              column={3}
              id={id}
            />
          )}
          {deliveryInfo && (
            <DescriptionTable
              key={'delivery'}
              tableColumns={deliveryColumn}
              title="配送信息跟踪"
              info={deliveryInfo}
              column={1}
              id={id}
            />
          )}
        </Watermark>
      </Modal>
    </div>
  );
};

export default React.memo(CollectOrderDetail);
