import { YESNO } from "@/utils/enum";
import { FormConfig } from "@jd/x-coreui";
import dayjs from "dayjs";

export const SearchConfig: FormConfig = {
    fields: [
        {
            fieldName: 'stateIdList',
            label: '省份',
            placeholder: '请选择省份,可多选',
            type: 'select',
            multiple: true,
            showSearch: true,
            labelInValue: false,
        },
        {
            fieldName: 'range',
            label: '选择范围',
            placeholder: '请选择范围',
            type: 'select',
            showSearch: true,
            options: [
                { label: '运营', value: YESNO.YES },
                { label: '全量', value: YESNO.NO }
            ]
        },
        {
            fieldName: 'dt',
            label: '日期范围',
            placeholder: '请选择日期范围',
            type: 'rangeTime',
            format: 'YYYY-MM-DD',
            showTime: false,
            disabledDate: (current) => {
                return current > dayjs().subtract(1, 'day');
            },
            xl: 12,
            xxl: 8,
            lg: 16,
            labelCol: {
                span: 4
            },
            wrapperCol: {
                span: 20
            },
        },
    ],
    linkRules: {

    }
}

export const TableConfig: any[] = [
    {
        title: '省份',
        dataIndex: 'stateName',
        align: 'center',
        width: 120,
        ellipsis: true,
        fixed: 'left',
    },
    {
        title: '站点数',
        dataIndex: 'stationNum',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '运营车辆数',
        dataIndex: 'deployVehicleNum',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '接驳总趟次',
        dataIndex: 'transportTimes',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '单车日均接驳趟次',
        dataIndex: 'vehicleDayAvgTransportTimes',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '日均趟次＜1站数',
        dataIndex: 'avgTransportTimesRangeOne',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '日均趟次∈[1,2)站数',
        dataIndex: 'avgTransportTimesRangeTwo',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '日均趟次∈[2,3)站数',
        dataIndex: 'avgTransportTimesRangeThree',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '日均趟次∈[3,4)站数',
        dataIndex: 'avgTransportTimesRangeFour',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '日均趟次≥4站数',
        dataIndex: 'avgTransportTimesRangeFive',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '站点运营率',
        dataIndex: 'stationBusinessRate',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '车辆运营率',
        dataIndex: 'vehicleBusinessRate',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
]