import { FormConfig } from '@jd/x-coreui';
export const tableColumns: any[] = [
  {
    title: '问题标签',
    dataIndex: 'bugLabelName',
    align: 'center',
    width: 150,
    fixed: 'left',
  },
  {
    title: '标签ID',
    dataIndex: 'bugLabelId',
    align: 'center',
    width: 250,
  },
  {
    title: '问题分类',
    dataIndex: 'bugCategoryName',
    align: 'center',
    width: 250,
  },
  {
    title: '模块',
    dataIndex: 'moduleName',
    align: 'center',
    width: 100,
  },

  {
    title: '关联缺陷数量',
    dataIndex: 'associatedBugCount',
    width: 150,
    align: 'center',
    fixed: 'center',
  },

  {
    title: '操作',
    dataIndex: 'operation',
    width: 150,
    align: 'center',
    fixed: 'right',
  },
];

export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'bugLabelName',
      label: '问题标签',
      placeholder: '请输入标签名称',
      type: 'input',
      allowClear: true,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xl: 6,
      xxl: 6,
    },
    {
      fieldName: 'bugCategoryId',
      label: '问题分类',
      placeholder: '请选择',
      type: 'select',
      allowClear: true,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xl: 6,
      xxl: 6,
    },
  ],
};
