import { Form, Radio, message, Row, Col, Select, Input } from 'antd';
import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { CommonEdit } from '@/components';
import { SensorSolutionApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { formatLocation } from '@/utils/utils';
import { YESNO } from '@/utils/enum';
import { ProductDropDownList } from '@/utils/constant';
import { useEditPageData } from '@/utils/hooks';
import AddUsage from './component/AddUsage';
import StatusItem from './component/StatusItem';
import { SensorSolutionTitle, PageType } from '@/utils/EditTitle';

const SensorSolutionEdit = () => {
  const fetchApi = new SensorSolutionApi();
  const navigator = useNavigate();
  const { id, type } = formatLocation(window.location.search);
  const [form] = Form.useForm();

  const detailData = useEditPageData(id, fetchApi.fetchSensorSchemeDetail);

  form.setFieldsValue({
    ...detailData,
  });

  const onSubmitClick = async () => {
    const formValue = await form.validateFields();
    const requestParams = {
      id: formValue.id,
      name: formValue.name,
      enable: formValue.enable,
      productType: formValue.productType,
      sensorSchemeDetailList: formValue.usageList?.map((item: any) => {
        return {
          // id: item.newAdd ? null : item.id,
          hardwareTypeId: item.hardwareTypeId,
          hardwareTypeUsageId: item.hardwareTypeUsageId,
          hardwareModelId: item.hardwareModelId,
          useNumber: item.useNumber,
          enable: item.enable,
        };
      }),
    };
    if (!id) {
      delete requestParams.id;
    }
    const result: any = await fetchApi.submitSensorScheme({
      type,
      requestBody: requestParams,
    });
    if (result.code === HttpStatusCode.Success) {
      message.success(result.message);
      gotoSensorListPage();
    } else {
      message.error(result.message);
    }
  };

  const gotoSensorListPage = () => {
    navigator('/app/sensorSolution');
  };

  return (
    <CommonEdit
      title={SensorSolutionTitle[type]}
      breadCrumbConfig={[
        {
          title: '传感器方案管理',
          route: '',
        },
        {
          title: SensorSolutionTitle[type],
          route: '',
        },
      ]}
      onSubmitClick={onSubmitClick}
      onCancleClick={gotoSensorListPage}
      hideSubmit={type === PageType.READONLY}
      cancelTitle={type === PageType.READONLY ? '返回' : '取消'}
    >
      <Form form={form} labelCol={{ span: 5 }} wrapperCol={{ span: 18 }}>
        <Row>
          <Col span={21}>
            <Form.Item
              label="传感器方案名称"
              name="name"
              rules={[{ required: true, message: '请输入传感器方案名称' }]}
            >
              <Input
                disabled={type === PageType.READONLY}
                maxLength={50}
                placeholder="请输入几代车+厂商+差别项"
              />
            </Form.Item>
            <Form.Item required={true} label="方案id" name="id">
              <Input maxLength={50} placeholder="系统生成" disabled />
            </Form.Item>
            <Form.Item
              name={'productType'}
              label={'所属产品'}
              rules={[{ required: true, message: '请选择所属产品' }]}
            >
              <Radio.Group
                disabled={type !== PageType.ADD}
                options={ProductDropDownList}
              />
            </Form.Item>
            <AddUsage
              disable={type === PageType.READONLY}
              initValues={detailData?.sensorSchemeDetailList}
              form={form}
              schemeId={detailData?.id}
            />
            <StatusItem
              name="enable"
              label="方案状态"
              form={form}
              disable={type === PageType.READONLY}
              items={[
                { code: YESNO.YES, name: '有效' },
                { code: YESNO.NO, name: '无效' },
              ]}
              initValues={detailData?.enable}
              defaultValue={1}
            />
            <Form.Item label=" " colon={false}>
              <div style={{ color: '#808080' }}>
                <p>状态说明：</p>
                <p>
                  1、只有“有效”的方案，才能被引用成为一个方案选择项，“无效”的方案，不能被引用；
                </p>
                <p>
                  2、方案状态从“有效”改为“无效”，历史有被引用，历史数据不受影响，再修改，该无效方案不在选择项范围内。
                </p>
              </div>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </CommonEdit>
  );
};

export default React.memo(SensorSolutionEdit);
