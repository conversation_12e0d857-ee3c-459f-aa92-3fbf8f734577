import { buildURL, parseResponseData } from '@/fetch/core/util';
export interface Poi {
  label: string;
  value: string;
  address?: string;
}
export enum LocationType {
  gcj_02 = 'gcj-02',
  wgs_84 = 'wgs-84',
}
const mapSearch = async (val: string | null) => {
  const postStr = {
    yingjiType: 1,
    keyWord: val,
    level: 18,
    mapBound: '-180,-90,180,90',
    queryType: 4,
    start: 0,
    count: 100,
    sourceType: 0,
  };
  let str = JSON.stringify(postStr);
  const tKey = 'dd515bb481c4d4c7f5b9056229e36e68';
  const url = `https://api.tianditu.gov.cn/v2/search?postStr=${str}&type=query&tk=${tKey}`;
  const resp = await window.fetch(url);
  const respBody = await parseResponseData(
    resp,
    resp.headers.get('Content-Type'),
  );
  if (respBody?.resultType == 4) {
    return fotmatPois(respBody?.suggests) || [];
  }
  return [];
};

const fotmatPois = (poisList: Array<any>) => {
  return poisList?.map((item, index) => {
    return {
      value: item.lonlat + ':' + index,
      label: item.name + '-' + item.address,
    };
  });
};

const fetchPoiList = (
  searchText: string,
  locationType = LocationType.gcj_02,
): Promise<Poi[]> => {
  return new Promise((resolve, reject) => {
    const win = window as any;
    win.$.ajax({
      type: 'get',
      url: buildURL({
        absoluteURL: 'https://apis.map.qq.com/ws/place/v1/suggestion',
        urlParams: {
          key: 'KRBBZ-VJEWS-Q3IOZ-67LGG-RVU2E-UEF6X',
          keyword: encodeURI(searchText),
          get_subpois: 1,
          output: 'jsonp',
        },
      }),
      dataType: 'jsonp',
      success: (res: any) => {
        if (res.status == 0) {
          const result = res?.data?.map((item: any) => {
            if (locationType === LocationType.wgs_84) {
              const locationWgs84 = win
                .coordtransform()
                .gcj02towgs84(item.location.lng, item.location.lat);
              return {
                label: item.title,
                value: locationWgs84[0] + '-' + locationWgs84[1],
                address: item.address,
              };
            }
            return {
              label: item.title,
              value: item?.location?.lng + '-' + item?.location?.lat,
              address: item.address,
            };
          });
          resolve(result);
        }
      },
    });
  });
};
const fetchGeocoder = (location: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    const win = window as any;
    win.$.ajax({
      type: 'get',
      url: buildURL({
        absoluteURL: 'https://apis.map.qq.com/ws/geocoder/v1/?location',
        urlParams: {
          key: 'KRBBZ-VJEWS-Q3IOZ-67LGG-RVU2E-UEF6X',
          location: location,
          output: 'jsonp',
        },
      }),
      dataType: 'jsonp',
      success: (res: any) => {
        let address = '';
        let detailAddress = {};
        if (res.status == 0) {
          if (res.result) {
            const { nation, province, city, district, street, street_number } =
              res.result.address_component;
            address = `${district}${street}${street_number}`;
            if (
              res.result.formatted_addresses.recommend &&
              res.result.formatted_addresses.recommend.length > 0
            ) {
              address = `${res.result.formatted_addresses.recommend}`;
            }
            detailAddress = {
              country: nation,
              province: province,
              city: city,
              district: district,
              street: street,
              streetNumber: street_number,
              name: res.result.formatted_addresses.recommend,
            };
          }
          resolve({
            address,
            detailAddress,
          });
        }
      },
      error: () => {
        console.error('反地理编码失败');
      },
    });
  });
};
export { mapSearch, fetchPoiList, fetchGeocoder };
