import { TableColumnsType } from 'antd';
export interface DataType {
  layerNumber: string;
  layerName: string;
  indicatorNumber: string;
  indicatorName: string;
  priority: number;
  modifyUser: string;
  modifyTime: string;
}

export const indicatorColumns: TableColumnsType<DataType> = [
  {
    title: '指标层名称',
    dataIndex: 'layerName',
    width: 150,
    align: 'center',
  },
  {
    title: '指标id',
    dataIndex: 'indicatorNumber',
    width: 150,
    align: 'center',
  },
  {
    title: '指标名称',
    dataIndex: 'indicatorName',
    width: 150,
    align: 'center',
  },
  {
    title: '权重',
    dataIndex: 'priority',
    width: 150,
    align: 'center',
  },
  {
    title: '操作人',
    dataIndex: 'modifyUser',
    width: 150,
    align: 'center',
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    width: 150,
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operations',
    width: 100,
    align: 'center',
  },
];
