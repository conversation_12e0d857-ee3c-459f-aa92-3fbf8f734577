import { CommonTableRequest } from './interface';
export enum UseStatus {
  IDLE = '0',
  USE = '1',
}
export interface PointsRequest extends CommonTableRequest {
  /** 点位类型 */
  pointType?: string | null;
}
/** 多合一点位管理*/
export interface PointsResponse {
  /** 点位编号 */
  pointNo: string;
  /** 点位名称 */
  pointName: string;
  /** 点位类型 */
  pointType: string;
  /** 点位类型名称 */
  pointTypeName: string;
  /** 所属地图名称 */
  mapName: string;
  /** 所属停靠站名称 */
  stopStationName: string;
  /** 绑定的设备名称 */
  bindDeviceName: string;
  /** 占用的设备名称 */
  useDeviceName: string;
  /** 停启用状态 */
  enable: string;
  /** 停启用状态名称 */
  enableName: string;
  /** 使用状态 */
  useStatus: UseStatus;
  /** 使用状态名称 */
  useStatusName: string;
}

/** 多合一管控区管理*/
export interface ZoneResponse {
  /** 区域编号 */
  zoneNo: string;
  /** 区域名称 */
  zoneName: string;
  /** 所属地图名称 */
  mapName: string;
  /** 所属停靠站名称 */
  /** 占用的设备名称 */
  useDeviceName: string;
  /** 停启用状态 */
  enable: string;
  /** 停启用状态名称 */
  enableName: string;
  /** 使用状态 */
  useStatus: UseStatus;
  /** 使用状态名称 */
  useStatusName: string;
}
