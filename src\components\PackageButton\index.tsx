/* eslint-disable no-unused-vars */

import React from 'react';
import { Button } from 'antd';

export enum ButtonType {
  PrimaryButton = 'primaryButton', // primary background color + text white color + 4px radius
  DefaultButton = 'defaultButton', //  white background color + text "text-normal-color" color + 4px radius
  WarnningButton = 'warningButton', //  red background color + text white color + 4px radius
}

const textColorConfig = {
  primaryButton: 'white',
  defaultButton: '#333',
  warningButton: 'white',
};

const backgroundColorConfig = {
  primaryButton: '#3c6ef0',
  defaultButton: 'white',
  warningButton: '#D9001B',
};

export const PackageButton = ({
  title,
  buttonType = ButtonType.PrimaryButton,
  disable,
  loading,
  onSubmitClick,
  height = 40,
  otherCSSProperties, // possible override {height、textColor、backgroundColor}
}: {
  title: string;
  buttonType?: ButtonType;
  disable?: boolean;
  loading?: boolean;
  onSubmitClick?: Function;
  height?: number;
  otherCSSProperties?: React.CSSProperties;
}) => {
  const primaryBtnStyle = (height: number): React.CSSProperties => {
    return {
      backgroundColor: backgroundColorConfig[buttonType],
      color: textColorConfig[buttonType],
      height: height,
      minWidth: 80,
      borderRadius: 4,
      ...otherCSSProperties,
    };
  };
  return (
    <Button
      className="my_button"
      loading={loading}
      disabled={disable}
      style={primaryBtnStyle(height)}
      onClick={() => {
        onSubmitClick ? onSubmitClick() : null;
      }}
    >
      {title}
    </Button>
  );
};
export {};
