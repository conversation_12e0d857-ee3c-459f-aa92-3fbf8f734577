import {
  dropDownListKey,
  dropDown<PERSON>ey,
  EnableDropDownList,
} from '@/utils/constant';
import { FormConfig } from '@/components';
export const tableColumns: any[] = [
  {
    title: '序号',
    dataIndex: 'appNumber',
    align: 'center',
    width: 80,
  },
  {
    title: '角色编号',
    dataIndex: 'number',
    align: 'center',
    width: 250,
    ellipsis: true,
  },
  {
    title: '角色名称',
    dataIndex: 'name',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '角色描述',
    dataIndex: 'description',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    align: 'center',
    width: 200,
    fixed: 'right',
  },
];

export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'name',
      label: '角色名称',
      placeholder: '请输入角色名称',
      type: 'input',
    },
  ],
};

export const editFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'module',
      label: '模块',
      placeholder: '请选择模块名称',
      type: 'select',
    },
    {
      fieldName: 'code',
      label: '编码',
      placeholder: '请输入编码名称',
      type: 'input',
    },
    {
      fieldName: 'message',
      label: '消息内容',
      placeholder: '请输入消息内容',
      type: 'input',
    },
    {
      fieldName: 'transform',
      label: '转换消息',
      placeholder: '请输入转换消息',
      type: 'input',
    },
    {
      fieldName: 'enable',
      label: '状态',
      placeholder: '请选择状态',
      type: 'radioGroup',
      options: EnableDropDownList,
    },
  ],
};
