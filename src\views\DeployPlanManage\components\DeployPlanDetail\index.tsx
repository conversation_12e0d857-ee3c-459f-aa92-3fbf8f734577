import {
  But<PERSON>,
  Card,
  Col,
  Descriptions,
  message,
  Modal,
  Row,
  Image,
  Space,
} from 'antd';
import React, { useState } from 'react';
import './index.scss';
import { DeployPlanSession, DeployPlanStageStatus } from '@/utils/enum';
import { DeployPlan } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  SwapOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  ZoomOutOutlined,
  ZoomInOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { isEmpty } from 'lodash';

const DeployPlanDetail = ({ data }: { data: any }) => {
  const fetchApi = new DeployPlan();
  const StageStatusNameMap = new Map([
    [DeployPlanSession.STATION_DELIVERY, '站点交付'],
    [DeployPlanSession.ROUTE_TEST_RUN, '线路试跑'],
    [DeployPlanSession.MAP_PRODUCTION, '地图制作'],
    [DeployPlanSession.MAP_COLLECTION, '地图采集'],
    [DeployPlanSession.ROUTE_CONFIRMATION, '确认路线'],
    [DeployPlanSession.INITIATOR_VERIFICATION, '发起人核实'],
    [DeployPlanSession.VEHICLE_ALLOCATION, '车辆调拨'],
    [DeployPlanSession.VEHICLE_INSURANCE, '车辆保险'],
    [DeployPlanSession.VEHICLE_CONFIRMATION, '车号确认'],
  ]);

  const [insuranceModal, setInsuranceModal] = useState<boolean>(false);
  const [insuranceInfo, setInsuranceInfo] = useState<any[]>([]);
  const [visible, setVisible] = useState<boolean>(false);
  const [current, setCurrent] = useState<number>(0);

  const checkInsurance = async () => {
    const vehicleNames = data.vehicleNames;
    if (!vehicleNames || vehicleNames?.length <= 0) {
      return;
    }
    setInsuranceModal(true);
    const res = await fetchApi.getInsuranceDetail(vehicleNames.split(','));
    if (res.code === HttpStatusCode.Success) {
      setInsuranceInfo(
        res.data.map((v) => ({
          deviceName: v.deviceName,
          deviceInsurances: v.deviceInsurances?.map((j) => ({
            deviceInsuranceId: j.deviceInsuranceId,
            policyNumber: j.policyNumber,
            location: !j.stationName
              ? !j.cityName
                ? `${j.provinceName}`
                : `${j.provinceName}-${j.cityName}`
              : `${j.provinceName}-${j.cityName}-${j.stationName}`,
            effectTime: `${j.effectiveStartTime}-${j.effectiveEndTime}`,
            url: j.url,
            fileKey: j.fileKey,
          })),
        })),
      );
    } else {
      message.error(res.message);
      setInsuranceModal(true);
    }
  };

  return (
    <div className="deploy-plan-detail">
      <Descriptions layout="vertical" column={4} colon={false}>
        <Descriptions.Item label="计划编号">
          {data.planNumber || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="计划类型">
          {data.planTypeName || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="所属站点">
          {data.stationName || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="当前阶段">
          <span style={{ color: '#7d77eb', fontWeight: '500' }}>
            {data.currentStage || '-'}
          </span>
        </Descriptions.Item>
        <Descriptions.Item label="创建人">
          {data.creatorErp || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="创建时间">
          {data.createDate || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="持续天数">
          {data.durationDays == undefined ? '-' : data.durationDays}
        </Descriptions.Item>
        <Descriptions.Item label="需求车辆">
          {data.needVehicleCount || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="供应商">
          {data.supplier || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="提供车数">
          {data.providedVehicleCount || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="车号">
          {data.vehicleNames || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="计划备注">
          {data.planRemark || '-'}
        </Descriptions.Item>
      </Descriptions>
      {data.stageInfoList?.map((v) => {
        return v ? (
          <div
            className={`stage-card ${
              v.stageStatus === DeployPlanStageStatus.PROCESSING
                ? 'processing'
                : ''
            }`}
            key={v.stageType}
          >
            <div className="card-title">
              <h4>{StageStatusNameMap.get(v.stageType)}</h4>
              <span>
                <span>{`指派人：${v.appointorErp}`}</span>
                {[DeployPlanSession.VEHICLE_INSURANCE].includes(
                  v.stageType,
                ) && (
                  <span
                    clstag="btn_check_insurance"
                    className="check-insurance"
                    onClick={() => {
                      checkInsurance();
                    }}
                  >
                    查看保险
                  </span>
                )}
                {[DeployPlanSession.ROUTE_CONFIRMATION].includes(v.stageType) &&
                  !isEmpty(v.joySkyImages) && (
                    <>
                      <span
                        clstag="btn_check_insurance"
                        className="check-insurance"
                        onClick={() => {
                          setVisible(true);
                        }}
                      >
                        查看图片
                      </span>
                      <Image.PreviewGroup
                        items={v.joySkyImages.map((item, index) => {
                          return {
                            src: item.url,
                            key: `${index}-${item.name}`,
                          };
                        })}
                        preview={{
                          closeIcon: (
                            <CloseOutlined
                              onClick={() => {
                                setVisible(false);
                              }}
                            />
                          ),
                          visible: visible,
                          toolbarRender: (
                            _,
                            {
                              transform: { scale },
                              actions: {
                                onFlipY,
                                onFlipX,
                                onRotateLeft,
                                onRotateRight,
                                onZoomOut,
                                onZoomIn,
                              },
                            },
                          ) => (
                            <Space size={12} className="toolbar-wrapper">
                              <SwapOutlined rotate={90} onClick={onFlipY} />
                              <SwapOutlined onClick={onFlipX} />
                              <RotateLeftOutlined onClick={onRotateLeft} />
                              <RotateRightOutlined onClick={onRotateRight} />
                              <ZoomOutOutlined
                                disabled={scale === 1}
                                onClick={onZoomOut}
                              />
                              <ZoomInOutlined
                                disabled={scale === 50}
                                onClick={onZoomIn}
                              />
                            </Space>
                          ),
                          onChange: (index) => {
                            setCurrent(index);
                          },
                        }}
                      ></Image.PreviewGroup>
                    </>
                  )}
              </span>
            </div>
            <Descriptions column={2}>
              <Descriptions.Item label="期望完成日期">
                {v.expectedDate || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="实际完成日期">
                {v.completedDate || '-'}
              </Descriptions.Item>
              {[DeployPlanSession.VEHICLE_ALLOCATION].includes(v.stageType) && (
                <Descriptions.Item label="接收人联系方式">
                  {v.receiveContact || '-'}
                </Descriptions.Item>
              )}
              {[DeployPlanSession.MAP_COLLECTION].includes(v.stageType) && (
                <Descriptions.Item label="采图车来源">
                  {v.mapVehicleSourceName || '-'}
                </Descriptions.Item>
              )}
              {[DeployPlanSession.MAP_COLLECTION].includes(v.stageType) &&
                v.mapVehicleRemark && (
                  <Descriptions.Item label="借车采图备注">
                    {v.mapVehicleRemark}
                  </Descriptions.Item>
                )}
              {[
                DeployPlanSession.VEHICLE_ALLOCATION,
                DeployPlanSession.INITIATOR_VERIFICATION,
              ].includes(v.stageType) && (
                <Descriptions.Item label="车辆到站日期">
                  {v.arrivalDate || '-'}
                </Descriptions.Item>
              )}
              {[DeployPlanSession.STATION_DELIVERY].includes(v.stageType) && (
                <Descriptions.Item label="站点交付日期">
                  {v.deliveryDate || '-'}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="执行人">
                {v.executorErp || '-'}
              </Descriptions.Item>

              <Descriptions.Item label="环节备注">
                {v.remark || '-'}
              </Descriptions.Item>
            </Descriptions>
          </div>
        ) : (
          <></>
        );
      })}

      {insuranceModal && (
        <Modal
          title="查看保险"
          open={insuranceModal}
          width={1000}
          footer={[
            <Button key="back" onClick={() => setInsuranceModal(false)}>
              关闭
            </Button>,
          ]}
          onCancel={() => setInsuranceModal(false)}
        >
          {insuranceInfo.map((v) => {
            return (
              <Card
                className="insurance-modal-content"
                key={v.deviceName}
                title={v.deviceName}
                bordered={false}
                style={{ marginBottom: 16 }}
              >
                {v.deviceInsurances.map((j, index) => (
                  <Card.Grid
                    key={j.deviceInsuranceId}
                    style={{ width: '100%', padding: '10px' }}
                  >
                    <Row justify="space-between" align="middle">
                      <Col>保单号: {j.policyNumber}</Col>
                      <Col>投保地: {j.location}</Col>
                      <Col>生效日期: {j.effectTime}</Col>
                      <Col>
                        <Button
                          type="link"
                          clstag="btn_preview_insurance"
                          onClick={() => {
                            window.open(j.url);
                          }}
                        >
                          预览保单
                        </Button>
                      </Col>
                    </Row>
                  </Card.Grid>
                ))}
              </Card>
            );
          })}
        </Modal>
      )}
    </div>
  );
};

export default React.memo(DeployPlanDetail);
