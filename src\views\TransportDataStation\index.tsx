import { CommonForm, FieldItem, FormConfig } from "@jd/x-coreui";
import React, { useEffect, useState } from "react";
import { SearchConfig, TableConfig } from "./config";
import { RootState } from "@/redux/store";
import { useSelector } from "react-redux";
import { TransportDataApi } from "@/fetch/business/transportDataStop";
import { HttpStatusCode } from "@/fetch/core/constant";
import dayjs from "dayjs";
import CommonTable from "@/components/CommonTable";
import { useTableData } from "@/components/CommonTable/useTableData";
import { Table } from "antd";

const fetchApi = new TransportDataApi();
const TransportDataStation = () => {

    const [searchConfig, setSearchConfig] = useState<FormConfig>(SearchConfig);
    const initSearchCondition = {
        searchForm: {
            cityIdList: null,
            stationIdList: null,
            dt: [dayjs().subtract(2, 'day'), dayjs().subtract(2, 'day')],
        },
        pageNum: 1,
        pageSize: 10,
    };

    const historySearchValues = useSelector(
        (state: RootState) => state.searchForm,
    );

    const [searchCondition, setSearchCondition] = useState<SearchCondition>(
        () => {
            return historySearchValues.searchValues
                ? historySearchValues.searchValues
                : initSearchCondition;
        },
    );

    const onSearchClick = (val) => {
        if (JSON.stringify(val) === JSON.stringify(searchCondition.searchForm)) {
            return;
        }
        const data = {
            ...searchCondition,
            searchForm: val,
        };
        setSearchCondition(data);
    };

    const { tableData, loading }: any = useTableData(
        {
            searchForm: {
                cityIdList: searchCondition.searchForm.cityIdList,
                stationIdList: searchCondition.searchForm.stationIdList,
                startDay: searchCondition.searchForm.dt[0].format('YYYY-MM-DD'),
                endDay: searchCondition.searchForm.dt[1].format('YYYY-MM-DD'),
            },
            pageNum: searchCondition.pageNum,
            pageSize: searchCondition.pageSize,
        },
        fetchApi.fetchStationTableList,
    );

    const formatColumns = () => {
        return TableConfig.map((col) => {
            switch (col.dataIndex) {
                default:
                    return {
                        ...col,
                        render: (text: any) => (text === 0 ? 0 : text || '-'),
                    };
            }
        });
    }

    useEffect(() => {
        const params = {
            dimType: 'CITY',
            filterIds: []
        };
        fetchApi.getDimSelect(params).then((res) => {
            if (res.code === HttpStatusCode.Success) {
                const field = searchConfig.fields.find((item: FieldItem) => item?.fieldName === 'cityIdList');
                field!.options = res.data?.map((val) => ({
                    value: val.dimValue,
                    label: val.dimName,
                }));
                setSearchConfig({ ...searchConfig });
            }
        });
    }, []);

    const [summaryData, setSummaryData] = useState<any>({
        deployVehicleNum: '-',
        transportTimes: '-',
        vehicleDayAvgTransportTimes: '-',
        stationBusinessRate: '-',
        vehicleBusinessRate: '-',
        coverRoadNum: '-',
        roadNum: '-',
        coverRoadRate: '-',
        stopNum: '-',
        stopUseRate: '-',
        noRunStopNum: '-',
    });

    useEffect(() => {
        fetchApi.fetchStationTableSummary({
            searchForm: {
                cityIdList: searchCondition.searchForm.cityIdList,
                stationIdList: searchCondition.searchForm.stationIdList,
                startDay: searchCondition.searchForm.dt[0].format('YYYY-MM-DD'),
                endDay: searchCondition.searchForm.dt[1].format('YYYY-MM-DD'),
            },
            pageNum: searchCondition.pageNum,
            pageSize: searchCondition.pageSize,
        }).then((res) => {
            if (res.code === HttpStatusCode.Success) {
                setSummaryData(res.data);
            }
        });
    }, [searchCondition.searchForm]);

    return (
        <>
            <CommonForm
                formConfig={searchConfig}
                defaultValue={searchCondition.searchForm}
                layout="inline"
                formType="search"
                colon={false}
                onSearchClick={onSearchClick}
                onResetClick={() => setSearchCondition({ ...initSearchCondition })}
            />
            <CommonTable
                tableListData={{
                    list: tableData?.list ?? [],
                    totalNumber: tableData?.total,
                    totalPage: tableData?.pages,
                }}
                columns={formatColumns()}
                loading={loading}
                rowKey={'stationId'}
                searchCondition={searchCondition}
                onPageChange={(value: any) => setSearchCondition(value)}
                summary={() => {
                    return <Table.Summary.Row>
                        <Table.Summary.Cell index={0} colSpan={1}>总计</Table.Summary.Cell>
                        <Table.Summary.Cell index={1} colSpan={1}>-</Table.Summary.Cell>
                        <Table.Summary.Cell index={2} colSpan={1}>-</Table.Summary.Cell>
                        <Table.Summary.Cell index={3} colSpan={1}>-</Table.Summary.Cell>
                        <Table.Summary.Cell index={4} colSpan={1}>-</Table.Summary.Cell>
                        <Table.Summary.Cell index={5} colSpan={1}>{summaryData?.deployVehicleNum}</Table.Summary.Cell>
                        <Table.Summary.Cell index={6} colSpan={1}>{summaryData?.transportTimes}</Table.Summary.Cell>
                        <Table.Summary.Cell index={7} colSpan={1}>{summaryData?.vehicleDayAvgTransportTimes}</Table.Summary.Cell>
                        <Table.Summary.Cell index={8} colSpan={1}>{summaryData?.stationBusinessRate}</Table.Summary.Cell>
                        <Table.Summary.Cell index={9} colSpan={1}>{summaryData?.vehicleBusinessRate}</Table.Summary.Cell>
                        <Table.Summary.Cell index={10} colSpan={1}>{summaryData?.coverRoadNum}</Table.Summary.Cell>
                        <Table.Summary.Cell index={11} colSpan={1}>{summaryData?.roadNum}</Table.Summary.Cell>
                        <Table.Summary.Cell index={12} colSpan={1}>{summaryData?.coverRoadRate}</Table.Summary.Cell>
                        <Table.Summary.Cell index={13} colSpan={1}>{summaryData?.stopNum}</Table.Summary.Cell>
                        <Table.Summary.Cell index={14} colSpan={1}>{summaryData?.stopUseRate}</Table.Summary.Cell>
                        <Table.Summary.Cell index={15} colSpan={1}>{summaryData?.noRunStopNum}</Table.Summary.Cell>
                    </Table.Summary.Row>
                }}
            />
        </>
    )
}

export default TransportDataStation;