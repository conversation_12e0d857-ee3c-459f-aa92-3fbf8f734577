import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import FormTitle from '@/components/FormTitle';
import BreadCrumb from '@/components/BreadCrumb';
import EditModuleTitle from '@/components/EditModuleTitle';
import EditBasicInfo from '../components/EditBasicInfo';
import EditVehicle from '../../CockpitManagement/components/EditVehicle';
import { Form, message } from 'antd';
import { PackageButton, ButtonType } from '@/components/PackageButton';
import FetchApi from '../utils/fetchApi';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  EditStationSearchformConfig,
  EditStationTableColumns,
} from '../utils/column';
import './index.scss';
import EditMember from '../components/EditMember';
import { isEmpty } from '@/utils/utils';

const AddOrEditCockpitTeam = () => {
  const fetchApi = new FetchApi();
  const navigator = useNavigate();
  const { cockpitTeamNumber, cockpitTeamName } = useParams();
  const [basicInfoForm] = Form.useForm();
  const stationInfo = useRef<any>();
  const allUsers = useRef<any>();
  const [bindStation, setBindStation] = useState<any[]>([]);
  const [bindMembers, setBindMembers] = useState<any[]>([]);
  const [selectableUsers, setSelectableUsers] = useState<any[]>([]);
  const [tableData, setTableData] = useState<any[]>([]);
  const basicInfoData = useRef<any>({
    cockpitTeamName: cockpitTeamName
      ? decodeURIComponent(cockpitTeamName)
      : null,
    cockpitTeamNumber: cockpitTeamNumber,
  });
  useEffect(() => {
    getAllUsers();
    if (cockpitTeamNumber) {
      getCockpitTeamInfo();
    }
  }, []);

  useEffect(() => {
    setTimeout(() => {
      updateSelectableUsers(bindMembers);
      updateTableData(bindMembers);
    }, 500);
  }, [bindMembers]);

  const resetTableData = (list: any, updateSecret: Function) => {
    if (!list) {
      return [];
    }
    const newList = list
      ?.filter((item) => item !== undefined)
      ?.map((item: any) => {
        if (typeof item?.secret === 'undefined') {
          item.secret = true;
        }
        return {
          ...item,
          phone: { item, updateSecret },
        };
      });
    return newList;
  };

  const updatePhoneSecret = (userName: any, secret: any) => {
    setTableData(
      tableData.map((item: any) => {
        if (item.userName === userName) {
          return {
            ...item,
            secret: secret,
          };
        } else {
          return {
            ...item,
            secret: true,
          };
        }
      }),
    );
  };

  // 获取全量权限系统用户
  const getAllUsers = async () => {
    const res = await fetchApi.getAllUserInfo();
    if (res.code === HttpStatusCode.Success) {
      allUsers.current = res.data ?? [];
      setSelectableUsers(res.data);
    } else {
      message.error(res.message ?? '获取信息失败！');
    }
  };

  // 当绑定人员变化时，要更新下拉框选项
  const updateSelectableUsers = (userList) => {
    if (isEmpty(userList)) {
      setSelectableUsers(allUsers.current);
    } else {
      let temp = allUsers.current;
      for (let item of userList) {
        temp = temp?.filter((user) => user.userName !== item);
      }
      setSelectableUsers(temp);
    }
  };

  // 当绑定人员变化时，要更新表格数据
  const updateTableData = (userList) => {
    if (isEmpty(userList)) {
      setTableData([]);
    } else {
      let temp: any = [];
      for (let item of userList) {
        const uesrInfo = allUsers.current.find(
          (user) => user.userName === item,
        );
        uesrInfo && temp.push(uesrInfo);
      }
      setTableData(temp);
    }
  };

  const getCockpitTeamInfo = async () => {
    const res = await fetchApi.getCockpitTeamInfo(cockpitTeamNumber ?? '');
    if (res.code === HttpStatusCode.Success) {
      res.data?.positionList && setBindStation(res.data.positionList);
      res.data?.userNameList && setBindMembers(res.data.userNameList);
    } else {
      message.error(res.message ?? '获取信息失败！');
    }
  };

  // 获取全量站点列表
  const fetchAllStation = async (values: any) => {
    const stationInfo = values.stationInfo || [];
    const res = await fetchApi.fetchStandardLevelFourStation({
      provinceIdList: stationInfo[0] ? [stationInfo[0]] : [],
      cityIdList: stationInfo[1] ? [stationInfo[1]] : [],
      stationBaseIdList: stationInfo[2] ? [stationInfo[2]] : [],
      stationUseCaseList: values.stationUseCaseList?.value && [
        values.stationUseCaseList?.value,
      ],
      stationType: values.stationTypeList?.value,
    });
    if (res.code === HttpStatusCode.Success) {
      return res.data;
    }
    return [];
  };

  const onSubmit = async () => {
    const basicInfo = await basicInfoForm.validateFields();
    const res = cockpitTeamNumber
      ? await fetchApi.editCockpitTeamInfo({
          ...basicInfo,
          countryIdList: stationInfo.current.country,
          provinceIdList: stationInfo.current.province,
          cityIdList: stationInfo.current.city,
          stationBaseIdList: stationInfo.current.station,
          cockpitTeamNumber: cockpitTeamNumber,
          userNameList: !isEmpty(bindMembers) ? bindMembers : null,
        })
      : await fetchApi.addCockpitTeam({
          ...basicInfo,
          countryIdList: stationInfo.current.country,
          provinceIdList: stationInfo.current.province,
          cityIdList: stationInfo.current.city,
          stationBaseIdList: stationInfo.current.station,
          userNameList: !isEmpty(bindMembers) ? bindMembers : null,
        });

    if (res.code === HttpStatusCode.Success) {
      cockpitTeamNumber
        ? message.success('编辑远驾团队成功')
        : message.success('新建远驾团队成功');
      navigator('/app/cockpitteam');
    } else {
      message.error(res.message);
    }
  };

  return (
    <div className="add-drive-team-container">
      <BreadCrumb
        items={[
          {
            title: '远驾团队管理',
            route: '/app/cockpitteam',
          },
          {
            title: cockpitTeamNumber ? '编辑远驾团队' : '新建远驾团队',
            route: '',
          },
        ]}
      />
      <div className="content">
        <FormTitle
          title={cockpitTeamNumber ? '编辑远驾团队' : '新建远驾团队'}
        />
        <EditModuleTitle title="基础信息" />
        <EditBasicInfo
          key={
            cockpitTeamNumber
              ? 'editDriveTeamBasicInfo'
              : 'addDriveTeamBasicInfo'
          }
          form={basicInfoForm}
          data={basicInfoData.current}
        />
        <EditModuleTitle title="团队成员" />
        <EditMember
          selectableUsers={selectableUsers}
          bindMembers={bindMembers}
          tableData={resetTableData(tableData, (userName: any, secret: any) =>
            updatePhoneSecret(userName, secret),
          )}
          setBindMembers={setBindMembers}
        />
        <EditModuleTitle title="绑定站点" />
        <EditVehicle
          bindInfoList={bindStation ?? []}
          searchConfig={EditStationSearchformConfig}
          tableColumns={EditStationTableColumns}
          initialExpandLevel={2}
          hideSelectAll={true}
          checkStrictly={true}
          tip="注：选中“国家-省份-城市-站点”任意级别，该级别与该远驾团队建立关联，如：远驾团队关联北京，则北京新增站点，自动关联到该远驾团队下。"
          initSearchformValue={{
            stationInfo: null,
            stationBaseIdList: null,
            stationUseCaseList: null,
            stationTypeList: null,
          }}
          fetchAllInfoList={(values: any) => fetchAllStation(values)}
          getSelectValues={(values: any) => {
            stationInfo.current = values;
          }}
        />
        <div className="btns">
          <PackageButton title="确定" onSubmitClick={onSubmit} />
          <PackageButton
            title="取消"
            buttonType={ButtonType.DefaultButton}
            onSubmitClick={() => navigator('/app/cockpitteam')}
            otherCSSProperties={{ marginLeft: '10px' }}
          />
        </div>
      </div>
    </div>
  );
};

export default React.memo(AddOrEditCockpitTeam);
