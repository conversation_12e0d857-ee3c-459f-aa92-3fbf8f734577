import { FormConfig } from '@/components';
export const POINT_TYPE_MAP = new Map([
  ['REVIEW_POINT', '复合点'],
  ['CAGE_TROLLEY_POINT', '笼车点'],
  ['CHARGE_POINT', '充电点'],
  ['BIND_BOX_POINT', '绑箱点'],
  ['PUT_IN_WAREHOUSE_PARK_POINT', '入库泊车点'],
  ['OUT_WAREHOUSE_PARK_POINT', '出库泊车点'],
  ['PUT_IN_WAREHOUSE_STOP_POINT', '入库停靠点'],
  ['OUT_WAREHOUSE_STOP_POINT', '出库停靠点'],
]);

export enum POINT_TYPE_VALUE {
  REVIEW_POINT = 1,
  CAGE_TROLLEY_POINT = 2,
  CHARGE_POINT = 3,
  BIND_BOX_POINT = 4,
  PUT_IN_WAREHOUSE_PARK_POINT = 5,
  OUT_WAREHOUSE_PARK_POINT = 6,
  PUT_IN_WAREHOUSE_STOP_POINT = 7,
  OUT_WAREHOUSE_STOP_POINT = 8,
}
export const SearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'pointType',
      label: '点位类型',
      type: 'select',
      placeholder: '请选择点位类型',
    },
  ],
};
export const columns = [
  {
    title: '点ID',
    width: 150,
    dataIndex: 'pointNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '点名称',
    width: 120,
    dataIndex: 'pointName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '点类型',
    width: 80,
    dataIndex: 'pointTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '所属地图',
    width: 80,
    dataIndex: 'mapName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '所属停靠站',
    width: 80,
    dataIndex: 'stopStationName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '绑定机器人',
    width: 120,
    dataIndex: 'bindDeviceName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '使用状态',
    width: 120,
    dataIndex: 'useStatusName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '占用机器人',
    width: 120,
    dataIndex: 'useDeviceName',
    align: 'center',
    ellipsis: true,
  },
];
