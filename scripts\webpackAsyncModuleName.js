module.exports = function ({ types: t }) {
  function singleModule(path, module, name) {
    const trueName = name ?? module?.value?.replace(/(.*\/)*([^.]+).*/ig, "$2");
    if (trueName) {
      addComments(module, trueName)
    }
    path.replaceWith(
      t.CallExpression(
        t.identifier('import'),
        [module]
      )
    )
  }

  function addComments(module, name) {
    module.leadingComments = [{
      type: "CommentBlock",
      value: `webpackChunkName: '${name}'`
    }]
  }

  return {
    visitor: {
      CallExpression: function (path) {
        const { node } = path
        if (t.isIdentifier(node.callee, { name: '_lazyLoad' })) {
          const module = node.arguments[0];
          const name = node.arguments[1];
          if (t.isStringLiteral(module)) {
            singleModule(path, module, name)
          } else {
            throw new Error('The first argument of the importName() must be a string.')
          }
        }
      }
    }
  }
}