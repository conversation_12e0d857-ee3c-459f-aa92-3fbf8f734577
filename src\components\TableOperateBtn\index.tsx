import React from 'react';
import { Tooltip } from 'antd';


const TableOperateBtn = ({
  show = true,
  title,
  styleName,
  handleClick,
  tooltipText='',
}: {
  show?: boolean;
  title: string;
  styleName?: 'enable' | 'unenable';
  handleClick?: Function;
  tooltipText?:string;
}) => {
  return (
    <>
      {show && (
         <Tooltip title={tooltipText}>
        <a
        
          className={styleName}
          style={{ marginRight: '5px', marginLeft: '5px' }}
          onClick={() => {
            handleClick && handleClick();
          }}
        >
          {title}
        </a>
        </Tooltip>
      )}
    </>
  );
};

export default React.memo(TableOperateBtn);
