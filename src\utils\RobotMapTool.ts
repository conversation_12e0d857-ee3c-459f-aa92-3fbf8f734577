import Map from 'ol/Map.js';
import View from 'ol/View.js';
import Projection from 'ol/proj/Projection.js';
import { Units } from 'ol/proj/Units.js';
import ImageLayer from 'ol/layer/Image';
import Static from 'ol/source/ImageStatic';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import Feature from 'ol/Feature.js';
import Polygon from 'ol/geom/Polygon.js';
import LineString from 'ol/geom/LineString';
import Point from 'ol/geom/Point.js';
import Style from 'ol/style/Style.js';
import Icon from 'ol/style/Icon.js';
import Fill from 'ol/style/Fill.js';
import Circle from 'ol/style/Circle';
import Stroke from 'ol/style/Stroke.js';
import Text from 'ol/style/Text';
export interface RobotMapToolProps {
  container: HTMLElement;
  projection: any;
  center: any;
}
export const generateProjection = (
  extent: [number, number, number, number],
) => {
  return new Projection({
    code: '2249',
    units: 'm',
    extent: extent,
  });
};

export class RobotMapTool {
  map: any;
  container: HTMLElement;
  center: any;
  constructor(options: RobotMapToolProps) {
    this.container = options.container;
    this.center = options.center;
    this.initMap(options.projection);
    this.initMapEvent();
  }

  initMap(projection: any) {
    this.map = new Map({
      target: this.container,
      view: new View({
        projection: projection,
        center: this.center,
        zoom: 3,
        maxZoom: 17,
        minZoom: 3,
      }),
    });
  }

  initMapEvent() {
    this.map.on('click', (e: any) => {});
  }

  /**
   * 创建图片图层
   * @param url
   * @param projection
   * @param extent
   * @param layerId
   * @returns
   */
  createImgLayer(
    url: string,
    projection: any,
    extent: [number, number, number, number],
    layerId: string,
  ) {
    const _imgLayer = new ImageLayer({
      zIndex: 90,
      source: new Static({
        url: url,
        projection: projection,
        imageExtent: extent,
      }),
    });
    _imgLayer.set('layerId', layerId);
    this.map.addLayer(_imgLayer);
    return _imgLayer;
  }

  /**
   * 创建矢量图层
   * @param opts 图层配置 style zIndex等
   * @param {string} layerId 图层Id
   * @param sourceData
   * @param type
   */
  createVectorLayer(
    opts: any,
    layerId: string,
    sourceData?: {
      type: 'Point' | 'LineString' | 'Polygon';
      coordinates: number[];
      id?: string | number;
      iconUrl?: string;
      name?: string;
    }[],
  ) {
    const _source = new VectorSource();
    if (sourceData && sourceData?.length > 0) {
      sourceData.forEach((val, index) => {
        let _feature: any;
        switch (val.type) {
          case 'LineString':
            _feature = this.createLine(
              val.coordinates,
              val.id ?? index + val.type,
            );
            break;
          case 'Point':
            _feature = this.createPointFeature({
              coordinates: val.coordinates,
              pointId: val.id ?? index + val.type,
              imgSrc: val.iconUrl,
              name: val.name,
            });
            break;
          case 'Polygon':
            _feature = this.createPolygon(
              val.coordinates,
              val.id ?? index + val.type,
            );
            break;
        }
        _source.addFeature(_feature);
      });
    }
    const _vectorLayer = new VectorLayer({
      ...opts,
      source: _source,
    });
    _vectorLayer.set('layerId', layerId);
    this.map.addLayer(_vectorLayer);
    return _vectorLayer;
  }

  /**
   * 创建点的feature
   * @param {Array} coordinates 点的位置
   * @param {string} imgSrc 图片地址
   * @param {string} name 点的名称
   * @param {string | number} pointId 点的id
   * @param {object} otherStyle 点的其他样式
   */
  createPointFeature = ({
    coordinates,
    pointId,
    imgSrc,
    name,
    otherStyle,
  }: {
    coordinates: any;
    pointId: string | number;
    imgSrc?: string;
    name?: string;
    otherStyle?: object;
  }) => {
    const pointFeature = new Feature({
      geometry: new Point(coordinates),
      name: pointId,
      zIndex: 99,
    });
    pointFeature.setId(pointId);
    const style = new Style({
      image: imgSrc
        ? new Icon({
            ...otherStyle,
            src: imgSrc,
          })
        : undefined,
      text: name
        ? new Text({
            text: name,
            font: '13px Calibri,sans-serif',
            stroke: new Stroke({
              color: 'white',
              width: 1,
            }),
            fill: new Fill({
              color: 'black',
            }),
          })
        : undefined,
    });
    pointFeature.setStyle(style);
    return pointFeature;
  };

  /**
   * 创建线的feature
   * @param {Array} coordinates 线的位置
   * @param {string | number} type
   * @param {string} lineId 线的id
   * @param {object} otherStyle 点的其他样式
   */
  createLine(
    coordinates: any,
    lineId: string | number,
    type?: 'dash' | 'solid',
    otherStyle?: any,
  ) {
    const lineFeature = new Feature({
      geometry: new LineString(coordinates),
      zIndex: 99,
      name: lineId,
    });
    const style = new Style({
      stroke: new Stroke({
        lineDash: type === 'dash' ? [4, 4, 4, 4] : [1, 1, 1, 1, 1, 1],
        color: 'blue',
        width: 2,
      }),
      ...otherStyle,
    });
    lineFeature.setStyle(style);
    return lineFeature;
  }

  /**
   * 创建多边形的feature
   * @param {Array} coordinates 线的位置
   * @param {string | number} polygonId 线的id
   * @param {object} otherStyle 点的其他样式
   */
  createPolygon = (
    coordinates: any,
    polygonId: string | number,
    otherStyle?: any,
  ) => {
    const polygonFeature = new Feature({
      geometry: new Polygon(coordinates),
      zIndex: 99,
    });
    polygonFeature.setId(polygonId);
    const style = new Style({
      fill: new Fill({
        // 填充样式
        color: 'rgba(255, 87, 77, 0.4)',
      }),
      ...otherStyle,
    });
    polygonFeature.setStyle(style);
    return polygonFeature;
  };

  /**
   * 获取图层
   * @param {string | undefined | null} layerId 图层Id
   */
  getLayer = (layerId: string | undefined | null) => {
    if (!layerId) {
      return null;
    }
    let layers = this.map.getLayers().getArray();
    for (let l of layers) {
      if (l.get('layerId') === layerId) {
        return l;
      }
    }
    return null;
  };
  /**
   * 获取图层的source
   * @param {string | undefined | null} layerId 图层Id
   */
  getSource = (layerId: string | undefined | null) => {
    const source = this.getLayer(layerId).getSource();
    return source;
  };

  /**
   * 改变图层显隐
   * @param layerId 图层Id
   * @param visible 是否显示 false不显示 true显示
   */
  changeLayerVisible = (layerId: any, visible: boolean) => {
    this.getLayer(layerId)?.setVisible(visible);
  };

  addFeatureToLayer(feature, layerId, layer) {}
}
