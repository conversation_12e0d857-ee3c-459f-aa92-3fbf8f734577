import OLMap from 'ol/Map.js';
import View from 'ol/View.js';
import Feature from 'ol/Feature.js';
import Polygon from 'ol/geom/Polygon.js';
import Point from 'ol/geom/Point.js';
import TileLayer from 'ol/layer/Tile.js';
import LayerVector from 'ol/layer/Vector.js';
import SourceVector from 'ol/source/Vector';
import Style from 'ol/style/Style.js';
import Icon from 'ol/style/Icon.js';
import Fill from 'ol/style/Fill.js';
import Circle from 'ol/style/Circle';
import Stroke from 'ol/style/Stroke.js';
import Draw from 'ol/interaction/Draw.js';
import { mouseOnly } from 'ol/events/condition.js';
import { convex } from './util';
import LineString from 'ol/geom/LineString';
import VectorSource from 'ol/source/Vector';
import VectorLayer from 'ol/layer/Vector.js';

export enum AnchorLayerType {
  MOVE_ROUND = 'VOT_TNTA',
  ROAD_CLOSED = 'VOT_ROAD_CLOSURE',
  NO_PARKING = 'VOT_NO_PARKING',
}
export enum LayerIdEnum {
  TIANMAP_LAYER = 'TIANMAP_LAYER',
  TDTCVA_LAYER = 'TDTCVA_LAYER',
  WSM_LAYER = 'WSM_LAYER',
  ANCHOR_LAYER = 'ANCHOR_LAYER',
  SEARCHPOINT_LAYER = 'SEARCHPOINT_LAYER',
  INTER_LAYER = 'INTER_LAYER',
  BOUNDARY_LAYER = 'BOUNDARY_LAYER',
  STOPLINE_LAYER = 'STOPLINE_LAYER',
  GATE_LAYER = 'GATE_LAYER',
  OPENAREA_LAYER = 'OPENAREA_LAYER',
}

export interface MapEditorToolProps {
  container: HTMLElement;
  centerPoint: [number, number];
  drawCallBack?: AnyFunc;
  selectCallBack?: AnyFunc;
}

/**
 * 返回图层默认的填充颜色
 *@param {AnchorLayerType} type 多边形类型枚举
 * @return {String} 色值
 */
export const returnNormalAnchorFillColor = (type: AnchorLayerType): string => {
  const normalFillColor = {
    [AnchorLayerType.MOVE_ROUND]: 'rgba(255,158,11,0.4)',
    [AnchorLayerType.ROAD_CLOSED]: 'rgba(255,87,77,0.4)',
    [AnchorLayerType.NO_PARKING]: 'rgba(60, 110, 240, 0.5)',
  };
  return normalFillColor[type];
};

/**
 * 选中图层填充色
 * @param {AnchorLayerType} type 多边形类型枚举
 * @return {String} 色值
 */
export const returnSelectedAnchorFillColor = (
  type: AnchorLayerType
): string => {
  const selectedFillColor = {
    [AnchorLayerType.MOVE_ROUND]: 'rgba(255,158,11,1)',
    [AnchorLayerType.ROAD_CLOSED]: 'rgba(255,87,77,1)',
    [AnchorLayerType.NO_PARKING]: 'rgba(60, 110, 240, 1)',
  };

  return selectedFillColor[type];
};

class MapEditorTool {
  options: any;
  isAnchorMoved: number = 0;
  isEndPntMoved: number = 0;
  heading: number = 0;
  container: HTMLElement;
  centerPoint: [number, number];
  points: any[] = [];
  lines: any[] = [];
  polygons: any[] = [];
  map: any;
  drawSource: any = new SourceVector();
  anchorLayer: any;
  drawing: boolean = false;
  draw: any; // 画笔内容
  selectedPolygon: any = null;
  baseLineItem: any;
  directionLineItem: any;
  directionEndPntItem: any;
  innerRactangle: any;
  outRactangle: any;
  startPoint: any;
  endPoint: any;

  constructor(options: MapEditorToolProps) {
    this.options = options;
    this.container = options.container;
    this.centerPoint = options.centerPoint;
    this.initMap();
    this.initAnchorLayer();
    this.initEventListener();
  }

  /**
   * 创建地图实例
   */
  initMap = () => {
    this.map = new OLMap({
      target: this.container,
      view: new View({
        center: this.centerPoint,
        projection: 'EPSG:4326',
        zoom: 20,
        maxZoom: 24,
      }),
    });
  };

  changeMapCenter = (coordinates: any) => {
    this.centerPoint = coordinates;
    const curView = this.map.getView();
    curView.animate({
      center: coordinates,
      duration: 400,
    });
  };

  /**
   * 获取图层
   * @param {string | undefined | null} layerId 图层Id
   */
  getLayer = (layerId: string | undefined | null) => {
    if (!layerId) {
      return null;
    }
    let layers = this.map.getLayers().getArray();
    for (let l of layers) {
      if (l.values_.layerId === layerId) {
        return l;
      }
    }
    return null;
  };

  /**
   * 改变图层显隐
   * @param layerId 图层Id
   * @param visible 是否显示 false不显示 true显示
   */
  changeLayerVisible = (layerId: any, visible: boolean) => {
    this.getLayer(layerId)?.setVisible(visible);
  };

  /**
   * 清空指定图层
   */
  clearLayer(layerId: string) {
    const l = this.getLayer(layerId);
    l && this.map.removeLayer(l);
  }

  initEventListener = () => {
    this.map.on('singleclick', (e: any) => {
      if (this.drawing) {
        return true;
      }
      const result = this.map.forEachFeatureAtPixel(
        e.pixel,
        (feature: any, layer: any) => {
          var type = feature.getGeometry().getType();
          var id = feature.getId();
          if (type == 'Polygon') {
            if (!this.selectedPolygon) {
              this.selectedPolygon = feature;
              this.options.selectCallBack &&
                this.options.selectCallBack(feature, id, 'focus');
            } else {
              const selectedPolygonId = this.selectedPolygon.getId();
              this.options.selectCallBack &&
                this.options.selectCallBack(
                  this.selectedPolygon,
                  selectedPolygonId,
                  'blur'
                );
              if (selectedPolygonId === id) {
                this.selectedPolygon = null;
              } else {
                this.options.selectCallBack &&
                  this.options.selectCallBack(feature, id, 'focus');
                this.selectedPolygon = feature;
              }
            }
            return true;
          }
          return false;
        }
      );
    });
  };

  selectPolygon = (id: any) => {
    if (!this.selectedPolygon) {
      this.selectedPolygon = this.polygons.filter(
        (item: any) => item.getId() === id
      )[0];
      this.options.selectCallBack(this.selectedPolygon, id, 'focus');
    } else {
      const selectedPolygonId = this.selectedPolygon.getId();
      this.options.selectCallBack &&
        this.options.selectCallBack(
          this.selectedPolygon,
          selectedPolygonId,
          'blur'
        );
      if (selectedPolygonId === id) {
        this.selectedPolygon = null;
      } else {
        this.options.selectCallBack &&
          this.options.selectCallBack(this.selectedPolygon, id, 'focus');
      }
    }
  };

  changeMapSize = () => {
    this.map.updateSize();
  };

  /**
   * 初始化绘制图层
   */
  initAnchorLayer() {
    const normalStyle = new Style({
      fill: new Fill({
        // 填充样式
        color: 'rgba(255,158,11,0.4)',
      }),
      stroke: new Stroke({
        // 线样式
        lineDash: [1, 1, 1, 1, 1, 1],
        color: '#ffffff',
        width: 0.5,
      }),
    });
    this.anchorLayer = new LayerVector({
      source: this.drawSource,
      style: normalStyle,
      zIndex: 99,
    });
    this.anchorLayer.set('layerId', LayerIdEnum.ANCHOR_LAYER);
    this.map.addLayer(this.anchorLayer);
  }

  /**
   * 添加popover
   * @param element
   */
  addOverlay(element: any) {
    this.map.addOverlay(element);
  }
  /**
   * 删除popover
   */
  clearOverlay() {
    this.map.getOverlays().clear();
  }
  /**
   * 添加图层，默认把绘制图层也添加上
   * @param layers 图层数组
   */
  addLayers = (layers: { layer: any; layerId: string }[]) => {
    layers?.forEach((item: any) => {
      const _layer = item.layer;
      _layer.set('layerId', item.layerId);
      this.map.addLayer(_layer);
    });
  };

  // 绘制连线
  addLine(coordinates: any, type: 'dash' | 'solid', name: string) {
    const featureLine = new Feature({
      geometry: new LineString(coordinates),
      zIndex: 999,
      name,
    });
    const style = new Style({
      stroke: new Stroke({
        // 线样式
        lineDash: type === 'dash' ? [4, 4, 4, 4] : [1, 1, 1, 1, 1, 1],
        color: 'red',
        width: 4,
      }),
    });
    featureLine.setStyle(style);
    const source = new VectorSource();
    source.addFeature(featureLine);
    const _layer = new VectorLayer({
      source,
    });
    this.map.addLayer(_layer);
  }
  /**
   * 绘制多边形
   * @param {Number} id 多边形id
   * @param {Array} points 多边形点坐标集合
   * @param {String} type 多边形业务类型
   */
  drawPolygon = (id: number, points: any, type: AnchorLayerType) => {
    const _points = [points];
    const polygonFeature = new Feature({
      geometry: new Polygon(_points),
      zIndex: 99,
    });
    polygonFeature.setId(
      id == undefined ? `popOverlay${this.polygons.length + 1}` : id
    );
    const style = new Style({
      fill: new Fill({
        // 填充样式
        color: returnNormalAnchorFillColor(type),
      }),
    });
    polygonFeature.setStyle(style);
    this.polygons.push(polygonFeature);
    this.drawSource.addFeature(polygonFeature);
  };

  /**
   * 开始多边形绘制
   */
  startDraw = () => {
    if (this.drawSource && !this.draw) {
      this.addInteraction();
    }
  };
  /**
   * 打开绘笔
   */
  addInteraction = () => {
    this.draw = new Draw({
      source: this.drawSource,
      type: 'Polygon',
      condition: mouseOnly,
      freehandCondition: function () {
        return false;
      },
      minPoints: 3,
    });
    this.draw.on('drawend', (evt: any) => {
      const coordinates = evt.feature.getGeometry().getCoordinates()[0];
      if (convex(coordinates, coordinates.length - 1) == 1) {
        const fId = `popOverlay${this.polygons.length + 1}`;
        evt.feature.setId(fId);
        this.polygons.push(evt.feature);
        this.options?.drawCallBack(
          true,
          evt.feature,
          this.polygons.length,
          coordinates
        );
      } else {
        this.options?.drawCallBack(false);
        setTimeout(() => {
          this.drawSource.removeFeature(evt.feature);
        }, 50);
      }
    });
    this.map.addInteraction(this.draw);
    this.drawing = true;
  };

  /**
   * 删除多边形
   * @param {number | string} id
   */
  deletePolygon(id: number | string) {
    for (let i = 0; i < this.polygons.length; i++) {
      if (this.polygons[i].getId() === id) {
        this.drawSource.removeFeature(this.polygons[i]);
        this.polygons.splice(i, 1);
        break;
      }
    }
  }

  /**
   * 清空所有图层
   */
  clearDraw() {
    this.drawSource.clear();
    this.polygons = [];
    this.lines = [];
    this.points = [];
    this.selectedPolygon = null;
  }

  /**
   * 取消绘制状态
   */
  cancelDrawingStatus() {
    this.map.removeInteraction(this.draw);
    this.draw = null;
    this.drawing = false;
  }

  /**
   * 选中多边型
   * @param {Number} id 索引值
   * @param {AnchorLayerType} type 类型枚举
   */
  highLightPolygon = (id: number, type: AnchorLayerType) => {
    const selectedPolygonId = this.selectedPolygon
      ? this.selectedPolygon.getId()
      : null;
    if (id === selectedPolygonId) {
      if (this.options.selectCallBack) {
        this.options.selectCallBack(
          this.selectedPolygon,
          selectedPolygonId,
          'blur'
        );
        this.selectedPolygon = null;
      }
      return;
    }
    // 先把选中项去除高亮，再重新高亮新的选中项
    if (selectedPolygonId) {
      if (this.options.selectCallBack) {
        this.options.selectCallBack(
          this.selectedPolygon,
          selectedPolygonId,
          'blur'
        );
        this.selectedPolygon = null;
      }
    }

    const polygon = this.polygons.filter((item: any) => item.getId() === id)[0];
    if (id !== undefined && polygon) {
      this.options.selectCallBack(polygon, id, 'focus');
      this.selectedPolygon = polygon;
      const posArray = this.selectedPolygon
        .getGeometry()
        .getFlatInteriorPoint();
      const mapExtent = this.map.getView().calculateExtent(this.map.getSize());
      if (
        posArray[0] >= mapExtent[0] &&
        posArray[0] <= mapExtent[2] &&
        posArray[1] >= mapExtent[1] &&
        posArray[1] <= mapExtent[3]
      ) {
        // console.log(posArray);
      } else {
        this.map.getView().setCenter(posArray);
      }
    }
  };

  /**
   * 创建图层
   * @param opts 图层配置 style zIndex等
   * @param {string} layerId 图层Id
   */
  createLayer = (opts: any, layerId: string, name?: string) => {
    const newSource = new SourceVector();
    const newLayer = new LayerVector({
      ...opts,
      source: newSource,
    });
    newLayer.set('layerId', layerId);
    this.map.addLayer(newLayer);
    return newLayer;
  };


  /**
   * 点的feature
   * @param {Array} coordinates 点的位置
   * @param {string} imgSrc 图片地址
   * @param {string} pointId 点的id
   * @param {object} otherStyle 点的其他样式
   */
  createPointFeature = (
    coordinates: any,
    imgSrc: string,
    pointId?: string,
    otherStyle?: object,
    anchor?: any
  ) => {
    const pointFeature = new Feature({
      geometry: new Point(coordinates),
      name: pointId || 'anchor',
      zIndex: 999,
    });
    pointFeature.setId(
      pointId === undefined ? `point${this.points.length + 1}` : pointId
    );
    const style = new Style({
      image: new Icon({
        ...otherStyle,
        src: imgSrc,
        anchor: anchor ?? [0.5, 1],
      }),
    });
    pointFeature.setStyle(style);
    return pointFeature;
  };

  /**
   *
   * 更改搜索点
   * @param {[number, number]} coordinates 点的坐标
   * @param {string} layerId 搜索点添加在哪个图层上 不传的话搜索点是添加在LayerIdEnum.SEARCHPOINT_LAYER图层上
   */
  changeSearchPoint = (coordinates: [number, number], layerId?: string) => {
    this.changeMapCenter(coordinates);
    const obtainedLayer = this.getLayer(LayerIdEnum.SEARCHPOINT_LAYER);
    if (obtainedLayer) {
      const searchPointIdx = this.points.findIndex(
        (item) => item.getId() === 'searchPoint'
      );
      obtainedLayer
        .getSource()
        .getFeatureById('searchPoint')
        .getGeometry()
        .setCoordinates(coordinates);
      this.points.splice(searchPointIdx, 1);
    } else {
      const newLayer = this.createLayer(
        {
          zIndex: 120,
        },
        LayerIdEnum.SEARCHPOINT_LAYER,
        'anchorLayer'
      );
      const searchPointFeature = this.createPointFeature(
        coordinates,
        require('./location.png'),
        'anchor',
        {
          scale: 0.3,
        }
      );
      newLayer.getSource()!.addFeature(searchPointFeature);
    }
  };

  styleFunction = (feature: any) => {
    var styles = [];
    styles.push(
      new Style({
        stroke: new Stroke({
          color: '#ff0000',
          width: 4,
        }),
      })
    );
    var _coords = feature.get('geometry').getCoordinates();
    for (var i = 0; i < _coords.length; i++) {
      styles.push(
        new Style({
          geometry: new Point(_coords[i]),
          image: new Circle({
            radius: 4,
            fill: new Fill({
              color: '#ffff',
            }),
            stroke: new Stroke({
              color: '#ff0000',
              width: 2,
            }),
          }),
        })
      );
    }
    return styles;
  };
}

export default MapEditorTool;
