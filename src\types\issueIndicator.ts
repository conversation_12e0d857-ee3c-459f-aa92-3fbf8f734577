/** 工单指标层表格数据 */
export interface IssueIndicatorLayerResponse {
  /** 指标层id */
  layerNumber: string;
  /** 指标层名称 */
  layerName: string;
  /** 指标层权重 */
  priority: number;
  /** 指标数量 */
  indicatorCount: number;
  /** 操作人 */
  modifyUser: string;
  /** 操作时间 */
  modifyTime: string;
}
[];

/** 工单指标层编辑 */
export interface IssueIndicatorLayerEdit {
  /** 指标层id */
  layerNumber: string;
  /** 权重 */
  priority: number;
}

/** 工单指标表格 */
export interface IssueIndicatorResponse {
  /** 指标层id */
  layerNumber: string;
  /** 指标层名称 */
  layerName: string;
  /** 操作人 */
  modifyUser: string;
  /** 操作时间 */
  modifyTime: string;
  /** 指标列表 */
  indicatorInfoList: {
    /** 指标编号 */
    indicatorNumber: string;
    /** 指标名称 */
    indicatorName: string;
    /** 指标权重 */
    priority: number;
  }[];
}

/** 工单指标编辑 */
export interface IssueIndicatorEdit {
  layerNumber: string;
  indicatorInfoList: {
    /** 指标编号 */
    indicatorNumber: string;
    /** 指标权重 */
    priority: number;
  }[];
}

/** 工单池表格 */
export interface IssueIndicatorPool {
  /** 事件id */
  indicatorNumber: string;
  /** 事件名称 */
  indicatorName: string;
  /** 权重 */
  priority: number;
  /** 所属工单池 */
  issuePool: string;
  /** 所属工单池名称 */
  issuePoolName: string;
  /** 操作人 */
  modifyUser: string;
  /** 操作时间 */
  modifyTime: string;
}
