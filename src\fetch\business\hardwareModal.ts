import { YESNO } from '@/utils/enum';
import { request } from '../core';
import { PageType } from '@/utils/enum';

export class HardwareModalApi {
  // 分页查询硬件型号数据列表
  fetchTableList({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/hardware_model/hardware_model_get_page_list',
      urlParams: {
        pageNum,
        pageSize,
      },
      body: searchForm,
    };
    return request(options);
  }
  //  新增编辑硬件型号
  submitHardwareModal({
    type,
    requestBody,
  }: {
    type: PageType;
    requestBody: Object;
  }) {
    const options: RequestOptions = {
      method: type === PageType.EDIT ? 'PUT' : 'POST',
      path:
        type === PageType.EDIT
          ? '/k2/management/hardware_model/hardware_model_edit'
          : '/k2/management/hardware_model/hardware_model_add',
      body: requestBody,
    };
    return request(options);
  }
  // 获取硬件型号详情
  fetchHardwearModalDetail(id: number | string) {
    const options: RequestOptions = {
      method: 'GET',
      path: `/k2/management/hardware_model/hardware_model_get_detail`,
      urlParams: {
        id,
      },
    };
    return request(options);
  }
  // 启用停用硬件型号
  updateHardwearTypeStatus({ id, enable }: { id: number; enable: YESNO }) {
    const options: RequestOptions = {
      method: 'PUT',
      path: '/k2/management/hardware_model/hardware_model_update_enable',
      body: {
        id,
        enable,
      },
    };
    return request(options);
  }
  //   根据用途ID查询硬件下拉列表
  getSelectListById(id: string | number) {
    const options: RequestOptions = {
      method: 'GET',
      path: 'k2/management/hardware_type/hardware_type_usage_get_select_list',
      urlParams: {
        hardwareTypeId: id,
      },
    };
    return request(options);
  }
}
