import React, { useEffect, useRef, useState } from 'react';
import { CommonForm, FormConfig } from '@jd/x-coreui';
import { message, Modal } from 'antd';
import { formConfig } from './formConfig';
import { StationFetchApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { debounce } from '@/utils/utils';
const requestApi = new StationFetchApi();
const BatchModifyStationPerson = ({
  open,
  setOpen,
}: {
  open: boolean;
  setOpen: Function;
}) => {
  const formRef = useRef<any>(null);
  const [config, setConfig] = useState<FormConfig>(formConfig);
  const [dropDownMap, setDropDownMap] = useState<any>({
    stationUseCase: [],
    productTypeList: [],
    userList: [],
  });
  useEffect(() => {
    getAllDropDown();
  }, []);
  useEffect(() => {
    setConfig({
      ...config,
      fields: config.fields.map((item) => {
        if (item.fieldName === 'useCase') {
          item.options = dropDownMap.stationUseCase;
        } else if (item.fieldName === 'productType') {
          item.options = dropDownMap.productTypeList;
        } else if (item.fieldName === 'personName') {
          item.options = dropDownMap.userList;
        }
        return item;
      }),
    });
  }, [dropDownMap]);
  const getCommonDropDownList = async () => {
    const res = await requestApi.getCommonDropDown([
      'STATION_USE_CASE',
      'PRODUCT_TYPE',
    ]);
    if (res && res.code === HttpStatusCode.Success) {
      return res.data;
    }
  };

  const getUserInfoList = async () => {
    const res = await requestApi.getUserInfoList();
    if (res.code === HttpStatusCode.Success && res.data) {
      return res.data;
    }
  };

  const getAllDropDown = () => {
    Promise.all([getCommonDropDownList(), getUserInfoList()]).then((res) => {
      setDropDownMap({
        stationUseCase: res[0].stationUseCaseList?.map((item: any) => {
          return {
            label: item.name,
            value: item.code,
          };
        }),
        productTypeList: res[0].productTypeList?.map((item: any) => {
          return {
            label: item.name,
            value: item.code,
          };
        }),
        userList: res[1]?.map((item: any) => {
          return {
            label: `${item.realName}/${item.erp}`,
            value: `${item.userName}/${item.phone}`,
          };
        }),
      });
    });
  };
  const onSubmit = debounce(async () => {
    try {
      const values = await formRef.current.validateFields();
      const params = {
        cityId: values.cityId[1],
        productType: values.productType,
        useCase: values.useCase,
        personName: values.personName.value.split('/')[0],
        contact: values.contact,
      };
      const res = await requestApi.batchModifyStationPerson(params);
      if (res.code === HttpStatusCode.Success) {
        setOpen(false);
        message.success('修改成功');
        formRef.current.resetFields();
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.error(e);
    }
  }, 1000);
  return (
    <>
      <Modal
        title="批量修改站点负责人"
        width={800}
        open={open}
        onCancel={() => {
          setOpen(false);
          formRef.current.resetFields();
        }}
        onOk={onSubmit}
      >
        <CommonForm
          formConfig={config}
          formType="edit"
          colon={false}
          defaultValue={{ fetchData: true }}
          getFormInstance={(formInstance) => {
            formRef.current = formInstance;
          }}
          onValueChange={(values, fieldName) => {
            if (fieldName === 'personName') {
              formRef.current.setFieldValue(
                'contact',
                values[fieldName].value.split('/')[1],
              );
            }
          }}
        />
      </Modal>
    </>
  );
};
export default React.memo(BatchModifyStationPerson);
