import React, { useEffect, useState } from 'react';
import StationBasicInfo from '.';
import StaticInfo from './StaticInfo';
import { BasicStationInfo } from '@/types';
import { Button, Form, message } from 'antd';
import { AnyFunc } from '@/global';
import { StationFetchApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
const requestApi = new StationFetchApi();
const EditableStationInfo = (props: {
  stationDetail: BasicStationInfo | null;
  dropDownMap: any;
  setDropDownMap: AnyFunc;
  updateDataCb?: AnyFunc;
}) => {
  const { stationDetail, dropDownMap, setDropDownMap, updateDataCb } = props;
  const [editing, setEditing] = useState(false);
  const [form] = Form.useForm();

  const [locationInfo, setLocationInfo] = useState<any>();

  useEffect(() => {
    if (stationDetail && editing) {
      form.setFieldsValue({
        name: stationDetail.name,
        productType: stationDetail.productType,
        type: stationDetail.type,
        useCase: stationDetail.useCase,
        cityInfo: [stationDetail.stateId, stationDetail.cityId],
        personName: {
          label: `${stationDetail.personChineseName}/${stationDetail.personErp}`,
          value: `${stationDetail.personName}/${stationDetail.contact}`,
        },
        contact: stationDetail.contact,
      });
      setLocationInfo({
        cityId: stationDetail.cityId,
        cityName: stationDetail.cityName,
        address: stationDetail.address,
        initPosition: {
          lat: stationDetail.lat,
          lon: stationDetail.lon,
        },
      });
    }
  }, [stationDetail, editing]);
  const handleSubmit = async () => {
    try {
      if (stationDetail) {
        const values = await form.validateFields();
        const editedStationInfo = {
          cityId: locationInfo.cityId,
          address: locationInfo.address,
          contact: values.contact,
          name: values.name,
          productType: values.productType,
          type: values.type,
          useCase: values.useCase,
          personName: values.personName.value.split('/')?.[0] || null,
          lat: locationInfo.initPosition.lat,
          lon: locationInfo.initPosition.lon,
          stationBaseId: stationDetail.stationBaseId,
        };
        const res = await requestApi.editStationInfo(editedStationInfo);
        if (res.code === HttpStatusCode.Success) {
          message.success('编辑运营部基础信息成功！');
          updateDataCb && updateDataCb();
          setEditing(false);
        } else {
          message.error(res.message);
        }
      }
    } catch (errorInfo) {
      message.error(errorInfo as string);
      console.log('Failed:', errorInfo);
    }
  };
  return (
    <>
      {!editing ? (
        <>
          <Button
            type="primary"
            style={{ marginBottom: '8px' }}
            onClick={() => {
              setEditing(true);
            }}
          >
            修改
          </Button>
          <StaticInfo stationDetail={stationDetail} />
        </>
      ) : (
        <>
          <Form
            form={form}
            wrapperCol={{ span: 19 }}
            labelCol={{ span: 6 }}
            colon={false}
            onFieldsChange={(changedFields) => {
              changedFields.forEach((field) => {
                if (field.name.includes('type') && field.value === 'SELF') {
                  setDropDownMap({
                    ...dropDownMap,
                  });
                } else if (
                  field.name.includes('type') &&
                  field.value === 'THIRD'
                ) {
                  setDropDownMap({
                    ...dropDownMap,
                  });
                }
              });
            }}
          >
            <StationBasicInfo
              dropDownMap={dropDownMap}
              locationInfo={locationInfo}
              setLocationInfo={setLocationInfo}
              disabled={true}
              form={form}
            />
          </Form>
          <div
            className="operation-btn"
            style={{ display: 'flex', justifyContent: 'center' }}
          >
            <Button
              type="primary"
              style={{ marginRight: '10px' }}
              onClick={() => {
                setEditing(false);
                handleSubmit();
              }}
            >
              确认
            </Button>
            <Button
              onClick={() => {
                setEditing(false);
              }}
            >
              取消
            </Button>
          </div>
        </>
      )}
    </>
  );
};
export default React.memo(EditableStationInfo);
