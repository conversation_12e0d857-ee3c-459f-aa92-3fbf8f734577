import React, { useContext, useEffect, useState } from 'react';
import {
  Form,
  Row,
  Col,
  Table,
  Button,
  message,
  FormInstance,
  Input,
  Select,
  Popover,
} from 'antd';
import { HttpStatusCode } from '@/fetch/core/constant';
import { hardwareEditTableColumns } from '../../utils/column';
import './index.scss';
import { RepairOrderFetchApi } from '@/fetch/business';

const EditableContext = React.createContext<FormInstance<any> | null>(null);
interface Item {
  key: number;
  id: number;
  oldHardwareModelId: number;
  oldHardwareModelName: string;
  oldHardwareModelModel: string;
  newHardwareModelId: number;
  newHardwareModelName: string;
  newHardwareModelModel: string;
  newHardwareModelNumber: string;
  newHardwareModelCost: number;
}
interface EditableRowProps {
  index: number;
}
const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

interface EditableCellProps {
  title: React.ReactNode;
  children: React.ReactNode;
  dataIndex: keyof Item;
  record: Item;
  oldHardwareInfoList: any[];
  newHardwareInfoList: any[];
  getNewHardwareOptions: Function;
  saveOldHardwareModel: Function;
  saveNewHardwareModel: Function;
  saveNewHardwareModelNumber: Function;
}

const EditableCell: React.FC<EditableCellProps> = ({
  title,
  children,
  dataIndex,
  record,
  oldHardwareInfoList,
  newHardwareInfoList,
  getNewHardwareOptions,
  saveOldHardwareModel,
  saveNewHardwareModel,
  saveNewHardwareModelNumber,
  ...restProps
}) => {
  let childNode = children;
  const form: any = useContext(EditableContext);
  useEffect(() => {
    form?.setFieldsValue({
      oldHardwareModelName: record?.oldHardwareModelName
        ? {
            label: record?.oldHardwareModelName,
            value: record?.oldHardwareModelId,
          }
        : null,
      newHardwareModelName: record?.newHardwareModelName
        ? {
            label: record?.newHardwareModelName,
            value: record?.newHardwareModelId,
          }
        : null,
      newHardwareModelNumber: record?.newHardwareModelNumber,
    });
  }, [record]);
  const makeOptions = (list: any[]) => {
    if (list) {
      return list.map((item: any) => {
        return {
          value: item.hardwareModelId,
          label: item.hardwareModelName,
        };
      });
    } else {
      return [];
    }
  };
  switch (dataIndex) {
    case 'oldHardwareModelName':
      childNode = (
        <Form.Item style={{ margin: 0 }} name={'oldHardwareModelName'}>
          <Select
            labelInValue
            placeholder={`请输入硬件名称`}
            options={makeOptions(oldHardwareInfoList)}
            onChange={(value: any) => {
              saveOldHardwareModel(value, record.id);
            }}
          />
        </Form.Item>
      );
      break;
    case 'oldHardwareModelModel':
      childNode = (
        <Popover content={record.oldHardwareModelModel} placement={'top'}>
          <div
            style={{
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
            }}
          >
            {record.oldHardwareModelModel}
          </div>
        </Popover>
      );
      break;
    case 'newHardwareModelName':
      childNode = (
        <Form.Item style={{ margin: 0 }} name={'newHardwareModelName'}>
          <Select
            labelInValue
            placeholder={`请输入硬件名称`}
            options={makeOptions(newHardwareInfoList)}
            onFocus={() => getNewHardwareOptions(record)}
            onChange={(value: any) => {
              saveNewHardwareModel(value, record.id);
            }}
          />
        </Form.Item>
      );
      break;
    case 'newHardwareModelModel':
      childNode = (
        <Popover content={record.newHardwareModelModel} placement={'top'}>
          <div
            style={{
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
            }}
          >
            {record.newHardwareModelModel}
          </div>
        </Popover>
      );
      break;
    case 'newHardwareModelNumber':
      childNode = (
        <Form.Item style={{ margin: 0 }} name={'newHardwareModelNumber'}>
          <Input
            style={{ textAlign: 'center' }}
            placeholder={`请输入设备序列号`}
            onBlur={(e) => {
              saveNewHardwareModelNumber(e.target.value, record.id);
            }}
          />
        </Form.Item>
      );
      break;
  }
  return <td {...restProps}>{childNode}</td>;
};

type EditableTableProps = Parameters<typeof Table>[0];
type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>;

interface Props {
  vehicleId: number;
  form: FormInstance;
  hardwareInfoList: any;
}

const EditHardwareInfo = (props: Props) => {
  const fetchApi = new RepairOrderFetchApi();
  const { vehicleId, hardwareInfoList } = props;
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [oldHardwareInfoList, setOldHardwareInfoList] = useState<any[]>([]);
  const [newHardwareInfoList, setNewHardwareInfoList] = useState<any[]>([]);

  useEffect(() => {
    if (hardwareInfoList) {
      setDataSource([
        ...hardwareInfoList.map((item: any) => {
          return {
            ...item,
            key: item.id,
          };
        }),
      ]);
    }
  }, [hardwareInfoList]);

  useEffect(() => {
    if (vehicleId) {
      getOldHardwareInfoList();
    }
  }, [vehicleId]);

  /**
   * 获取当前车辆的维修硬件名称下拉框列表
   */
  const getOldHardwareInfoList = async () => {
    try {
      const res: any = await fetchApi.fetchVehicleHardwareModel(vehicleId);
      if (res.code === HttpStatusCode.Success) {
        setOldHardwareInfoList(res.data);
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    } finally {
      //
    }
  };

  /**
   * 获取某硬件类型下硬件型号列表
   * @param {any} record 当前点击的行
   */
  const getNewHardwareOptions = (record: any) => {
    setNewHardwareInfoList([]);
    const oldHardwareModelInfo = oldHardwareInfoList.filter((item) => {
      return item.hardwareModelId === record.oldHardwareModelId;
    })[0];
    // 判断旧硬件是不是车型硬件，是车型硬件自动带入新硬件，不是车型硬件请求下拉框内容
    if (!oldHardwareModelInfo || oldHardwareModelInfo?.isOfVehicleType === 1) {
      return;
    }
    fetchApi
      .fetchHardwareModel(oldHardwareModelInfo?.hardwareTypeId)
      .then((res: any) => {
        setNewHardwareInfoList(res.data);
      });
  };

  /**
   * 保存选中的旧硬件信息
   * @param {any} value 选中的旧硬件
   * @param {number} id 当前行id
   */
  const saveOldHardwareModel = (value: any, id: number) => {
    const newData = [...dataSource];
    const index = newData.findIndex((item) => id === item.id);
    const item = newData[index];
    const oldHardwareInfo = oldHardwareInfoList.filter((item) => {
      return item.hardwareModelId === value.value;
    })[0];
    if (oldHardwareInfo.isOfVehicleType === 1) {
      newData.splice(index, 1, {
        ...item,
        oldHardwareModelId: value.value,
        oldHardwareModelName: value.label,
        oldHardwareModelModel: oldHardwareInfo.hardwareModelModel,
        newHardwareModelId: value.value,
        newHardwareModelName: value.label,
        newHardwareModelModel: oldHardwareInfo.hardwareModelModel,
      });
    } else {
      newData.splice(index, 1, {
        ...item,
        oldHardwareModelId: value.value,
        oldHardwareModelName: value.label,
        oldHardwareModelModel: oldHardwareInfo.hardwareModelModel,
        newHardwareModelId: null,
        newHardwareModelName: null,
        newHardwareModelModel: '系统带入型号',
      });
    }
    setDataSource(newData);
  };

  /**
   * 保存选中的新硬件信息
   * @param {any} value 选中的旧硬件
   * @param {number} id 当前行id
   */
  const saveNewHardwareModel = (value: any, id: number) => {
    const newData = [...dataSource];
    const index = newData.findIndex((item) => id === item.id);
    const item = newData[index];
    const newHardwareInfo = newHardwareInfoList.filter((item) => {
      return item.hardwareModelId === value.value;
    })[0];
    newData.splice(index, 1, {
      ...item,
      newHardwareModelId: value.value,
      newHardwareModelName: value.label,
      newHardwareModelModel: newHardwareInfo.hardwareModelModel,
    });
    setDataSource(newData);
  };

  /**
   * 保存更换后硬件序列号
   * @param  {any} value 更换后硬件序列号
   * @param  {number} id 当前行id
   */
  const saveNewHardwareModelNumber = (value: any, id: number) => {
    const newData = [...dataSource];
    const index = newData.findIndex((item) => id === item.id);
    const item = newData[index];
    newData.splice(index, 1, {
      ...item,
      newHardwareModelNumber: value,
    });
    setDataSource(newData);
  };

  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  };

  const columns = hardwareEditTableColumns.map((col: any) => {
    return {
      ...col,
      onCell: (record: any) => ({
        record,
        dataIndex: col.dataIndex,
        title: col.title,
        oldHardwareInfoList: oldHardwareInfoList,
        newHardwareInfoList: newHardwareInfoList,
        getNewHardwareOptions: getNewHardwareOptions,
        saveOldHardwareModel: saveOldHardwareModel,
        saveNewHardwareModel: saveNewHardwareModel,
        saveNewHardwareModelNumber: saveNewHardwareModelNumber,
      }),
    };
  });

  const makeEditDatasource = () => {
    const editList = dataSource?.map((item: any, index: any) => {
      return {
        name: index === 0 ? '+' : '-',
        key: item.id,
      };
    });
    return editList;
  };

  /**
   * 增加一行
   */
  const onAddClick = () => {
    const filteredList = dataSource.filter((item) => {
      return (
        item.oldHardwareModelName === undefined ||
        item.newHardwareModelName === undefined ||
        item.newHardwareModelName === null ||
        item.oldHardwareModelName === null
      );
    });
    if (filteredList.length > 0) {
      message.error('请先完善当前信息');
      return;
    }
    setDataSource([
      ...dataSource,
      {
        id: Date.now(),
        key: Date.now(),
        oldHardwareModelName: null,
        oldHardwareModelModel: '系统带入型号',
        oldHardwareModelId: null,
        newHardwareModelName: null,
        newHardwareModelModel: '系统带入型号',
        newHardwareModelId: null,
        newHardwareModelNumber: null,
      },
    ]);
  };

  /**
   * 删除一行
   * @param {number} index 要删除行的index
   */
  const onDeleteClick = (index: number) => {
    dataSource.splice(index, 1);
    setDataSource([...dataSource]);
  };

  useEffect(() => {
    props.form.setFieldsValue({
      requireHardwareModelInfoList: dataSource.map((item) => {
        return {
          oldHardwareModelId: item.oldHardwareModelId,
          newHardwareModelId: item.newHardwareModelId,
          newHardwareModelNumber: item.newHardwareModelNumber,
        };
      }),
    });
  }, [JSON.stringify(dataSource)]);

  return (
    <Row>
      <Col span={22}>
        <Table
          rowClassName={'rewrite-table'}
          pagination={false}
          components={components}
          bordered
          dataSource={dataSource}
          columns={columns as ColumnTypes}
        />
      </Col>
      <Col span={1}>
        <Table
          columns={[
            {
              title: 'has',
              dataIndex: 'name',
              align: 'left',
              onHeaderCell: (data, index) => {
                return {
                  style: {
                    backgroundColor: 'rgba(0, 0, 0, 0)',
                    color: 'white',
                    borderWidth: 0,
                  },
                };
              },
              onCell: (data, index) => {
                return {
                  style: {
                    backgroundColor: 'white',
                    borderWidth: 0,
                    padding: 0,
                    height: 65,
                  },
                };
              },
              render: (name, _: any, index: number) => {
                const bgColor = name === '+' ? '#31C2A6' : '#D9001B';
                return name === '-' ? (
                  <Button
                    onClick={() => {
                      onDeleteClick && onDeleteClick(index);
                    }}
                    style={{
                      marginLeft: 15,
                      color: bgColor,
                      width: 55,
                      borderColor: bgColor,
                      borderRadius: 4,
                    }}
                  >
                    {name}
                  </Button>
                ) : (
                  <Button
                    onClick={() => {
                      onAddClick && onAddClick();
                    }}
                    style={{
                      marginLeft: 15,
                      color: bgColor,
                      width: 55,
                      borderColor: bgColor,
                      borderRadius: 4,
                    }}
                  >
                    {name}
                  </Button>
                );
              },
            },
          ]}
          dataSource={makeEditDatasource()}
          pagination={false}
        />
      </Col>
    </Row>
  );
};

export default React.memo(EditHardwareInfo);
