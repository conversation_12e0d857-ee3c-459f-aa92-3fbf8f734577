import { FormConfig } from '@jd/x-coreui';

const formConfig: FormConfig = {
  fields: [
    {
      fieldName: 'taskName',
      label: '任务名称',
      type: 'input',
      labelCol: {
        span: 6,
      },
      placeholder: '请输入任务名称',
    },
    {
      fieldName: 'stationId',
      label: '所属站点',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      labelCol: {
        span: 6,
      },
      placeholder: '请选择所属站点',
    },
    {
      fieldName: 'creatorUsername',
      label: '创建人',
      type: 'input',
      labelCol: {
        span: 6,
      },
      placeholder: '请输入创建人',
    },
    {
      fieldName: 'taskStatus',
      label: '任务状态',
      type: 'select',
      labelInValue: false,
      labelCol: {
        span: 6,
      },
      placeholder: '请选择任务状态',
    },
  ],
};

export default formConfig;
