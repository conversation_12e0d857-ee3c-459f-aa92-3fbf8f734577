export interface XMapVersionDataType {
  id: number;
  mapId: number;
  mapCode: string;
  mapBlockName: string;
  rawDataVersion: number;
  mapVersion: number;
  mapState: string;
  minX: string;
  minY: string;
  maxX: string;
  maxY: number;
  offsetX: number;
  offsetY: number;
  utmZone: number;
  modifyTime: string;
  rawDataDescription: string;
  preMapVersion: string;
  cityName: number;
  areaName: number;
}

export interface XMapInfoDataType extends Omit<XMapVersionDataType, 'mapId'> {
  // id: string;
}
