import { CommonForm, CommonTable, FieldItem, FormConfig, useTableData } from "@jd/x-coreui";
import React, { useEffect, useState } from "react";
import { OperationConfig, SearchConfig, TableConfig } from "./config";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { TransportModelApi } from "@/fetch/business/transportModel";
import { showModal, TableOperateBtn } from "@/components";
import dayjs from "dayjs";
import { HttpStatusCode } from "@/fetch/core/constant";
import { message } from "antd";

const fetchApi = new TransportModelApi();
const TransportModelRecommendResult = () => {

    const [searchConfig, setSearchConfig] = useState<FormConfig>(SearchConfig);

    const initSearchCondition = {
        searchForm: {
            dt: dayjs().subtract(2, 'day'),
            recommandUnmannedErcheng: null,
            provinceCompanyCode: null,
            areaId: null,
            cityId: null,
            siteId: null,
            roadareaId: null,
        },
        pageNum: 1,
        pageSize: 10,
    };

    const historySearchValues = useSelector(
        (state: RootState) => state.searchForm,
    );

    const [searchCondition, setSearchCondition] = useState<SearchCondition>(
        () => {
            return historySearchValues.searchValues
                ? historySearchValues.searchValues
                : initSearchCondition;
        },
    );

    const onSearchClick = (val) => {
        if (JSON.stringify(val) === JSON.stringify(searchCondition.searchForm)) {
            return;
        }
        const data = {
            ...searchCondition,
            searchForm: val,
        };
        setSearchCondition(data);
    };

    const { tableData, reloadTable, loading }: any = useTableData(
        {
            searchForm: {
                ...searchCondition.searchForm,
                dt: searchCondition.searchForm.dt.format('YYYY-MM-DD'),
                recommandUnmannedErcheng: searchCondition.searchForm.recommandUnmannedErcheng?.value,
                provinceCompanyCode: searchCondition.searchForm.provinceCompanyCode?.value,
                areaId: searchCondition.searchForm.areaId?.value,
                cityId: searchCondition.searchForm.cityId?.value,
                siteId: searchCondition.searchForm.siteId?.value,
                roadareaId: searchCondition.searchForm.roadareaId?.value,
            },
            pageNum: searchCondition.pageNum,
            pageSize: searchCondition.pageSize,
        },
        fetchApi.fetchResultTableList,
    );

    const formatColumns = () => {
        return TableConfig.map((col) => {
            switch (col.dataIndex) {
                case 'needFlag':
                case 'recommandUnmannedErcheng':
                case 'useCar':
                    return {
                        ...col,
                        render: (text: any) => text == null ? '-' : text ? '是' : '否'
                    };
                case 'diatance':
                case 'lineTtdlNum':
                case 'lineCCollectNum':
                case 'lineAllNum':
                case 'bCollectOrder':
                case 'cCollectOrder':
                case 'deliverOrder':
                case 'properTimelyRateAvg':
                case 'siteFirstPickupRateAvg':
                case 'roadCollectOntimeRateIncreaseAvg':
                case 'siteCollectOntimeIncreaseAvg':
                case 'roadCourierProductionIncreaseAvg':
                case 'siteCourierProductionIncreaseAvg':
                case 'collectOverTimeCountAvg':
                case 'cancelRateAllAvg':
                case 'goodsWeight':
                case 'goodsVolume':
                    return {
                        ...col,
                        render: (text: any) => text ? text.toFixed(2) : '-'
                    };
                case 'operation':
                    return {
                        ...col,
                        render: (text: any, record: any, index: number) => {
                            return (
                                <div className="operate">
                                    <TableOperateBtn
                                        title="是否需要接驳"
                                        handleClick={() => {
                                            let formRef: any = null;
                                            showModal({
                                                title: '是否需要接驳',
                                                content: <CommonForm formConfig={OperationConfig} layout="vertical" formType="edit" colon={false} labelAlign="left" getFormInstance={(ref) => {
                                                    formRef = ref;
                                                }} />,
                                                footer: {
                                                    showOk: true,
                                                    showCancel: true,
                                                    okFunc: async (cb) => {
                                                        try {
                                                            const values = await formRef.validateFields();
                                                            values.siteId = record.siteId;
                                                            values.roadNo = record.roadareaId;
                                                            const res = await fetchApi.saveRoadMark(values);
                                                            if (res.code === HttpStatusCode.Success) {
                                                                message.success("编辑成功");
                                                                cb();
                                                                reloadTable();
                                                            } else {
                                                                message.error(res.message || '接口请求错误');
                                                            }
                                                        } catch (e) { }
                                                    },
                                                    cancelFunc: (cb) => {
                                                        cb();
                                                    }
                                                }
                                            })
                                        }}
                                    />
                                </div>
                            );
                        },
                    };
                default:
                    return {
                        ...col,
                        render: (text: any) => `${text || '-'}`,
                    };
            }
        });
    };

    useEffect(() => {
        fetchApi.getModelResultSelect('PROVINCE').then((res) => {
            if (res.code === HttpStatusCode.Success) {
                const field = searchConfig.fields.find((item: FieldItem) => item?.fieldName === 'provinceCompanyCode');
                field!.options = res.data?.map((val) => ({
                    label: val.name,
                    value: val.value,
                }));
                setSearchConfig({ ...searchConfig });
            }
        })
        fetchApi.getModelResultSelect('AREA').then((res) => {
            if (res.code === HttpStatusCode.Success) {
                const field = searchConfig.fields.find((item: FieldItem) => item?.fieldName === 'areaId');
                field!.options = res.data?.map((val) => ({
                    label: val.name,
                    value: val.value,
                }));
                setSearchConfig({ ...searchConfig });
            }
        })
        fetchApi.getModelResultSelect('CITY').then((res) => {
            if (res.code === HttpStatusCode.Success) {
                const field = searchConfig.fields.find((item: FieldItem) => item?.fieldName === 'cityId');
                field!.options = res.data?.map((val) => ({
                    label: val.name,
                    value: val.value,
                }));
                setSearchConfig({ ...searchConfig });
            }
        })
        fetchApi.getModelResultSelect('STATION').then((res) => {
            if (res.code === HttpStatusCode.Success) {
                const field = searchConfig.fields.find((item: FieldItem) => item?.fieldName === 'siteId');
                field!.options = res.data?.map((val) => ({
                    label: val.name,
                    value: val.value,
                }));
                setSearchConfig({ ...searchConfig });
            }
        })
    }, []);

    return (
        <>
            <CommonForm
                formConfig={searchConfig}
                defaultValue={searchCondition.searchForm}
                layout="inline"
                formType="search"
                colon={false}
                onSearchClick={onSearchClick}
                onResetClick={() => setSearchCondition({ ...initSearchCondition })}
            />
            <CommonTable
                tableListData={{
                    list: tableData?.list ?? [],
                    totalNumber: tableData?.total,
                    totalPage: tableData?.pages,
                }}
                columns={formatColumns()}
                loading={loading}
                rowKey={'id'}
                searchCondition={searchCondition}
                onPageChange={(value: any) => setSearchCondition(value)}
            />
        </>
    )
}

export default TransportModelRecommendResult;