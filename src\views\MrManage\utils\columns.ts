import { FormConfig } from '@jd/x-coreui';
import { message } from 'antd';
export const tableColumns: any[] = [
  {
    title: 'MR_ID',
    dataIndex: 'mrNumber',
    align: 'left',
    width: 50,
    fixed: 'left',
  },
  {
    title: 'MR合入时间',
    dataIndex: 'mrUpdateTime',
    align: 'left',
    width: 90,
  },
  {
    title: '是否提测',
    dataIndex: 'isSubmitTest',
    align: 'left',
    width: 60,
  },
  {
    title: '不提测原因',
    dataIndex: 'noTestReason',
    align: 'left',
    width: 90,
  },
  {
    title: 'fix_jira',
    dataIndex: 'fixJiraKeyList',
    align: 'left',
    width: 160,
  },
  {
    title: '模块',
    dataIndex: 'module',
    align: 'left',
    width: 80,
  },
  {
    title: '是否有依赖conf',
    dataIndex: 'isDependOnConfName',
    align: 'left',
    width: 90,
  },
  {
    title: '依赖的conf文件名',
    dataIndex: 'dependencyConfName',
    align: 'left',
    width: 90,
  },
  {
    title: '提测内容',
    dataIndex: 'description',
    align: 'left',
    width: 120,
  },
  {
    title: '期待结果',
    dataIndex: 'expectedOutcome',
    align: 'left',
    width: 60,
  },
  {
    title: '研发人员',
    dataIndex: 'developer',
    align: 'left',
    width: 90,
  },
  {
    title: '操作步骤',
    dataIndex: 'operationStep',
    align: 'left',
    width: 90,
  },
  {
    title: '已测试天数',
    dataIndex: 'testedDays',
    align: 'left',
    width: 60,
  },
  {
    title: '实车测试人员',
    dataIndex: 'realCarTester',
    align: 'left',
    width: 90,
  },
  {
    title: '测试结果',
    dataIndex: 'testResultName',
    align: 'left',
    width: 60,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    align: 'left',
    width: 50,
  },
  {
    title: '是否依赖MR',
    dataIndex: 'isDependOnMrName',
    align: 'left',
    width: 60,
  },
  {
    title: '依赖MR_ID',
    dataIndex: 'dependencyMrId',
    align: 'left',
    width: 50,
  },
  {
    title: 'Rover版本号',
    dataIndex: 'roverVersion',
    align: 'left',
    width: 80,
  },
  {
    title: '发版周',
    dataIndex: 'releaseWeek',
    align: 'left',
    width: 50,
  },
  {
    title: 'MR状态',
    dataIndex: 'mrStatusName',
    align: 'left',
    width: 60,
  },
  {
    title: 'coding测试审核人',
    dataIndex: 'testReviewer',
    align: 'left',
    width: 90,
  },
  {
    title: '测试方式',
    dataIndex: 'testMethodName',
    align: 'left',
    width: 90,
  },
  {
    title: '软件/模块依赖',
    dataIndex: 'dependency',
    align: 'left',
    width: 60,
  },
  {
    title: '是否为专项测试',
    dataIndex: 'isSpecificTestName',
    align: 'left',
    width: 60,
  },
  {
    title: '专项名称',
    dataIndex: 'specificTestName',
    align: 'left',
    width: 60,
  },
  {
    title: '仿真测试人员',
    dataIndex: 'simulationTester',
    align: 'left',
    width: 90,
  },
  {
    title: '关注程度',
    dataIndex: 'attentionLevelName',
    align: 'left',
    width: 60,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 100,
    align: 'left',
    fixed: 'right',
  },
];

export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'mrNumber',
      label: 'MR_ID',
      placeholder: '请输入MR_ID',
      type: 'input',
      validatorRules: [
        {
          validator: (rule: any, value: any) => {
            const regex = /^\d+$/g;
            if (!value?.trim()) {
              return Promise.resolve();
            }
            if (regex.test(value?.trim())) {
              return Promise.resolve();
            }
            message.error('MR_ID仅能输入数字！');
            return Promise.reject();
          },
        },
      ],
    },
    {
      fieldName: 'mrBranch',
      label: 'MR_BRANCH',
      placeholder: '请选择MR_BRANCH',
      type: 'select',
    },
    {
      fieldName: 'isSubmitTest',
      label: '是否提测',
      placeholder: '请选择是否提测',
      type: 'select',
      labelInValue: false,
      options: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
    },
    {
      fieldName: 'fixJiraKey',
      label: 'fix_Jira',
      placeholder: '请输入fix_Jira',
      type: 'input',
    },
    {
      fieldName: 'module',
      label: '模块',
      placeholder: '请输入关键字查询',
      type: 'input',
      allowClear: true,
    },
    {
      fieldName: 'developer',
      label: '研发人员',
      placeholder: '请输入ERP',
      type: 'input',
    },
    {
      fieldName: 'testResultList',
      label: '测试结果',
      placeholder: '请选择',
      type: 'select',
      multiple: true,
      labelInValue: false,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xxl: 4,
      xl: 6,
    },
    {
      fieldName: 'mrStatus',
      label: 'MR状态',
      placeholder: '请选择MR状态',
      type: 'select',
    },
    {
      fieldName: 'isDependOnConf',
      label: '是否有依赖conf',
      placeholder: '请选择',
      type: 'select',
      labelInValue: false,
      options: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
    },
    {
      fieldName: 'isSpecificTest',
      label: '是否有专项测试',
      placeholder: '请选择',
      type: 'select',
      labelInValue: false,
      options: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
    },
    {
      fieldName: 'specificTestName',
      label: '专项名称',
      placeholder: '请输入专项项目名称',
      type: 'input',
    },
    {
      fieldName: 'testPassDate',
      label: '测试通过日期',
      type: 'rangeTime',
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      xxl: 8,
      xl: 12,
      lg: 16,
    },
    {
      fieldName: 'mrReleaseDate',
      label: 'MR发版日期',
      type: 'rangeTime',
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      xxl: 8,
      xl: 12,
      lg: 16,
    },
    {
      fieldName: 'time',
      label: 'MR合入时间',
      type: 'rangeTime',
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      hidden: false,
      xxl: 8,
      xl: 12,
      lg: 16,
    },
  ],
  linkRules: {
    showMRMergeTime: [
      {
        linkFieldName: 'time',
        rule: 'visible',
        dependenceData: [true],
      },
    ],
  },
};
