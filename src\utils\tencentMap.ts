/* eslint-disable no-invalid-this */
/* eslint-disable no-cond-assign */
/* eslint-disable require-jsdoc */
/* eslint-disable new-cap */

const winAny = window as any;
const { qq, coordtransform } = winAny;
import icon from '@/assets/image/common/map_marker.png';
import { fetchGeocoder } from '@/views/StationManagement/utils/dataSearch';
export class TencentMap {
  public onMarked: Function = () => {};
  public container: HTMLElement | null = null;
  public map: any = null;
  public selectedMark:
    | {
        lat: any;
        lon: any;
        address: any;
        detailAddress: any;
      }
    | any = null;

  geocoder: any = null;
  infoWin: any = null;
  markers: any[] = [];
  selectedMapMaker: any = null;
  searchService: any = null;
  poiSearchCallBack: Function = (results: any) => {
    const pois = results.detail.pois;
    if (pois && pois.length) {
      const latlngBounds = new qq.maps.LatLngBounds();
      for (let i = 0, l = pois.length; i < l; i++) {
        const poi = pois[i];
        latlngBounds.extend(poi.latLng);
        this._poiSearchResult(pois, i);
      }
      this.map.fitBounds(latlngBounds);
    }
  };

  initLocation: {
    lon: number;
    lat: number;
  } = { lat: 33.95640812, lon: 118.33975212 };

  constructor({
    onMarked,
    target,
    initLocation,
  }: {
    onMarked: any;
    target: string;
    initLocation: {
      lon: number;
      lat: number;
    } | null;
  }) {
    this.onMarked = onMarked;
    this.container = document.getElementById(target);
    if (initLocation) {
      this.initLocation = initLocation;
    }
    this._initMap();
  }

  setMapCenter({
    addMarker,
    position,
  }: {
    addMarker?: boolean;
    position: { lon: any; lat: any };
  }) {
    const center = new qq.maps.LatLng(position.lat, position.lon);
    this.map.panTo(center);
    this.map.setCenter(center);
    if (addMarker) {
      this._configMarker('', center, '');
    } else if (this.selectedMark) {
      this.selectedMark = {
        ...position,
        address: this.selectedMark.address,
        detailAddress: this.selectedMark.detailAddress,
      };
      this.selectedMapMaker.setPosition(center);
    }
  }

  searchPoisWithAddress({ city, address }: { city?: any; address: string }) {
    const location = city && city.length > 0 ? city : '北京';
    this._clearMarks(this.markers);
    this.searchService.setLocation(location);
    this.searchService.setPageIndex(0);
    this.searchService.setPageCapacity(20);
    this.searchService.search(address);
  }

  deallocMap() {
    this.map = null;
    this.selectedMark = null;
  }

  _clearMarks(marks: any) {
    let overlay;
    while ((overlay = this.markers.pop())) {
      overlay.setMap(null);
    }
  }

  _setSelectedMarker(marker: any) {
    this.selectedMark = marker;
    this.markers.forEach((item) => {
      if (marker === item) {
        marker.setIcon(icon);
      } else {
        item.setIcon('');
      }
    });
  }

  _poiSearchResult(pois: any, index: number) {
    this._configMarker(
      pois[index].address,
      pois[index].latLng,
      pois[index].name,
    );
  }

  _configMarker(address: any, position: any, title: any) {
    this._clearMarks(this.markers);
    const marker = new qq.maps.Marker({
      map: this.map,
      draggable: true,
      position: new qq.maps.LatLng(position.lat, position.lng),
      title: title,
      icon: new qq.maps.MarkerImage(icon),
    });
    this.markers.push(marker);
    this.selectedMapMaker = marker;
    this.selectedMark = {
      lat: position.lat,
      lon: position.lng,
      address: '',
      detailAddress: {},
    };
    qq.maps.event.addListener(marker, 'dragend', (e: any) => {
      this.selectedMark = {
        lat: e.latLng.lat,
        lon: e.latLng.lng,
        address: '',
        detailAddress: {},
      };
    });
    fetchGeocoder(`${position.lat},${position.lng}`).then((res) => {
      this.selectedMark = this.selectedMark ? this.selectedMark : {};
      this.selectedMark.address = res.address || '';
      this.selectedMark.detailAddress = res.detailAddress || {};
      this.onMarked ? this.onMarked(this.selectedMark) : null;
    });
  }

  _initMap() {
    if (!qq) {
      return;
    }
    const center = new qq.maps.LatLng(
      this.initLocation.lat,
      this.initLocation.lon,
    );
    const myOptions = {
      center: center,
      zoom: 21,
      draggableCursor: 'crosshair',
    };
    const qqMap = new qq.maps.Map(this.container, myOptions);
    qq.maps.event;
    this.map = qqMap;
    qq.maps.event.addListener(qqMap, 'click', (e: any) => {
      this._clearMarks(this.markers);
      this._configMarker('', e.latLng, '');
    });
    this.infoWin = new qq.maps.InfoWindow({
      map: this.map,
    });
    this.searchService = new qq.maps.SearchService({
      complete: this.poiSearchCallBack,
      // 若服务请求失败，则运行以下函数
      error: (error: any) => {
        // alert("出错了。" + error);
      },
    });
  }
}

export const transformLocationWgs84togcj02 = (location: {
  lon: any;
  lat: any;
}): { lon: number; lat: number } => {
  const formatedLoaction = coordtransform().wgs84togcj02(
    parseFloat(location.lon),
    parseFloat(location.lat),
  );
  return { lon: formatedLoaction[0], lat: formatedLoaction[1] };
};

export const transformLocationGcj02towgs84 = (location: {
  lon: any;
  lat: any;
}): { lon: number; lat: number } => {
  const formatedLoaction = coordtransform().gcj02towgs84(
    parseFloat(location.lon),
    parseFloat(location.lat),
  );
  return { lon: formatedLoaction[0], lat: formatedLoaction[1] };
};
