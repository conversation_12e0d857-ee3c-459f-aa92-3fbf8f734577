import { TableColumnsType } from 'antd';
export interface DataType {
  indicatorNumber: string;
  indicatorName: string;
  priority: number;
  issuePool: string;
  issuePoolName: string;
  modifyUser: string;
  modifyTime: string;
}

export const issuePoolColumns: TableColumnsType<DataType> = [
  {
    title: '事件ID',
    dataIndex: 'indicatorNumber',
    width: 150,
    align: 'center',
  },
  {
    title: '事件名称',
    dataIndex: 'indicatorName',
    width: 150,
    align: 'center',
  },
  {
    title: '指标赋值',
    dataIndex: 'priority',
    width: 150,
    align: 'center',
  },
  {
    title: '归属工单池',
    dataIndex: 'issuePoolName',
    width: 150,
    align: 'center',
  },
  {
    title: '操作人',
    dataIndex: 'modifyUser',
    width: 150,
    align: 'center',
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    width: 150,
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operations',
    width: 100,
    align: 'center',
  },
];
