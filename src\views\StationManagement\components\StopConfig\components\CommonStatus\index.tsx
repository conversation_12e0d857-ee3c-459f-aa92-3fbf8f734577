/* eslint-disable no-unused-vars */

import { Form, FormInstance, Radio } from 'antd';
import React, { useEffect, useState } from 'react';


const StatusItem = (
  {
    disable,
    name,
    label,
    form,
    items,
    initValue,
    defaultValue,
    onValueChanged
  }: {
    disable?: boolean,
    name: string,
    label: string,
    form: FormInstance,
    items: any[] | null,
    initValue: any,
    defaultValue?: any,
    onValueChanged?: Function
  }
) => {
  const [value, setValue] = useState(defaultValue);
  useEffect(() => {
    if (initValue || initValue === 0) {
      setValue(initValue)
      form.setFieldsValue({
        [name]: initValue
      })
    } else if (defaultValue || defaultValue === 0) {
      setValue(defaultValue)
      form.setFieldsValue({
        [name]: defaultValue
      })
    }
  }, [defaultValue, initValue]);

  const useStyle: React.CSSProperties = {
    color: "#31C2A6", marginLeft: 20
  }
  const unuseStyle: React.CSSProperties = {
    color: "red"
  }

  const makeRadioOptions = () => {
    return items?.map((item: any) => {
      return {
        label: item.name,
        value: item.code,
        style: item.code == 1 ? useStyle : unuseStyle
      }
    })
  }
  return (
    <Form.Item
      name={name}
      label={label}
      rules={[{ required: true, message: `请选择${label}` }]}
    >
      <Radio.Group
        disabled={disable}
        onChange={(e: any) => {
          setValue(e.target.value)
          form.setFieldsValue({
            [name]: e.target.value
          })
          onValueChanged && onValueChanged(e.target.value)
        }}
        value={value}
        options={makeRadioOptions()}
      />
    </Form.Item>
  );
}

export default React.memo(StatusItem)
