/* eslint-disable no-undef */

import { LoginApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';

const fetchApi = new LoginApi();
export const passportLogout = () => {
  $.ajax({
    url: 'https://ssa.jdl.cn/exit',
    dataType: 'jsonp',
    success: (resp) => {
      const response = JSON.parse(resp);
      if (response.result === 'success') {
        fetchApi.logout().then((res) => {
          if (res.code === HttpStatusCode.Success) {
            window.location.replace('/login');
            // localStorage.setItem('loginState', LoginState.LOGOUT);
          }
        });
      }
    },
  });
};

// export const passportLogin = () => {
//   $.getJSON('https://sso.jd.com/setCookie?t=sso.jdl.cn&callback=?',function(res){
//     console.log(res);
//   });
// };