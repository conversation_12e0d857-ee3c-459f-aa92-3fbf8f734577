import React, { ReactNode } from 'react';
import './index.scss';

const EditModule = (props: {
  title: string;
  children?: ReactNode;
  subTitle?: string;
}) => {
  return (
    <div className="edit-module-container">
      <div className="module-title-container">
        <img
          className="icon"
          src={require('@/assets/image/common/editModuleIcon.png')}
        />
        <span className="title">{props.title}</span>
        {props.subTitle && <span className="subTitle">{props.subTitle}</span>}
      </div>
      <div className="content">{props.children}</div>
    </div>
  );
};

export default React.memo(EditModule);
