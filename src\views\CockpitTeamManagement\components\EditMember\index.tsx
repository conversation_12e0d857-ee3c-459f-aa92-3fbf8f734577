import React, { useEffect, useState } from 'react';
import { Row, Col, Select, Button, Table, Popconfirm, message } from 'antd';
import { AnyFunc } from '@/global';
import { MemberColumns } from './columns';
import FetchApi from '../../utils/fetchApi';
import { HttpStatusCode } from '@/fetch/core/constant';
import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons';
import { isEmpty } from 'lodash';
interface Props {
  selectableUsers: any[];
  bindMembers: any[];
  tableData: any[];
  setBindMembers: AnyFunc;
}
const fetchApi = new FetchApi();
const EditMember = (props: Props) => {
  const { selectableUsers, bindMembers, tableData, setBindMembers } = props;
  const [selectUser, setSelectUser] = useState<string>();
  const [currentPhone, setCurrentPhone] = useState<string | null>(null);

  // render user info in dropdown
  const renderUserInfo = (option: any) => {
    let res = '';
    selectableUsers.forEach((user) => {
      if (user.userName === option.value) {
        res = `${user.userName ?? '-'}/${user.realName ?? '-'}/${
          user.phone ?? '-'
        }/${user.jdErp ?? '-'}/${user.roleInfos ?? '-'}`;
      }
    });
    return res;
  };
  // add select user
  const addUser = async () => {
    if (selectUser) {
      const res = await fetchApi.fetchUserteam([selectUser]);
      if (res.code === HttpStatusCode.Success) {
        if (!isEmpty(res.data)) {
          message.error(
            `该${res.data.join(';')}成员归属其他远驾团队，不允许提交！`,
          );
          return;
        } else {
          if (bindMembers.find((member: any) => member === selectUser)) {
            message.error('该用户已添加！');
            return;
          } else {
            setBindMembers([...bindMembers, selectUser]);
          }
        }
      }
    }
  };

  const removeUser = (userName: string) => {
    setBindMembers(
      bindMembers.filter((user: any) => {
        return user !== userName;
      }),
    );
  };

  const getPhone = async (record: any) => {
    const res: any = await fetchApi.getUserPhone(record.userName);
    if (res.code === HttpStatusCode.Success) {
      setCurrentPhone(res.data.phone);
    }
  };
  const formatColumns = (columns: any) => {
    return columns.map((col: any) => {
      if (col.dataIndex === 'phone') {
        col.render = (params: any, record: any) => {
          return (
            <div
              style={{
                display: 'flex',
                width: '100%',
                justifyContent: 'center',
              }}
            >
              <div
                style={{ color: '#1677ff' }}
                onClick={() => {
                  if (params.item.secret) {
                    getPhone(params.item);
                  }
                  params.updateSecret(
                    params.item.userName,
                    !params.item.secret,
                  );
                  setCurrentPhone(null);
                }}
              >
                {params.item.secret ? (
                  <EyeInvisibleOutlined />
                ) : (
                  <EyeOutlined />
                )}
              </div>
              {!params.item.secret && currentPhone
                ? currentPhone
                : params.item.phone}
            </div>
          );
        };
      } else if (col.dataIndex === 'operations') {
        col.render = (text: string, record: any) => {
          return (
            <div className="operate">
              <Popconfirm
                placement="left"
                title="确认删除？"
                onConfirm={() => {
                  removeUser(record.userName);
                }}
                okText={'确定'}
                cancelText={'取消'}
              >
                <a className="operate-btn">删除</a>
              </Popconfirm>
            </div>
          );
        };
      } else if (col.dataIndex === 'rowIndex') {
        col.render = (text: string, record: any, index: number) => {
          return <>{index + 1}</>;
        };
      } else if (col.dataIndex === 'roleInfos') {
        col.render = (text: any, record: any) => {
          return record.roleInfos ? <>{record.roleInfos}</> : <>--</>;
        };
      }
      return col;
    });
  };
  return (
    <>
      <Row justify={'center'}>
        <Col span={19}>
          <Select
            style={{ width: '100%' }}
            options={
              selectableUsers &&
              selectableUsers.map((user) => {
                return {
                  label: user.realName,
                  value: user.userName,
                };
              })
            }
            onChange={(value) => {
              setSelectUser(value);
            }}
            filterOption={(input: any, option: any) => {
              const label: any = option?.label || '';
              return (
                label.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
              );
            }}
            showSearch
            allowClear
            optionRender={(option) => {
              return <>{renderUserInfo(option)}</>;
            }}
          ></Select>
        </Col>
        <Col span={1}>
          <Button type="primary" onClick={addUser} disabled={!selectUser}>
            添加
          </Button>
        </Col>
      </Row>
      <Row justify={'center'}>
        <Col span={20}>
          <Table
            columns={formatColumns(MemberColumns)}
            dataSource={tableData}
            loading={false}
            rowKey="jdErp"
            pagination={false}
            scroll={{
              y: 500,
            }}
          />
        </Col>
      </Row>
    </>
  );
};
export default React.memo(EditMember);
