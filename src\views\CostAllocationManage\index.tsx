import { Table, InputNumber, Button, message, Input } from 'antd';
import React, { useState, useEffect } from 'react';
import {
  CostAllocationColumns,
  CostAllocationFormConfig,
} from './utils/columns';
import CostManage, {
  CostAllocationItem,
  CostAllocationEditItem,
} from '@/fetch/business/costManage';
import {
  CommonForm,
  CommonTable,
  FormConfig,
  useTableData,
} from '@jd/x-coreui';
import { CommonApi } from '@/fetch/business/commonFetch';
import { HttpStatusCode } from '@/fetch/core/constant';
import { isEqual } from 'lodash';
import './index.scss';
interface EditRecord {
  actualRevenue?: number | null;
  remark?: string | null;
}

const CostAllocationManage = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [tableKey, setTableKey] = useState<string>();
  const [editedRowsMap, setEditedRowsMap] = useState<Map<number, EditRecord>>(
    new Map(),
  );
  const [editableData, setEditableData] = useState<CostAllocationItem[]>();

  const initSearchCondition = {
    searchForm: {
      provinceId: null,
      cityId: null,
      stationId: null,
      stationInfo: null,
      startTime: null,
      endTime: null,
      timeRange: null,
    },
    pageNum: 1,
    pageSize: 10,
  };

  const [searchFormConfig, setSearchFormConfig] = useState<FormConfig>(
    CostAllocationFormConfig,
  );
  const [searchCondition, setSearchCondition] = useState(initSearchCondition);

  const { tableData, loading, reloadTable } = useTableData<any, any>(
    {
      ...searchCondition.searchForm,
      pageNum: searchCondition.pageNum,
      pageSize: searchCondition.pageSize,
    },
    CostManage.getCostAllocationPageList,
  );
  useEffect(() => {
    if (isEditing) {
      CostManage.getCostAllocationPageList({
        ...searchCondition.searchForm,
        pageNum: searchCondition.pageNum,
        pageSize: searchCondition.pageSize,
      }).then((res) => {
        if (res.code === HttpStatusCode.Success) {
          setEditableData(res.data.list);
        }
      });
    }
  }, [isEditing, searchCondition]);
  useEffect(() => {
    new CommonApi()
      .getStationDepartment({
        stationProductType: 'vehicle',
        cityIdList: [],
        stationUseCaseList: [],
        stationType: '',
        enable: '',
      })
      .then((res) => {
        if (res.code === HttpStatusCode.Success) {
          setSearchFormConfig({
            ...searchFormConfig,
            fields: searchFormConfig.fields.map((v) => {
              return v.fieldName === 'stationInfo'
                ? { ...v, options: res.data }
                : v;
            }),
          });
        }
      });
  }, []);
  // 处理单元格编辑
  const handleCellEdit = ({
    costAllocationId,
    actualRevenue,
    remark,
  }: {
    costAllocationId: number;
    actualRevenue: number | null | undefined;
    remark: string | null | undefined;
  }) => {
    setEditedRowsMap((prev) => {
      const newMap = new Map(prev);

      newMap.set(costAllocationId, {
        actualRevenue: actualRevenue,
        remark: remark,
      });

      return newMap;
    });

    // 更新可编辑表格的显示数据
    setEditableData((prev) =>
      prev?.map((item) =>
        item.costAllocationId === costAllocationId
          ? {
              ...item,
              actualRevenue: actualRevenue,
              remark: remark,
            }
          : item,
      ),
    );
  };

  // 处理编辑模式切换
  const handleEditClick = () => {
    setEditableData(tableData?.list || []);
    setIsEditing(true);
  };

  // 处理保存
  const handleSave = async () => {
    try {
      // 构造提交数据
      const editedData = Array.from(editedRowsMap.entries()).map(
        ([costAllocationId, record]) => {
          const submitData: CostAllocationEditItem = {
            costAllocationId,
            actualRevenue: record.actualRevenue,
            remark: record.remark,
          };
          return submitData;
        },
      );

      if (editedData.length === 0) {
        setIsEditing(false);
        return;
      }

      await CostManage.editCostAllocation({ costAllocationList: editedData });
      message.success('保存成功');
      setIsEditing(false);
      setEditedRowsMap(new Map());
      setSearchCondition({
        ...searchCondition,
        pageNum: 1,
      });
      reloadTable();
    } catch (error) {
      message.error('保存失败');
    }
  };

  // 处理取消
  const handleCancel = () => {
    setIsEditing(false);
    setEditedRowsMap(new Map());
    setEditableData([]);
    setSearchCondition(initSearchCondition);
  };

  const middleBtns = [
    {
      label: '编辑',
      onClick: handleEditClick,
      title: '编辑',
    },
  ];

  const formatColumns = () => {
    return CostAllocationColumns.map((item) => {
      switch (item.dataIndex) {
        case 'actualRevenue':
          return {
            ...item,
            render: (text: number, record: CostAllocationItem) => {
              const editedValue = editedRowsMap.get(
                record.costAllocationId,
              )?.actualRevenue;
              return (
                <InputNumber
                  value={editedValue !== undefined ? editedValue : text}
                  precision={2}
                  onChange={(value) => {
                    handleCellEdit({
                      costAllocationId: record.costAllocationId,
                      actualRevenue: value,
                      remark: record.remark,
                    });
                  }}
                />
              );
            },
          };
        case 'remark':
          return {
            ...item,
            render: (text: string, record: CostAllocationItem) => {
              const editedValue = editedRowsMap.get(
                record.costAllocationId,
              )?.remark;
              return (
                <Input.TextArea
                  value={editedValue !== undefined ? editedValue : text}
                  maxLength={100}
                  onChange={(e) =>
                    handleCellEdit({
                      costAllocationId: record.costAllocationId,
                      actualRevenue: record.actualRevenue,
                      remark: e.target.value,
                    })
                  }
                />
              );
            },
          };
        default:
          return {
            ...item,
          };
      }
    });
  };
  const onSearchClick = (values) => {
    const data = {
      searchForm: {
        ...values,
        startTime: values.timeRange
          ? values.timeRange[0].format('YYYY-MM')
          : null,
        endTime: values.timeRange
          ? values.timeRange[1].format('YYYY-MM')
          : null,
        provinceId:
          values.stationInfo && values.stationInfo.length > 0
            ? values.stationInfo[0]
            : null,
        cityId:
          values.stationInfo && values.stationInfo.length >= 2
            ? values.stationInfo[1]
            : null,
        stationId:
          values.stationInfo && values.stationInfo.length === 3
            ? values.stationInfo[2]
            : null,
      },
      pageSize: 10,
      pageNum: 1,
    };
    delete data.searchForm.stationInfo;
    delete data.searchForm.timeRange;
    if (!isEditing) {
      if (isEqual(data, searchCondition)) {
        setTableKey(new Date().getMilliseconds().toString());
      } else {
        setSearchCondition(data);
      }
    }
  };
  return (
    <div>
      <CommonForm
        formConfig={searchFormConfig}
        layout={'inline'}
        defaultValue={searchCondition.searchForm}
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={() => {
          if (!isEditing) {
            setSearchCondition(initSearchCondition);
          }
        }}
      />
      {!isEditing ? (
        <>
          <CommonTable
            columns={CostAllocationColumns}
            tableListData={{
              list: tableData?.list || [],
              totalPage: tableData?.pages,
              totalNumber: tableData?.total,
            }}
            tableKey={tableKey}
            loading={loading}
            rowKey="costAllocationId"
            middleBtns={middleBtns}
            onPageChange={(paginationData: any) => {
              setSearchCondition({
                ...searchCondition,
                pageNum: paginationData.pageNum,
                pageSize: paginationData.pageSize,
              });
            }}
            pageSizeOptions={['10', '20', '50', '500']}
          />
          <div className="footer-info">
            <div className="footer-info-item">
              <span className="footer-info-item-value">
                预计总收入：{tableData?.expectedTotalRevenue || 0}元
              </span>
            </div>
            <div className="footer-info-item">
              <span className="footer-info-item-value">
                实际总收入：{tableData?.actualTotalRevenue || 0}元
              </span>
            </div>
          </div>
        </>
      ) : (
        <div className="common-table">
          <div className="middle-btn">
            <Button type="primary" onClick={handleSave}>
              保存
            </Button>
            <Button onClick={handleCancel} style={{ marginLeft: 8 }}>
              取消
            </Button>
          </div>
          <Table
            scroll={{ x: '800px', y: 'calc(80vh - 200px)' }}
            columns={formatColumns()}
            dataSource={editableData}
            rowKey="costAllocationId"
            pagination={{
              current: searchCondition.pageNum,
              pageSize: searchCondition.pageSize,
              total: tableData?.total || 0,
              onChange: (page, pageSize) => {
                setSearchCondition((prev) => ({
                  ...prev,
                  pageNum: page,
                  pageSize: pageSize,
                }));
              },
              pageSizeOptions: ['10', '20', '50', '500'],
            }}
          />
        </div>
      )}
    </div>
  );
};

export default CostAllocationManage;
