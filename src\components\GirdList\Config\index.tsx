/* eslint-disable react/display-name */
/* eslint-disable no-unused-vars */
import { Row, Col, Card } from 'antd';
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import './index.scss';
import ConfigForm from './ConfigForm';
import { commonDataSelector } from '@/redux/reducer/commonData';
const GridConfig = ({
  // gridCofigArr,
  disabled,
}: {
  // gridCofigArr:any[],
  disabled?: boolean;
}) => {
  const gridCofigArr = useSelector(commonDataSelector).gridSizeList;
  return (
    <Row>
      <Col span={20} offset={3}>
        <Card style={{ width: '100% ', marginBottom: '20px' }}>
          <div style={{ marginBottom: '20px' }}>
            <h3>
              <span className="star">*</span>货箱格口规格配置(单位:mm)
            </h3>
          </div>
          <div style={{ paddingLeft: '150px' }}>
            {gridCofigArr &&
              gridCofigArr.map((item: any, index: number) => {
                return (
                  <ConfigForm
                    key={'config_form_' + index}
                    formIndex={index}
                    formCon={item}
                    disabled={disabled}
                  ></ConfigForm>
                );
              })}
          </div>
        </Card>
      </Col>
    </Row>
  );
};

export default React.memo(GridConfig);
