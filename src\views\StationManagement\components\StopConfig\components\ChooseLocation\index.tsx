/* eslint-disable require-jsdoc */
/* eslint-disable no-unused-vars */
import { Modal, Select } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import LineString from 'ol/geom/LineString';
import Feature from 'ol/Feature.js';
import TileLayer from 'ol/layer/Tile.js';
import Point from 'ol/geom/Point.js';
import { Circle as CircleStyle, Fill, Stroke, Style } from 'ol/style.js';
import Polygon from 'ol/geom/Polygon.js';
import { XYZ, TileWMS } from 'ol/source.js';
import VectorLayer from 'ol/layer/Vector.js';
import VectorSource from 'ol/source/Vector.js';
import CustomButton, { ButtonType } from '@/components/CustomButton';
import { HttpStatusCode } from '@/fetch/core/constant';
import { StationFetchApi } from '@/fetch/business';
import './index.scss';
import { LocationType } from '@/views/StationManagement/utils/dataSearch';
import { calculateCoordinatePoint } from '@/utils/MapEditorTool/util';
import MapTools from '@/utils/MapEditorTool';
import { returnTileLayers } from '@/utils/constant';
import DebounceAsyncSelect from '../../../DebounceSelect';
const win = window as any;
const { proj4, deg } = win;
export interface Location {
  lon: number;
  lat: number;
  head: number;
}

export enum LayerIdEnum {
  TIANMAP_LAYER = 'TIANMAP_LAYER',
  TDTCVA_LAYER = 'TDTCVA_LAYER',
  WSM_LAYER = 'WSM_LAYER',
  ANCHOR_LAYER = 'ANCHOR_LAYER',
  SEARCHPOINT_LAYER = 'SEARCHPOINT_LAYER',
  INTER_LAYER = 'INTER_LAYER',
  BOUNDARY_LAYER = 'BOUNDARY_LAYER',
  STOPLINE_LAYER = 'STOPLINE_LAYER',
  GATE_LAYER = 'GATE_LAYER',
}

const utm2wgs84 = (Lon: any, Lat: any, zoneNum: any) => {
  const utm = '+proj=utm +zone=' + zoneNum;
  const wgs84 = '+proj=longlat +ellps=WGS84 +datum=WGS84 +no_defs';
  return proj4(utm, wgs84, [Lon, Lat]);
};

const wgs842utm = (Lon: any, Lat: any, zoneNum: any) => {
  const utm = '+proj=utm +zone=' + zoneNum;
  const wgs84 = '+proj=longlat +ellps=WGS84 +datum=WGS84 +no_defs';
  return proj4(wgs84, utm, [Lon, Lat]);
};

const fetchApi = new StationFetchApi();
const ChooseLocationMap = (props: any) => {
  const mapRef = useRef<any>(null);
  const zoneRef = useRef<any>(null);
  const [, forceUpdate] = useState(0);
  const _lonLatHead = useRef<any>({
    lon: props?.initPosition?.lon || 0,
    lat: props?.initPosition?.lat || 0,
    head: deg(props?.initPosition?.head || 0),
  });
  const vehicleConf = useRef<any>(null);
  const [mapInfo, setMapInfo] = useState<any>(null);
  // 选中地图回调函数
  const confirmSearch = async (params: any) => {
    const pos = params.value?.split('-')?.map((i: string) => parseFloat(i));
    getMapInfo(pos);
  };

  const initMap = () => {
    const { initPosition } = props;
    const { lat, lon } = initPosition || {};
    mapRef.current = new MapTools({
      container: document.getElementById('wmsmap-container')!,
      centerPoint: [lon ?? 0, lat || 0],
    });

    // 天地图矢量底图
    const tianMapLayer = new TileLayer({
      source: new XYZ({
        url: 'https://t{0-7}.tianditu.gov.cn/DataServer?x={x}&y={y}&l={z}&T=vec_c&tk=dd515bb481c4d4c7f5b9056229e36e68',
        crossOrigin: 'anonymous',
        projection: 'EPSG:4326',
        minZoom: 10,
        maxZoom: 18,
      }),
    });

    // 天地图注记图
    const tdtcvaLayer = new TileLayer({
      source: new XYZ({
        url: 'https://t{0-7}.tianditu.gov.cn/DataServer?T=cva_w&x={x}&y={y}&l={z}&tk=dd515bb481c4d4c7f5b9056229e36e68',
        crossOrigin: 'anonymous',
        minZoom: 10,
        maxZoom: 18,
      }),
    });

    mapRef.current.addLayers([
      {
        layer: tianMapLayer,
        layerId: LayerIdEnum.TIANMAP_LAYER,
      },
      {
        layer: tdtcvaLayer,
        layerId: LayerIdEnum.TDTCVA_LAYER,
      },
    ]);
  };

  const initMapEvents = () => {
    //移动点调整事件开关
    mapRef.current.map.on('pointerdown', function (event: any) {
      const pixel = mapRef.current.map.getEventPixel(event.originalEvent);
      const feature = mapRef.current.map.getFeaturesAtPixel(pixel, {
        hitTolerance: 3,
      });
      if (feature) {
        // 移动前如果点中图形要素了，需要判断是原点还是调整点；移动后有可能点中图形要素但不是原点或调整点的，此时判断状态值
        if (
          feature[0]?.values_?.name == 'anchor' ||
          mapRef.current.isAnchorMoved == 1
        ) {
          if (mapRef.current.isAnchorMoved == 1) {
            mapRef.current.isAnchorMoved = 0;
          } else {
            mapRef.current.isAnchorMoved = 1;
          }
        } else if (
          feature[0]?.values_?.name == 'endPnt' ||
          mapRef.current.isEndPntMoved == 1
        ) {
          if (mapRef.current.isEndPntMoved == 1) {
            mapRef.current.isEndPntMoved = 0;
          } else {
            mapRef.current.isEndPntMoved = 1;
          }
        }
        // 移动后可能没点中图形要素，这时用状态值判断
      } else if (mapRef.current.isAnchorMoved == 1) {
        if (mapRef.current.isAnchorMoved == 1) {
          mapRef.current.isAnchorMoved = 0;
        } else {
          mapRef.current.isAnchorMoved = 1;
        }
      } else if (mapRef.current.isEndPntMoved == 1) {
        if (mapRef.current.isEndPntMoved == 1) {
          mapRef.current.isEndPntMoved = 0;
        } else {
          mapRef.current.isEndPntMoved = 1;
        }
      }

      // 设置移动时的鼠标样式
      if (feature === null) {
        mapRef.current.map.getTargetElement().style.cursor = 'auto';
      } else if (
        feature[0]?.values_?.name === 'anchor' ||
        feature[0]?.values_?.name === 'endPnt'
      ) {
        mapRef.current.map.getTargetElement().style.cursor = 'move';
      }
    });
    //移动点调整事件
    mapRef.current.map.on('pointermove', function (event: any) {
      // 鼠标的坐标
      const pos = event.coordinate;
      if (mapRef.current.isAnchorMoved == 1) {
        _markerUpdate({
          lon: pos[0],
          lat: pos[1],
          head: _lonLatHead.current.head,
        });
        _lonLatHead.current = {
          ..._lonLatHead.current,
          lon: parseFloat(Number(pos[0]).toFixed(8)),
          lat: parseFloat(Number(pos[1]).toFixed(8)),
        };
        forceUpdate(Date.now());
      } else if (mapRef.current.isEndPntMoved == 1) {
        //调整点移动
        //修改坐标
        drawDragDirectionLine(pos[0], pos[1]);
        const pnts = mapRef.current.directionLineItem
          .getGeometry()
          .getCoordinates();
        zoneRef.current = pnts[0][0] / 6.0 + 31;
        const startPoint = wgs842utm(pnts[0][0], pnts[0][1], zoneRef.current);
        const endPnt = wgs842utm(pnts[1][0], pnts[1][1], zoneRef.current);
        let angles = Math.atan2(
          endPnt[1] - startPoint[1],
          endPnt[0] - startPoint[0],
        );
        angles = (angles * 180) / Math.PI; //转换为角度
        _lonLatHead.current.head = parseFloat(Number(angles).toFixed(8));
        drawDirectionRectangle(
          startPoint[0],
          startPoint[1],
          angles,
          {
            width: vehicleConf.current?.width,
            frontEdgeToCenter: vehicleConf.current?.frontEdgeToCenter,
            backEdgeToCenter: vehicleConf.current?.backEdgeToCenter,
          },
          'inner',
        );

        drawDirectionRectangle(
          startPoint[0],
          startPoint[1],
          angles,
          {
            width: Number(vehicleConf.current?.width) + 1,
            frontEdgeToCenter:
              Number(vehicleConf.current?.frontEdgeToCenter) + 0.5,
            backEdgeToCenter:
              Number(vehicleConf.current?.backEdgeToCenter) + 0.5,
          },
          'outline',
        );
        forceUpdate(Date.now());
      } else {
        mapRef.current.map.getTargetElement().style.cursor = 'auto';
      }
    });
  };

  const getVehicleType = async () => {
    const res = await fetchApi.getSizeOfVehicle();
    if (res && res.code === HttpStatusCode.Success) {
      const { width, length, frontEdgeToCenter, backEdgeToCenter } =
        res?.data || {};
      vehicleConf.current = {
        length,
        width,
        frontEdgeToCenter,
        backEdgeToCenter,
      };
      getMapInfo([
        props?.initPosition?.lon || 116.55762943048826,
        props?.initPosition?.lat || 39.78524594729655,
      ]);
    }
  };

  const drawPoint = (
    coordinates: any,
    imgSrc: any,
    type: string,
    anchor: any,
  ) => {
    const piontFeture = mapRef.current.createPointFeature(
      coordinates,
      imgSrc,
      type,
      null,
      anchor,
    );
    if (type === 'anchor') {
      if (mapRef.current.startPoint != null) {
        mapRef.current.startPoint.setGeometry(new Point(coordinates));
        return;
      } else {
        const source = new VectorSource();
        source.addFeature(piontFeture);
        const _layer = new VectorLayer({
          source: source,
          zIndex: 99999,
        });
        mapRef.current.startPoint = piontFeture;
        mapRef.current.map.addLayer(_layer);
      }
    } else if (type === 'endPnt') {
      if (mapRef.current.endPoint != null) {
        mapRef.current.endPoint.setGeometry(new Point(coordinates));
        return;
      } else {
        const source = new VectorSource();
        source.addFeature(piontFeture);
        const _layer = new VectorLayer({
          source: source,
          zIndex: 99999,
        });
        mapRef.current.endPoint = piontFeture;
        mapRef.current.map.addLayer(_layer);
      }
    }
  };

  const createLineFeature = (
    coordinates: any,
    type: 'dash' | 'solid',
    name: string,
  ) => {
    const featureLine = new Feature({
      geometry: new LineString(coordinates),
      zIndex: 999,
      name,
      id: `${name}_${Date.now()}`,
    });
    const style = new Style({
      stroke: new Stroke({
        // 线样式
        lineDash: type === 'dash' ? [4, 4, 4, 4] : [1, 1, 1, 1, 1, 1],
        color: 'red',
        width: 4,
      }),
    });
    featureLine.setStyle(style);
    return featureLine;
  };

  const createPolygonFeature = (
    coordinates: any[],
    type: 'inner' | 'outline',
  ) => {
    const polygonFeature = new Feature({
      geometry: new Polygon(coordinates),
      zIndex: 9999,
      name: 'rectangleLayer',
    });
    polygonFeature.setId(`popOverlay${Date.now()}`);
    const style = new Style({
      fill: new Fill({
        // 填充样式
        color: 'rgba(60, 110, 240, 0.2)',
      }),
      stroke:
        type === 'inner'
          ? new Stroke({
              // 线样式
              lineDash: [1, 1, 1, 1, 1, 1],
              color: '#0D85FF',
              width: 2,
            })
          : new Stroke({
              // 线样式
              lineDash: [4, 4, 4, 4],
              color: '#0D85FF',
              width: 2,
            }),
    });
    polygonFeature.setStyle(style);
    return polygonFeature;
  };

  const drawBaseLine = function (x: number, y: number) {
    const baseLinePnts = new Array();
    const baseLineFromPnt = new Array();
    baseLineFromPnt[0] = x;
    baseLineFromPnt[1] = y;
    baseLinePnts.push(baseLineFromPnt);
    const baseLineToPnt = new Array();
    baseLineToPnt[0] = x + 3;
    baseLineToPnt[1] = y;
    baseLinePnts.push(baseLineToPnt);
    const transData = baseLinePnts.map((i) =>
      utm2wgs84(i[0], i[1], zoneRef.current),
    );
    const lineFeature = createLineFeature(transData, 'solid', 'baseline');
    if (mapRef.current.baseLineItem != null) {
      mapRef.current.baseLineItem.setGeometry(new LineString(transData));
      return;
    } else {
      const source = new VectorSource();
      source.addFeature(lineFeature);
      const _layer = new VectorLayer({
        source,
        zIndex: 99999,
      });
      mapRef.current.map.addLayer(_layer);
      mapRef.current.baseLineItem = lineFeature;
      mapRef.current.baseLineLayer = _layer;
    }
  };

  const drawDirectionLine = (x: number, y: number, angles: number) => {
    const baseLinePnts = new Array();
    const baseLineFromPnt = new Array();
    baseLineFromPnt[0] = x;
    baseLineFromPnt[1] = y;
    baseLinePnts.push(baseLineFromPnt);
    const lonlat = {
      x: y,
      y: x,
    };
    const restPnt = calculateCoordinatePoint(lonlat, angles, 3);
    const baseLineToPnt = new Array();
    baseLineToPnt[0] = restPnt.y;
    baseLineToPnt[1] = restPnt.x;
    baseLinePnts.push(baseLineToPnt);
    const transData = baseLinePnts.map((i) =>
      utm2wgs84(i[0], i[1], zoneRef.current),
    );
    drawPoint(transData[1], require('./u209e.png'), 'endPnt', [0.5, 0.5]);
    const lineFeture = createLineFeature(transData, 'dash', 'directionLine');
    if (mapRef.current.directionLineItem != null) {
      mapRef.current.directionLineItem.setGeometry(new LineString(transData));
      return;
    } else {
      const source = new VectorSource();
      source.addFeature(lineFeture);
      const _layer = new VectorLayer({
        source,
        zIndex: 99999,
      });
      mapRef.current.map.addLayer(_layer);
      mapRef.current.directionLineItem = lineFeture;
      mapRef.current.directionLineLayer = _layer;
    }
  };

  const drawDragDirectionLine = (x: number, y: number) => {
    const eventlnglat = [x ?? 0, y ?? 0];
    zoneRef.current = eventlnglat[0] / 6.0 + 31;
    const pos = wgs842utm(eventlnglat[0], eventlnglat[1], zoneRef.current);
    if (mapRef.current.directionLineItem == null) return;
    const pnts = mapRef.current.directionLineItem
      .getGeometry()
      .getCoordinates();
    if (pnts == null || 2 != pnts.length) {
      return;
    }
    if (mapRef.current.directionLineItem) {
      mapRef.current.directionLineItem.setGeometry(
        new LineString([pnts[0], [x, y]]),
      );
    }
    const startPoint = wgs842utm(pnts[0][0], pnts[0][1], zoneRef.current);
    drawPoint([x, y], require('./u209e.png'), 'endPnt', [0.5, 0.5]);
    let angles = Math.atan2(pos[1] - startPoint[1], pos[0] - startPoint[0]);
    angles = (angles * 180) / Math.PI; //转换为角度
    _lonLatHead.current = {
      lon: pnts[0][0],
      lat: pnts[0][1],
      head: parseFloat(Number(angles).toFixed(8)),
    };
    forceUpdate(Date.now());
  };

  const _markerUpdate = (position: any) => {
    const eventlnglat = [position?.lon ?? 0, position?.lat ?? 0];
    zoneRef.current = eventlnglat[0] / 6.0 + 31;
    const pos = wgs842utm(eventlnglat[0], eventlnglat[1], zoneRef.current);
    drawDirectionRectangle(
      pos[0],
      pos[1],
      position.head,
      {
        width: vehicleConf.current?.width,
        frontEdgeToCenter: vehicleConf.current?.frontEdgeToCenter,
        backEdgeToCenter: vehicleConf.current?.backEdgeToCenter,
      },
      'inner',
    );
    drawDirectionRectangle(
      pos[0],
      pos[1],
      position.head,
      {
        width: Number(vehicleConf.current?.width) + 1,
        frontEdgeToCenter: Number(vehicleConf.current?.frontEdgeToCenter) + 0.5,
        backEdgeToCenter: Number(vehicleConf.current?.backEdgeToCenter) + 0.5,
      },
      'outline',
    );
    drawBaseLine(pos[0], pos[1]);
    drawDirectionLine(pos[0], pos[1], _lonLatHead.current.head);
    drawPoint(eventlnglat, require('./location.png'), 'anchor', [0.5, 1]);
  };

  const drawDirectionRectangle = function (
    x: number,
    y: number,
    angles: number,
    vehicleTypeData: any,
    type: 'inner' | 'outline',
  ) {
    const longLen = Math.sqrt(
      Math.pow(vehicleTypeData.width / 2, 2) +
        Math.pow(vehicleTypeData.frontEdgeToCenter, 2),
    );
    const shortLen = Math.sqrt(
      Math.pow(vehicleTypeData.width / 2, 2) +
        Math.pow(vehicleTypeData.backEdgeToCenter, 2),
    );
    const longAtan = Math.atan(
      vehicleTypeData.width / 2 / vehicleTypeData.frontEdgeToCenter,
    );
    const shortAtan = Math.atan(
      vehicleTypeData.width / 2 / vehicleTypeData.backEdgeToCenter,
    );
    const longAngle = (longAtan * 180) / Math.PI;
    const shortAngle = (shortAtan * 180) / Math.PI;
    // 从左下角起顺时针旋转，依次为P1,P2,P3,P4
    const angleP1 = 180 + angles + shortAngle;
    const angleP2 = 180 + angles - shortAngle;
    const angleP3 = angles + longAngle;
    const angleP4 = angles - longAngle;
    const lonlat = {
      x: y,
      y: x,
    };
    const P1 = calculateCoordinatePoint(lonlat, angleP1, shortLen);
    const P2 = calculateCoordinatePoint(lonlat, angleP2, shortLen);
    const P3 = calculateCoordinatePoint(lonlat, angleP3, longLen);
    const P4 = calculateCoordinatePoint(lonlat, angleP4, longLen);
    drawRectangle(
      [
        [
          utm2wgs84(P1.y, P1.x, zoneRef.current),
          utm2wgs84(P2.y, P2.x, zoneRef.current),
          utm2wgs84(P3.y, P3.x, zoneRef.current),
          utm2wgs84(P4.y, P4.x, zoneRef.current),
          utm2wgs84(P1.y, P1.x, zoneRef.current),
        ],
      ],
      type,
    );
  };

  const drawRectangle = (coordinates: any[], type: 'inner' | 'outline') => {
    const polygonFeature = createPolygonFeature(coordinates, type);
    if (type === 'inner') {
      if (mapRef.current.innerRactangle != null) {
        mapRef.current.innerRactangle.setGeometry(new Polygon(coordinates));
        return;
      } else {
        mapRef.current.innerRactangle = polygonFeature;
        const source = new VectorSource();
        source.addFeature(polygonFeature);
        const _layer = new VectorLayer({
          source: source,
          zIndex: 99999,
        });
        mapRef.current.map.addLayer(_layer);
      }
    } else if (type === 'outline') {
      if (mapRef.current.outRactangle != null) {
        mapRef.current.outRactangle.setGeometry(new Polygon(coordinates));
        return;
      } else {
        mapRef.current.outRactangle = polygonFeature;
        const source = new VectorSource();
        source.addFeature(polygonFeature);
        const _layer = new VectorLayer({
          source: source,
          zIndex: 99999,
        });
        mapRef.current.map.addLayer(_layer);
      }
    }
  };

  const getMapInfo = async (pos: any[]) => {
    const res = await fetchApi.getMapByPosition({
      longitude: pos[0],
      latitude: pos[1],
    });
    setMapInfo(res?.data);
    mapRef.current.changeMapCenter(pos);
    _markerUpdate({
      lon: Number(pos[0]),
      lat: Number(pos[1]),
      head: _lonLatHead.current.head,
    });
    _lonLatHead.current = {
      ..._lonLatHead.current,
      lon: Number(pos[0]),
      lat: Number(pos[1]),
    };
  };

  useEffect(() => {
    if (mapInfo?.mapId && mapInfo?.mapVersion) {
      for (let item of returnTileLayers()) {
        mapRef.current.clearLayer(item.layerId);
        const layer = new TileLayer({
          source: new TileWMS({
            url: item.url,
            params: {
              ...item.params,
              CQL_FILTER: `map_id=${mapInfo?.mapId} and map_version=${mapInfo?.mapVersion}`,
            },
          }),
        });
        mapRef.current.addLayers([
          {
            layer: layer,
            layerId: item.layerId,
          },
        ]);
      }
    }
  }, [mapInfo?.mapId, mapInfo?.mapVersion]);

  useEffect(() => {
    initMap();
    getVehicleType();
    initMapEvents();
  }, []);

  return (
    <Modal
      bodyStyle={{ padding: 0 }}
      maskClosable={false}
      visible={props.visiable}
      closable={false}
      footer={null}
      forceRender={true}
      width={1000}
    >
      <div className="stopPoint-modal-container">
        <div className="serch-bar">
          <DebounceAsyncSelect
            placeholder={'请搜索停车点位置'}
            onChange={(data: any) => {
              confirmSearch(data);
            }}
            locationType={LocationType.wgs_84}
          />
        </div>
        <div className="nextStepFooter">
          <div className="wms-map-container">
            <div className="map" id="wmsmap-container" />
            <div className="map-bottom">
              <div>{`当前经度：${_lonLatHead.current?.lon ?? 0} E`}</div>
              <div>{`当前纬度：${_lonLatHead.current?.lat ?? 0} N`}</div>
              <div>{`当前朝向：${_lonLatHead.current?.head ?? 0} °`}</div>
            </div>
          </div>
          <div style={{ marginTop: 20 }}>
            <p style={{ color: 'red', marginBottom: 0 }}>说明：</p>
            <p style={{ color: 'red', marginBottom: 0 }}>
              1、系统按配送车最大车型(id:19)的长*宽(2.450m×1.183m)加载停靠效果，(
              <span style={{ color: '#31C2A6' }}>绿实线框</span>
              )车型示意图停靠四周距离均为50厘米(
              <span style={{ color: '#31C2A6' }}>绿虚线框</span>
              )，如2个框整体在黄色车道线内，则满足停靠标准。
            </p>
            <p style={{ color: 'red', marginBottom: 0 }}>
              2、红色虚线原点为车头方向，坐标中心点为车辆后轴中心点。
            </p>
            <p style={{ color: 'red', marginBottom: 0 }}>
              3、点击位置图标，可移动打点位置；点击角度图标，可调整打点角度
            </p>
          </div>
          <div>
            <div className="bottom-btn">
              <CustomButton
                title="确定"
                otherCSSProperties={{ marginRight: 10 }}
                onSubmitClick={() => {
                  props.onSubmit &&
                    props.onSubmit({
                      ..._lonLatHead.current,
                    });
                }}
              />
              <CustomButton
                title="取消"
                buttonType={ButtonType.DefaultButton}
                onSubmitClick={() => {
                  props.onCancle ? props.onCancle() : null;
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ChooseLocationMap;
