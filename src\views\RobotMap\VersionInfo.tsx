import React, { useState, useEffect } from 'react';
import { CommonTable, TableOperateBtn, CommonForm } from '@/components';
import { versionTableColumns } from './utils/columns';
import { useTableData } from '@/components/CommonTable/useTableData';
import { RobotMapApi } from '@/fetch/business';
import { useSelector, useDispatch } from 'react-redux';
import { formatLocation } from '@/utils/utils';
import { useLocation, useNavigate } from 'react-router-dom';
import { HttpStatusCode } from '@/fetch/core/constant';
import { pageSizeOptions } from '@/utils/constant';
import { Table, Breadcrumb, Row, Col } from 'antd';
import './versionInfo.scss';
const fetchApi = new RobotMapApi();

const VersionInfo = () => {
  const navigator = useNavigate();
  const { mapNumber, version } = formatLocation(window.location.search);
  const [mapInfo, setMapInfo] = useState<any>({});
  const [searchCondition, setSearchCondition] = useState<{
    pageNum: number;
    pageSize: number;
    number: string;
  }>({
    number: mapNumber,
    pageNum: 1,
    pageSize: 10,
  });
  const { tableData, loading } = useTableData(
    searchCondition,
    fetchApi.fetchVersionList,
  );

  useEffect(() => {
    fetchMapInfo();
  }, [mapNumber, version]);

  const fetchMapInfo = async () => {
    const res = await fetchApi.fetchMapInfo(mapNumber, version);
    if (res.code === HttpStatusCode.Success) {
      setMapInfo(res.data);
    }
  };
  const BreadcrumbItems = [
    {
      title: (
        <a
          onClick={() => {
            navigator('/app/robotMapManage');
          }}
        >
          地图管理
        </a>
      ),
    },
    {
      title: '版本信息',
    },
  ];

  const versionInfoTitle = [
    { label: '地图ID', value: 'number' },
    { label: '地图名称', value: 'name' },
    { label: '所属产品', value: 'productName' },
    { label: '所属站点', value: 'groupName' },
  ];

  const formateColumns = () => {
    return versionTableColumns.map((col) => {
      switch (col.dataIndex) {
        case 'operation':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return (
                <div className="operate">
                  <TableOperateBtn
                    title="查看地图"
                    handleClick={() => {
                      navigator(
                        `/app/robotMapManage/versionInfo/mapInfo?number=${record.number}&version=${record.version}&mapName=${mapInfo.name}`,
                      );
                    }}
                  />
                </div>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  return (
    <div className="version-info">
      <Breadcrumb items={BreadcrumbItems} />
      <div className="map-info">
        <Row gutter={[0, 16]}>
          {versionInfoTitle.map((item) => {
            return (
              <Col span={6}>
                <div key={item.value}>{`${item.label}：${
                  mapInfo[item.value] ?? '-'
                }`}</div>
              </Col>
            );
          })}
        </Row>
      </div>
      <Table
        columns={formateColumns()}
        dataSource={tableData?.list}
        rowKey={(record: any) => record.version}
        loading={loading}
        pagination={{
          position: ['bottomRight'],
          total: tableData?.total,
          current: searchCondition?.pageNum,
          pageSize: searchCondition?.pageSize,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: pageSizeOptions,
          showTotal: (total) => `共${total}条记录`,
        }}
        onChange={(
          paginationData: any,
          filters: any,
          sorter: any,
          extra: any,
        ) => {
          if (extra.action === 'paginate') {
            const { current, pageSize } = paginationData;
            setSearchCondition({
              ...searchCondition,
              pageNum: current,
              pageSize: pageSize,
            });
          }
        }}
      />
    </div>
  );
};

export default React.memo(VersionInfo);
