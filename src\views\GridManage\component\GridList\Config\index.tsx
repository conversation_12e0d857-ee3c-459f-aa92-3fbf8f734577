/* eslint-disable react/display-name */
/* eslint-disable no-unused-vars */
import { Row, Col, Card, Select } from 'antd';
import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import './index.scss';
import ConfigForm from './ConfigForm';
import {
  boxGridSelector,
  setGridSizeListAction,
} from '@/redux/reducer/boxGrid';
import { useCommonDropDown } from '@/utils/hooks';
import { formatOptions } from '@/utils/utils';
import { dropDownListKey, dropDownKey } from '@/utils/constant';
import { GridManageApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { clone } from 'lodash';
import { ProductType } from '@/utils/enum';

const GridConfig = ({
  // gridCofigArr,
  disabled,
  productionType,
}: {
  // gridCofigArr:any[],
  disabled?: boolean;
  productionType: string;
}) => {
  const fetchApi = new GridManageApi();
  const dispatch = useDispatch();
  const gridCofigArr = useSelector(boxGridSelector).gridSizeList;
  const dropDownData = useCommonDropDown([dropDownKey.ROBOT_GRID_TYPE]);

  return (
    <Row>
      <Col span={20} offset={3}>
        <Card style={{ width: '100% ', marginBottom: '20px' }}>
          <div style={{ marginBottom: '20px' }}>
            <h3>
              <span className="star">*</span>货箱格口规格配置(单位:mm)
            </h3>
          </div>
          <div
            style={{
              paddingLeft:
                productionType === ProductType.VEHICLE ? '140px' : '20px',
            }}
          >
            {gridCofigArr &&
              gridCofigArr.map((item: any, index: number) => {
                return (
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                    key={item}
                  >
                    {productionType !== ProductType.VEHICLE && (
                      <Select
                        defaultValue={item.type}
                        disabled={disabled}
                        style={{
                          marginBottom: '26px',
                          marginRight: '10px',
                          width: '150px',
                        }}
                        placeholder="请选择格口类型"
                        options={formatOptions(
                          dropDownData[dropDownListKey.ROBOT_GRID_TYPE],
                        )}
                        onChange={async (e) => {
                          const res = await fetchApi.getRobotGrid(e);
                          if (res.code === HttpStatusCode.Success) {
                            const list = clone(gridCofigArr);
                            const sizeStr = `${res.data.width} × ${res.data.length} × ${res.data.height}`;
                            list.splice(index, 1, {
                              ...res.data,
                              sizeStr,
                              type: e,
                            });
                            dispatch(setGridSizeListAction(list));
                          }
                        }}
                      />
                    )}
                    <ConfigForm
                      key={'config_form_' + index}
                      formIndex={index}
                      formCon={item}
                      disabled={disabled}
                      productionType={productionType}
                    ></ConfigForm>
                  </div>
                );
              })}
          </div>
        </Card>
      </Col>
    </Row>
  );
};

export default React.memo(GridConfig);
