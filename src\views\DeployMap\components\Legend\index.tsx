import React, { useEffect, useRef, useState } from 'react';
import './index.scss';
import { MapLayerEnum } from '../../utils/enum';
import DeployMapApi from '@/fetch/business/deployMap';
import { HttpStatusCode } from '@/fetch/core/constant';
import { isEmpty } from 'lodash';

interface LayerConfig {
  key: MapLayerEnum | 'HDMaps';
  visible: boolean;
  value?: string;
}

const Legend = ({
  changeLayerVisible,
  stationStopMarkerRef,
  isEditing,
  checkLayerExist,
}: {
  changeLayerVisible: any;
  stationStopMarkerRef: any;
  isEditing: boolean;
  checkLayerExist: (layerId: string) => Promise<boolean>;
}) => {
  const HDMaps = new Map([
    [MapLayerEnum.LINE_BOUNDARY, '车道边线'],
    [MapLayerEnum.CENTER_LINE, '车道中心线'],
    [MapLayerEnum.OPEN_AREA, '开阔区域'],
    [MapLayerEnum.INTERSECTION, '路口'],
    [MapLayerEnum.GATE_LAYER, '大门'],
    [MapLayerEnum.STOP_LINE, '停止线'],
  ]);
  const StationLayers = new Map([
    [MapLayerEnum.STATION_POINT_ENABLE_LAYER, '启用站点'],
    [MapLayerEnum.STATION_POINT_DISABLE_LAYER, '停用站点'],
  ]);
  const StopLayers = new Map([
    [MapLayerEnum.STOP_POINT_ENABLE_LAYER, '启用停靠点'],
    [MapLayerEnum.STOP_POINT_DISABLE_LAYER, '停用停靠点'],
  ]);
  const HDMAP_INIT_INVISIBLE_LAYERS: any[] = [
    MapLayerEnum.LINE_BOUNDARY,
    MapLayerEnum.GATE_LAYER,
    MapLayerEnum.OPEN_AREA,
  ];
  const [plannedRouteConfig, setPlannedRouteConfig] = useState<LayerConfig>({
    key: MapLayerEnum.PLANNED_ROUTE_LAYER,
    value: '线路',
    visible: true,
  });
  const [stationPointConfig, setStationPointConfig] = useState<LayerConfig>({
    key: MapLayerEnum.STATION_POINT_LAYER,
    value: '站点',
    visible: true,
  });
  const [stopPointConfig, setStopPointConfig] = useState<LayerConfig>({
    key: MapLayerEnum.STOP_POINT_LAYER,
    value: '停靠点',
    visible: true,
  });
  const [pointsNameConfig, setPointsNameConfig] = useState<LayerConfig>({
    key: MapLayerEnum.POINTS_NAME_LAYER,
    value: '点位名称',
    visible: false,
  });
  const [HDMapsConfig, setHDMapsConfig] = useState<LayerConfig>({
    key: 'HDMaps',
    value: '高精地图',
    visible: false,
  });
  const [HDMapsLabelList, setHDMapsLabelList] = useState<LayerConfig[]>([]);
  const [stationLabelList, setStationLabelList] = useState<LayerConfig[]>([]);
  const [stopLabelList, setStopLabelList] = useState<LayerConfig[]>([]);
  const [showHDMapsOptions, setShowHDMapsOptions] = useState<boolean>(false);
  const [showStationOptions, setShowStationOptions] = useState<boolean>(false);
  const [showStopOptions, setShowStopOptions] = useState<boolean>(false);
  useEffect(() => {
    getUserLayerConfig();
  }, []);

  useEffect(() => {
    const allHidden = HDMapsLabelList.every((item) => !item.visible);
    if (HDMapsLabelList.length > 0) {
      setHDMapsConfig({ ...HDMapsConfig, visible: allHidden ? false : true });
    }
  }, [HDMapsLabelList]);
  useEffect(() => {
    const allHidden = stationLabelList.every((item) => !item.visible);
    if (stationLabelList.length > 0) {
      setStationPointConfig({
        ...stationPointConfig,
        visible: allHidden ? false : true,
      });
    }
  }, [stationLabelList]);
  useEffect(() => {
    const allHidden = stopLabelList.every((item) => !item.visible);
    if (stopLabelList.length > 0) {
      setStopPointConfig({
        ...stopPointConfig,
        visible: allHidden ? false : true,
      });
    }
  }, [stopLabelList]);
  useEffect(() => {
    const allStationHidden = stationLabelList.every((item) => !item.visible);
    const allStopHidden = stopLabelList.every((item) => !item.visible);
    if (allStationHidden && allStopHidden) {
      setPointsNameConfig({
        ...pointsNameConfig,
        visible: false,
      });
    }
  }, [stationLabelList, stopLabelList]);
  useEffect(() => {
    if (plannedRouteConfig.visible) {
      setPlannedRouteConfig({
        ...plannedRouteConfig,
        visible: !isEditing,
      });
    } else {
      if (!isEditing) {
        changeLayerVisible?.(
          plannedRouteConfig.key,
          plannedRouteConfig.visible,
        );
      }
      return;
    }
  }, [isEditing]);

  /**
   * 初始化获取完图层配置后对图层做处理
   */
  const setConfigLayersVisibility = (config: any) => {
    const {
      plannedRouteConfig,
      pointsNameConfig,
      HDMapsLabelList,
      stationLabelList,
      stopLabelList,
    } = config;
    // 处理站点/停靠点/名称
    setTimeout(() => {
      if (stationStopMarkerRef) {
        const stationEnableLabelVisible = stationLabelList.find(
          (v) => v.key === MapLayerEnum.STATION_POINT_ENABLE_LAYER,
        )?.visible;
        const stationDisableLabelVisible = stationLabelList.find(
          (v) => v.key === MapLayerEnum.STATION_POINT_DISABLE_LAYER,
        )?.visible;
        const stopEnableLabelVisible = stopLabelList.find(
          (v) => v.key === MapLayerEnum.STOP_POINT_ENABLE_LAYER,
        )?.visible;
        const stopDisableLabelVisible = stopLabelList.find(
          (v) => v.key === MapLayerEnum.STOP_POINT_DISABLE_LAYER,
        )?.visible;
        stationStopMarkerRef.setLayerVisibility(
          MapLayerEnum.STATION_POINT_ENABLE_LAYER,
          stationEnableLabelVisible,
        );
        stationStopMarkerRef.setLayerVisibility(
          MapLayerEnum.STATION_POINT_DISABLE_LAYER,
          stationDisableLabelVisible,
        );
        stationStopMarkerRef.setLayerVisibility(
          MapLayerEnum.STOP_POINT_ENABLE_LAYER,
          stopEnableLabelVisible,
        );
        stationStopMarkerRef.setLayerVisibility(
          MapLayerEnum.STOP_POINT_DISABLE_LAYER,
          stopDisableLabelVisible,
        );
        if (!pointsNameConfig.visible) {
          stationStopMarkerRef.setAllStationLabelsVisibility(false);
          stationStopMarkerRef.setAllStopLabelsVisibility(false);
        } else {
          if (stationEnableLabelVisible) {
            stationStopMarkerRef.setEnableStationLabelsVisibility(true);
          } else {
            stationStopMarkerRef.setEnableStationLabelsVisibility(false);
          }
          if (stationDisableLabelVisible) {
            stationStopMarkerRef.setDisableStationLabelsVisibility(true);
          } else {
            stationStopMarkerRef.setDisableStationLabelsVisibility(false);
          }
          if (stopEnableLabelVisible) {
            stationStopMarkerRef.setEnableStopLabelsVisibility(true);
          } else {
            stationStopMarkerRef.setEnableStopLabelsVisibility(false);
          }
          if (stopDisableLabelVisible) {
            stationStopMarkerRef.setDisableStopLabelsVisibility(true);
          } else {
            stationStopMarkerRef.setDisableStopLabelsVisibility(false);
          }
        }
      }
    }, 1000);
    // 处理高精地图
    HDMapsLabelList.forEach((v) => {
      changeLayerVisible?.(v.key, v.visible);
    });
    // 处理线路
    changeLayerVisible?.(plannedRouteConfig.key, plannedRouteConfig.visible);
  };

  /**
   * 设置默认图层显隐配置
   */
  const setDefaultConfig = () => {
    const arr1 = Array.from(HDMaps, ([key, value]) => ({
      key,
      value,
      visible: HDMAP_INIT_INVISIBLE_LAYERS.includes(key) ? false : true,
    }));
    const arr2 = Array.from(StationLayers, ([key, value]) => ({
      key,
      value,
      visible: key === MapLayerEnum.STATION_POINT_ENABLE_LAYER ? true : false,
    }));
    const arr3 = Array.from(StopLayers, ([key, value]) => ({
      key,
      value,
      visible: key === MapLayerEnum.STOP_POINT_ENABLE_LAYER ? true : false,
    }));
    setTimeout(() => {
      if (stationStopMarkerRef) {
        stationStopMarkerRef.setLayerVisibility(
          MapLayerEnum.STATION_POINT_DISABLE_LAYER,
          false,
        );
        stationStopMarkerRef.setLayerVisibility(
          MapLayerEnum.STOP_POINT_DISABLE_LAYER,
          false,
        );
        HDMAP_INIT_INVISIBLE_LAYERS.forEach((v) => {
          checkLayerExist(v).then((res) => {
            if (res) {
              changeLayerVisible?.(v, false);
            }
          });
        });
      }
    }, 1000);
    setHDMapsLabelList(arr1);
    setStationLabelList(arr2);
    setStopLabelList(arr3);
  };
  /**
   * 获取用户图层显隐配置
   */
  const getUserLayerConfig = async () => {
    try {
      const res = await DeployMapApi.getUserLayerConfig(['mapLayerConfig']);
      if (res.code === HttpStatusCode.Success) {
        if (res.data && !isEmpty(res.data)) {
          const config = res.data[0].configParams;
          setPlannedRouteConfig(config.plannedRouteConfig);
          setStationPointConfig(config.stationPointConfig);
          setStopPointConfig(config.stopPointConfig);
          setPointsNameConfig(config.pointsNameConfig);
          setHDMapsConfig(config.HDMapsConfig);
          setHDMapsLabelList(config.HDMapsLabelList);
          setStationLabelList(config.stationLabelList);
          setStopLabelList(config.stopLabelList);
          setConfigLayersVisibility(config);
        } else {
          // 执行默认配置
          setDefaultConfig();
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  /**
   * 更新用户显隐配置
   *
   * @returns
   */
  const updateUserLayerConfig = async (configParams: {
    plannedRouteConfig: any;
    stationPointConfig: any;
    stopPointConfig: any;
    pointsNameConfig: any;
    HDMapsConfig: any;
    HDMapsLabelList: any;
    stationLabelList: any;
    stopLabelList: any;
  }) => {
    try {
      const res = await DeployMapApi.updateUserLayerConfig({
        configType: 'mapLayerConfig',
        configParams,
      });
    } catch (err) {
      console.error(err);
    }
  };

  const changeVisible = (val: any) => {
    const { key } = val;
    if (val.key === MapLayerEnum.POINTS_NAME_LAYER) {
      const stationPointLayer = stationPointConfig;
      const stopPointLayer = stopPointConfig;
      if (!stationPointLayer?.visible && !stopPointLayer?.visible) {
        return;
      }
    }
    switch (key) {
      case 'HDMaps':
        handleHDMapsVisibility(val);
        break;
      case MapLayerEnum.POINTS_NAME_LAYER:
        handlePointNameLayerVisibility(val);
        break;
      case MapLayerEnum.STATION_POINT_LAYER:
        changeAllStationLayer(val);
        break;
      case MapLayerEnum.STOP_POINT_LAYER:
        changeAllStopLayer(val);
        break;
      default:
        setPlannedRouteConfig({ ...plannedRouteConfig, visible: !val.visible });
        changeLayerVisible?.(val.key, !val.visible);
        updateUserLayerConfig({
          plannedRouteConfig: {
            ...plannedRouteConfig,
            visible: !val.visible,
          },
          stationPointConfig,
          stopPointConfig,
          pointsNameConfig,
          HDMapsConfig,
          HDMapsLabelList,
          stationLabelList,
          stopLabelList,
        });
        break;
    }
  };

  const handlePointNameLayerVisibility = (val: any) => {
    const newVisibility = !val.visible;
    setPointsNameConfig({ ...pointsNameConfig, visible: newVisibility });
    // 更新图层可见性
    if (stationStopMarkerRef) {
      const stationEnableLabelVisible = stationLabelList.find(
        (v) => v.key === MapLayerEnum.STATION_POINT_ENABLE_LAYER,
      )?.visible;
      const stationDisableLabelVisible = stationLabelList.find(
        (v) => v.key === MapLayerEnum.STATION_POINT_DISABLE_LAYER,
      )?.visible;
      const stopEnableLabelVisible = stopLabelList.find(
        (v) => v.key === MapLayerEnum.STOP_POINT_ENABLE_LAYER,
      )?.visible;
      const stopDisableLabelVisible = stopLabelList.find(
        (v) => v.key === MapLayerEnum.STOP_POINT_DISABLE_LAYER,
      )?.visible;
      if (!newVisibility) {
        if (stationEnableLabelVisible) {
          stationStopMarkerRef.setEnableStationLabelsVisibility(false);
        }
        if (stationDisableLabelVisible) {
          stationStopMarkerRef.setDisableStationLabelsVisibility(false);
        }
        if (stopEnableLabelVisible) {
          stationStopMarkerRef.setEnableStopLabelsVisibility(false);
        }
        if (stopDisableLabelVisible) {
          stationStopMarkerRef.setDisableStopLabelsVisibility(false);
        }
      } else {
        if (stationEnableLabelVisible) {
          stationStopMarkerRef.setEnableStationLabelsVisibility(true);
        }
        if (stationDisableLabelVisible) {
          stationStopMarkerRef.setDisableStationLabelsVisibility(true);
        }
        if (stopEnableLabelVisible) {
          stationStopMarkerRef.setEnableStopLabelsVisibility(true);
        }
        if (stopDisableLabelVisible) {
          stationStopMarkerRef.setDisableStopLabelsVisibility(true);
        }
      }
    }
    updateUserLayerConfig({
      plannedRouteConfig,
      stationPointConfig,
      stopPointConfig,
      pointsNameConfig: {
        ...pointsNameConfig,
        visible: newVisibility,
      },
      HDMapsConfig,
      HDMapsLabelList,
      stationLabelList,
      stopLabelList,
    });
  };

  // 处理 HDMaps 可见性
  const handleHDMapsVisibility = async (val: LayerConfig) => {
    const newVisibility = !val.visible;
    setHDMapsLabelList(
      HDMapsLabelList.map((v) => ({ ...v, visible: newVisibility })),
    );
    HDMapsLabelList.forEach(async (v) => {
      if (HDMAP_INIT_INVISIBLE_LAYERS.includes(v.key)) {
        await checkLayerExist(v.key);
      }
      changeLayerVisible?.(v.key, newVisibility);
    });
    setHDMapsConfig({ ...HDMapsConfig, visible: !val.visible });
    updateUserLayerConfig({
      plannedRouteConfig,
      stationPointConfig,
      stopPointConfig,
      pointsNameConfig,
      HDMapsConfig: {
        ...HDMapsConfig,
        visible: !val.visible,
      },
      HDMapsLabelList: HDMapsLabelList.map((v) => ({
        ...v,
        visible: newVisibility,
      })),
      stationLabelList,
      stopLabelList,
    });
  };

  const changeHDMapsVisible = async (val: any) => {
    const newVisibility = !val.visible;
    if (HDMAP_INIT_INVISIBLE_LAYERS.includes(val.key) && newVisibility) {
      await checkLayerExist(val.key);
    }
    setHDMapsLabelList(
      HDMapsLabelList.map((v) =>
        v.key === val.key ? { ...v, visible: !val.visible } : v,
      ),
    );
    changeLayerVisible && changeLayerVisible(val.key, newVisibility);
    updateUserLayerConfig({
      plannedRouteConfig,
      stationPointConfig,
      stopPointConfig,
      pointsNameConfig,
      HDMapsConfig,
      HDMapsLabelList: HDMapsLabelList.map((v) =>
        v.key === val.key ? { ...v, visible: !val.visible } : v,
      ),
      stationLabelList,
      stopLabelList,
    });
  };

  const handleHDMapsShow = (show: boolean) => {
    setShowHDMapsOptions(show);
  };

  const formatLabel = (val: any) => {
    return (
      <>
        <div
          className="legend-label"
          key={val.key}
          onClick={() => changeVisible(val)}
          onMouseEnter={() => {
            switch (val.key) {
              case 'HDMaps':
                handleHDMapsShow(true);
                break;
              case MapLayerEnum.STATION_POINT_LAYER:
                setShowStationOptions(true);
                break;
              case MapLayerEnum.STOP_POINT_LAYER:
                setShowStopOptions(true);
                break;
              default:
                break;
            }
          }}
          onMouseLeave={() => {
            switch (val.key) {
              case 'HDMaps':
                handleHDMapsShow(false);
                break;
              case MapLayerEnum.STATION_POINT_LAYER:
                setShowStationOptions(false);
                break;
              case MapLayerEnum.STOP_POINT_LAYER:
                setShowStopOptions(false);
                break;
              default:
                break;
            }
          }}
        >
          <i className={val.visible ? 'open' : 'close'}></i>
          <span>{val.value}</span>
        </div>
        {renderExtraArrow(val.key)}
      </>
    );
  };
  const formatExtraLabels = ({
    labelList,
    cb,
    mainClass,
    childClass,
    show,
    setShow,
  }: {
    labelList: any;
    cb: any;
    mainClass: string;
    childClass: string;
    show: boolean;
    setShow: any;
  }) => {
    return (
      <div
        className={show ? 'show ' + mainClass : 'hidden ' + mainClass}
        onMouseEnter={() => {
          setShow(true);
        }}
        onMouseLeave={() => {
          setShow(false);
        }}
      >
        {labelList.map((v) => {
          return (
            <div
              className={'legend-label ' + childClass}
              key={v.key}
              onClick={() => cb(v)}
            >
              <i className={v.visible ? 'open' : 'close'}></i>
              <span>{v.value}</span>
            </div>
          );
        })}
      </div>
    );
  };
  const renderExtraArrow = (key) => {
    switch (key) {
      case 'HDMaps':
        return (
          <i className={showHDMapsOptions ? 'arrow open' : 'arrow close'}></i>
        );
      case MapLayerEnum.STATION_POINT_LAYER:
        return (
          <i className={showStationOptions ? 'arrow open' : 'arrow close'}></i>
        );
      case MapLayerEnum.STOP_POINT_LAYER:
        return (
          <i className={showStopOptions ? 'arrow open' : 'arrow close'}></i>
        );
      default:
        return null;
    }
  };
  const changeAllStationLayer = (val: any) => {
    const newVisibility = !val.visible;
    setStationLabelList(
      stationLabelList.map((v) => ({ ...v, visible: newVisibility })),
    );
    setShowStationOptions(!showStationOptions);
    // 更新图层可见性
    if (stationStopMarkerRef) {
      stationLabelList.forEach((val) => {
        stationStopMarkerRef.setLayerVisibility(val.key, newVisibility);
      });
    }
    if (!newVisibility) {
      stationStopMarkerRef.setEnableStationLabelsVisibility(false);
      stationStopMarkerRef.setDisableStationLabelsVisibility(false);
    } else {
      if (pointsNameConfig.visible) {
        stationStopMarkerRef.setEnableStationLabelsVisibility(true);
        stationStopMarkerRef.setDisableStationLabelsVisibility(true);
      }
    }
    setStationPointConfig({ ...stationPointConfig, visible: !val.visible });
    updateUserLayerConfig({
      plannedRouteConfig,
      stationPointConfig: {
        ...stationPointConfig,
        visible: !val.visible,
      },
      stopPointConfig,
      pointsNameConfig,
      HDMapsConfig,
      HDMapsLabelList,
      stationLabelList: stationLabelList.map((v) => ({
        ...v,
        visible: newVisibility,
      })),
      stopLabelList,
    });
  };
  const changeAllStopLayer = (val: any) => {
    const newVisibility = !val.visible;
    setStopLabelList(
      stopLabelList.map((v) => ({ ...v, visible: newVisibility })),
    );
    setShowStopOptions(!showStopOptions);
    // 更新图层可见性
    if (stationStopMarkerRef) {
      stopLabelList.forEach((val) => {
        stationStopMarkerRef.setLayerVisibility(val.key, newVisibility);
      });
    }
    if (!newVisibility) {
      stationStopMarkerRef.setEnableStopLabelsVisibility(false);
      stationStopMarkerRef.setDisableStopLabelsVisibility(false);
    } else {
      if (pointsNameConfig.visible) {
        stationStopMarkerRef.setEnableStopLabelsVisibility(true);
        stationStopMarkerRef.setDisableStopLabelsVisibility(true);
      }
    }
    setStopPointConfig({ ...stopPointConfig, visible: !val.visible });
    updateUserLayerConfig({
      plannedRouteConfig,
      stationPointConfig,
      stopPointConfig: {
        ...stopPointConfig,
        visible: !val.visible,
      },
      pointsNameConfig,
      HDMapsConfig,
      HDMapsLabelList,
      stationLabelList,
      stopLabelList: stopLabelList.map((v) => ({
        ...v,
        visible: newVisibility,
      })),
    });
  };
  // 处理站点子图层点击
  const changeStationLayerVisible = (val: any) => {
    const newVisibility = !val.visible;
    setStationLabelList(
      stationLabelList.map((v) =>
        v.key === val.key ? { ...v, visible: newVisibility } : v,
      ),
    );
    // 更新图层可见性
    if (stationStopMarkerRef) {
      stationStopMarkerRef.setLayerVisibility(val.key, newVisibility);
      if (val.key === MapLayerEnum.STATION_POINT_ENABLE_LAYER) {
        stationStopMarkerRef.setEnableStationLabelsVisibility(
          !newVisibility ? newVisibility : pointsNameConfig.visible,
        );
      } else if (val.key === MapLayerEnum.STATION_POINT_DISABLE_LAYER) {
        stationStopMarkerRef.setDisableStationLabelsVisibility(
          !newVisibility ? newVisibility : pointsNameConfig.visible,
        );
      }
    }
    updateUserLayerConfig({
      plannedRouteConfig,
      stationPointConfig,
      stopPointConfig,
      pointsNameConfig,
      HDMapsConfig,
      HDMapsLabelList,
      stationLabelList: stationLabelList.map((v) =>
        v.key === val.key ? { ...v, visible: newVisibility } : v,
      ),
      stopLabelList,
    });
  };

  // 处理停靠点子图层点击
  const changeStopLayerVisible = (val: any) => {
    const newVisibility = !val.visible;
    setStopLabelList(
      stopLabelList.map((v) =>
        v.key === val.key ? { ...v, visible: newVisibility } : v,
      ),
    );
    // 更新图层可见性
    if (stationStopMarkerRef) {
      stationStopMarkerRef.setLayerVisibility(val.key, newVisibility);
      if (val.key === MapLayerEnum.STOP_POINT_ENABLE_LAYER) {
        stationStopMarkerRef.setEnableStopLabelsVisibility(
          !newVisibility ? newVisibility : pointsNameConfig.visible,
        );
      } else if (val.key === MapLayerEnum.STOP_POINT_DISABLE_LAYER) {
        stationStopMarkerRef.setDisableStopLabelsVisibility(
          !newVisibility ? newVisibility : pointsNameConfig.visible,
        );
      }
    }
    updateUserLayerConfig({
      plannedRouteConfig,
      stationPointConfig,
      stopPointConfig,
      pointsNameConfig,
      HDMapsConfig,
      HDMapsLabelList,
      stationLabelList,
      stopLabelList: stopLabelList.map((v) =>
        v.key === val.key ? { ...v, visible: newVisibility } : v,
      ),
    });
  };

  return (
    <div className="legend-container">
      {formatExtraLabels({
        labelList: HDMapsLabelList,
        cb: changeHDMapsVisible,
        mainClass: 'HD-maps-legend',
        childClass: 'HDMap',
        show: showHDMapsOptions,
        setShow: handleHDMapsShow,
      })}

      {formatExtraLabels({
        labelList: stationLabelList,
        cb: changeStationLayerVisible,
        mainClass: 'station-legend',
        childClass: 'station-layer',
        show: showStationOptions,
        setShow: setShowStationOptions,
      })}

      {formatExtraLabels({
        labelList: stopLabelList,
        cb: changeStopLayerVisible,
        mainClass: 'stop-legend',
        childClass: 'stop-layer',
        show: showStopOptions,
        setShow: setShowStopOptions,
      })}

      <div className="map-legend">
        {formatLabel(plannedRouteConfig)}
        {formatLabel(stationPointConfig)}
        {formatLabel(stopPointConfig)}
        {formatLabel(pointsNameConfig)}
        {formatLabel(HDMapsConfig)}
      </div>
    </div>
  );
};
export default React.memo(Legend);
