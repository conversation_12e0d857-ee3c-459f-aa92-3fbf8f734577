import { YESNO } from '@/utils/enum';
import { request } from '../core';

export class IssueLabelManageApi {
  fetchDomain = `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}`;
  // 分页查询列表
  fetchTableList({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: any;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/bugLabel/getPageList',
      body: {
        pageNum,
        pageSize,
        ...searchForm,
      },
      newGeteway: true,
    };
    return request(options);
  }

  //  新建
  addLabelInfo(requestBody: Object) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/bugLabel/add',
      body: requestBody,
      newGeteway: true,
    };
    return request(options);
  }

  //  编辑
  editLabelInfo(requestBody: Object) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/bugLabel/edit',
      body: requestBody,
      newGeteway: true,
    };
    return request(options);
  }
  // 获取详情
  getBugLabelInfo(bugLabelId: string) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/bugLabel/getBugLabelInfo',
      body: {
        bugLabelId,
      },
      newGeteway: true,
    };
    return request(options);
  }

  // 删除问题分类
  delete(requestBody: Object) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/bugLabel/delete',
      body: requestBody,
      newGeteway: true,
    };
    return request(options);
  }

  getModuleList() {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/bugCategory/getModuleList',
      newGeteway: true,
    };
    return request(options);
  }
}

export const getBugCategoryDownList = (moudleCode?: string) => {
  const options: RequestOptions = {
    method: 'POST',
    path: '/k2/management/bugCategory/getBugCategoryDownList',
    newGeteway: true,
    body: {
      moudleCode,
    },
  };
  return request(options);
};

export const addBugCategory = (param: {
  bugCategoryName: string;
  moduleCode: string;
}) => {
  const options: RequestOptions = {
    method: 'POST',
    path: '/k2/management/bugCategory/add',
    newGeteway: true,
    body: param,
  };
  return request(options);
};
