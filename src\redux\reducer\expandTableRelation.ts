/* eslint-disable no-unused-vars */
import { createSlice } from '@reduxjs/toolkit';
const expandTableRelationSlice = createSlice({
  name: 'expandTableRelation',
  initialState: {
    relationObj: null,
  },
  reducers: {
    changeRelationObj(state, event) {
      state.relationObj = event.payload;
    },
    clearRelationObj(state, event) {
      state.relationObj = null;
    },
  },
});

export const expandTableRelationReducer = expandTableRelationSlice.reducer;
export const { changeRelationObj, clearRelationObj } =
  expandTableRelationSlice.actions;
