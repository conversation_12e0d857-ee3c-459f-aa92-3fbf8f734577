import { YESNO } from '@/utils/enum';
import { request } from '../core';

export class VehicleInstructionApi {
  // 分页查询数据列表
  fetchTableList({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
        path: '/k2/management/pattern_plate/pattern_plate_page',
        urlParams: {
          pageNum,
          pageSize,
        },
      body: searchForm,
    };
    return request(options);
  }
  //  新增编辑
  submitEditInfo(requestBody: Object) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/pattern_plate/pattern_plate_add',
      body: requestBody,
    };
    return request(options);
  }
  // 获取详情
  fetchDetail(id: number | string) {
    const options: RequestOptions = {
      method: 'GET',
      path: `/k2/management/pattern_plate/pattern_plate_get_content/${id}`,
    };
    return request(options);
  }
  // 删除模板
  deleteInfo(id: number) {
    const options: RequestOptions = {
      method: 'GET',
      path: `/k2/management/pattern_plate/pattern_plate_delete/${id}`,
    };
    return request(options);
  }
}
