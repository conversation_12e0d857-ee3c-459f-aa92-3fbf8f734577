import { YESNO } from '@/utils/enum';
import { request } from '../core';

export class AppOperationApi {
  // 分页查询数据列表
  fetchTableList({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/app_access_log/app_access_log_get_page_list',
      urlParams: {
        pageNum,
        pageSize,
      },
      body: searchForm,
    };
    return request(options);
  }
}
