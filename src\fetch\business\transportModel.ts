import { request } from '../core';

export class TransportModelApi {

  fetchDomain = `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}`;

  // 分页查询数据列表
  fetchTableList = ({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/datacenter/web/transport/model/getSiteConfigPage`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: { ...searchForm, pageNum, pageSize },
    };
    return request(options);
  }

  // 查询站点名称下拉列表
  fetchSiteNameList = () => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/datacenter/web/transport/model/getSiteNameList`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
    };
    return request(options);
  };

  // 删除站点
  deleteSite = (id: number) => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/datacenter/web/transport/model/deleteSiteConfig`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: {
        id,
      }
    };
    return request(options);
  }

  // 添加站点
  addSite = (siteConfigList: any[]) => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/datacenter/web/transport/model/addSiteConfig`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: {
        dataList: siteConfigList
      }
    };
    return request(options);
  }

  // 更新站点
  updateSite = (params: Object) => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/datacenter/web/transport/model/updateSiteConfig`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: { ...params }
    };
    return request(options);
  }

  // 分页查询结果列表
  fetchResultTableList = ({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/datacenter/web/transport/model/getRecommendResultPage`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: { ...searchForm, pageNum, pageSize },
    };
    return request(options);
  }

  // 获取模型结果下拉框
  getModelResultSelect = (type: string) => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/datacenter/web/transport/model/getModelResultSelect`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: {
        type,
      }
    };
    return request(options);
  }

  // 获取模型结果路区下拉框
  getModelResultRoadSelect = (siteId: string) => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/datacenter/web/transport/model/getModelResultRoadSelect`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: {
        siteId,
      }
    };
    return request(options);
  }

  // 保存路区标记
  saveRoadMark = (params: Object) => {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/datacenter/web/transport/model/saveRoadMark`,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
      body: { ...params }
    };
    return request(options);
  }
}