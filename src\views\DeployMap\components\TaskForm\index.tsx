import React, { useEffect, useRef, useState } from 'react';
import { Button, message, Form, Select, Input, Radio } from 'antd';
import Drawer from '@/components/Drawer';
import { DrawerBtn } from '../TaskSearchForm';
import './index.scss';
import { cloneDeep, debounce, isEqual, isNull } from 'lodash';
import { decompressLocation, isEmpty } from '@/utils/utils';
import GeoLocationSelect from '../GeoLocationSelect';
import {
  ROUTE_PLAN_TYPE,
  PRECISE_TYPE,
  TASK_ROUTE_COLOR,
  LayerIdEnum,
  TASK_ROUTE_HIGHLIGHT_COLOR,
  TASK_ROUTE_COLOR_STRING,
} from '@/utils/constant';
import { ENABLE_ENUM } from '../../utils/constant';
import { buildURL } from '@/fetch/core/util';
import { HttpStatusCode } from '@/fetch/core/constant';
import DeployMapApi from '@/fetch/business/deployMap';
import { Map as OlMap } from 'ol';
import {
  transformLocationGcj02towgs84,
  transformLocationWgs84togcj02,
} from '@/utils/tencentMap';
import { Vector as VectorSource } from 'ol/source';
import { Vector as VectorLayer } from 'ol/layer';
import { LineString } from 'ol/geom';
import { Feature } from 'ol';
import PolylineEditor from '@/utils/DeployMapTool/PolylineEditor';
import { Style, Stroke } from 'ol/style';
import { showModal } from '@/components';
import MapOperate from '../../utils/mapOperate';
import { FeatureLike } from 'ol/Feature';
import * as turf from '@turf/turf';
const TaskForm = ({
  stationList,
  taskId,
  setIsEditing,
  map,
  startPoint,
  endPoint,
  selectedTaskInfo,
  isEditingRef,
  getTaskList,
  setStartPoint,
  setEndPoint,
  setSelectedTaskId,
  stopList,
}: {
  stationList: any[];
  taskId: number | null;
  setIsEditing: (isEditing: boolean) => void;
  map: OlMap;
  startPoint: any;
  endPoint: any;
  selectedTaskInfo: any;
  isEditingRef: React.MutableRefObject<boolean>;
  getTaskList: () => void;
  setStartPoint: (startPoint: any) => void;
  setEndPoint: (endPoint: any) => void;
  setSelectedTaskId: (taskId: number | null) => void;
  stopList?: Array<{
    pointId: string | number;
    pointName: string;
    latitude: number;
    longitude: number;
    [key: string]: any;
  }>;
}) => {
  const commonFormRef = useRef<any>(null);

  const editRouteLayerRef = useRef<VectorLayer<VectorSource>>(
    new VectorLayer({
      source: new VectorSource(),
      style: (feature: FeatureLike) => {
        return new Style({
          stroke: new Stroke({
            color: TASK_ROUTE_COLOR.GREEN,
            width: 6,
            lineCap: 'round',
            lineJoin: 'round',
          }),
        });
      },
    }),
  );
  const polylineEditorRef = useRef<PolylineEditor | null>(null);
  const mapOperateRef = useRef<any>(null);
  mapOperateRef.current = new MapOperate(map);
  const [routes, setRoutes] = useState<any>(null);
  const [roadNameList, setRoadNameList] = useState<any[]>([]);
  const [planRes, setPlanRes] = useState<any>(null);
  const [taskRouteColor, setTaskRouteColor] = useState<string>(
    TASK_ROUTE_COLOR.YELLOW,
  );
  const timerRef = useRef<any>(null);
  const onRoutesChangeRef = useRef<any>(
    (params: { length: number; controlPoints: any }) => {},
  );
  const handleOpenOrClose = (type: 'open' | 'close') => {
    if (commonFormRef.current) {
      commonFormRef.current.style.display = type === 'open' ? 'block' : 'none';
    }
  };
  const [formInstance] = Form.useForm();

  useEffect(() => {
    if (taskId) {
      timerRef.current = setInterval(() => {
        startRouteTask(taskId);
      }, 60000);
    } else {
      clearInterval(timerRef.current);
    }
  }, [taskId]);

  useEffect(() => {
    if (selectedTaskInfo) {
      if (!isEmpty(selectedTaskInfo.taskRouteList)) {
        const coords = selectedTaskInfo.taskRouteList.map((item: any) => [
          item.longitude,
          item.latitude,
        ]);
        const turfFeature = turf.points(coords);
        const center = turf.center(turfFeature).geometry.coordinates;
        const lineString = new LineString(coords);
        map.getView().setCenter(center);
        map.getView().fit(lineString);
      }
      formInstance.setFieldsValue({
        taskName: selectedTaskInfo.taskName,
        stationId: selectedTaskInfo.stationId,
        preciseType: selectedTaskInfo.preciseType,
        routePlanType: selectedTaskInfo.routePlanType,
      });
      setRoutes({
        totalMileage: selectedTaskInfo.totalMileage,
        taskRouteList: selectedTaskInfo.taskRouteList,
      });
    } else {
      const enabledStationList = stationList.filter(
        (item) => item.enabled === ENABLE_ENUM.ENABLE,
      );
      if (!isEmpty(enabledStationList) && enabledStationList.length === 1) {
        formInstance.setFieldsValue({
          stationId: enabledStationList[0].pointId,
        });
      }
    }
  }, [selectedTaskInfo, stationList]);
  useEffect(() => {
    if (routes && !isEmpty(routes.taskRouteList)) {
      const lineFeature = new Feature({
        geometry: new LineString(
          routes?.taskRouteList?.map((coord: any) => [
            coord.longitude,
            coord.latitude,
          ]),
        ),
      });
      lineFeature.set('id', 'baseLine');
      editRouteLayerRef.current.getSource()?.clear();
      editRouteLayerRef.current.getSource()?.addFeature(lineFeature);
      onRoutesChangeRef.current = ({ length, controlPoints }) => {
        setRoutes({
          ...routes,
          totalMileage: Number(length.toFixed(2)),
          taskRouteList: controlPoints
            .map((point: any) => ({
              latitude: point[1],
              longitude: point[0],
            }))
            .filter(
              (point, index, self) =>
                index ===
                self.findIndex(
                  (p) =>
                    p.latitude === point.latitude &&
                    p.longitude === point.longitude,
                ),
            ),
        });
      };
      if (!polylineEditorRef.current) {
        polylineEditorRef.current = new PolylineEditor(
          map,
          editRouteLayerRef.current,
          lineFeature.getGeometry() as LineString,
          {
            onChange: ({ length, controlPoints }) => {
              onRoutesChangeRef.current({
                length,
                controlPoints,
              });
            },
          },
        );
        polylineEditorRef.current?.open();
        polylineEditorRef.current?.enableEditing();
      } else {
        polylineEditorRef.current.removePolylineLayer();
        polylineEditorRef.current.setPolylineLayer(editRouteLayerRef.current);
        polylineEditorRef.current.setTarget(
          lineFeature.getGeometry() as LineString,
        );
      }
    } else {
      editRouteLayerRef.current.getSource()?.clear();
      polylineEditorRef.current?.removeFromMap();
      polylineEditorRef.current = null;
    }
    setStartPoint(null);
    setEndPoint(null);
  }, [routes]);
  useEffect(() => {
    const formValues: any = {};

    if (startPoint) {
      formValues.startPoint = {
        lat: startPoint.latitude,
        lon: startPoint.longitude,
        label: startPoint.pointName,
        index: startPoint.index || 0,
      };
    }

    if (endPoint) {
      formValues.endPoint = {
        lat: endPoint.latitude,
        lon: endPoint.longitude,
        label: endPoint.pointName,
        index: endPoint.index || 0,
      };
    }

    formInstance.setFieldsValue(formValues);
  }, [startPoint, endPoint]);

  useEffect(() => {
    editRouteLayerRef.current.setStyle(
      new Style({
        stroke: new Stroke({
          color: taskRouteColor,
          width: 6,
          lineCap: 'round',
          lineJoin: 'round',
        }),
      }),
    );
  }, [taskRouteColor]);

  // 获取路线关键点坐标
  const getKeyPointCoords = (route: any, allCoords: any) => {
    const { steps } = route;

    // 获取每个step的起点和终点索引
    const keyPointIndices = steps.reduce((acc: number[], step: any) => {
      const [start, end] = step.polyline_idx;
      return [...acc, start, end - 1];
    }, []);

    // 过滤出关键点坐标
    const keyPointCoords = keyPointIndices
      .map((index: number) => {
        const coordIndex = Math.floor(index / 2);
        return allCoords[coordIndex];
      })
      .filter(Boolean);

    return keyPointCoords;
  };
  const confirmPlanRoute = debounce(async () => {
    try {
      const val = {
        startPoint: formInstance.getFieldValue('startPoint'),
        endPoint: formInstance.getFieldValue('endPoint'),
        preciseType: formInstance.getFieldValue('preciseType'),
        routePlanType: formInstance.getFieldValue('routePlanType'),
      };

      if (isEqual(val.startPoint, val.endPoint)) {
        message.error('起点和终点不能相同');
        return;
      }
      if (routes && !isEmpty(routes.taskRouteList)) {
        showModal({
          content: '更改起、终点线路会重新规划，请确认是否更改',
          footer: {
            showOk: true,
            showCancel: true,
            okText: '确认',
            cancelText: '取消',
            okFunc: debounce(async (cb) => {
              handleConfirmPlanRoute(val);
              cb && cb();
            }, 500),
            cancelFunc: (cb) => {
              cb && cb();
            },
          },
        });
      } else {
        handleConfirmPlanRoute(val);
      }
    } catch (err) {
      console.error(err);
    }
  }, 500);
  const handleConfirmPlanRoute = async (val: any) => {
    try {
      const preciseType = val.preciseType;
      const routePlanType = val.routePlanType;
      // const startPoint_gcj02 = transformLocationWgs84togcj02(val.startPoint);
      // const endPoint_gcj02 = transformLocationWgs84togcj02(val.endPoint);
      const res: any = await fetchPlanRoute(
        routePlanType,
        `${val.startPoint.lat},${val.startPoint.lon}`,
        `${val.endPoint.lat},${val.endPoint.lon}`,
      );

      if (res.status === 0) {
        if (isEmpty(res.result.routes)) {
          message.error('规划路线失败，请重试');
          return;
        }
        // 解压路线坐标，进行坐标转换，并设置到allCoords
        const newRes = cloneDeep(res);
        newRes.result.routes.forEach((route: any, index: number) => {
          const coords = decompressLocation(route.polyline);
          route.allCoords = coords;
          route.index = index;
        });
        const turfFeature = turf.points(
          newRes.result.routes[0].allCoords.map((item: any) => [
            item.lon,
            item.lat,
          ]),
        );
        const center = turf.center(turfFeature).geometry.coordinates;
        const lineString = new LineString(
          newRes.result.routes[0].allCoords.map((item: any) => [
            item.lon,
            item.lat,
          ]),
        );
        map.getView().setCenter(center);
        map.getView().fit(lineString);
        setPlanRes(newRes);
        setRouteInfoFromPlanRes(newRes.result.routes[0], preciseType);
      }
    } catch (err) {
      console.error(err);
      message.error('规划路线失败，请重试');
    }
  };
  const fetchPlanRoute = (
    routePlanType: ROUTE_PLAN_TYPE,
    startPoint: string,
    endPoint: string,
  ) => {
    if (routePlanType === ROUTE_PLAN_TYPE.MOTOR_VEHICLE) {
      return new Promise((resolve, reject) => {
        const win = window as any;
        win.$.ajax({
          type: 'get',
          url: buildURL({
            absoluteURL: 'https://apis.map.qq.com/ws/direction/v1/driving',
            urlParams: {
              key: 'KRBBZ-VJEWS-Q3IOZ-67LGG-RVU2E-UEF6X',
              from: startPoint,
              to: endPoint,
              policy: 'LEAST_TIME,AVOID_HIGHWAY',
              output: 'jsonp',
              get_mp: 1,
            },
          }),
          dataType: 'jsonp',
          success: (res: any) => {
            resolve(res);
          },
        });
      });
    } else {
      return new Promise((resolve, reject) => {
        const win = window as any;
        win.$.ajax({
          type: 'get',
          url: buildURL({
            absoluteURL: 'https://apis.map.qq.com/ws/direction/v1/bicycling',
            urlParams: {
              key: 'KRBBZ-VJEWS-Q3IOZ-67LGG-RVU2E-UEF6X',
              from: startPoint,
              to: endPoint,
              output: 'jsonp',
            },
          }),
          dataType: 'jsonp',
          success: (res: any) => {
            resolve(res);
          },
        });
      });
    }
  };
  const setRouteInfoFromPlanRes = (route: any, preciseType: PRECISE_TYPE) => {
    const allCoords = route.allCoords;
    const coordinates =
      preciseType === PRECISE_TYPE.HIGH_PRECISION
        ? allCoords
        : getKeyPointCoords(route, allCoords);
    setRoadNameList(route.steps.map((step: any) => step.road_name));
    setRoutes({
      totalMileage: Number((route.distance / 1000).toFixed(2)),
      taskRouteList: coordinates
        .map((coord) => ({
          latitude: coord.lat,
          longitude: coord.lon,
        }))
        .filter(
          (point, index, self) =>
            index ===
            self.findIndex(
              (p) =>
                p.latitude === point.latitude &&
                p.longitude === point.longitude,
            ),
        ),
    });
  };

  const startRouteTask = async (taskId: number) => {
    try {
      const res = await DeployMapApi.startRouteTask(taskId);
      if (res.code !== HttpStatusCode.Success) {
        message.error(res.message || '开始路线任务失败');
        return;
      } else {
        setSelectedTaskId(taskId);
        setIsEditing(true);
        isEditingRef.current = true;
        mapOperateRef.current?.changeLayerVisible(
          LayerIdEnum.PLANNED_ROUTE_LAYER,
          false,
        );
      }
    } catch (err) {
      console.error(err);
      message.error('系统异常，请稍后重试');
    }
  };

  const endRouteTask = async (taskId: number) => {
    try {
      const res = await DeployMapApi.endRouteTask(taskId);
      if (res.code !== HttpStatusCode.Success) {
        message.error(res.msg || '结束路线任务失败');
      } else {
        clearInterval(timerRef.current);
      }
    } catch (err) {
      console.error(err);
      message.error('系统异常，请稍后重试');
    }
  };
  const handleCancel = () => {
    setIsEditing(false);
    isEditingRef.current = false;

    // 使用延迟重试机制确保线路图层可见性正确设置
    mapOperateRef.current?.changeLayerVisible(
      LayerIdEnum.PLANNED_ROUTE_LAYER,
      true,
      0, // 初始重试次数
      10, // 增加最大重试次数
      200, // 增加重试延迟
    );

    polylineEditorRef.current?.removeFromMap();
    polylineEditorRef.current = null;
    editRouteLayerRef.current.getSource()?.clear();
    setSelectedTaskId(null);
    getTaskList();
    setStartPoint(null);
    setEndPoint(null);
  };
  const submitTask = async () => {
    try {
      if (!routes || isEmpty(routes.taskRouteList)) {
        message.error('请先规划线路');
        return;
      }
      const val = await formInstance.validateFields();
      const preciseType = val.preciseType;
      const routePlanType = val.routePlanType;
      const taskName = val.taskName;
      const stationId = val.stationId;
      const params = {
        taskName,
        stationId,
        taskRouteList: routes?.taskRouteList?.map((item: any) => {
          const gcj02Latlng = transformLocationGcj02towgs84({
            lon: item.longitude,
            lat: item.latitude,
          });
          return {
            longitude: gcj02Latlng.lon,
            latitude: gcj02Latlng.lat,
          };
        }),
        roadNameList: roadNameList,
        preciseType,
        routePlanType,
        taskRouteColor: TASK_ROUTE_COLOR_STRING[taskRouteColor],
        totalMileage: routes?.totalMileage,
      };
      showModal({
        title: taskId ? '编辑线路勘查' : '创建线路勘查',
        content: '是否确认此线路勘查提交完成，提交后不可修改',
        footer: {
          showOk: true,
          showCancel: true,
          okText: '确认',
          cancelText: '取消',
          okFunc: debounce(
            async (cb) => {
              let res: any;

              if (taskId) {
                res = await DeployMapApi.modifyTaskRoute({
                  taskId: Number(taskId),
                  ...params,
                });
              } else {
                res = await DeployMapApi.createTaskRoute(params);
              }
              if (res.code === HttpStatusCode.Success) {
                message.success(
                  taskId ? '编辑线路勘查成功' : '创建线路勘查成功',
                );
                if (taskId) {
                  endRouteTask(Number(taskId));
                }
                cb && cb();
                handleCancel();
              }
            },
            500,
            { leading: true, trailing: false },
          ),
          cancelFunc: (cb) => {
            cb && cb();
          },
        },
      });
    } catch (err) {
      console.error(err);
    }
  };

  return (
    <Drawer
      className="task-form"
      defaultOpened={true}
      openWidth="428px"
      openBtn={
        <DrawerBtn type="open" onClick={() => handleOpenOrClose('open')} />
      }
      closeBtn={
        <DrawerBtn type="close" onClick={() => handleOpenOrClose('close')} />
      }
    >
      <div className="drawer-content" ref={commonFormRef}>
        <div className="task-form-header">
          <div className="task-form-header-title">
            {taskId ? '编辑采图任务' : '创建采图任务'}
          </div>
        </div>

        <Form
          form={formInstance}
          initialValues={{
            preciseType: PRECISE_TYPE.HIGH_PRECISION,
            routePlanType: ROUTE_PLAN_TYPE.MOTOR_VEHICLE,
          }}
          style={{ padding: '16px' }}
          colon={false}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          labelAlign="left"
        >
          <Form.Item
            name="taskName"
            label="任务名称"
            rules={[{ required: true, message: '请输入任务名称' }]}
          >
            <Input placeholder="请输入任务名称" allowClear maxLength={20} />
          </Form.Item>
          <Form.Item
            name="stationId"
            label="所属站点"
            rules={[{ required: true, message: '请选择所属站点' }]}
          >
            <Select
              showSearch
              filterOption={(input, option) =>
                option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              placeholder="请选择站点"
              options={stationList
                .filter((item) => item.enabled === ENABLE_ENUM.ENABLE)
                .map((item) => ({
                  label: item.pointName,
                  value: item.pointId,
                }))}
            />
          </Form.Item>
          <Form.Item
            name="startPoint"
            label={
              <>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="8" cy="8" r="8" fill="#12B35D" />
                  <circle cx="8" cy="8" r="6" fill="white" />
                  <circle cx="8" cy="8" r="4" fill="#12B35D" />
                </svg>
                <span style={{ marginLeft: '8px' }}>起点</span>
              </>
            }
          >
            <GeoLocationSelect
              dockPoints={
                stopList
                  ?.filter((item) => item.enabled === ENABLE_ENUM.ENABLE)
                  .map((item) => ({
                    id: `${item.pointId}`,
                    name: item.pointName,
                    address: '',
                    latitude: item.latitude,
                    longitude: item.longitude,
                  })) || []
              }
              onChange={(val, option) => {
                // 如果val为空，表示用户清空了选择
                if (!val || !option) {
                  formInstance.setFieldsValue({ startPoint: undefined });
                  setStartPoint(null);
                  return;
                }

                const selectedItem = option?.item;
                const pointData = {
                  label: selectedItem?.name || selectedItem?.title,
                  lat: selectedItem?.latitude || selectedItem?.location?.lat,
                  lon: selectedItem?.longitude || selectedItem?.location?.lng,
                  id: selectedItem?.id,
                };
                formInstance.setFieldsValue({ startPoint: pointData });
                setStartPoint({
                  pointName: pointData.label,
                  latitude: pointData.lat,
                  longitude: pointData.lon,
                  pointId: startPoint?.id,
                });
              }}
              value={
                startPoint
                  ? {
                      value: startPoint.pointId || startPoint.pointName,
                      label: startPoint.pointName,
                    }
                  : undefined
              }
              placeholder="搜索起点位置"
            />
          </Form.Item>
          <div
            className="reverse-start-end"
            onClick={() => {
              // 先获取当前的起点终点值
              const start_temp = formInstance.getFieldValue('startPoint');
              const end_temp = formInstance.getFieldValue('endPoint');
              // 更新表单值
              formInstance.setFieldsValue({
                startPoint: end_temp,
                endPoint: start_temp,
              });

              // 更新状态
              if (end_temp?.label) {
                setStartPoint({
                  pointName: end_temp?.label,
                  latitude: end_temp?.lat,
                  longitude: end_temp?.lon,
                  pointId: end_temp?.id,
                });
              } else {
                setStartPoint(null);
              }
              if (start_temp?.label) {
                setEndPoint({
                  pointName: start_temp?.label,
                  latitude: start_temp?.lat,
                  longitude: start_temp?.lon,
                  pointId: start_temp?.id,
                });
              } else {
                setEndPoint(null);
              }
            }}
          >
            <img
              style={{ width: '16px', height: '16px' }}
              src={require('@/assets/image/mapTrackPage/reverse-icon.png')}
            ></img>
          </div>
          <Form.Item
            name="endPoint"
            label={
              <>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="8" cy="8" r="8" fill="#FC3737" />
                  <circle cx="8" cy="8" r="6" fill="white" />
                  <circle cx="8" cy="8" r="4" fill="#FC3737" />
                </svg>
                <span style={{ marginLeft: '8px' }}>终点</span>
              </>
            }
          >
            <GeoLocationSelect
              dockPoints={
                stopList
                  ?.filter((item) => item.enabled === ENABLE_ENUM.ENABLE)
                  .map((item) => ({
                    id: `${item.pointId}`,
                    name: item.pointName,
                    address: '',
                    latitude: item.latitude,
                    longitude: item.longitude,
                  })) || []
              }
              onChange={(val, option) => {
                // 如果val为空，表示用户清空了选择
                if (!val || !option) {
                  formInstance.setFieldsValue({ endPoint: undefined });
                  setEndPoint(null);
                  return;
                }

                const selectedItem = option?.item;
                const pointData = {
                  label: selectedItem?.name || selectedItem?.title,
                  lat: selectedItem?.latitude || selectedItem?.location?.lat,
                  lon: selectedItem?.longitude || selectedItem?.location?.lng,
                  id: selectedItem?.id,
                };
                formInstance.setFieldsValue({ endPoint: pointData });
                setEndPoint({
                  pointName: pointData.label,
                  latitude: pointData.lat,
                  longitude: pointData.lon,
                  pointId: endPoint?.id,
                });
              }}
              value={
                endPoint
                  ? {
                      value: endPoint.pointId || endPoint.pointName,
                      label: endPoint.pointName,
                    }
                  : undefined
              }
              placeholder="搜索终点位置"
            />
          </Form.Item>
          <Form.Item name="routePlanType" label="" required>
            <Radio.Group
              onChange={(e) => {
                if (planRes) {
                  showModal({
                    content: '修改规划方案需要重新生成规划路线',
                    footer: {
                      showOk: true,
                      showCancel: true,
                      okText: '确认',
                      cancelText: '取消',
                      okFunc: (cb) => {
                        setPlanRes(null);
                        setRoutes(null);
                        cb && cb();
                      },
                      cancelFunc: (cb) => {
                        // 取消时恢复原来的值
                        formInstance.setFieldsValue({
                          routePlanType:
                            e.target.value === ROUTE_PLAN_TYPE.MOTOR_VEHICLE
                              ? ROUTE_PLAN_TYPE.NON_MOTOR_VEHICLE
                              : ROUTE_PLAN_TYPE.MOTOR_VEHICLE,
                        });
                        cb && cb();
                      },
                    },
                  });
                }
              }}
            >
              <Radio value={ROUTE_PLAN_TYPE.MOTOR_VEHICLE}>机动车</Radio>
              <Radio value={ROUTE_PLAN_TYPE.NON_MOTOR_VEHICLE}>非机动车</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item name="preciseType" label="" required>
            <Radio.Group
              onChange={(e) => {
                if (planRes) {
                  showModal({
                    content: '修改规划精度需要重新生成规划路线',
                    footer: {
                      showOk: true,
                      showCancel: true,
                      okText: '确认',
                      cancelText: '取消',
                      okFunc: (cb) => {
                        setPlanRes(null);
                        setRoutes(null);
                        cb && cb();
                      },
                      cancelFunc: (cb) => {
                        // 取消时恢复原来的值
                        formInstance.setFieldsValue({
                          preciseType:
                            e.target.value === PRECISE_TYPE.HIGH_PRECISION
                              ? PRECISE_TYPE.STANDARD_PRECISION
                              : PRECISE_TYPE.HIGH_PRECISION,
                        });
                        cb && cb();
                      },
                    },
                  });
                }
              }}
            >
              <Radio value={PRECISE_TYPE.HIGH_PRECISION}>高精规划</Radio>
              <Radio value={PRECISE_TYPE.STANDARD_PRECISION}>标精规划</Radio>
            </Radio.Group>
          </Form.Item>
          {planRes && (
            <Form.Item name="planRes" label="线路">
              <Radio.Group
                onChange={(e) => {
                  const index = e.target.value;
                  setRouteInfoFromPlanRes(
                    planRes.result.routes[index],
                    formInstance.getFieldValue('preciseType'),
                  );
                }}
                defaultValue={planRes.result.routes[0].index}
              >
                {planRes.result.routes.map((route: any, index: number) => (
                  <Radio value={route.index}>线路{index + 1}</Radio>
                ))}
              </Radio.Group>
            </Form.Item>
          )}
          {routes && !isNull(routes?.totalMileage) && (
            <Form.Item name="totalMileage" label="线路里程">
              <span>{routes?.totalMileage}km</span>
            </Form.Item>
          )}
          <Form.Item name="taskRouteColor" label="线路颜色">
            <div className="task-route-color-container">
              <div
                className="task-route-color-item"
                style={{
                  backgroundColor: TASK_ROUTE_COLOR.YELLOW,
                  border:
                    taskRouteColor === TASK_ROUTE_COLOR.YELLOW
                      ? `2px solid ${
                          TASK_ROUTE_HIGHLIGHT_COLOR[TASK_ROUTE_COLOR.YELLOW]
                        }`
                      : 'none',
                }}
                onClick={() => {
                  formInstance.setFieldsValue({
                    taskRouteColor: TASK_ROUTE_COLOR.YELLOW,
                  });
                  setTaskRouteColor(TASK_ROUTE_COLOR.YELLOW);
                }}
              ></div>
              <div
                className="task-route-color-item"
                style={{
                  backgroundColor: TASK_ROUTE_COLOR.GREEN,
                  border:
                    taskRouteColor === TASK_ROUTE_COLOR.GREEN
                      ? `2px solid ${
                          TASK_ROUTE_HIGHLIGHT_COLOR[TASK_ROUTE_COLOR.GREEN]
                        }`
                      : 'none',
                }}
                onClick={() => {
                  formInstance.setFieldsValue({
                    taskRouteColor: TASK_ROUTE_COLOR.GREEN,
                  });
                  setTaskRouteColor(TASK_ROUTE_COLOR.GREEN);
                }}
              ></div>
              <div
                className="task-route-color-item"
                style={{
                  backgroundColor: TASK_ROUTE_COLOR.BLUE,
                  border:
                    taskRouteColor === TASK_ROUTE_COLOR.BLUE
                      ? `2px solid ${
                          TASK_ROUTE_HIGHLIGHT_COLOR[TASK_ROUTE_COLOR.BLUE]
                        }`
                      : 'none',
                }}
                onClick={() => {
                  formInstance.setFieldsValue({
                    taskRouteColor: TASK_ROUTE_COLOR.BLUE,
                  });
                  setTaskRouteColor(TASK_ROUTE_COLOR.BLUE);
                }}
              ></div>
            </div>
          </Form.Item>
        </Form>
        <div className="routes-info">
          <Button type="default" onClick={confirmPlanRoute}>
            确认生成线路
          </Button>
        </div>

        <div className="action-btn-container">
          <Button
            type="default"
            onClick={() => {
              if (taskId) {
                endRouteTask(taskId);
              }
              handleCancel();
            }}
            style={{
              background: 'rgba(245,245,246,1)',
              borderRadius: '8px',
              color: '#23252B',
              marginRight: '16px',
              border: 'none',
            }}
          >
            取消
          </Button>
          <Button
            type="primary"
            onClick={submitTask}
            style={{
              background: 'rgba(60,110,240,1)',
              borderRadius: '8px',
              color: '#FFFFFF',
              border: 'none',
            }}
          >
            提交
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default TaskForm;
