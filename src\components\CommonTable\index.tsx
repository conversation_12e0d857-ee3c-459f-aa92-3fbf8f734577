import React, { ReactNode, useEffect, useState } from "react";
import {
  But<PERSON>,
  Popconfirm,
  Table as BaseTable,
  ConfigProvider as ConfigProvider5,
} from "antd";
import "./index.scss";
import { AnyFunc, AnyObject } from "../../global";
// 列表页面可选页数
export const defaultPageSizeOptions: string[] = ["10", "20", "30", "40", "100"];
export interface MiddleBtns {
  title: string;
  sourceCode?: string;
  btnType?: "primary" | "default" | "textBtn";
  onClick: Function;
  enablePopConfirm?: boolean;
  popConfirmContent?: string;
  clstag?: string; // 埋点坑位id
  style?: object;
}
export interface TableProps<T> {
  className?: string;
  theme?: "light" | "dark";
  tableListData: { list: object[]; totalNumber?: number; totalPage?: number };
  columns: any[];
  loading?: boolean;
  rowKey: string; // table里面每一行的唯一标识key
  middleBtns?: MiddleBtns[] | null | undefined; // 中间按键
  searchRef?: any;
  searchCondition?: T;
  rowClassName?: (record: T, index: number) => string;
  onPageChange?: Function;
  expandable?: any; // 配置展开属性
  rowSelection?: object;
  notPage?: boolean;
  scrollY?: number;
  tableKey?: string;
  crossPageSelect?: AnyFunc;
  pageSizeOptions?: string[];
  summary?: (currentData) => ReactNode;
}

export const customLocale = {
  filterConfirm: "确定",
  filterReset: "重置",
  emptyText: "暂无数据",
  pagination: {
    items_per_page: "/页",
    jump_to: "跳至",
    page: "",
    all_item_text: "全部",
    next_page_text: "下一页",
    prev_page_text: "上一页",
    prev_5_text: "前 5 页",
    next_5_text: "后 5 页",
    first_page_text: "第一页",
    last_page_text: "最后一页",
  },
};

function CommonTable<T extends AnyObject = AnyObject>(props: TableProps<T>) {
  const {
    className,
    middleBtns,
    loading,
    columns,
    searchRef,
    tableListData,
    searchCondition,
    onPageChange,
    rowKey,
    expandable,
    rowSelection,
    notPage,
    scrollY,
    tableKey,
    theme = "light",
    crossPageSelect,
    rowClassName,
    summary,
  } = props;
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);

  // /* 计算搜索框高度，以使Table高度自适应屏幕高度 */
  const tableSize = () => {
    if (searchRef && searchRef.current && searchRef.current.clientHeight) {
      const height = `calc(80vh - ${searchRef.current.clientHeight + 100}px)`;
      return height;
    }
    return "65vh";
  };

  const crossPageSelectRowSelection = {
    selectedRowKeys: selectedRowKeys,
    onSelect: (record: any, selected: boolean) => {
      let keys: any[] = [];
      let rows: any[] = [];
      if (selected) {
        keys = selectedRowKeys.concat([record[rowKey]]);
        rows = selectedRows.concat([record]);
      } else {
        keys = selectedRowKeys.filter((v) => v !== record[rowKey]);
        rows = selectedRows.filter((v) => v[rowKey] !== record[rowKey]);
      }
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
      crossPageSelect &&
        crossPageSelect(keys, rows, () => {
          setSelectedRowKeys([]);
          setSelectedRows([]);
        });
    },
    onSelectAll: (
      selected: boolean,
      selectedRowsInfo: any,
      changeRows: any
    ) => {
      let keys: any[] = [];
      let rows: any[] = [];
      if (selected) {
        const set1 = new Set(selectedRowKeys);
        const set2 = new Set(selectedRows);
        changeRows.forEach((v: any) => {
          set1.add(v[rowKey]);
          set2.add(v);
        });
        keys = [...set1];
        rows = [...set2];
      } else {
        const arr1 = selectedRows.filter((v) => {
          return !changeRows.some((i: any) => i[rowKey] === v[rowKey]);
        });
        const arr2 = selectedRowKeys.filter((v) => {
          return !changeRows.some((i: any) => i[rowKey] === v);
        });

        keys = [...arr2];
        rows = [...arr1];
      }
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
      crossPageSelect &&
        crossPageSelect(keys, rows, () => {
          setSelectedRowKeys([]);
          setSelectedRows([]);
        });
    },
  };

  
  return (
    <div
      className={`common-table ${className || ""} ${
        theme == "dark" ? "dark-theme" : ""
      }`}
    >
      <div className="middle-btn">
        {middleBtns &&
          middleBtns.map((item: any) => {
            return (
              <>
                {item?.enablePopConfirm ? (
                  <Popconfirm
                    title={item.popConfirmContent}
                    onConfirm={item.onClick}
                    key={item.key}
                  >
                    <Button type="primary" clstag={item.clstag} style={item.style}>
                      {item.title}
                    </Button>
                  </Popconfirm>
                ) : (
                  <Button
                    type={item.btnType ?? "primary"}
                    key={item.key}
                    clstag={item.clstag}
                    onClick={() => item.onClick()}
                    style={item.style}
                  >
                    {item.title}
                  </Button>
                )}
              </>
            );
          })}
      </div>
      <ConfigProvider5 prefixCls="x-coreui">
        <BaseTable
          key={tableKey}
          locale={customLocale}
          rowClassName={rowClassName && rowClassName}
          columns={columns}
          loading={loading}
          dataSource={tableListData?.list ?? []}
          rowKey={(record: any) => record[rowKey]}
          expandable={expandable}
          summary={summary}
          scroll={{
            y: scrollY ?? tableSize(),
          }}
          rowSelection={
            crossPageSelect
              ? crossPageSelectRowSelection
              : rowSelection
              ? rowSelection
              : undefined
          }
          pagination={
            notPage
              ? false
              : {
                  position: ["bottomRight"],
                  total: tableListData?.totalNumber,
                  current: searchCondition?.pageNum,
                  pageSize: searchCondition?.pageSize,
                  showQuickJumper: true,
                  showSizeChanger: true,
                  pageSizeOptions:
                    props.pageSizeOptions ?? defaultPageSizeOptions,
                  showTotal: (total) => `共${total}条记录`,
                  locale: customLocale.pagination,
                }
          }
          onChange={(
            paginationData: any,
            filters: any,
            sorter: any,
            extra: any
          ) => {
            if (extra.action === "paginate") {
              const { current, pageSize } = paginationData;
              const newSearchValue = {
                ...searchCondition,
                pageNum: current,
                pageSize: pageSize,
              };
              onPageChange && onPageChange(newSearchValue);
            }
          }}
        />
      </ConfigProvider5>
    </div>
  );
}

export default React.memo(CommonTable);
