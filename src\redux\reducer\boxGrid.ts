
import { createSlice } from '@reduxjs/toolkit';
// eslint-disable-next-line no-unused-vars
import { RootState } from '../store';

const initialState: any = {
  gridPositionValue: {},
  gridSizeList: [],
  gridList: [],
  refreshPreview: Date.now(),
};

const boxGrid = createSlice({
  name: 'boxGrid',
  initialState: initialState,
  reducers: {
    setGridPositionValueAction(state, action) {
      state.gridPositionValue = action.payload;
    },
    setGridSizeListAction(state, action) {
      state.gridSizeList = action.payload;
    },
    setGridListAction(state, action) {
      state.gridList = action.payload;
      state.refreshPreview = Date.now();
    },
  },
});

export const boxGridReducer = boxGrid.reducer;
export const {
  setGridPositionValueAction,
  setGridSizeListAction,
  setGridListAction,
} = boxGrid.actions;
export const boxGridSelector = (state: RootState) => {
  return state.boxGrid;
};
