import React, { useEffect, useRef, useState } from 'react';
import {
  CommonForm,
  FieldItem,
  FormConfig,
  sendGlobalEvent,
} from '@jd/x-coreui';
import { BreadCrumb, showModal } from '@/components';
import { formatLocation } from '@/utils/utils';
import { Button, message } from 'antd';
import { PageType } from '@/utils/EditTitle';
import './edit.scss';
import {
  IssueLabelManageApi,
  addBugCategory,
  getBugCategoryDownList,
} from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { getFormConfig } from './utils/constant';

export const Title = {
  [PageType.ADD]: '新建问题标签',
  [PageType.EDIT]: '编辑问题标签',
};

const fetchApi = new IssueLabelManageApi();
const LabelEdit = () => {
  const { id, type } = formatLocation(window.location.search);
  const [config, setConfig] = useState<FormConfig>(getFormConfig(type));
  const [update, forceUpdate] = useState<number>(0);
  const [detail, setDetail] = useState<any>(null);
  const formRef = useRef<any>(null);

  const handleAddCategory = () => {
    const moduleValue = formRef.current.getFieldValue('moduleCode');
    if (!moduleValue?.value) {
      message.error('请选择模块，再操作新增分类！');
      return;
    }

    let form: any = null;
    showModal({
      title: '新增问题分类',
      content: (
        <CommonForm
          defaultValue={{
            moduleCode: moduleValue?.label,
          }}
          formConfig={{
            fields: [
              {
                label: '问题分类名称',
                fieldName: 'bugCategoryName',
                type: 'input',
                placeholder: '请输入分类名称',
                validatorRules: [
                  {
                    required: true,
                    message: '请输入分类名称',
                  },
                ],
              },
              {
                label: '对应模块',
                fieldName: 'moduleCode',
                placeholder: '请选择模块',
                type: 'text',
              },
            ],
          }}
          getFormInstance={(ref: any) => {
            form = ref;
          }}
        />
      ),
      footer: {
        showCancel: true,
        showOk: true,
        cancelFunc: (cb: () => void) => {
          cb();
        },
        okFunc: (cb: () => void) => {
          form
            .validateFields()
            .then((res) => {
              const bugCategoryName = form.getFieldValue('bugCategoryName');
              const moduleCode = moduleValue?.value;
              addBugCategory({
                bugCategoryName,
                moduleCode,
              })
                .then(async (res) => {
                  if (res?.code === HttpStatusCode.Success) {
                    message.success('新增成功！');
                    const res = await getBugCategoryDownList(moduleCode);
                    if (res?.code === HttpStatusCode.Success) {
                      const field = config.fields.find(
                        (item: FieldItem) => item.fieldName === 'bugCategoryId',
                      );
                      field!.options = res?.data?.map((item) => ({
                        label: item.bugCategoryName,
                        value: item.bugCategoryId,
                      }));
                      setConfig({
                        ...config,
                      });
                      sendGlobalEvent('FORCE_UPDATE_CONFIG', {
                        name: 'tagName',
                        config: config,
                      });
                    }
                  } else {
                    res?.message && message.error(res?.message);
                  }
                })
                .catch((e) => {})
                .finally(() => {
                  cb();
                });
            })
            .catch((e) => {});
        },
      },
    });
  };
  const breadCrumbItems = [
    {
      title: '问题标签管理',
      route: '',
    },
    {
      title: Title[type],
      route: '',
    },
  ];

  const submit = async () => {
    try {
      const values = await formRef.current.validateFields();
      const request =
        type === PageType.ADD ? fetchApi.addLabelInfo : fetchApi.editLabelInfo;
      request({
        bugLabelId: detail?.bugLabelId,
        bugLabelName: values?.bugLabelName,
        bugCategoryId: values?.bugCategoryId,
        moduleCode: values?.moduleCode?.value,
      })
        .then((res) => {
          if (res?.code === HttpStatusCode.Success) {
            message.success(type === PageType.ADD ? '新建成功' : '编辑成功');
            history.go(-1);
          } else {
            res?.message && message.error(res?.message);
          }
        })
        .catch((e) => {
          message.error(type === PageType.ADD ? '新建失败' : '编辑失败');
        });
    } catch (e) {}
  };
  useEffect(() => {
    fetchApi.getModuleList().then((res) => {
      if (res?.code === HttpStatusCode.Success) {
        const field = config.fields.find(
          (item) => item.fieldName === 'moduleCode',
        );
        field!.options = res?.data?.map((item) => ({
          label: item.moduleName,
          value: item.moduleCode,
        }));
        setConfig({ ...config });
      }
    });
  }, []);
  useEffect(() => {
    if (type === PageType.EDIT) {
      fetchApi.getBugLabelInfo(id).then((res) => {
        if (res?.code === HttpStatusCode.Success) {
          setDetail(res?.data);
          formRef.current.setFieldsValue(res?.data);
        }
      });
    }
  }, []);
  return (
    <div className="label-edit-container" style={{ padding: '10px' }}>
      <div className="bread-crub">
        <BreadCrumb items={breadCrumbItems} />
      </div>
      <div
        className="content"
        style={{
          position: 'relative',
        }}
      >
        <h1 className="title">{Title[type]}</h1>
        <CommonForm
          formConfig={config}
          name="tagName"
          defaultValue={{
            bugCategoryId: detail?.bugCategoryId,
            bugLabelId: detail?.bugLabelId,
            bugLabelName: detail?.bugLabelName,
            moduleCode: detail?.moduleCode
              ? {
                  label: detail?.moduleName,
                  value: detail?.moduleCode,
                }
              : null,
          }}
          getFormInstance={(ref: any) => {
            formRef.current = ref;
          }}
        />
        {type === PageType.ADD && (
          <a
            style={{
              position: 'absolute',
              bottom: '80px',
              right: '86px',
              fontSize: '14px',
              color: '#3c6ef0',
              cursor: 'pointer',
            }}
            onClick={handleAddCategory}
          >
            新增
          </a>
        )}

        <div className="btn-group">
          <Button type="primary" onClick={submit}>
            确定
          </Button>
          <Button
            type="default"
            style={{ marginLeft: '20px' }}
            onClick={() => {
              history.go(-1);
            }}
          >
            取消
          </Button>
        </div>
      </div>
    </div>
  );
};

export default LabelEdit;
