/* eslint-disable no-unused-vars */
import { Col, Form, Modal, Input, Radio, message, FormInstance } from 'antd';
import React, { useEffect, useState } from 'react';
import { PhoneOutlined, UserOutlined } from '@ant-design/icons';
import { editAndAddBlackList, checkDuplicateStationName } from '../../fetchApi';
import { checkPhone } from '@/utils/utils';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { HttpStatusCode } from '@/fetch/core/constant';

const ConfigBlackList = ({
  visiable,
  onSubmit,
  userId,
  customerName,
  customerPhone,
  enableName,
}: {
  visiable: boolean;
  userId: string | null;
  customerName: string | null;
  customerPhone: string | null;
  enableName: string | null;
  onSubmit: Function;
}) => {
  const [editForm] = Form.useForm();
  const [submitStatus, setSubmitStatus] = useState(false);
  const [validUserPhone, setValidUserPhone] = useState<{
    validateStatus: 'success' | 'warning' | 'error' | 'validating' | '';
    message: string | null;
  }>({
    validateStatus: '',
    message: null,
  });
  const submitClick = async () => {
    editForm.submit();
    if (submitStatus || !checkPhone(editForm.getFieldValue('customerPhone'))) {
      setValidUserPhone({
        validateStatus: 'error',
        message: '请输入正确的电话号码',
      });
      return;
    }
    const formValues = await editForm.validateFields();
    setSubmitStatus(true);
    const formData = {
      ...formValues,
      enable: radioValue,
    };
    if (userId) {
      formData['id'] = userId;
    }
    const response: any = await editAndAddBlackList(formData);
    if (response.code === HttpStatusCode.Success) {
      message.success(response.message);
      onSubmit ? onSubmit(true) : null;
    } else {
      message.error(response.message);
    }
    setSubmitStatus(false);
  };

  const checkPhoneRepetition = async (customerPhone: string) => {
    if (customerPhone && customerPhone.length > 0) {
      setValidUserPhone({
        validateStatus: 'validating',
        message: null,
      });
      if (!checkPhone(customerPhone)) {
        setValidUserPhone({
          validateStatus: 'error',
          message: '请输入正确的电话号码',
        });
      } else {
        const result: any = await checkDuplicateStationName({
          id: userId,
          customerPhone,
        });
        if (result.code === HttpStatusCode.Success) {
          setValidUserPhone({
            validateStatus: 'success',
            message: null,
          });
        } else {
          setValidUserPhone({
            validateStatus: 'error',
            message: result.message,
          });
        }
      }
    }
  };

  const [radioValue, setRadioValue] = useState(1);
  useEffect(() => {
    if (enableName) {
      setRadioValue(enableName === '启用' ? 1 : 0);
    }
    editForm.setFieldsValue({
      customerName,
      customerPhone,
    });
  }, []);


  return (
    <div>
      <Modal
        visible={visiable}
        title="配置黑名单"
        keyboard={false}
        maskClosable={false}
        onOk={submitClick}
        confirmLoading={submitStatus}
        onCancel={() => {
          if (!submitStatus) {
            onSubmit ? onSubmit(false) : null;
          }
        }}
      >
        <Form form={editForm}>
          <Col>
            <Form.Item
              label={<PhoneOutlined />}
              name="customerPhone"
              hasFeedback
              validateStatus={validUserPhone.validateStatus}
              help={validUserPhone.message}
              rules={[
                {
                  required: true,
                  message: <p style={{ color: 'red' }}>请输入用户手机号</p>,
                },
              ]}
            >
              <Input
                autoFocus
                allowClear
                placeholder="请输入用户手机号码"
                onChange={(e) => {
                  editForm.setFieldsValue({
                    customerPhone: e.target.value.slice(0, 15),
                  });
                }}
                onBlur={(e) => {
                  checkPhoneRepetition(e.target.value);
                }}
              ></Input>
            </Form.Item>
          </Col>
          <Col>
            <Form.Item
              label={<UserOutlined />}
              name="customerName"
              rules={[{ required: true, message: '请输入用户姓名' }]}
            >
              <Input
                placeholder="请输入用户姓名"
                maxLength={20}
                allowClear
              ></Input>
            </Form.Item>
          </Col>
        
        </Form>
      </Modal>
    </div>
  );
};

export default React.memo(ConfigBlackList);
