import { dropDownListKey, dropDownKey } from '@/utils/constant';
import { FieldItem, FormConfig } from '@/components';
export const tableColumns = [
  {
    title: '供应商',
    dataIndex: 'supplierName',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '所属产品',
    dataIndex: 'productTypeName',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '车型名称',
    dataIndex: 'name',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '车型id',
    dataIndex: 'id',
    align: 'center',
    width: 80,
    ellipsis: true,
  },
  {
    title: '底盘名称',
    dataIndex: 'chassis',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '计算平台',
    dataIndex: 'compute',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '传感器方案名称',
    dataIndex: 'sensorScheme',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '车辆组装厂商',
    dataIndex: 'factoryName',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '车型状态',
    dataIndex: 'enableName',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '操作人',
    dataIndex: 'modifyUser',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    align: 'center',
    width: 180,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    align: 'center',
    width: 250,
    ellipsis: true,
    fixed: 'right',
  },
];
export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'name',
      label: '车型名称',
      placeholder: '请输入车型名称',
      type: 'input',
    },
    {
      fieldName: 'productType',
      label: '所属产品',
      placeholder: '请选择所属产品',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.PRODUCT_TYPE,
      dropDownListKey: dropDownListKey.PRODUCT_TYPE,
    },
    {
      fieldName: 'manufactory',
      label: '组装厂商',
      placeholder: '请选择组装厂商',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.PRODUCT_MANUFACTORY,
      dropDownListKey: dropDownListKey.PRODUCT_MANUFACTORY,
    },
    {
      fieldName: 'enable',
      label: '车型状态',
      placeholder: '请选择模版状态',
      type: 'select',
      specialFetch: 'commonDown',
      dropDownKey: dropDownKey.ENABLE,
      dropDownListKey: dropDownListKey.ENABLE,
    },
  ],
};

export const sensorTableColumn: any[] = [
  {
    title: '序号',
    dataIndex: '',
    align: 'center',
    ellipsis: true,
    render: (text: any, re: any, index: any) => index + 1,
  },
  {
    title: '传感器设备',
    dataIndex: 'hardwareTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '用途',
    dataIndex: 'hardwareTypeUsageName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '硬件名称',
    dataIndex: 'hardwareModelName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '型号',
    dataIndex: 'hardwareModelModel',
    align: 'center',
    ellipsis: true,
  },
  { title: '数量', dataIndex: 'useNumber', align: 'center', ellipsis: true },
];
