import { FormConfig } from '@jd/x-coreui';
import { MaintainAbnormalDurationApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import React from 'react';

const fetchApi = new MaintainAbnormalDurationApi();

export enum RecordMode {
  VEHICLE = 'VEHICLE',
  STATION = 'STATION',
}
export const SearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'time',
      label: '异常时段',
      type: 'rangeTime',
      format: 'YYYY-MM-DD HH:00',
      labelCol: { span: 3 },
      wrapperCol: { span: 21 },
      xxl: 8,
      xl: 12,
      lg: 16,
    },
    {
      fieldName: 'stationInfo',
      label: '省市站',
      placeholder: '请选择省市站信息',
      type: 'cascader',
      showSearch: true,
      options: [],
      mapRelation: { label: 'name', value: 'id', children: 'children' },
    },
    {
      fieldName: 'deviceName',
      label: '车牌号',
      placeholder: '请输入车牌号',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      options: [],
    },
  ],
  linkRules: {
    fetchVehicle: [
      {
        linkFieldName: 'deviceName',
        rule: 'fetchData',
        fetchFunc: async (val) => {
          const res = await fetchApi.getVehicle();
          if (res.code === HttpStatusCode.Success && Array.isArray(res.data)) {
            return res.data.map((v) => ({ label: v, value: v }));
          } else {
            return [];
          }
        },
      },
    ],
  },
};

export const TableColumns: any[] = [
  {
    title: '异常开始时间',
    dataIndex: 'startTime',
    align: 'center',
    width: 160,
    ellipsis: true,
  },
  {
    title: '异常结束时间',
    width: 160,
    dataIndex: 'endTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '站点',
    width: 100,
    dataIndex: 'stationName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '站点状态',
    width: 70,
    dataIndex: 'stationStatusName',
    align: 'center',
  },
  {
    title: '车牌号',
    width: 100,
    dataIndex: 'deviceName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '供应商',
    width: 80,
    dataIndex: 'supplier',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车架号',
    width: 130,
    dataIndex: 'serialNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '是否车辆故障',
    width: 80,
    dataIndex: 'breakdownName',
    align: 'center',
  },
  {
    title: '异常原因',
    width: 100,
    dataIndex: 'disabledCauseName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车辆不可用时长(小时)',
    width: 130,
    dataIndex: 'disabledDuration',
    align: 'center',
  },
  {
    title: '影响运营时长(小时)',
    width: 130,
    dataIndex: 'notOperationDuration',
    align: 'center',
  },
  {
    title: '最后操作人',
    width: 130,
    dataIndex: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后操作时间',
    width: 180,
    dataIndex: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 80,
    fixed: 'right',
  },
];

export const AddAbnormalDurationFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'abnormalTime',
      label: '异常时段',
      type: 'rangeTime',
      format: 'YYYY-MM-DD HH:00',
      required: true,
      validatorRules: [
        {
          required: true,
          message: '请选择异常时段',
        },
      ],
      labelCol: {
        span: 5,
      },
      wrapperCol: {
        span: 18,
      },
    },
    {
      fieldName: 'recordMode',
      label: '录入方式',
      type: 'radioGroup',
      required: true,
      validatorRules: [
        {
          required: true,
          message: '请选择录入方式',
        },
      ],
      options: [
        {
          label: '按站点',
          value: RecordMode.STATION,
        },
        {
          label: '按车',
          value: RecordMode.VEHICLE,
        },
      ],
      labelCol: {
        span: 5,
      },
      wrapperCol: {
        span: 18,
      },
    },
    {
      fieldName: 'breakdown',
      label: '是否车辆故障',
      type: 'radioGroup',
      required: true,
      validatorRules: [
        {
          required: true,
          message: '请选择是否车辆故障',
        },
      ],
      options: [
        {
          label: '是',
          value: '1',
        },
        {
          label: '否',
          value: '0',
        },
      ],
      labelCol: {
        span: 5,
      },
      wrapperCol: {
        span: 18,
      },
    },
    {
      label: '异常原因',
      childrenList: ['disabledCauseKey', 'addDisabledCause'],
      labelCol: {
        span: 5,
      },
      wrapperCol: {
        span: 18,
      },
      required: true,
    },
    {
      fieldName: 'disabledCauseKey',
      marginLeft: 0,
      marginRight: 0,
      width: '50%',
      isChild: true,
      placeholder: '请选择异常原因',
      type: 'select',
      options: [],
      labelInValue: false,
      validatorRules: [
        {
          required: true,
          message: '请选择异常原因',
        },
      ],
    },
    {
      fieldName: 'addDisabledCause',
      marginLeft: 0,
      marginRight: 0,
      width: '25%',
      isChild: true,
      placeholder: '请选择异常原因',
      type: 'ReactNode',
    },
    {
      fieldName: 'stationBaseId',
      label: '站点',
      placeholder: '请选择站点名称',
      type: 'select',
      showSearch: true,
      required: true,
      labelInValue: false,
      validatorRules: [
        {
          required: true,
          message: '请选择站点名称',
        },
      ],
      labelCol: {
        span: 5,
      },
      wrapperCol: {
        span: 18,
      },
    },
    {
      fieldName: 'deviceName',
      label: '车牌号',
      placeholder: '请输入车牌号',
      type: 'select',
      showSearch: true,
      required: true,
      labelInValue: false,
      validatorRules: [
        {
          required: true,
          message: '请输入车牌号',
        },
      ],
      labelCol: {
        span: 5,
      },
      wrapperCol: {
        span: 18,
      },
    },
  ],
  linkRules: {
    recordMode: [
      {
        linkFieldName: 'stationBaseId',
        rule: 'clear',
      },
      {
        linkFieldName: 'deviceName',
        rule: 'clear',
      },
      {
        linkFieldName: 'stationBaseId',
        rule: 'visible',
        dependenceData: ['STATION'],
      },
      {
        linkFieldName: 'stationBaseId',
        rule: 'fetchData',
        fetchFunc: async (val) => {
          if (val !== RecordMode.STATION) {
            return [];
          }
          const res = await fetchApi.getStation();
          if (res.code === HttpStatusCode.Success && Array.isArray(res.data)) {
            return res.data.map((v) => ({ label: v.name, value: v.id }));
          } else {
            return [];
          }
        },
      },
      {
        linkFieldName: 'deviceName',
        rule: 'visible',
        dependenceData: [RecordMode.VEHICLE],
      },
      {
        linkFieldName: 'deviceName',
        rule: 'fetchData',
        fetchFunc: async (val) => {
          if (val !== RecordMode.VEHICLE) {
            return [];
          }
          const res = await fetchApi.getVehicle();
          if (res.code === HttpStatusCode.Success && Array.isArray(res.data)) {
            return res.data.map((v) => ({ label: v, value: v }));
          } else {
            return [];
          }
        },
      },
    ],
    addDisabledCause: [
      {
        linkFieldName: 'disabledCauseKey',
        rule: 'fetchData',
        fetchFunc: async () => {
          const res = await fetchApi.getDisabledCause();
          if (res.code === HttpStatusCode.Success && Array.isArray(res.data)) {
            return res.data.map((v) => ({ label: v.name, value: v.key }));
          } else {
            return [];
          }
        },
      },
    ],
  },
};

export const AbnormalTableColumns: any[] = [
  {
    title: '异常开始时间',
    dataIndex: 'startTime',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '异常结束时间',
    dataIndex: 'endTime',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '不可用时长(小时)',
    dataIndex: 'disabledDuration',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: (
      <span>
        <span style={{ color: '#FF5153' }}>*</span>影响运营时长(小时)
      </span>
    ),
    dataIndex: 'notOperationDuration',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
];
