import React, { useState } from 'react';
import {
  CommonForm,
  FormConfig,
  CommonTable,
  useTableData,
} from '@jd/x-coreui';
import { SearchConfig, TableColumns } from './utils/columns';
import InsuranceManageApi from '@/fetch/business/insuranceManage';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { useNavigate } from 'react-router-dom';
import VehicleInsuranceModal from './component/VehicleInsuranceModal';
import { InsuredStatus } from '@/utils/constant';
import { saveSearchValues } from '@/redux/reducer/searchForm';
const InsuranceManage = () => {
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const [vehicleInsuranceModalVisible, setVehicleInsuranceModalVisible] =
    useState(false);
  const [
    vehicleInsuranceModalInsuranceId,
    setVehicleInsuranceModalInsuranceId,
  ] = useState<number | null>(null);
  const [searchFormConfig, setSearchFormConfig] =
    useState<FormConfig>(SearchConfig);
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const initSearchCondition = {
    searchForm: {
      policyNumber: null,
      deviceName: null,
      insuredStatus: null,
      effectiveStartTime: null,
      effectiveEndTime: null,
      effectiveTime: null,
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValues.searchValues
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );
  const { tableData, loading } = useTableData(
    {
      ...searchCondition.searchForm,
      pageNum: searchCondition.pageNum,
      pageSize: searchCondition.pageSize,
    },
    InsuranceManageApi.getInsurancePageList,
  );
  const editBtn = (record: any) => {
    switch (record.insuredStatus) {
      case InsuredStatus.EXPIRED:
        return (
          <span style={{ color: '#c2c2c2', cursor: 'not-allowed' }}>编辑</span>
        );
      default:
        return (
          <span
            style={{ color: '#3875F7', cursor: 'pointer' }}
            onClick={() => {
              dispatch(
                saveSearchValues({
                  routeName: location.pathname,
                  searchValues: searchCondition,
                }),
              );
              navigator(
                `/app/insuranceManage/editInsurance/${record.insuranceId}`,
              );
            }}
          >
            编辑
          </span>
        );
    }
  };
  const formatColumns = () => {
    return TableColumns.map((col) => {
      switch (col.dataIndex) {
        case 'deviceCount':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return (
                <>
                  <span
                    style={{ color: '#3875F7', cursor: 'pointer' }}
                    onClick={() => {
                      setVehicleInsuranceModalVisible(true);
                      setVehicleInsuranceModalInsuranceId(record.insuranceId);
                    }}
                  >
                    {record.deviceCount}
                  </span>
                </>
              );
            },
          };
        case 'policyCoverageAmount':
          return {
            ...col,
            render: (text: number) => {
              if (!text) return '-';
              return text.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            },
          };
        case 'operate':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return <>{editBtn(record)}</>;
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };
  const middleBtns: any[] = [
    {
      show: true,
      title: '新建',
      onClick: () => navigator('/app/insuranceManage/addInsurance'),
    },
  ];
  const onSearchClick = (val: any) => {
    let effectiveStartTime: any;
    let effectiveEndTime: any;
    if (val?.effectiveTime && val?.effectiveTime.length > 0) {
      const startMoment = val?.effectiveTime[0];
      if (startMoment) {
        effectiveStartTime = startMoment.format('YYYY-MM-DD');
      }
      const endMoment = val?.effectiveTime[1];
      if (endMoment) {
        effectiveEndTime = endMoment.format('YYYY-MM-DD');
      }
    }
    const data = {
      searchForm: {
        ...val,
        effectiveStartTime,
        effectiveEndTime,
      },
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(data);
  };
  const onResetClick = () => {
    setSearchCondition({ ...initSearchCondition });
  };
  return (
    <div>
      <CommonForm
        formConfig={searchFormConfig}
        layout={'inline'}
        defaultValue={searchCondition.searchForm}
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={onResetClick}
      />
      <CommonTable
        columns={formatColumns()}
        rowKey={'insuranceId'}
        middleBtns={middleBtns}
        searchCondition={searchCondition}
        onPageChange={(value: any) => {
          setSearchCondition(value);
        }}
        loading={loading}
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
      />
      <VehicleInsuranceModal
        visible={vehicleInsuranceModalVisible}
        onCancel={() => {
          setVehicleInsuranceModalVisible(false);
          setVehicleInsuranceModalInsuranceId(null);
        }}
        onOk={() => {
          setVehicleInsuranceModalVisible(false);
          setVehicleInsuranceModalInsuranceId(null);
        }}
        insuranceId={vehicleInsuranceModalInsuranceId || null}
      />
    </div>
  );
};
export default InsuranceManage;
