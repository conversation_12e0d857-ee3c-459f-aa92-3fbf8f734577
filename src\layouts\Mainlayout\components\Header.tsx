import React, { useEffect, useState } from 'react';
import { RootState } from '@/redux/store';
import { useSelector, useDispatch } from 'react-redux';
import { debounce } from '@/utils/utils';
import { CommonApi } from '@/fetch/business';
import { Select, message } from 'antd';
import { HttpStatusCode } from '@/fetch/core/constant';
import { changeUserInfo } from '@/redux/reducer/common';
import { passportLogout } from '@/utils/authHelper';
const Header = () => {
  const fetchApi = new CommonApi();
  const dispatch = useDispatch();
  const { userName, companyNumber, companyName } = useSelector(
    (state: RootState) => state.common,
  );
  const [userNameList, setUserNameList] = useState<any[]>([]);
  useEffect(() => {
    userName && getUserList();
  }, [userName]);
  const getUserList = async () => {
    const res = await fetchApi.getUserNameList(userName!);
    if (res.code === HttpStatusCode.Success) {
      setUserNameList(res.data);
    }
  };

  const changeUserName = async (value: any) => {
    if (value === userName) {
      return;
    }
    const data = userNameList.filter((item) => item.userName === value);
    const res = await fetchApi.changeUser(value);
    if (res.code === HttpStatusCode.Success) {
      dispatch(changeUserInfo(data[0]));
      message.success('切换账号成功！');
    } else {
      message.error(res.message);
    }
  };
  return (
    <>
      <div className="main-head-content">
        <div>无人车基础数据管理平台</div>
        <div className="basic-user-content">
          <img
            src={require('@/assets/image/common/userIcon.png')}
            className="avatar"
          ></img>
          <div className="username">
            <Select
              options={userNameList.map((item: any) => {
                return {
                  label: `${item.userName}/${item.companyName}`,
                  value: item.userName,
                };
              })}
              value={`${userName}/${companyName}`}
              onSelect={(value: string) => changeUserName(value)}
            />
          </div>
          <div className="log-out-btn" onClick={passportLogout}>
            退出
          </div>
        </div>
      </div>
    </>
  );
};
export default React.memo(Header);
