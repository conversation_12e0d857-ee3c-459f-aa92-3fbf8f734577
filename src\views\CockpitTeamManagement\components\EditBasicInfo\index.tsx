import React, { useState, useEffect } from 'react';
import { Form, Input, FormInstance } from 'antd';

interface Props {
  form: FormInstance;
  data: any;
}

const EditBasicInfo = (props: Props) => {
  const { form, data } = props;

  useEffect(() => {
    form.setFieldsValue(data);
  }, [data]);

  return (
    <Form form={form} labelCol={{ span: 3 }} wrapperCol={{ span: 19 }}>
      {data.cockpitTeamNumber ? (
        <Form.Item label="远驾团队编号">{data.cockpitTeamNumber}</Form.Item>
      ) : (
        <Form.Item label="远驾团队编号">系统生成</Form.Item>
      )}
      <Form.Item
        label="远驾团队名称"
        name="cockpitTeamName"
        rules={[{ required: true, message: '请输入远驾团队名称' }]}
      >
        <Input
          placeholder="请输入远驾团队名称，最多输入30位"
          maxLength={30}
          allowClear
        />
      </Form.Item>
    </Form>
  );
};

export default React.memo(EditBasicInfo);
