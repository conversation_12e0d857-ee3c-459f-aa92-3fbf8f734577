import React, { useState, useEffect } from 'react';
import { CommonTable, CommonForm, TableOperateBtn } from '@/components';
import { searchConfig, tableColumns } from './utils/columns';
import { ResourceManageApi } from '@/fetch/business';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/store';
import { useNavigate } from 'react-router-dom';
import { saveSearchValues } from '@/redux/reducer/searchForm';
import { HttpStatusCode } from '@/fetch/core/constant';
import { message, Popconfirm } from 'antd';
import { PageType } from '@/utils/EditTitle';
import { TableListType } from '@/utils/constant';

const ResourceManage = () => {
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const fetchApi = new ResourceManageApi();
  const { historySearchValues } = useSelector((state: RootState) => ({
    historySearchValues: state.searchForm,
  }));
  const initSearchCondition = {
    searchForm: {
      name: '',
      app: null,
      resourceCode: null,
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValues.searchValues
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [tableData, setTableData] = useState<TableListType>({
    list: [],
    total: 0,
    pages: 0,
  });
  const [expandedKey, setExpandedKey] = useState<number[]>([]);

  useEffect(() => {
    fetchTableData();
  }, [searchCondition]);

  const middleBtns: any[] = [
    {
      show: true,
      title: '新建资源',
      onClick: () => {
        localStorage.setItem('ExpandedKey', JSON.stringify(expandedKey));
        handleEdit(PageType.ADD);
      },
    },
  ];
  const formateColumns = () => {
    return tableColumns.map((col) => {
      switch (col.dataIndex) {
        case 'operation':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return (
                <div className="operate">
                  <TableOperateBtn
                    title="编辑"
                    handleClick={() => {
                      localStorage.setItem(
                        'ExpandedKey',
                        JSON.stringify(expandedKey),
                      );
                      handleEdit(PageType.EDIT, record.number);
                    }}
                  />
                  <Popconfirm
                    placement="left"
                    title={'确定删除'}
                    onConfirm={() => {
                      deleteClick(record.number);
                    }}
                    okText="确定"
                    cancelText="取消"
                    overlayStyle={{ maxWidth: 800 }}
                  >
                    <a style={{ marginLeft: '5px', marginRight: '5px' }}>
                      删除
                    </a>
                  </Popconfirm>
                  <TableOperateBtn
                    title="添加子资源"
                    handleClick={() => {
                      localStorage.setItem(
                        'ExpandedKey',
                        JSON.stringify(expandedKey),
                      );
                      navigator(
                        `/app/resourceManage/edit?id=${record.number}&type=${PageType.ADD}&app=${record.app}`,
                      );
                    }}
                  />
                </div>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  const fetchTableData = async () => {
    try {
      setLoading(true);
      const params = {
        searchForm: {
          name: searchCondition.searchForm.name,
          app: searchCondition.searchForm.app?.value,
          resourceCode: searchCondition.searchForm.resourceCode,
        },
        pageNum: searchCondition.pageNum,
        pageSize: searchCondition.pageSize,
      };
      const response: any = await fetchApi.fetchTableList(params);
      if (response.code === HttpStatusCode.Success) {
        if (!response.data || (response.data && !response.data.records)) {
          response.data.records = [];
        }
        setTableData({
          pages: response.data.pages,
          total: response.data.total,
          list: recursionTree(response.data.list),
        });
      } else {
        setTableData({
          ...tableData,
        });
      }
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };
  /**
   * 递归循环,hasChildren为false时,删除children字段,否则tree会认为还有下一级
   * @param {any}data
   * @return {Array}Value
   */
  const recursionTree = (data: any) => {
    const arr =
      data &&
      data.map((item: any) => {
        if (item.hasChildren) {
          recursionTree(item.children);
        } else {
          delete item.children;
        }
        return item;
      });
    return arr;
  };

  const deleteClick = async (number: any) => {
    const response: any = await fetchApi.delResource(number);
    if (response.code === HttpStatusCode.Success) {
      message.success(response.message);
      fetchTableData();
    } else {
      message.error(response.message);
    }
  };

  const handleEdit = (type: PageType, id?: number) => {
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: searchCondition,
      }),
    );
    navigator(
      id
        ? `/app/resourceManage/edit?type=${type}&id=${id}`
        : `/app/resourceManage/edit?type=${type}`,
    );
  };

  const onSearchClick = (val) => {
    const data = {
      searchForm: val,
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(data);
  }; /* 过滤空值、空字符串为“-” */
  const filteredDataSource = (list: any[]) => {
    return list.map((item: any) => {
      const newObjc: any = {};
      Object.entries(item).forEach(([key, value], index) => {
        let noEmptyValue = value;
        if (value === null) {
          noEmptyValue = '-';
        } else if (`${value}`.replace(/(^s*)|(s*$)/g, '').length <= 0) {
          noEmptyValue = '-';
        }
        newObjc[key] = noEmptyValue;
      });
      return newObjc;
    });
  };
  const onExpand = (_: any, key: any) => {
    const tempArr = expandedKey.length > 0 ? [...expandedKey] : [];
    const idx = expandedKey.indexOf(key.number);
    if (idx > -1) {
      tempArr.splice(idx, 1);
    } else {
      tempArr.push(key.number);
    }
    setExpandedKey(tempArr);
  };
  return (
    <>
      <CommonForm
        formConfig={searchConfig}
        defaultValue={searchCondition.searchForm}
        layout="inline"
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={() => setSearchCondition({ ...initSearchCondition })}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        expandable={{
          indentSize: 25,
          onExpand: (expanded, record) => {
            if (expanded) {
              setExpandedRowKeys(expandedRowKeys.concat(record.number));
            } else {
              setExpandedRowKeys(
                expandedRowKeys.filter((item) => item !== record.number),
              );
            }
          },
          expandedRowKeys: expandedRowKeys,
        }}
        columns={formateColumns()}
        loading={loading}
        rowKey={'number'}
        middleBtns={middleBtns}
        searchCondition={searchCondition}
        onPageChange={(value: any) => setSearchCondition(value)}
      />
    </>
  );
};

export default React.memo(ResourceManage);
