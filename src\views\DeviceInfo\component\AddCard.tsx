/* eslint-disable no-unused-vars */

import {
  Col,
  Form,
  FormInstance,
  Input,
  Row,
  Select,
  Button,
  Table,
  Modal,
  message,
  Popconfirm,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { CustomButton } from '@/components';

interface NetCard {
  id: any;
  cardNo: string | null;
  telecomOperatorName: string | null;
  telecomOperator: string | null;
  enable: number;
  newAdd?: boolean;
}

const AddCard = ({
  disable,
  form,
  telecomOperatorList,
  initValues,
}: {
  disable?: boolean;
  form: FormInstance;
  telecomOperatorList: any[];
  initValues?: any;
}) => {
  const [cardList, setCardList] = useState<NetCard[]>([]);
  const usageNameColums: any[] = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
      width: 90,
      render: (text: any, record: any, index: number) => index + 1,
    },
    {
      title: '运营商',
      dataIndex: 'telecomOperatorName',
      align: 'center',
      ellipsis: true,
    },
    { title: '卡号', dataIndex: 'cardNo', align: 'center', ellipsis: true },
  ];
  const [colums, setColums] = useState<any[]>(usageNameColums);
  useEffect(() => {
    if (!disable) {
      setColums([
        ...usageNameColums,
        {
          title: '操作',
          dataIndex: '',
          align: 'center',
          width: 150,
          // eslint-disable-next-line react/display-name
          render: (params: any) => {
            return (
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-around',
                }}
              >
                <a
                  onClick={() => {
                    setShowEditCardModal({
                      card: params,
                      show: true,
                    });
                  }}
                >
                  编辑
                </a>
                <Popconfirm
                  title="是否确认删除此条物流网卡号"
                  onConfirm={() => {
                    deleteCardNo(params.id, params.newAdd);
                  }}
                >
                  <a style={{ color: 'red' }}>删除</a>
                </Popconfirm>
              </div>
            );
          },
        },
      ]);
    } else {
      setColums([...usageNameColums]);
    }
  }, [disable, cardList]);

  const [showEditCardModal, setShowEditCardModal] = useState<{
    show: boolean;
    card: NetCard | null;
  }>({
    show: false,
    card: null,
  });
  const [editForm] = Form.useForm();
  const makeTableDatasource = () => {
    return cardList.filter((item) => item.enable === 1);
  };

  const deleteCardNo = async (cardNoId: any, newAdd: boolean) => {
    if (newAdd) {
      setCardList([...cardList.filter((item) => item.id !== cardNoId)]);
    } else {
      cardList.forEach((item) => {
        if (item.id === cardNoId) {
          item.enable = 0;
        }
      });
      setCardList([...cardList]);
    }
  };

  const checkRepeat = (editValue: any) => {
    const { card } = showEditCardModal;
    let repeated = false;
    if (card) {
      editValue.id = card.id;
      const repeatedList = cardList.filter(
        (item) =>
          item.id !== editValue.id &&
          `${item.cardNo}-${item.telecomOperator}` ===
            `${editValue.cardNo}-${editValue.telecomOperator}`,
      );
      if (repeatedList.length > 0) {
        repeated = true;
      }
    } else {
      const repeatedList = cardList.filter(
        (item) =>
          `${item.cardNo}-${item.telecomOperator}` ===
          `${editValue.cardNo}-${editValue.telecomOperator}`,
      );
      if (repeatedList.length === 1) {
        repeated = true;
      }
    }
    return repeated;
  };

  const onModalSubmit = async () => {
    try {
      const editValue = await editForm.validateFields();
      if (checkRepeat(editValue)) {
        message.error('新建物流网卡号已存在');
        return;
      }
      if (!showEditCardModal.card) {
        editValue.newAdd = true;
        editValue.enable = 1;
        editValue.id = `${2021 + cardList.length}`;
        setCardList([...cardList.concat([editValue])]);
      } else {
        setCardList([
          ...cardList.map((item) => {
            if (item.id === showEditCardModal.card?.id) {
              item.cardNo = editValue.cardNo;
              item.telecomOperator = editValue.telecomOperator;
              item.telecomOperatorName = editValue.telecomOperatorName;
            }
            return item;
          }),
        ]);
      }
      editForm.resetFields();
      setShowEditCardModal({
        card: null,
        show: false,
      });
    } catch (e) {
      //
    }
  };

  const onModalCancel = () => {
    editForm.resetFields();
    setShowEditCardModal({
      card: null,
      show: false,
    });
  };

  useEffect(() => {
    form.setFieldsValue({
      vehicleCardNoList: cardList,
    });
  }, [JSON.stringify(cardList)]);

  useEffect(() => {
    if (initValues) {
      setCardList(
        initValues.map((item: any) => {
          return {
            ...item,
            enable:
              item.enable === null || item.enable === undefined
                ? 1
                : item.enable,
          };
        }),
      );
    }
  }, [initValues]);

  useEffect(() => {
    editForm.setFieldsValue({
      ...showEditCardModal.card,
    });
  }, [showEditCardModal]);

  return (
    <>
      <Form.Item name="vehicleCardNoList" label="添加物流网卡号">
        <>
          {disable ? null : (
            <CustomButton
              disable={disable}
              title="新建"
              height={35}
              onSubmitClick={() => {
                setShowEditCardModal({
                  card: null,
                  show: true,
                });
              }}
            />
          )}
          <div style={{ marginTop: '10px' }}>
            <Table
              rowKey={(recored) => recored.id}
              pagination={false}
              size="small"
              bordered
              columns={colums.filter((item) =>
                disable ? item.dataIndex != 'operate' : true,
              )}
              dataSource={makeTableDatasource()}
            />
          </div>
          <Modal
            width="50vw"
            maskClosable={false}
            title={showEditCardModal.card != null ? '编辑卡号' : '新建卡号'}
            closable={false}
            visible={showEditCardModal.show}
            okText="确定"
            cancelText="取消"
            onOk={onModalSubmit}
            onCancel={onModalCancel}
            forceRender
          >
            <Form labelCol={{ span: 4 }} form={editForm}>
              <Form.Item
                name="telecomOperator"
                style={{ height: 0, margin: 0 }}
              >
                <div></div>
              </Form.Item>
              <Form.Item
                name="telecomOperatorName"
                label="运营商"
                rules={[{ required: true, message: '请选择运营商' }]}
              >
                <Select
                  options={telecomOperatorList?.map((item: any) => {
                    return { label: item.name, value: item.code };
                  })}
                  placeholder="请选择运营商"
                  maxLength={50}
                  onChange={(value: any) => {
                    editForm.setFieldsValue({
                      telecomOperator: value,
                      telecomOperatorName: telecomOperatorList.filter(
                        (item: any) => item.code === value,
                      )[0].name,
                    });
                  }}
                />
              </Form.Item>
              <Form.Item
                name="cardNo"
                label="物流网卡号"
                rules={[
                  { required: true, message: '请输入物流网卡号' },
                  // { pattern: /^[1][0-9]{10}$/, message: '请输入合法的手机号' },
                ]}
              >
                <Input
                  placeholder="请输入物流网卡号"
                  allowClear
                  maxLength={11}
                  onBlur={(e) => {
                    const value = `${e.target.value || ''}`;
                    editForm.setFieldsValue({
                      cardNo: value.trim(),
                    });
                  }}
                />
              </Form.Item>
            </Form>
          </Modal>
        </>
      </Form.Item>
    </>
  );
};

export default React.memo(AddCard);
