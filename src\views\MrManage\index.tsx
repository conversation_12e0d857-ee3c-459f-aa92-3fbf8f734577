import React, { useState, useEffect, useRef } from 'react';
import { useForm } from 'antd/lib/form/Form';
import {
  CommonTable,
  CommonForm,
  FormConfig,
  useTableData,
} from '@jd/x-coreui';
import { showModal } from '@/components';
import {
  message,
  Select,
  Tabs,
  Checkbox,
  Modal,
  Form,
  Radio,
  InputNumber,
  Input,
} from 'antd';
import { TableOperateBtn } from '@/components';
import { searchConfig, tableColumns } from './utils/columns';
import { MrManageApi, CommonApi } from '@/fetch/business';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/store';
import { useNavigate } from 'react-router-dom';
import { YESNO, MrTestStatus, MrStatus, MRFocusLevel } from '@/utils/enum';
import { saveSearchValues } from '@/redux/reducer/searchForm';
import { HttpStatusCode } from '@/fetch/core/constant';
import { PageType } from '@/utils/EditTitle';
import { formatDateStringToSecond, formatDateToSecond } from '@/utils/utils';
import { TestResultValue } from './utils/constant';
import './index.scss';
import { buildURL, buildFetchInit } from '@/fetch/core/util';
import { AnyFunc } from '@/global';
import { formatLocation } from '@/utils/utils';
import wrapVisibleCommonForm from '@/components/CommonForm/wrapVisibleForm';
const WrapCommonForm = wrapVisibleCommonForm(CommonForm);
import dayjs from 'dayjs';
const isoWeek = require('dayjs/plugin/isoWeek');
dayjs.extend(isoWeek);
const _format = 'YYYY-MM-DD HH:mm:ss';
const { confirm } = Modal;

const MrManage = () => {
  const CheckboxGroup = Checkbox.Group;
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const fetchApi = new MrManageApi();
  const commonFetchApi = new CommonApi();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const urlData = formatLocation(window.location.search);

  const initSearchCondition = {
    searchForm: {
      time: null,
      startTime: null,
      endTime: null,
      mrNumber: urlData.mrNumber,
      fixJiraKey: null,
      isSubmitTest: null,
      module: null,
      testResultList: [],
      mrBranch: null,
      testPassStartDate: null,
      testPassEndDate: null,
      testPassDate: null,
      mrReleaseStartDate: null,
      mrReleaseEndDate: null,
      mrReleaseDate: null,
      mrStatus: null,
      isDependOnConf: null,
      isSpecificTest: null,
      specificTestName: null,
      developer: null,
    },
    pageNum: 1,
    pageSize: 20,
  };
  const searchFormRef = useRef<any>(null);
  const searchFormDomRef = useRef<any>(null);
  const [, forceUpdate] = useState<number>(0);
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValues.searchValues
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );
  const [activeTabKey, setActiveTabKey] = useState<string>(() => {
    return historySearchValues.searchValues?.activeTabKey
      ? historySearchValues.searchValues?.activeTabKey
      : 'null/null';
  });
  const [selectMrInfo, setSelectMrInfo] = useState<{
    selectedRowKeys: any[];
    selectedRows: any[];
    clearFunc: AnyFunc;
  }>({
    selectedRowKeys: [],
    selectedRows: [],
    clearFunc: () => {},
  });
  const [searchFormConfig, setSearchFormConfig] =
    useState<FormConfig>(searchConfig);
  // 控制弹出框是否弹出
  const [syncJira, setSyncJira] = useState(false);
  const [syncJiraInfo, setSyncJiraInfo] = useState<any>({});
  const [syncJiraLoading, setSyncJiraLoading] = useState(false);
  const [showMRMergeTime, setShowMRMergeTime] = useState<boolean>(true);
  const [updateAttention, setUpdateAttention] = useState<any>({
    modalOpen: false,
    defaultList: [],
    optionalList: [],
    checkedList: [],
  });
  const { tableData, loading, reloadTable } = useTableData(
    searchCondition,
    fetchApi.fetchTableList,
  );
  const [versionForm] = useForm();
  useEffect(() => {
    formatSearchConfig();
  }, []);

  const formatSearchConfig = () => {
    Promise.all([
      fetchApi.getMrBranchList(),
      commonFetchApi.getCommonDropDown({
        keyList: ['MR_TEST_RESULT', 'MR_STATUS'],
      }),
    ]).then((res) => {
      const [branchRes, commonRes] = res;
      setSearchFormConfig({
        ...searchFormConfig,
        fields: searchFormConfig.fields.map((v) => {
          if (
            v.fieldName === 'mrBranch' &&
            branchRes.code === HttpStatusCode.Success
          ) {
            return {
              ...v,
              options: branchRes.data.map((l) => ({
                value: l,
                label: l,
              })),
            };
          }
          if (
            v.fieldName === 'testResultList' &&
            commonRes.code === HttpStatusCode.Success
          ) {
            return {
              ...v,
              options: commonRes.data.mrTestResultList.map((l) => ({
                value: l.code,
                label: l.name,
              })),
            };
          }
          if (
            v.fieldName === 'mrStatus' &&
            commonRes.code === HttpStatusCode.Success
          ) {
            return {
              ...v,
              options: commonRes.data.mrStatusList.map((l) => ({
                value: l.code,
                label: l.name,
              })),
            };
          }
          return v;
        }),
      });
    });
  };
  const getCurrentWeek = (opt: 'current' | 'pre') => {
    if (opt == 'current') {
      const lastFriday = dayjs().startOf('isoWeek').subtract(3, 'day');
      const thisThursday = dayjs().startOf('isoWeek').add(3, 'day');
      return {
        label: `本周(${lastFriday.format('MM/DD')}-${thisThursday.format(
          'MM/DD',
        )})`,
        key: `${lastFriday.startOf('day').format(_format)}/${thisThursday
          .endOf('day')
          .format(_format)}`,
      };
    } else {
      const twoWeeksAgoFriday = dayjs().startOf('isoWeek').subtract(10, 'day');
      const lastThursday = dayjs().startOf('isoWeek').subtract(4, 'day');
      return {
        label: `上周(${twoWeeksAgoFriday.format('MM/DD')}-${lastThursday.format(
          'MM/DD',
        )})`,
        key: `${twoWeeksAgoFriday.startOf('day').format(_format)}/${lastThursday
          .endOf('day')
          .format(_format)}`,
      };
    }
  };
  const calculateTime = () => {
    return [
      {
        key: 'null/null',
        label: '全部',
      },
      {
        ...getCurrentWeek('current'),
      },
      {
        ...getCurrentWeek('pre'),
      },
    ];
  };

  const formateColumns = () => {
    return tableColumns.map((col) => {
      switch (col.dataIndex) {
        case 'fixJiraKeyList':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              const { fixJiraKeyList } = record;
              return (
                <div style={{ display: 'flex', flexDirection: 'column' }}>
                  {fixJiraKeyList !== null && fixJiraKeyList !== '-'
                    ? fixJiraKeyList.map((item: any) => (
                        <a
                          key={item.jiraKey}
                          onClick={() => {
                            window.open(item.jiraUrl);
                          }}
                        >
                          {item.jiraKey}；
                        </a>
                      ))
                    : '-'}
                </div>
              );
            },
          };
        // case 'associationMrIds':
        //   return {
        //     ...col,
        //     render: (text: any, record: any, index: number) => {
        //       const { associationMrInfoList } = record;
        //       return (
        //         <div style={{ display: 'flex', flexDirection: 'column' }}>
        //           {associationMrInfoList?.length > 0
        //             ? associationMrInfoList.map((v) => {
        //                 return (
        //                   <a
        //                     key={v}
        //                     onClick={() => {
        //                       if (!v.mrId) {
        //                         message.error(`MR_ID【${v.mrNumber}】不存在`);
        //                         return;
        //                       }
        //                       navigator(
        //                         `/app/mrManage/edit?type=${PageType.EDIT}&id=${v.mrId}`,
        //                       );
        //                     }}
        //                   >
        //                     {v.mrNumber}；
        //                   </a>
        //                 );
        //               })
        //             : '-'}
        //         </div>
        //       );
        //     },
        //   };
        case 'operation':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return (
                <div className="operate">
                  <TableOperateBtn
                    title="编辑"
                    handleClick={() => {
                      dispatch(
                        saveSearchValues({
                          routeName: location.pathname,
                          searchValues: {
                            ...searchCondition,
                            activeTabKey,
                          },
                        }),
                      );
                      navigator(
                        `/app/mrManage/edit?type=${PageType.EDIT}&id=${record.mrId}`,
                      );
                    }}
                  />
                  {record.fixJiraKeyList &&
                    record.fixJiraKeyList.length > 0 &&
                    (record.testResult == null ? (
                      ''
                    ) : (
                      <>
                        {record.isSyncJira == YESNO.NO &&
                        (record.testResult === MrTestStatus.PASS ||
                          record.testResult === MrTestStatus.NOT_PASS_RETAIN ||
                          record.testResult ===
                            MrTestStatus.NOT_PASS_ROLLBACK) ? (
                          <TableOperateBtn
                            title="同步"
                            tooltipText="可实现同步缺陷状态、或拖拽行云迭代卡片状态"
                            handleClick={() => {
                              setSyncJira(true);
                              setSyncJiraInfo(record);
                            }}
                          />
                        ) : (
                          '同步'
                        )}
                      </>
                    ))}
                </div>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  const middleBtns: any[] = [
    {
      show: true,
      title: '获取新合入MR',
      key: 'getNewMR',
      onClick: () => handleFetchNewMR(),
    },
    {
      show: true,
      title: '导出',
      key: 'exportMR',
      onClick: () => handleExportMR(),
    },
    {
      show: true,
      title: '批量待发版',
      key: 'batchToBeReleased',
      onClick: () => handleBatchToBeReleased(),
    },
    {
      show: true,
      title: '批量已发版',
      key: 'batchReleased',
      onClick: () => handleBatchReleased(),
    },
    {
      show: true,
      title: '批量标记关注程度底色',
      key: 'batchMarkerColor',
      onClick: () => handleMarkMr(),
    },
  ];

  const handleFetchNewMR = async () => {
    const response: any = await fetchApi.updataMR();
    if (response.code === HttpStatusCode.Success) {
      message.success(response.message);
      reloadTable();
    } else {
      message.error(response.message);
    }
  };

  //导出
  const handleExportMR = async () => {
    if (selectMrInfo.selectedRowKeys.length <= 0) {
      message.error('请至少选择一条数据进行导出');
      return;
    }
    const res = await fetchApi.getExportFiledList();
    if (res.code === HttpStatusCode.Success && res.data) {
      setUpdateAttention({
        modalOpen: true,
        defaultList: res.data.defaultList || [],
        optionalList: res.data.optionalList || [],
        checkedList: [],
      });
    }
  };

  const makeDownloadUrl = () => {
    const params: RequestOptions = {
      path: '/k2/management/merge_request_test/merge_request_info_list_export',
      method: 'POST',
      body: {
        mrIds: selectMrInfo.selectedRowKeys,
        exportFields: updateAttention.checkedList.concat(
          updateAttention.defaultList.map((v) => v.id),
        ),
      },
      contentType: 'application/json',
    };
    window
      .fetch(buildURL(params), buildFetchInit(params))
      .then((res) => {
        return res.blob();
      })
      .then((blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `合并请求测试信息-${new Date().getTime()}.xlsx`;
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        selectMrInfo.clearFunc();
        setSelectMrInfo({
          selectedRowKeys: [],
          selectedRows: [],
          clearFunc: () => {},
        });
        setUpdateAttention({
          modalOpen: false,
          defaultList: [],
          optionalList: [],
          checkedList: [],
        });
      });
  };

  //批量待发版
  const handleBatchToBeReleased = () => {
    if (selectMrInfo.selectedRowKeys?.length <= 0) {
      message.error('请勾选至少1条【测试通过】或【测试不通过】的MR操作！');
      return;
    }
    let contentText = '';
    const testResultList = selectMrInfo.selectedRows
      .filter(
        (row) =>
          row.testResult === MrTestStatus.PASS ||
          row.testResult === MrTestStatus.NOT_PASS_ROLLBACK ||
          row.testResult === MrTestStatus.NOT_PASS_RETAIN,
      )
      .map((row) => row.mrId);
    if (testResultList.length <= 0) {
      message.error('MR状态为【测试通过、测试不通过】才可操作批量！');
      return;
    }
    if (selectMrInfo.selectedRowKeys.length !== testResultList.length) {
      contentText =
        testResultList.length +
        '条MR状态可修改为【待发版】,其他MR测试结果不符合条件，请确认是否批量修改成待发版？';
    } else {
      contentText =
        testResultList.length +
        '条MR状态可修改为【待发版】,请确认是否批量修改成待发版?';
    }
    confirm({
      title: <div style={{ textAlign: 'center' }}>批量待发版</div>,
      content: contentText,
      icon: null,
      onOk() {
        fetchApi.batchToBeReleased(testResultList).then((res) => {
          if (res.code === HttpStatusCode.Success) {
            reloadTable();
            selectMrInfo.clearFunc();
            setSelectMrInfo({
              selectedRowKeys: [],
              selectedRows: [],
              clearFunc: () => {},
            });
            message.success('操作成功!');
          } else {
            message.error('操作失败，原因：' + res.message);
          }
        });
      },
      onCancel() {},
    });
  };
  /**
   * 获取版本号下拉框列表
   */
  const getRoverVersion = async () => {
    const res = await fetchApi.getRoverVersion();
    if (res.code === HttpStatusCode.Success) {
      return res.data?.map((item: any) => {
        return {
          label: item.version,
          value: item.version,
        };
      });
    }
    message.error('操作失败，原因：' + res.message);
    return [];
  };
  //批量已发版
  const handleBatchReleased = async () => {
    if (selectMrInfo.selectedRowKeys?.length <= 0) {
      message.error('请勾选至少1条【待发版】的MR操作！');
      return;
    }
    let contentText = '';
    const mrStatusList = selectMrInfo.selectedRows
      .filter((row) => row.mrStatus === MrStatus.TO_BE_RELEASE)
      .map((row) => row.mrId);
    if (mrStatusList.length <= 0) {
      message.error('MR状态为【待发版】才可操作批量！');
      return;
    }
    if (selectMrInfo.selectedRowKeys.length !== mrStatusList.length) {
      contentText =
        mrStatusList.length +
        '条MR状态可修改为【已发版】,其他状态不是【待发版】，请选择发布版本号';
    } else {
      contentText =
        mrStatusList.length + '条MR状态可修改为【已发版】,请选择发布版本号';
    }
    const versionNameOptions = await getRoverVersion();
    versionForm.setFieldsValue({ releaseVersion: null, releaseWeek: null });
    confirm({
      title: <div style={{ textAlign: 'center' }}>批量已发版</div>,
      content: (
        <Form form={versionForm}>
          <p>{contentText}</p>
          <Form.Item
            label="发布版本"
            name="releaseVersion"
            labelAlign="left"
            rules={[{ required: true, message: '请选择一个发布版本' }]}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            style={{ marginBottom: 0 }}
            required={false} // 取消默认的必填标记
          >
            <Select
              style={{ width: '250px', alignSelf: 'auto' }}
              placeholder={'请选择Rover版本号'}
              options={versionNameOptions}
              showSearch
              allowClear
              filterOption={(input, option) => {
                const label: any = option?.label || '';
                return label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              }}
            />
          </Form.Item>
          <Form.Item
            label="发版周(W)"
            name="releaseWeek"
            labelAlign="left"
            rules={[{ required: true, message: '请输入发版周数' }]}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            style={{ marginBottom: 0, marginTop: '10px' }}
            required={false} // 取消默认的必填标记
          >
            <InputNumber
              min={1}
              style={{ width: '250px' }}
              precision={0}
              maxLength={2}
              controls={false}
              placeholder={'请输入发版周数'}
            />
          </Form.Item>
        </Form>
      ),
      icon: null,
      onOk() {
        versionForm
          .validateFields()
          .then((values) =>
            fetchApi.batchReleased({
              mrIdList: mrStatusList,
              roverVersion: values.releaseVersion,
              releaseWeek: values.releaseWeek,
            }),
          )
          .then((res) => {
            if (res.code === HttpStatusCode.Success) {
              reloadTable();
              selectMrInfo.clearFunc();
              setSelectMrInfo({
                selectedRowKeys: [],
                selectedRows: [],
                clearFunc: () => {},
              });
              message.success('操作成功!');
            } else {
              message.error('操作失败，原因：' + res.message);
            }
          })
          .catch((errorInfo) => {
            console.log(errorInfo);
          });
      },
      onCancel() {},
    });
  };

  const handleMarkMr = () => {
    if (selectMrInfo.selectedRowKeys.length <= 0) {
      message.error('请至少选择一条数据');
      return;
    }
    let selected;
    showModal({
      title: <div style={{ textAlign: 'center' }}>批量标记MR关注程度</div>,
      content: (
        <div>
          <h4>{`已选MR数：${selectMrInfo.selectedRowKeys.length}`}</h4>
          <h4>已选MR标记关注等级</h4>
          <Radio.Group
            options={[
              {
                value: MRFocusLevel.FOCUS_ON,
                label: '重点关注（MR内容改为红底色）',
              },
              {
                value: MRFocusLevel.NOT_FOCUS_ON,
                label: '不关注（MR内容改为灰底色）',
              },
              {
                value: MRFocusLevel.NORMAL,
                label: '正常（MR内容改为默认底色）',
              },
            ]}
            onChange={(e) => (selected = e.target.value)}
          />
        </div>
      ),
      footer: {
        showOk: true,
        showCancel: true,
        okFunc: async (cb) => {
          if (!selected) {
            message.error('请选择关注等级');
            return;
          }
          const res = await fetchApi.updateAttentionLevel({
            attentionLevel: selected,
            mrIds: selectMrInfo.selectedRowKeys,
          });
          if (res.code === HttpStatusCode.Success) {
            message.success('操作成功！');
            reloadTable();
            selectMrInfo.clearFunc();
            setSelectMrInfo({
              selectedRowKeys: [],
              selectedRows: [],
              clearFunc: () => {},
            });
            cb();
          } else {
            message.error(res.message);
          }
        },
        cancelFunc: (cb) => {
          cb();
        },
      },
    });
  };

  const onSearchClick = (val) => {
    const searchVal = formatSearchVal(val);
    const data = {
      searchForm: {
        ...searchVal,
        endTime: showMRMergeTime
          ? searchVal.endTime
          : searchCondition.searchForm.endTime,
        startTime: showMRMergeTime
          ? searchVal.startTime
          : searchCondition.searchForm.startTime,
      },
      pageNum: 1,
      pageSize: 20,
    };
    setSearchCondition(data);
  };

  const syncJiraClick = async () => {
    setSyncJiraLoading(true);
    const response: any = await fetchApi.syncJira(syncJiraInfo.mrId);
    if (response.code === HttpStatusCode.Success) {
      setSyncJiraLoading(false);
      setSyncJira(false);
      message.success('操作成功！');
      reloadTable();
    } else {
      setSyncJiraLoading(false);
      setSyncJira(false);
      message.error(response.message);
    }
  };

  const getRowClassName = (record, index) => {
    if (record.attentionLevel === MRFocusLevel.FOCUS_ON) {
      return 'table-row-red';
    }
    if (record.attentionLevel === MRFocusLevel.NOT_FOCUS_ON) {
      return 'table-row-grey';
    }
    return '';
  };

  const changeTimeTab = (key) => {
    setActiveTabKey(key);
    const val = searchFormRef.current.getFieldsValue();
    const searchVals = formatSearchVal(val);
    selectMrInfo.clearFunc();
    setSelectMrInfo({
      selectedRowKeys: [],
      selectedRows: [],
      clearFunc: () => {},
    });
    if (key === 'null/null') {
      setShowMRMergeTime(true);
      searchFormRef.current.setFieldsValue({
        time: null,
      });
      setSearchCondition({
        pageNum: 1,
        pageSize: 20,
        searchForm: {
          ...searchVals,
          startTime: null,
          endTime: null,
        },
      });
    } else {
      setShowMRMergeTime(false);
      setSearchCondition({
        pageNum: 1,
        pageSize: 20,
        searchForm: {
          ...searchVals,
          startTime: key.split('/')[0],
          endTime: key.split('/')[1],
        },
      });
    }
  };

  const formatSearchVal = (val) => {
    let mrNumberValue = val?.mrNumber;
    if (mrNumberValue) {
      const containsNonNumeric = /\D/.test(mrNumberValue);
      if (containsNonNumeric) {
        message.error('MR_ID仅能输入数字！');
        return false;
      }
    }
    let formatTime: any =
      val?.time?.length > 0 ? formatDateToSecond(val?.time) : {};
    let formatTestPassDate: any =
      val?.testPassDate?.length > 0
        ? formatDateToSecond(val?.testPassDate)
        : {};
    let formatMRReleaseDate: any =
      val?.mrReleaseDate?.length > 0
        ? formatDateToSecond(val?.mrReleaseDate)
        : {};

    return {
      ...val,
      startTime: formatTime.startTime,
      endTime: formatTime.endTime,
      testPassStartDate: formatTestPassDate.startTime,
      testPassEndDate: formatTestPassDate.endTime,
      mrReleaseStartDate: formatMRReleaseDate.startTime,
      mrReleaseEndDate: formatMRReleaseDate.endTime,
    };
  };

  return (
    <div className="mr-manage">
      <div ref={searchFormDomRef}>
        <WrapCommonForm
          className="mr-search-form"
          formConfig={searchFormConfig}
          defaultValue={{
            ...searchCondition.searchForm,
            showMRMergeTime: showMRMergeTime,
          }}
          layout="inline"
          formType="search"
          colon={false}
          getFormInstance={(ref) => (searchFormRef.current = ref)}
          onSearchClick={onSearchClick}
          onResetClick={() =>
            setSearchCondition({
              ...initSearchCondition,
              searchForm: {
                ...initSearchCondition.searchForm,

                endTime: showMRMergeTime
                  ? null
                  : searchCondition.searchForm.endTime,
                startTime: showMRMergeTime
                  ? null
                  : searchCondition.searchForm.startTime,
              },
            })
          }
          onVisibilityChange={() => {
            forceUpdate((prev) => prev + 1);
          }}
        />
      </div>
      <Tabs
        onChange={changeTimeTab}
        type="card"
        items={calculateTime()}
        activeKey={activeTabKey}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        rowClassName={getRowClassName}
        columns={formateColumns()}
        loading={loading}
        rowKey={'mrId'}
        middleBtns={middleBtns}
        searchCondition={searchCondition}
        onPageChange={(value: any) => setSearchCondition(value)}
        crossPageSelect={(keys, rows, clearFunc) => {
          setSelectMrInfo({
            selectedRowKeys: keys,
            selectedRows: rows,
            clearFunc: clearFunc,
          });
        }}
        searchRef={searchFormDomRef}
        pageSizeOptions={['20', '50', '100', '200', '500']}
      />
      <Modal
        title="是否同步"
        open={syncJira}
        onCancel={() => setSyncJira(false)}
        onOk={syncJiraClick}
        okButtonProps={{ loading: syncJiraLoading }}
        okText={syncJiraLoading ? '同步中' : '确认'}
        cancelText="取消"
      >
        {syncJiraInfo.testResult === MrTestStatus.PASS && (
          <p>
            请确认同步并关闭fix_Jira【
            {syncJiraInfo.fixJiraKeyList &&
              syncJiraInfo.fixJiraKeyList !== '-' &&
              syncJiraInfo.fixJiraKeyList.map((item: any, idx: number) => (
                <span key={'sync_' + item.jiraKey}>
                  {item.jiraKey}
                  {idx < syncJiraInfo.fixJiraKeyList.length - 1 ? (
                    <span>; </span>
                  ) : (
                    ''
                  )}
                </span>
              ))}
            】吗？
          </p>
        )}
        {(syncJiraInfo.testResult === MrTestStatus.NOT_PASS_RETAIN ||
          syncJiraInfo.testResult === MrTestStatus.NOT_PASS_ROLLBACK) && (
          <p>
            请确认同步并重新开启fix_Jira【
            {syncJiraInfo.fixJiraKeyList &&
              syncJiraInfo.fixJiraKeyList !== '-' &&
              syncJiraInfo.fixJiraKeyList.map((item: any, idx: number) => (
                <span key={'sync_' + item.jiraKey}>
                  {item.jiraKey}
                  {idx < syncJiraInfo.fixJiraKeyList.length - 1 ? (
                    <span>; </span>
                  ) : (
                    ''
                  )}
                </span>
              ))}
            】吗？
          </p>
        )}
      </Modal>
      <Modal
        title={<div style={{ textAlign: 'center' }}>导出查询结果设置</div>}
        open={updateAttention.modalOpen}
        onCancel={() =>
          setUpdateAttention({
            modalOpen: false,
            defaultList: [],
            optionalList: [],
            checkedList: [],
          })
        }
        onOk={makeDownloadUrl}
        okText="导出"
        cancelText="取消"
      >
        <div>
          <div>{`导出查询结果：共计${selectMrInfo.selectedRowKeys.length}条`}</div>
          <h4>默认已选字段</h4>
          <CheckboxGroup
            options={updateAttention.defaultList.map((v) => ({
              label: v.name,
              value: v.id,
              disabled: false,
            }))}
            disabled={true}
            value={updateAttention.defaultList.map((v) => v.id)}
          />
          <h4>可选导出字段</h4>
          <Checkbox
            indeterminate={
              updateAttention.checkedList.length > 0 &&
              updateAttention.checkedList.length <
                updateAttention.optionalList.length
            }
            onChange={(e) =>
              setUpdateAttention({
                ...updateAttention,
                checkedList: e.target.checked
                  ? updateAttention.optionalList.map((v) => v.id)
                  : [],
              })
            }
            checked={
              updateAttention.checkedList.length ===
              updateAttention.optionalList.length
            }
          >
            全选
          </Checkbox>
          <br />
          <CheckboxGroup
            options={updateAttention.optionalList.map((v) => ({
              label: v.name,
              value: v.id,
            }))}
            value={updateAttention.checkedList}
            onChange={(list) =>
              setUpdateAttention({
                ...updateAttention,
                checkedList: list,
              })
            }
          />
        </div>
      </Modal>
    </div>
  );
};

export default React.memo(MrManage);
