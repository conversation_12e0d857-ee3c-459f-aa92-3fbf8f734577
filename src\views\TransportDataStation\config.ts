import { TransportDataApi } from "@/fetch/business/transportDataStop";
import { HttpStatusCode } from "@/fetch/core/constant";
import { FormConfig } from "@jd/x-coreui";
import dayjs from "dayjs";

const fetchApi = new TransportDataApi();
export const SearchConfig: FormConfig = {
    fields: [
        {
            fieldName: 'cityIdList',
            label: '城市',
            placeholder: '请选择城市,可多选',
            type: 'select',
            multiple: true,
            showSearch: true,
            labelInValue: false,
        },
        {
            fieldName: 'stationIdList',
            label: '站点',
            placeholder: '请选择站点,可多选',
            type: 'select',
            multiple: true,
            showSearch: true,
            labelInValue: false,
        },
        {
            fieldName: 'dt',
            label: '日期范围',
            placeholder: '请选择日期范围',
            type: 'rangeTime',
            format: 'YYYY-MM-DD',
            showTime: false,
            disabledDate: (current) => {
                return current > dayjs().subtract(1, 'day');
            },
            xl: 12,
            xxl: 8,
            lg: 16,
            labelCol: {
                span: 4
            },
            wrapperCol: {
                span: 20
            },
        },
    ],
    linkRules: {
        cityIdList: [
            {
                linkFieldName: 'stationIdList',
                rule: 'clear'
            },
            {
                linkFieldName: 'stationIdList',
                rule: 'fetchData',
                fetchFunc: async (val) => {
                    const params = {
                        dimType: 'STATION',
                        filterIds: val,
                    };
                    const res = await fetchApi.getDimSelect(params);
                    if (res.code === HttpStatusCode.Success) {
                        return res.data.map((item) => {
                            return {
                                value: item.dimValue,
                                label: item.dimName,
                            };
                        });
                    } else {
                        return [];
                    }
                },
            },
        ],
    }
}

export const TableConfig: any[] = [
    {
        title: '站点',
        dataIndex: 'stationName',
        align: 'center',
        width: 200,
        ellipsis: true,
        fixed: 'left',
    },
    {
        title: '城市',
        dataIndex: 'cityName',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '片区',
        dataIndex: 'areaName',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '行政区',
        dataIndex: 'countyName',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '运营经理',
        dataIndex: 'manager',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '运营车辆数',
        dataIndex: 'deployVehicleNum',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '累计接驳总趟次',
        dataIndex: 'transportTimes',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '单车日均接驳趟次',
        dataIndex: 'vehicleDayAvgTransportTimes',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '站点运营率',
        dataIndex: 'stationBusinessRate',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '车辆运营率',
        dataIndex: 'vehicleBusinessRate',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '覆盖路区数',
        dataIndex: 'coverRoadNum',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '路区总数',
        dataIndex: 'roadNum',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '覆盖路区率',
        dataIndex: 'coverRoadRate',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '停靠点总数',
        dataIndex: 'stopNum',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '停靠点使用比例',
        dataIndex: 'stopUseRate',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '日均使用次数=0的停靠点个数',
        dataIndex: 'noRunStopNum',
        align: 'center',
        width: 240,
        ellipsis: true,
    },
]