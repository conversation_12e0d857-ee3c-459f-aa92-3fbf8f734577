import { combineReducers } from '@reduxjs/toolkit';
import searchForm from './searchForm';
import commonReducer from './common';
import activeTabKey from './activeTabKey';
import { expandTableRelationReducer } from './expandTableRelation';
import vehicleManage from './vehicleManage';
import commonData from './commonData';

import { boxGridReducer } from './boxGrid';
export const rootReducer = combineReducers({
  searchForm: searchForm,
  common: commonReducer,
  boxGrid: boxGridReducer,
  activeTabKey: activeTabKey,
  vehicleManage: vehicleManage,
  commonData: commonData,
  expandTableRelation: expandTableRelationReducer,
});
