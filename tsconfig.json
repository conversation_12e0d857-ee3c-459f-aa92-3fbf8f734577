{
  "compilerOptions": {
    "downlevelIteration": true,
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "paths": {
      "@/*": ["./src/*"]
    },
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    // "noEmit": true,
    "jsx": "react",
    "declaration": true,
    "emitDeclarationOnly": true,
    "noImplicitAny": false
  },
  "include": ["src"]
}
