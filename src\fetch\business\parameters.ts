import { request } from '../core';
import { Method } from '../core/constant';
export enum DateType {
  DAY = 'DAY',
  WEEK = 'WEEK',
  MONTH = 'MONTH',
}
export default class RequestApi {
  /**
   * 获取站点下拉列表
   * @return {Promise}
   */
  public getStationList = (): Promise<any> => {
    return request({
      method: Method.GET,
      path: '/datacenter/screen/metadata/stationList',
    });
  };

  /**
   * 获取完成订单数据
   * @return {Promise}
   */
  public getCompletedOrderData = (params: {
    stationIdList: number[];
    type: DateType;
    dayList?: string[];
    weekList?: number[];
    monthList?: number[];
    excluded: number;
  }): Promise<any> => {
    return request({
      method: Method.POST,
      path: '/datacenter/screen/display/getCompletedOrderData',
      body: params,
    });
  };

  /**
   * 获取同时段完成订单数据
   * @return {Promise}
   */
  public getPeriodCompletedOrderData = (params: {
    stationIdList: number[];
  }): Promise<any> => {
    return request({
      method: Method.POST,
      path: '/datacenter/screen/display/getPeriodCompletedOrderData',
      body: params,
    });
  };

  /**
   * 获取完成订单数据
   * @return {Promise}
   */
  public getAbnormalData = (params: {
    stationIdList: number[];
    type: DateType;
    dayList?: string[];
    weekList?: number[];
    monthList?: number[];
  }): Promise<any> => {
    return request({
      method: Method.POST,
      path: '/datacenter/screen/display/getAbnormalData',
      body: params,
    });
  };

  /**
   * 创建目标单量
   * @return {Promise}
   */
  public addStationTargetOrder = (params: {
    targetList: {
      stationId: number;
      stationName: string;
      targetOrderList: {
        date: string;
        target: number;
      }[];
    }[];
  }): Promise<any> => {
    return request({
      method: Method.POST,
      path: '/datacenter/screen/targetOrder/addStationTargetOrder',
      body: params,
    });
  };

  /**
   * 获取目标单量
   * @return {Promise}
   */
  public getStationTargetOrder = (params: {
    date: string;
    stationId: string;
  }): Promise<any> => {
    return request({
      method: Method.GET,
      path: '/datacenter/screen/targetOrder/getStationTargetOrder',
      urlParams: params,
    });
  };

  /**
   * 获取车效
   * @return {Promise}
   */
  public getVehicleEfficiency = (): Promise<any> => {
    return request({
      method: Method.GET,
      path: '/datacenter/screen/targetOrder/getVehicleEfficiency',
    });
  };

  /**
   * 获取门店运营情况数据
   * @return {Promise}
   */
  public getStationOperateInfo = (params: {
    date: string;
    stationIds: string;
  }): Promise<any> => {
    return request({
      method: Method.GET,
      path: '/datacenter/screen/operations/getStationOperateInfo',
      urlParams: params,
    });
  };

  /**
   * 新增门店运营情况数据
   * @return {Promise}
   */
  public addStationOperateInfo = (params: {
    operateList: any[];
  }): Promise<any> => {
    return request({
      method: Method.POST,
      path: '/datacenter/screen/operations/addStationOperateInfo',
      body: params,
    });
  };

  /**
   * 新增目标倒计时事件
   * @return {Promise}
   */
  public addGoalCountDownEvent = (params: {
    countDownEventList: any[];
  }): Promise<any> => {
    return request({
      method: Method.POST,
      path: '/datacenter/screen/countdown/addGoalCountDownEvent',
      body: params,
    });
  };

  /**
   * 获取倒计时事件数据
   * @return {Promise}
   */
  public getCountDownEventInfo = (): Promise<any> => {
    return request({
      method: Method.GET,
      path: '/datacenter/screen/countdown/getCountDownEventInfo',
    });
  };

  /**
   * 删除倒计时事件数据
   * @return {Promise}
   */
  public deleteCountDownEventInfo = (idList: number[]): Promise<any> => {
    return request({
      method: Method.POST,
      path: '/datacenter/screen/countdown/deleteCountDownEventInfo',
      body: idList,
    });
  };
  /**
   * 获取订单漏斗模型数据
   * @return {Promise}
   */
  public getOrderFunnelModelData = (params: {
    stationIdList: number[];
    startDay: string;
    endDay: string;
    excluded: number;
  }): Promise<any> => {
    return request({
      method: Method.POST,
      path: '/datacenter/screen/display/getOrderFunnelModelData',
      body: params,
    });
  };

  /**
   * 获取车辆空闲时长数据
   * @return {Promise}
   */
  public getVehicleIdleTimeData = (params: {
    stationIdList: number[];
    type: DateType;
    day: string;
    excluded: number;
  }): Promise<any> => {
    return request({
      method: Method.POST,
      path: '/datacenter/screen/display/getVehicleIdleTimeData',
      body: params,
    });
  };

  /**
   * 获取车效数据
   * @return {Promise}
   */
  public getVehicleEfficiencyData = (params: {
    stationIdList: number[];
    type: DateType;
    dayList?: string[];
    weekList?: number[];
    monthList?: number[];
    excluded: number;
  }): Promise<any> => {
    return request({
      method: Method.POST,
      path: '/datacenter/screen/display/getVehicleEfficiencyData',
      body: params,
    });
  };
}
