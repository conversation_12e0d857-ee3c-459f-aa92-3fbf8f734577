import { configureStore } from '@reduxjs/toolkit';

import { rootReducer } from './reducer/rootReducer';
import createSagaMiddleware from 'redux-saga';
import rootSaga from './rootSaga';

const sageMiddleware = createSagaMiddleware();
export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) => {
    return getDefaultMiddleware({
      thunk: false,
      serializableCheck: false,
    }).concat([sageMiddleware]);
  },
});

sageMiddleware.run(rootSaga);
const win = window as any;
win.store = store;
export type AppDispatch = typeof store.dispatch;
export type RootState = ReturnType<typeof rootReducer>;
