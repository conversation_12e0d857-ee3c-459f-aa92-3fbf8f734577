import { Table } from 'antd'
import React, { useState, useEffect } from 'react'
import TriangularContainer from '../TriangularContainer'
import { fetchSensorInfoByVechileTypeId } from 'src/fetch/business/commonApi'

const vehicleModelTableColumn: any[] = [
  { title: ' ', dataIndex: 'index', align: 'center', ellipsis: true },
  { title: '底盘名称', dataIndex: 'chassis', align: 'center', ellipsis: true },
  { title: '计算平台名称', dataIndex: 'compute', align: 'center', ellipsis: true },
  // { title: '推流相机名称', dataIndex: 'videoCamera', align: 'center', ellipsis: true },
  { title: '传感器方案名称', dataIndex: 'sensorScheme', align: 'center', ellipsis: true },
  { title: '组装厂商名称', dataIndex: 'manufactory', align: 'center', ellipsis: true },
  // { title: '推流类型', dataIndex: 'videoType', align: 'center', ellipsis: true }
]

const sensorTableColumn: any[] = [
  {
    title: '序号', dataIndex: 'id', align: 'center', ellipsis: true,
    render: (record: any, _: any, index: any) => index + 1
  },
  { title: '传感器设备', dataIndex: 'hardwareTypeName', align: 'center', ellipsis: true },
  { title: '用途', dataIndex: 'hardwareTypeUsageName', align: 'center', ellipsis: true },
  { title: '硬件名称', dataIndex: 'hardwareModelName', align: 'center', ellipsis: true },
  { title: '型号', dataIndex: 'hardwareModelModel', align: 'center', ellipsis: true },
  { title: '数量', dataIndex: 'useNumber', align: 'center', ellipsis: true }
]
const VehicleModelInfo = ({
  vehicleModelId
}: {
  vehicleModelId: any
}) => {

  const [dataSource, setDataSource] = useState<{
    loading: boolean,
    vehicleModelId: string,
    vehicleModel: any[],
    sensorSchemeId: string,
    sensorScheme: any[]
  }>({
    loading: false,
    vehicleModelId: vehicleModelId,
    vehicleModel: [],
    sensorSchemeId: '',
    sensorScheme: []
  })

  const getVehicleSensorInfo = async () => {
    setDataSource({
      ...dataSource,
      loading: false
    })
    const response: any = await fetchSensorInfoByVechileTypeId(vehicleModelId)
    let vehicleModelInfo: any[] = []
    let sensorSchemeNameInfo: any[] = []
    let sensorSchemeId = ''
    if (response.code === "OK") {
      vehicleModelInfo = makeVehicleModelInfoDataSource(response.data)
      sensorSchemeNameInfo = response.data.sensorSchemeDetailList
      sensorSchemeId = response.data.sensorSchemeId
    }
    setDataSource({
      loading: false,
      vehicleModelId,
      vehicleModel: vehicleModelInfo,
      sensorSchemeId,
      sensorScheme: sensorSchemeNameInfo
    })
  }

  const makeVehicleModelInfoDataSource = (info: any) => {
    const list: any[] = [
      {
        'index': '名称',
        'chassis': info.chassisName || '-',
        'compute': info.computeName || '-',
        'videoCamera': info.videoCameraName || '-',
        'sensorScheme': info.sensorSchemeName || '-',
        'manufactory': info.manufactoryName || '-',
        'videoType': info.videoTypeName || '-'
      },
      {
        'index': '型号/id',
        'chassis': info.chassisModel || '-',
        'compute': info.computeModel || '-',
        'videoCamera': info.videoCameraModel || '-',
        'sensorScheme': info.sensorSchemeId || '-',
        'manufactory': info.manufactoryId || '-',
        'videoType': info.videoTypeModel || '-',
      }
    ]
    return list
  }

  useEffect(() => {
    if (vehicleModelId) {
      getVehicleSensorInfo()
    }
  }, [vehicleModelId])

  return <TriangularContainer customStyle={{ backgroundColor: '#f1f1f1', padding: "10px" }}>
    <div style={{ display: "flex", flexDirection: "row", alignItems: "center" }}>
      <div style={{ height: 20, width: 5, backgroundColor: "red", marginRight: 6 }} />
      {`车型id： ${vehicleModelId}`}
    </div>
    <div style={{ marginTop: 10 }}>
      <Table
        rowKey={(record) => record.chassis}
        size="small"
        loading={dataSource.loading}
        bordered
        columns={vehicleModelTableColumn}
        dataSource={dataSource.vehicleModel}
        pagination={false}
      />
    </div>

    <div style={{ display: "flex", flexDirection: "row", alignItems: "center", marginTop: 20 }}>
      <div style={{ height: 20, width: 5, backgroundColor: "red", marginRight: 6 }} />
      {`传感器方案id： ${dataSource.sensorSchemeId}`}
    </div>
    <div style={{ marginTop: 10 }}>
      <Table
        rowKey={(record) => record.id}
        size="small"
        loading={dataSource.loading}
        bordered
        columns={sensorTableColumn}
        dataSource={dataSource.sensorScheme}
        pagination={false}
      />
    </div>
  </TriangularContainer>
}

export default React.memo(VehicleModelInfo)