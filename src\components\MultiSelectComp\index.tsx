import React, { useState, useEffect } from 'react';
import { TreeSelect } from 'antd';
import './index.scss';

type Iprop = {
  onChange: any;
  options: {
    label: any;
    value: any;
  }[];
};
const MultiSelectComp = (props: Iprop) => {
  const { options, onChange } = props;
  const [selectedValues, setSelectedValues] = useState<any>();
  useEffect(() => {
    setSelectedValues(options?.map(({ value }) => value));
  }, [options]);
  return (
    <div className={`multi-select-wrapper`}>
      <TreeSelect
        showSearch={false}
        onChange={(value, labelList, extra) => {
          setSelectedValues(value);
          onChange(value);
        }}
        value={selectedValues}
        treeCheckable={true}
        maxTagCount={1}
        treeData={options}
        treeNodeLabelProp={'label'}
        getPopupContainer={(triggerNode) => triggerNode.parentElement}
      ></TreeSelect>
    </div>
  );
};

export default React.memo(MultiSelectComp);
