import { Popconfirm, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { handleVehicleManage } from '@/redux/reducer/vehicleManage';
import { DeviceLifeApi } from '@/fetch/business';
import { productionColumns } from '../../utils/column';
import CheckStatusModal from '@/views/RepairOrderManagement/components/CheckStatusModal';
import { PageType, vehicleStatus } from '../../utils/constant';
import { HttpStatusCode } from '@/fetch/core/constant';
import { TabType } from '../../utils/constant';
import { CommonTable, showModal, TableOperateBtn } from '@/components';
import { useTableData } from '@/components/CommonTable/useTableData';
import { DeviceProduceResponse, DeviceRequest } from '@/types/deviceLife';
import DivideVehicle from '../../DivideVehicle';
import { AnyFunc } from '@/global';
interface Props {
  searchForm: any;
  activeTabKey: string;
  click: number;
  setSearchForm: AnyFunc;
  searchRef: any;
  tableKey: string;
  setTableKey: any;
}
const fetchApi = new DeviceLifeApi();
const ProductionTab = (props: Props) => {
  const {
    searchForm,
    activeTabKey,
    click,
    setSearchForm,
    searchRef,
    tableKey,
    setTableKey,
  } = props;
  const dispatch = useDispatch();
  const [bindStationModal, setBindStationModal] = useState(false);
  const [calibrationModal, setCalibrationModal] = useState(false);

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]); // 在用车辆、车辆调度池批量选中的数据keys
  const [selectedRows, setSelectedRows] = useState<any>([]); // 去分配存储分配车辆详细信息

  const [operateItem, setOperateItem] = useState<any>(null);

  const [vehicleId, setVehicleId] = useState(null);
  // 选中车辆点击批量验收紧接着点击批量出场或者批量验收，选中的数据没有更新，无法进行批量出厂，所以当批量验收成功后将车辆id存在这个数组中
  const [vehicleIdList, setVehicleIdList] = useState<any>([]);

  const { tableData, loading } = useTableData<
    DeviceRequest,
    DeviceProduceResponse
  >(searchForm, fetchApi.fetchDeviceProduceTable, tableKey);
  useEffect(() => {
    if (activeTabKey === TabType.VEHICLE_PRODUCTION) {
      setSelectedRowKeys([]);
      setSelectedRows([]);
      setSearchForm({
        ...searchForm,
        pageNum: 1,
        pageSize: 10,
      });
    }
  }, [activeTabKey, click]);
  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedRows(selectedRows);
    },
  };
  // 批量验收
  const handleAcceptance = () => {
    if (selectedRowKeys.length > 0) {
      // 场景三:选中车辆先进行批量验收，然后还进行批量验收，没有更新选中的行
      if (JSON.stringify(vehicleIdList) === JSON.stringify(selectedRowKeys)) {
        message.error('仅生命周期全为“新建、调度驳回”车辆，才可操作批量验收！');
      } else {
        // 场景二：已选最少1车辆，但生命周期不全是“新建、调度驳回”
        const filterVehicle = selectedRows.filter((item: any) => {
          if (
            item.hardwareStatus !== vehicleStatus.NEW &&
            item.hardwareStatus !== vehicleStatus.REJECT
          ) {
            return item;
          }
        });
        if (filterVehicle.length > 0) {
          message.error(
            '仅生命周期全为“新建、调度驳回”车辆，才可操作批量验收！',
          );
          return;
        }
        showModal({
          content: (
            <div style={{ textAlign: 'center' }}>
              <p>{`共计${selectedRowKeys.length}辆已生产完成，验收通过吗？`}</p>
            </div>
          ),
          footer: {
            showOk: true,
            showCancel: true,
            okFunc: (cb) => {
              acceptanceOk(selectedRowKeys as number[]);
              cb();
            },
            cancelFunc: (cb) => {
              setOperateItem(null);
              cb();
            },
          },
        });
      }
    } else {
      // 场景一：未选择车辆
      message.error('请至少选择一条“新建、调度驳回”的数据进行批量验收!');
    }
  };
  // 批量车辆配置
  const handleVehicleConfig = () => {
    if (selectedRowKeys.length > 0) {
      dispatch(handleVehicleManage(selectedRows));
      setBindStationModal(true);
    } else {
      message.error('请至少选择一条数据进行批量车辆配置!');
    }
  };
  // 批量出厂
  const handleLeaveFactory = () => {
    if (selectedRowKeys.length > 0) {
      const deviceNameList: string[] = [];
      selectedRows.forEach((item: any) => {
        deviceNameList.push(item.deviceName);
      });
      // 场景三:选中车辆先进行批量验收，紧接着进行批量出厂，没有更新选中的行
      if (JSON.stringify(vehicleIdList) === JSON.stringify(selectedRowKeys)) {
        showModal({
          content: (
            <div style={{ textAlign: 'center' }}>
              <p>{`共计${selectedRowKeys.length}辆已满足出厂条件，`}</p>
              <p>确认出厂进到【车辆调度】吗？</p>
            </div>
          ),
          footer: {
            showOk: true,
            showCancel: true,
            okFunc: (cb) => {
              leaveFactoryOk(deviceNameList);
              cb();
            },
            cancelFunc: (cb) => {
              setOperateItem(null);
              cb();
            },
          },
        });
      } else {
        // 场景二：已选最少1车辆，但生命周期不全是“待出厂”
        const filterVehicle = selectedRows.filter((item: any) => {
          if (item.hardwareStatus !== vehicleStatus.LEAVE) {
            return item;
          }
        });
        if (filterVehicle.length > 0) {
          message.error('仅生命周期全为“待出厂”车辆，才可操作批量出厂！');
          return;
        }
        showModal({
          content: (
            <div style={{ textAlign: 'center' }}>
              <p>{`共计${selectedRowKeys.length}辆已满足出厂条件，`}</p>
              <p>确认出厂进到【车辆调度】吗？</p>
            </div>
          ),
          footer: {
            showOk: true,
            showCancel: true,
            okFunc: (cb) => {
              leaveFactoryOk(deviceNameList);
              cb();
            },
            cancelFunc: (cb) => {
              setOperateItem(null);
              cb();
            },
          },
        });
      }
    } else {
      // 场景一：未选择车辆
      message.error('请至少选择一条“待出厂”的数据进行批量出厂!');
    }
  };
  // 点击验收
  const goAcceptance = (record: any) => {
    setOperateItem(record);
    showModal({
      content: (
        <div style={{ textAlign: 'center' }}>
          <p>{`确认车牌号${record?.deviceName}生产完成，验收通过吗？`}</p>
        </div>
      ),
      footer: {
        showOk: true,
        showCancel: true,
        okFunc: (cb) => {
          acceptanceOk([record?.id]);
          cb();
        },
        cancelFunc: (cb) => {
          setOperateItem(null);
          cb();
        },
      },
    });
  };
  // 点击确认出厂
  const goLeaveFactory = (record: any) => {
    setOperateItem(record);
    showModal({
      content: (
        <div style={{ textAlign: 'center' }}>
          <p>{`车牌号${record?.deviceName}已满足出厂条件，`}</p>
          <p>确认出厂进到【车辆调度】吗？</p>
        </div>
      ),
      footer: {
        showOk: true,
        showCancel: true,
        okFunc: (cb) => {
          leaveFactoryOk([record?.deviceName]);
          cb();
        },
        cancelFunc: (cb) => {
          setOperateItem(null);
          cb();
        },
      },
    });
  };
  // 验收成功
  const acceptanceOk = async (deviceBaseIdList: number[]) => {
    try {
      const response: any = await fetchApi.deviceCheckAccept(
        deviceBaseIdList ? deviceBaseIdList : (selectedRowKeys as number[]),
      );
      if (response.code === HttpStatusCode.Success) {
        message.success(response.message);
        setTableKey(new Date().getMilliseconds().toString());
        setVehicleIdList([]);
        setSelectedRowKeys([]);
        setSelectedRows([]);
      } else {
        message.error(response.message);
      }
    } catch (e) {
      console.log(e);
    } finally {
      setOperateItem(null);
    }
  };
  // 出厂成功
  const leaveFactoryOk = async (deviceNameList) => {
    try {
      const res: any = await fetchApi.deviceLeaveFactory({
        deviceNameList: operateItem ? [operateItem.deviceName] : deviceNameList,
      });
      if (res.code === HttpStatusCode.Success) {
        message.success(res.message);
        setTableKey(new Date().getMilliseconds().toString());
        setVehicleIdList([]);
        setSelectedRowKeys([]);
        setSelectedRows([]);
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    } finally {
      setOperateItem(null);
    }
  };

  /**
   * 删除车辆
   * @param {any} record
   */
  const deleteVehicle = async (record: any) => {
    try {
      const res = await fetchApi.deleteDevice(record.deviceName);
      if (res && res.code === HttpStatusCode.Success) {
        message.success(res.message);
        setTableKey(new Date().getMilliseconds().toString());
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log(e);
    }
  };

  const formatColumns = () => {
    return productionColumns.map((col: any) => {
      switch (col.dataIndex) {
        case 'order':
          col.render = (text: any, record: any, index: number) =>
            `${(searchForm.pageNum - 1) * searchForm.pageSize + index + 1}`;
          break;
        case 'checkStatusName':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) =>
            record.checkStatusName || '-';
          break;
        case 'stationUseCaseName':
          col.render = (text: any) => `${text || '-'}`;
          break;
        case 'stationName':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) => record.stationName || '-';
          break;
        case 'vehicleBusinessType':
          // eslint-disable-next-line react/display-name
          col.render = (text: any, record: any) =>
            record.vehicleBusinessType || '-';
          break;
        case 'product':
          // eslint-disable-next-line react/display-name
          col.render = (item: any, record: any) => {
            return (
              <div className="operate-btn">
                <TableOperateBtn
                  title="验收"
                  handleClick={() => goAcceptance(record)}
                  show={
                    record.hardwareStatus === vehicleStatus.NEW ||
                    record.hardwareStatus === vehicleStatus.REJECT
                  }
                />
                <TableOperateBtn
                  title="确认出厂"
                  handleClick={() => goLeaveFactory(record)}
                  show={record.hardwareStatus === vehicleStatus.LEAVE}
                />
                <Popconfirm
                  placement="left"
                  title={'是否要删除吗？'}
                  onConfirm={() => deleteVehicle(record)}
                  okText="确定"
                  cancelText="取消"
                  overlayStyle={{ maxWidth: 800 }}
                >
                  <a>删除</a>
                </Popconfirm>
                <TableOperateBtn
                  title="查看报告"
                  handleClick={() => {
                    setVehicleId(record.id);
                    setCalibrationModal(true);
                  }}
                  show={record.checkStatus}
                />
              </div>
            );
          };
          break;
        default:
          return col;
      }
      return col;
    });
  };
  const middleBtns = [
    {
      show: true,
      title: '批量绑站',
      key: 'vehicleConfig',
      onClick: () => handleVehicleConfig(),
    },
    {
      show: true,
      title: '批量验收',
      key: 'vehicleAccept',
      onClick: () => handleAcceptance(),
    },
    {
      show: true,
      title: '批量出厂',
      key: 'vehicleLeaveFactory',
      onClick: () => handleLeaveFactory(),
    },
  ];
  return (
    <div>
      <CommonTable
        middleBtns={middleBtns}
        searchRef={searchRef}
        rowKey={'id'}
        rowSelection={{ ...rowSelection }}
        columns={formatColumns()}
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        loading={loading}
        onPageChange={(paginationData: any) => {
          setSearchForm({
            ...searchForm,
            pageNum: paginationData.pageNum,
            pageSize: paginationData.pageSize,
          });
        }}
      />
      {/* 查看标定结果 */}
      {calibrationModal && (
        <CheckStatusModal
          visible={calibrationModal}
          onOk={() => {
            setCalibrationModal(false);
          }}
          onCancel={() => {
            setCalibrationModal(false);
          }}
          id={vehicleId}
        />
      )}
      {/** 批量绑站 */}
      {bindStationModal && (
        <DivideVehicle
          pageType={PageType.productBindStation}
          show={bindStationModal}
          setShow={setBindStationModal}
          setTableKey={setTableKey}
        />
      )}
    </div>
  );
};

export default React.memo(ProductionTab);
