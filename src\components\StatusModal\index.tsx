import { Modal } from 'antd';
import React from 'react';
interface Props {
  enable: number | string | null; // 车辆停用和启用ENABLE、DISABLE， 其他为1、0
  modalVisible: boolean;
  submiting?: boolean;
  type: string;
  verbalTrick?: boolean; // 区分"有效"还是"启用"话术，默认是启用
  number?: number;
  handleOk: () => void;
  handleCancel: () => void;
}

const nameTypeMap = new Map([
  ['usingVehicle', '车辆'],
  ['gird', '格口'],
  ['stopCollection', '代收点'],
  ['stopPerson', '代收人'],
  ['station', '站点'],
  ['stopPoint', '停靠点'],
  ['blackList', '黑名单'],
  ['vendorType', '厂商状态'],
  ['hardWareModelType', '硬件状态'],
  ['boxGird', '模板状态'],
]);

const StatusModal = (props: Props) => {
  const { enable, modalVisible, type } = props;
  const name = nameTypeMap.get(type);
  let title = null;
  if (type === 'blackList') {
    title = `确认${enable == 1 ? '启用' : '停用'}`;
  }
  if (props.verbalTrick && type !== 'blackList') {
    title = `是否确认${enable === 0 ? '无效' : '有效'}`;
  } else if (type !== 'blackList') {
    title = `是否确认${enable === 'ENABLE' || enable == 1 ? '启用' : '停用'}`;
  }

  const noticeMessage = () => {
    let notice = null;
    if (type === 'blackList') {
      return enable === '1' ? (
        <p>{`确定批量启用共计${props.number}位用户吗？启用后，用户进入黑名单。`}</p>
      ) : (
        <p>{`确定批量停用共计${props.number}位用户吗？停用将从黑名单中释放。`}</p>
      );
    }
    if (props.verbalTrick) {
      notice = (
        <p>
          该{name}是否改为{enable === 0 ? '无效' : '有效'}
        </p>
      );
    } else {
      notice =
        enable === 1 || enable === 'ENABLE' ? (
          <p>
            {name}启用后，该{name}关联的所有业务数据将应用于业务运营
          </p>
        ) : (
          <p>
            {name}停用后，该{name}关联的所有业务数据将无法应用于业务运营
          </p>
        );
    }
    return notice;
  };

  return (
    <Modal
      maskClosable={false}
      keyboard={false}
      title={title}
      visible={modalVisible}
      onOk={props.handleOk}
      confirmLoading={props.submiting ?? false}
      onCancel={props.handleCancel}
    >
      {noticeMessage()}
    </Modal>
  );
};
export default StatusModal;
