/* eslint-disable react/display-name */
/* eslint-disable no-unused-vars */

import { Form, FormInstance, Button, Table, Modal, Input, message } from 'antd';
import React, { useEffect, useState, useReducer } from 'react';
import { checkPhone, isEmpty } from '@/utils/utils';
import CommonStatus from '../CommonStatus';
import { reducer, State, User } from './reducer';
import SecurityPhone from '@/components/SecurityPhone';

const enum BussinessUserType {
  COLLECTION = '被接驳人',
  CONNECTION = '代收人',
}
const TransferEdit = ({
  form,
  initValues,
  show,
}: {
  form: FormInstance;
  initValues?: any;
  show: boolean;
}) => {
  const stopCollectionColumns: any = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
      width: 90,
      render: (text: any, record: number, index: number) => index + 1,
    },
    { title: '被接驳人姓名', dataIndex: 'name', align: 'center', width: 180 },
    {
      title: '被接驳人手机',
      dataIndex: 'userPhone',
      align: 'center',
      width: 180,
      render: (params: any) => {
        const { userId, contact, secret } = params;
        return (
          <SecurityPhone
            phone={contact}
            security={secret}
            onClick={(updatedSecrit: boolean) => {
              dispatch({
                type: 'updatePhoneSecret',
                payload: { userId, type: 'collection' },
              });
            }}
          />
        );
      },
    },
    {
      title: '被接驳人状态',
      dataIndex: 'enable',
      align: 'center',
      width: 110,
      render: (enable: any) => {
        const statusName = enable === 1 ? '启用' : '停用';
        const style: React.CSSProperties = {
          color: enable === 1 ? '#31C2A6' : '#D9001B',
        };
        return <div style={style}>{statusName}</div>;
      },
    },
    {
      title: '是否默认',
      dataIndex: 'isDefault',
      align: 'center',
      width: 110,
      render: (enable: any) => {
        const isDefaultName = enable === 1 ? '是' : '否';
        const style: React.CSSProperties = {
          color: enable === 1 ? '#31C2A6' : '#D9001B',
        };
        return <div style={style}>{isDefaultName}</div>;
      },
    },
    {
      title: '操作',
      dataIndex: '',
      align: 'center',
      width: 180,
      render: (params: any) => {
        const { enable } = params;
        const style: React.CSSProperties = {
          color: enable === 0 ? '#31C2A6' : '#D9001B',
        };
        return (
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-around',
            }}
          >
            <a
              style={style}
              onClick={() => {
                dispatch({
                  type: 'showUpdateStatus',
                  payload: {
                    show: true,
                    user: {
                      ...params,
                      isDefault: params.enable === 1 ? 0 : params.isDefault,
                    },
                    type: 'collection',
                  },
                });
                setBusinessUserType(BussinessUserType.COLLECTION);
              }}
            >
              {params.enable === 1 ? '停用' : '启用'}
            </a>
            {params.isDefault === 1 || params.enable === 0 ? null : (
              <a
                onClick={() => {
                  dispatch({
                    type: 'updateUser',
                    payload: {
                      user: { ...params, isDefault: 1 },
                      type: 'collection',
                    },
                  });
                }}
              >
                设为默认
              </a>
            )}
            <a
              onClick={() => {
                editForm.setFieldsValue({
                  ...params,
                });
                dispatch({
                  type: 'showEdit',
                  payload: { show: true, user: params, type: 'collection' },
                });
                setBusinessUserType(BussinessUserType.COLLECTION);
              }}
            >
              编辑
            </a>
            <a
              style={{ color: '#D9001B' }}
              onClick={() => {
                dispatch({
                  type: 'showDelete',
                  payload: { show: true, user: params, type: 'collection' },
                });
                setBusinessUserType(BussinessUserType.COLLECTION);
              }}
            >
              删除
            </a>
          </div>
        );
      },
    },
  ];
  const stopConnectionColumns: any = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
      width: 90,
      render: (text: any, record: number, index: number) => index + 1,
    },
    { title: '代收人姓名', dataIndex: 'name', align: 'center', width: 180 },
    {
      title: '代收人手机',
      dataIndex: 'userPhone',
      align: 'center',
      width: 180,
      render: (params: any) => {
        const { userId, contact, secret } = params;
        return (
          <SecurityPhone
            phone={contact}
            security={secret}
            onClick={(updatedSecrit: boolean) => {
              dispatch({
                type: 'updatePhoneSecret',
                payload: { userId, type: 'connection' },
              });
            }}
          />
        );
      },
    },
    {
      title: '代收人状态',
      dataIndex: 'enable',
      align: 'center',
      width: 110,
      render: (enable: any) => {
        const statusName = enable === 1 ? '启用' : '停用';
        const style: React.CSSProperties = {
          color: enable === 1 ? '#31C2A6' : '#D9001B',
        };
        return <div style={style}>{statusName}</div>;
      },
    },
    {
      title: '是否默认',
      dataIndex: 'isDefault',
      align: 'center',
      width: 110,
      render: (enable: any) => {
        const isDefaultName = enable === 1 ? '是' : '否';
        const style: React.CSSProperties = {
          color: enable === 1 ? '#31C2A6' : '#D9001B',
        };
        return <div style={style}>{isDefaultName}</div>;
      },
    },
    {
      title: '操作',
      dataIndex: '',
      align: 'center',
      width: 180,
      render: (params: any) => {
        const { enable } = params;
        const style: React.CSSProperties = {
          color: enable === 0 ? '#31C2A6' : '#D9001B',
        };
        return (
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-around',
            }}
          >
            <a
              style={style}
              onClick={() => {
                dispatch({
                  type: 'showUpdateStatus',
                  payload: {
                    show: true,
                    user: {
                      ...params,
                      isDefault: params.enable === 1 ? 0 : params.isDefault,
                    },
                    type: 'connection',
                  },
                });
                setBusinessUserType(BussinessUserType.CONNECTION);
              }}
            >
              {params.enable === 1 ? '停用' : '启用'}
            </a>
            {params.isDefault === 1 || params.enable === 0 ? null : (
              <a
                onClick={() => {
                  dispatch({
                    type: 'updateUser',
                    payload: {
                      user: { ...params, isDefault: 1 },
                      type: 'connection',
                    },
                  });
                }}
              >
                设为默认
              </a>
            )}
            <a
              onClick={() => {
                editForm.setFieldsValue({
                  ...params,
                });
                dispatch({
                  type: 'showEdit',
                  payload: { show: true, user: params, type: 'connection' },
                });
                setBusinessUserType(BussinessUserType.CONNECTION);
              }}
            >
              编辑
            </a>
            <a
              style={{ color: '#D9001B' }}
              onClick={() => {
                dispatch({
                  type: 'showDelete',
                  payload: { show: true, user: params, type: 'connection' },
                });
                setBusinessUserType(BussinessUserType.CONNECTION);
              }}
            >
              删除
            </a>
          </div>
        );
      },
    },
  ];

  const [state, dispatch] = useReducer(reducer, {
    userList: {
      stopCollectionUserList: [],
      stopConnectionUserList: [],
    },
    showEdit: {
      user: null,
      show: false,
      type: 'collection',
    },
    showUpdateStatus: {
      user: null,
      show: false,
      type: 'collection',
    },
    showDelete: {
      user: null,
      show: false,
      type: 'collection',
    },
  });
  const { userList, showEdit, showUpdateStatus, showDelete } = state;
  const [businessUserType, setBusinessUserType] = useState('');
  const [editForm] = Form.useForm();
  const makeUserDataSource = (userList: any) => {
    if (!userList) {
      return [];
    }
    return userList.map((item: any) => {
      return {
        key: item.id,
        ...item,
        userPhone: {
          contact: item.contact,
          secret: item.secret ?? true,
          userId: item.id,
        },
      };
    });
  };

  const repeated = (newValue: User) => {
    const { user, type } = showEdit;
    if (type === 'collection') {
      if (user) {
        const filteredList = userList.stopCollectionUserList
          ? userList.stopCollectionUserList.filter(
              (item: any) =>
                item.id !== user.id &&
                `${item.contact}` === `${newValue.contact}`,
            )
          : [];
        if (filteredList.length >= 1) {
          return true;
        }
      } else {
        const filteredList = userList.stopCollectionUserList
          ? userList.stopCollectionUserList.filter(
              (item) => `${item.contact}` === `${newValue.contact}`,
            )
          : [];
        if (filteredList.length === 1) {
          return true;
        }
      }
    } else if (type === 'connection') {
      if (user) {
        const filteredList = userList.stopConnectionUserList
          ? userList.stopConnectionUserList.filter(
              (item: any) =>
                item.id !== user.id &&
                `${item.contact}` === `${newValue.contact}`,
            )
          : [];
        if (filteredList.length >= 1) {
          return true;
        }
      } else {
        const filteredList = userList.stopConnectionUserList
          ? userList.stopConnectionUserList.filter(
              (item) => `${item.contact}` === `${newValue.contact}`,
            )
          : [];
        if (filteredList.length === 1) {
          return true;
        }
      }
    }
    return false;
  };

  useEffect(() => {
    form.setFieldsValue({
      businessUserList: userList,
    });
  }, [JSON.stringify(userList)]);

  useEffect(() => {
    if (initValues) {
      dispatch({ type: 'fetchInit', payload: { userList: initValues } });
    }
  }, [initValues]);

  return !show ? (
    <></>
  ) : (
    <>
      <Form.Item name="businessUserList" label="业务员信息">
        <div>
          <div>
            <Button
              style={{ borderWidth: 0, marginRight: 10 }}
              type="primary"
              onClick={() => {
                dispatch({
                  type: 'showEdit',
                  payload: { show: true, user: null, type: 'collection' },
                });
                setBusinessUserType(BussinessUserType.COLLECTION);
              }}
            >
              新增被接驳人
            </Button>
            <Button
              style={{ borderWidth: 0 }}
              type="primary"
              onClick={() => {
                dispatch({
                  type: 'showEdit',
                  payload: { show: true, user: null, type: 'connection' },
                });
                setBusinessUserType(BussinessUserType.CONNECTION);
              }}
            >
              新增代收人
            </Button>
          </div>
          <div style={{ marginTop: 10 }}>
            <Table
              columns={stopCollectionColumns}
              bordered
              dataSource={makeUserDataSource(userList.stopCollectionUserList)}
              pagination={false}
            />
          </div>
          <div style={{ marginTop: 10 }}>
            <Table
              columns={stopConnectionColumns}
              bordered
              dataSource={makeUserDataSource(userList.stopConnectionUserList)}
              pagination={false}
            />
          </div>
        </div>
      </Form.Item>
      {showEdit.show ? (
        <Modal
          open={showEdit.show}
          title={`${businessUserType}配置`}
          closable={false}
          maskClosable={false}
          onOk={async () => {
            const editValue = await editForm.validateFields();
            if (repeated(editValue)) {
              message.error('该手机号已存在，不允许重复创建！');
              return;
            }

            if (!showEdit.user) {
              editValue.id = Date.now();
              if (showEdit.type === 'connection') {
                if (
                  isEmpty(userList.stopConnectionUserList) &&
                  editValue.isDefault === 0
                ) {
                  message.warning(`需有一个默认的${businessUserType}`);
                  return;
                }
              } else {
                if (
                  isEmpty(userList.stopCollectionUserList) &&
                  editValue.isDefault === 0
                ) {
                  message.warning(`需有一个默认的${businessUserType}`);
                  return;
                }
              }
              dispatch({
                type: 'addNewUser',
                payload: {
                  newUser: { ...editValue, enable: 1 },
                  type: showEdit.type,
                },
              });
            } else {
              editValue.id = showEdit.user.id;
              editValue.enable = showEdit.user.enable;
              editValue.isDefault = showEdit.user.isDefault;
              dispatch({
                type: 'updateUser',
                payload: { user: editValue, type: showEdit.type },
              });
            }
            dispatch({
              type: 'showEdit',
              payload: { show: false, user: null, type: showEdit.type },
            });
            editForm.resetFields();
          }}
          onCancel={() => {
            dispatch({
              type: 'showEdit',
              payload: { show: false, user: null, type: showEdit.type },
            });
            editForm.resetFields();
          }}
        >
          <Form form={editForm}>
            <Form.Item
              name="name"
              label="姓名"
              rules={[{ required: true, message: '请输入姓名' }]}
            >
              <Input
                autoComplete="off"
                placeholder="请输入姓名"
                maxLength={20}
              />
            </Form.Item>
            <Form.Item
              name="contact"
              label="电话"
              rules={[
                { required: true, message: '' },
                {
                  validator: (_, value: any) => {
                    if (!checkPhone(value)) {
                      return Promise.reject(Error('请输入正确的电话号码'));
                    } else {
                      return Promise.resolve();
                    }
                  },
                },
              ]}
            >
              <Input
                autoComplete="off"
                placeholder="请输入电话"
                maxLength={50}
              />
            </Form.Item>
            {!showEdit.user?.name && !showEdit.user?.contact && (
              <CommonStatus
                form={editForm}
                name="isDefault"
                label="是否默认"
                items={[
                  { code: 1, name: '是' },
                  { code: 0, name: '否' },
                ]}
                initValue={showEdit.user?.isDefault}
                defaultValue={0}
              />
            )}
          </Form>
        </Modal>
      ) : null}
      <Modal
        open={showUpdateStatus.show}
        title={`${
          showUpdateStatus.user?.enable === 1 ? '停用' : '启用'
        }${businessUserType}`}
        closable={false}
        maskClosable={false}
        onCancel={() => {
          dispatch({
            type: 'showUpdateStatus',
            payload: { show: false, user: null },
          });
        }}
        onOk={() => {
          const user: any = showUpdateStatus.user;
          user.enable = user.enable === 1 ? 0 : 1;
          dispatch({
            type: 'updateUser',
            payload: { show: false, user, type: showUpdateStatus.type },
          });
          dispatch({
            type: 'showUpdateStatus',
            payload: { show: false, user: null, type: showUpdateStatus.type },
          });
        }}
      >
        {showUpdateStatus.user?.enable === 1
          ? `${businessUserType}【${showUpdateStatus.user?.name}】停用后，该${businessUserType}关联的所有业务数据将无法应用于业务运营`
          : `${businessUserType}【${showUpdateStatus.user?.name}】启用后，该${businessUserType}关联的所有业务数据将应用于业务运营`}
      </Modal>
      <Modal
        open={showDelete.show}
        title={`删除${businessUserType}`}
        closable={false}
        maskClosable={false}
        onCancel={() => {
          dispatch({
            type: 'showDelete',
            payload: { show: false, user: null, type: showDelete.type },
          });
        }}
        onOk={() => {
          const user: any = showDelete.user;
          dispatch({
            type: 'deleteUser',
            payload: { userId: user.id, type: showDelete.type },
          });
          dispatch({
            type: 'showDelete',
            payload: { show: false, user: null, type: showDelete.type },
          });
        }}
      >
        {`是否确认删除${businessUserType}【${showDelete.user?.name}】/【${showDelete.user?.contact}】`}
      </Modal>
    </>
  );
};

export default TransferEdit;
