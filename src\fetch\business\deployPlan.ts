import { ProductType } from '@/utils/enum';
import { request } from '../core';
export class DeployPlan {
  getDeploymentPlanList(params: {
    pageSize: number;
    pageNum: number;
    planType?: string;
    stateId?: string;
    cityId?: string;
    stationBaseId?: string;
    currentStage?: string;
    creatorErp?: string;
    currentExecutorErp?: string;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}/deployment/getDeploymentPlanList`,
      body: params,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
    };
    return request(options);
  }

  getStation() {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/station_base/get_station_address_list',
      body: { stationProductType: ProductType.VEHICLE, enable: 1 },
    };
    return request(options);
  }

  getDeploymentPlanDetail(params: { planNumber: string }) {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}/deployment/getDeploymentPlanDetail`,
      body: params,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
    };
    return request(options);
  }

  createSupplementaryPlan(params: {
    stationBaseId: string;
    planType: string;
    needVehicleCount?: string;
    supplier?: string;
    mapVehicleSource?: string;
    stageInfoList: any[];
  }) {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}/deployment/createSupplementaryPlan`,
      body: params,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
    };
    return request(options);
  }

  urge(params: { processInstanceId: string }) {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}/deployment/urge`,
      body: params,
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
    };
    return request(options);
  }

  getInsuranceDetail(deviceNameList: any[]) {
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}/k2/management/insurance_info/get_devices_effective_insurance_list`,
      body: { deviceNameList },
      headers: {
        'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
      },
    };
    return request(options);
  }
}
