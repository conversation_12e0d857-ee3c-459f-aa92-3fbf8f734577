.task-form {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  .drawer-expand-icon,
  .drawer-collapse-icon {
    width: 15px;
    height: 66px;
    background-color: rgb(60, 110, 240);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 30px 0 0 30px;
    position: absolute;
    left: -15px;
    box-shadow: 0 2px 8px 0 rgba(35, 37, 43, 0.1);
    transition: all 0.3s ease-in-out;
    cursor: pointer;
    &:hover {
      transform: scale(1.1);
    }
  }
  .drawer-content {
    height: calc(100vh - 80px);
    background: #fff;
    .task-form-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid #e4e5e9;
    }
    .task-form-content {
      padding: 16px;
    }
    .routes-info {
      padding: 16px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .routes-info-content {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex: 1;
        .routes-info-content-item {
          display: flex;
          align-items: flex-start;

          .routes-info-content-item-label {
            text-align: right;
            padding-right: 8px;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
            line-height: 32px;
          }
          .routes-info-content-item-value {
            flex: 1;
            min-height: 32px;
            line-height: 32px;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
          }
        }
      }
    }
    .action-btn-container {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 16px;
      border-top: 1px solid #e4e5e9;
      box-shadow: 0 -4px 16px 0 rgba(35, 37, 43, 0.08);
    }
  }
  .reverse-start-end {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    margin-top: -16px;
    cursor: pointer;
  }
  .task-route-color-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .task-route-color-item {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}
