export enum LoginState {
  LOGINING = 'LOGINING',
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGOUT = 'LOGOUT',
  LOGIN_FAILED = 'LOGIN_FAILED',
}
export enum YESNO {
  YES = 1,
  NO = 0,
}

export enum ProductType {
  VEHICLE = 'vehicle',
  ROBOT = 'robot',
  INTEGRATE = 'integrate',
}

export const enum StopType {
  PICKUP = 'PICKUP', // 取货点
  HOME = 'HOME', // HOME点
  STRADDLE = 'STRADDLE', // 跨站停靠点
  LOADING = 'LOADING', // 装载点
}

export enum MrTestStatus {
  PASS = 0, // 测试通过
  NOT_PASS_RETAIN = 1, // 测试不通过(保留)
  LONG_TERM_TEST = 2, //长期测试
  TO_BE_TEST = 3, //待测试
  NOT_PASS_ROLLBACK = 7, // 测试不通过(已回滚)
}
export enum MrStatus {
  TO_BE_RELEASE = 'TO_BE_RELEASE', // 待发版
  RELEASED = 'RELEASED', // 已发版
}
export enum MRFocusLevel {
  FOCUS_ON = 'FOCUS_ON',
  NOT_FOCUS_ON = 'NOT_FOCUS_ON',
  NORMAL = 'NORMAL',
}

export enum PageType {
  ADD = 'ADD',
  EDIT = 'EDIT',
  READONLY = 'READONLY',
}

export enum ENABLE {
  ENABLE = 1,
  DISABLE = 0,
}

export enum dropDownKey {
  VEHICLE_TYPE = 'VEHICLE_TYPE', // 车型类型
  VEHICLE_HARDWARE_STATUS = 'VEHICLE_HARDWARE_STATUS', // 车辆生命周期
  VEHICLE_CHECK_STATUS = 'VEHICLE_CHECK_STATUS', // 标定检测状态
  VEHICLE_OWNER_USE_CASE = 'VEHICLE_OWNER_USE_CASE', // 车辆归属方
  VEHICLE_BUSINESS_TYPE = 'VEHICLE_BUSINESS_TYPE', // 车辆类型
  DEFAULT_STATUS = 'DEFAULT_STATUS', // 存在未完成维修单

  // 站点管理
  STATION_USECASE = 'STATION_USE_CASE', // 站点用途
  STATION_TYPE = 'STATION_TYPE', // 站点类型
  STATION_STATUS = 'STATION_STATUS', // 站点状态

  // 停靠点管理
  STOP_TYPE = 'STOP_TYPE_NEW', // 点位类型
  STOP_STATUS = 'STOP_STATUS', // 点位状态

  // 代收点管理
  STOP_COLLECTION_STATUS = 'STOP_COLLECTION_STATUS', // 代收点状态

  // 开机启动异常管理
  VEHICLE_EXCEPTION_DEVICE = 'VEHICLE_EXCEPTION_DEVICE', // 车辆异常设备
  VEHICLE_EXCEPTION_MODULE = 'VEHICLE_EXCEPTION_MODULE', // 车辆异常模块
}

export enum dropDownListKey {
  VEHICLE_TYPE = 'vehicleTypeList', // 车型类型
  VEHICLE_HARDWARE_STATUS = 'vehicleHardwareStatusList', // 车辆生命周期
  VEHICLE_CHECK_STATUS = 'vehicleCheckStatusList', // 标定检测状态
  VEHICLE_OWNER_USE_CASE = 'vehicleOwnerUseCaseList', // 车辆归属方
  VEHICLE_BUSINESS_TYPE = 'vehicleBusinessTypeList', // 车辆类型
  DEFAULT_STATUS = 'defaultStatusList', // 存在未完成维修单

  // 站点管理
  STATION_USECASE = 'stationUseCaseList', // 站点用途
  STATION_TYPE = 'stationTypeList', // 站点类型
  STATION_STATUS = 'stationStatusList', // 站点状态

  // 停靠点管理
  STOP_TYPE = 'stopTypeNewList', // 点位类型
  STOP_STATUS = 'stopStatusList', // 点位状态

  // 代收点管理
  STOP_COLLECTION_STATUS = 'stopCollectionStatusList', // 代收点状态

  // 开机启动异常管理
  VEHICLE_EXCEPTION_DEVICE = 'vehicleExceptionDeviceList', // 车辆异常设备
  VEHICLE_EXCEPTION_MODULE = 'vehicleExceptionModuleList', // 车辆异常模块
}

export enum DeployPlanSession {
  VEHICLE_CONFIRMATION = 'VEHICLE_CONFIRMATION', // 车号确认
  VEHICLE_INSURANCE = 'VEHICLE_INSURANCE', //车辆保险
  VEHICLE_ALLOCATION = 'VEHICLE_ALLOCATION', // 车辆调拨
  INITIATOR_VERIFICATION = 'INITIATOR_VERIFICATION', // 发起人核实
  ROUTE_CONFIRMATION = 'ROUTE_CONFIRMATION', // 确认路线
  MAP_COLLECTION = 'MAP_COLLECTION', // 地图采集
  MAP_PRODUCTION = 'MAP_PRODUCTION', // 地图制作
  ROUTE_TEST_RUN = 'ROUTE_TEST_RUN', // 线路试跑
  STATION_DELIVERY = 'STATION_DELIVERY', // 站点交付
  COMPLETED = 'COMPLETED', // 已完成
}

export enum MapVehicleSource {
  ALLOCATED_VEHICLES = 'ALLOCATED_VEHICLES', // 自有车采图
  OTHER_VEHICLES = 'OTHER_VEHICLES', //借车采图
}

export enum DeployPlanType {
  MAP_SUPPLEMENT_PLAN = 'MAP_SUPPLEMENT_PLAN', // 地图补充计划
  VEHICLE_ALLOCATION_PLAN = 'VEHICLE_ALLOCATION_PLAN', //车辆调拨计划
  STATION_DEPLOYMENT_PLAN = 'STATION_DEPLOYMENT_PLAN',
}

export enum DeployPlanStageStatus {
  COMPLETED = 'COMPLETED',
  NOT_STARTED = 'NOT_STARTED',
  PROCESSING = 'PROCESSING',
}

export enum VehicleSupplier {
  JD = 'JD',
  NEOLIX = 'NEOLIX',
  RINO = 'RINO',
}
