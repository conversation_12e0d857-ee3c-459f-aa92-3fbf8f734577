import React from 'react';
import { ENABLE } from '@/utils/enum';
import { Popconfirm } from 'antd';
import './index.scss';

export const BtnsTitleMap = new Map([
  [ENABLE.ENABLE, '停用'],
  [ENABLE.DISABLE, '启用'],
]);
export const EnableKeyMap = new Map([
  [0, 1],
  [1, 0],
]);

interface Props {
  message: string;
  onConfirm: Function;
  status: number;
  okText?: string;
  cancelText?: string;
}
const CommonEnableBtn = (props: Props) => {
  const { message, onConfirm, status, okText, cancelText } = props;

  return (
    <Popconfirm
      placement="left"
      title={message}
      onConfirm={() => onConfirm()}
      okText={okText ?? '确定'}
      cancelText={cancelText ?? '取消'}
      overlayStyle={{ maxWidth: 800 }}
    >
      <a className={`enable-btn-${status}`}>{BtnsTitleMap.get(status)}</a>
    </Popconfirm>
  );
};

export default React.memo(CommonEnableBtn);
