import { request } from '@/fetch/core';
class FetchApi {
  /**
   * 获取驾驶团队列表接口
   * @return {Promise}
   */
  public fetchCockpitTeamList = async (): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'GET',
      path: '/k2/management/cockpit_team/get_cockpit_team_list',
    };
    return request(requestOptions);
  };

  /**
   *  分页获取驾驶舱列表接口
   * @param {object} params
   * @param {number} params.pageNum
   * @param {number} params.pageSize
   * @param {string} params.cockpitNumber
   * @param {string} params.cockpitTeamNumber
   * @return {Promise}
   */
  public fetchCockpitInfoPageList = async (params: {
    pageNum: number;
    pageSize: number;
    cockpitNumber?: string;
    cockpitTeamNumber?: string;
    cockpitType?: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: `/k2/management/cockpit/cockpit_info_get_page_list`,
      urlParams: {
        pageNum: params.pageNum,
        pageSize: params.pageSize,
      },
      body: {
        cockpitNumber: params.cockpitNumber,
        cockpitTeamNumber: params.cockpitTeamNumber,
        cockpitType: params.cockpitType,
      },
    };
    return request(requestOptions);
  };

  /**
   * 新建驾驶舱接口
   * @param {object} params
   * @param {string} params.cockpitNumber
   * @param {string} params.cockpitName
   * @param {string} params.cockpitTeamNumber
   * @return {Promise}
   */
  public addCockpit = async (params: {
    cockpitNumber: string;
    cockpitName: string;
    cockpitTeamNumber: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/k2/management/cockpit/add_cockpit',
      body: params,
    };
    return request(requestOptions);
  };

  /**
   * 获取驾驶舱信息接口
   * @param {string} cockpitNumber
   * @return {Promise}
   */
  public getCockpitInfo = async (cockpitNumber: string): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'GET',
      path: '/k2/management/cockpit/get_cockpit_info',
      urlParams: {
        cockpitNumber: cockpitNumber,
      },
    };
    return request(requestOptions);
  };

  /**
   * 编辑驾驶舱信息接口
   * @param {object} params
   * @param {string} params.cockpitNumber
   * @param {string} params.cockpitName
   * @param {string} params.cockpitTeamNumber
   * @return {Promise}
   */
  public editCockpitInfo = async (params: {
    cockpitNumber: string;
    cockpitName: string;
    cockpitTeamNumber: string;
    deviceNameList: string[];
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/k2/management/cockpit/edit_cockpit_info',
      body: params,
    };
    return request(requestOptions);
  };

  /**
   * 删除驾驶舱接口
   * @param {string} cockpitNumber
   * @return {Promise}
   */
  public deleteCockpit = async (cockpitNumber: string): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'DELETE',
      path: '/k2/management/cockpit/delete_cockpit',
      urlParams: {
        cockpitNumber: cockpitNumber,
      },
    };
    return request(requestOptions);
  };

  /**
   * 获取全量的车辆的接口
   * @param {object} params 请求参数
   * @param {Array} params.cityIdList 城市Id列表
   * @param {Array} params.stationIdList 站点Id列表
   * @param {Array} params.vehicleNameList 车牌号列表
   * @param {string} params.cockpitTeamNumber 远驾团队编号
   * @return {Promise}
   */
  fetchStandardVehicle(params: {
    provinceIdList: any[];
    cityIdList: any[];
    stationBaseIdList: any[];
    vehicleNameList: any[];
    cockpitTeamNumber: string;
  }): Promise<any> {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/k2/management/cockpit_team/get_device_info',
      body: params,
    };

    return request(requestOptions);
  }

  // 绑定车辆搜索条件下拉框
  getDepartmentOptions(params: any) {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/k2/management/cockpit_team/get_position_list',
      body: params,
    };

    return request(requestOptions);
  }

  // 查看绑定车辆
  getBindedVehicle(params: string) {
    const requestOptions: RequestOptions = {
      method: 'GET',
      path: '/k2/management/cockpit/get_bind_device_info',
      urlParams: {
        cockpitNumber: params,
      },
    };

    return request(requestOptions);
  }

  /**
   * 获取站点用途和站点类型下拉列表
   * @param {object} params 请求参数
   * @return {Promise}
   */
  public fetchStationUseCaseAndType = async (params: {
    keyList: string[];
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common_get_drown_list',
      body: params,
    };
    return request(requestOptions);
  };

  /** 获取ApiKey */
  public getAPIKey = async (params: {
    cockpitNumber: string;
  }): Promise<any> => {
    const requestOptions: RequestOptions = {
      method: 'GET',
      path: '/k2/management/cockpit/get_api_key',
      urlParams: params,
    };
    return request(requestOptions);
  };
}

export default FetchApi;
