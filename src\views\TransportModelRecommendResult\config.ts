import { TransportModelApi } from "@/fetch/business/transportModel";
import { HttpStatusCode } from "@/fetch/core/constant";
import { YESNO } from "@/utils/enum"
import { FormConfig } from "@jd/x-coreui"
import dayjs from "dayjs";

const fetchApi = new TransportModelApi();

export const SearchConfig: FormConfig = {
    fields: [
        {
            fieldName: 'dt',
            label: '日期',
            placeholder: '请选择日期',
            type: 'datePicker',
            disabledDate: (current) => {
                return current > dayjs().subtract(1, 'day');
            },
        },
        {
            fieldName: 'recommandUnmannedErcheng',
            label: '是否推荐接驳',
            placeholder: '是否推荐接驳',
            type: 'select',
            options: [
                { label: '是', value: YESNO.YES },
                { label: '否', value: YESNO.NO }
            ]
        },
        {
            fieldName: 'provinceCompanyCode',
            label: '省区',
            placeholder: '请选择省区',
            type: 'select',
            showSearch: true,
        },
        {
            fieldName: 'areaId',
            label: '片区',
            placeholder: '请选择片区',
            type: 'select',
            showSearch: true,
        },
        {
            fieldName: 'cityId',
            label: '城市',
            placeholder: '请选择城市',
            type: 'select',
            showSearch: true,
        },
        {
            fieldName: 'siteId',
            label: '站点',
            placeholder: '请选择站点',
            type: 'select',
            showSearch: true,
        },
        {
            fieldName: 'roadareaId',
            label: '路区',
            placeholder: '请选择路区',
            type: 'select',
            showSearch: true,
        },
    ],
    linkRules: {
        siteId: [
            {
                linkFieldName: 'roadareaId',
                rule: 'clear'
            },
            {
                linkFieldName: 'roadareaId',
                rule: 'fetchData',
                fetchFunc: async (val) => {
                    if (!val) {
                        return [];
                    }
                    const res = await fetchApi.getModelResultRoadSelect(val.value);
                    if (res.code === HttpStatusCode.Success) {
                        return res.data.map((item) => {
                            return {
                                label: item.value + '-' + item.name,
                                value: item.value,
                            };
                        });
                    } else {
                        return [];
                    }
                },
            }
        ]
    }
}

export const TableConfig: any[] = [
    {
        title: '日期',
        dataIndex: 'dt',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '省区id',
        dataIndex: 'provinceCompanyCode',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '省区名称',
        dataIndex: 'provinceCompanyName',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '片区id',
        dataIndex: 'areaId',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '片区名称',
        dataIndex: 'areaName',
        align: 'center',
        width: 200,
        ellipsis: true,
    },
    {
        title: '城市id',
        dataIndex: 'cityId',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: '城市名称',
        dataIndex: 'cityName',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '站点id',
        dataIndex: 'siteId',
        align: 'center',
        width: 120,
        ellipsis: true
    },
    {
        title: '站点名称',
        dataIndex: 'siteName',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '路区编号',
        dataIndex: 'roadareaId',
        align: 'center',
        width: 120,
        ellipsis: true,
        fixed: 'left',
    },
    {
        title: '路区名称',
        dataIndex: 'roadareaName',
        align: 'center',
        width: 200,
        ellipsis: true,
        fixed: 'left',
    },
    {
        title: '路区类型',
        dataIndex: 'roadareaType',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '是否推荐接驳',
        dataIndex: 'recommandUnmannedErcheng',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '真实作业模式类型',
        dataIndex: 'realJobmode',
        align: 'center',
        width: 150,
        ellipsis: true
    },
    {
        title: '路区中心点与站点距离',
        dataIndex: 'diatance',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '站点维度增加无人车数量',
        dataIndex: 'unmannedVehicleNumAvg',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '线路',
        dataIndex: 'line',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '线路派送单量',
        dataIndex: 'lineTtdlNum',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '线路C揽单量',
        dataIndex: 'lineCCollectNum',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '线路排名',
        dataIndex: 'rank',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '线路总单量',
        dataIndex: 'lineAllNum',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '站点天度揽收班次总数量',
        dataIndex: 'totalcollectwavecnt',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '站点天度派件班次总数量',
        dataIndex: 'totaldeliverwavecnt',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '站点下揽收班次号',
        dataIndex: 'collectWaveCode',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '站点下派送班次号',
        dataIndex: 'delieverWaveCode',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '可推荐揽收班次号',
        dataIndex: 'recommendcollectwave',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '可推荐揽收班次数量',
        dataIndex: 'recommendcollectwavecnt',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '可推荐派送班次号',
        dataIndex: 'recommenddeliverwave',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '可推荐派送班次数量',
        dataIndex: 'recommenddeliverwavecnt',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '可推荐揽收班次线路对应单量',
        dataIndex: 'recommendcollectwavelinebillcnt',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '可推荐派送班次线路对应单量',
        dataIndex: 'recommenddeliverwavelinebillcnt',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '揽派接驳属性',
        dataIndex: 'type',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '路区全天B端揽收单量',
        dataIndex: 'bCollectOrder',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '路区全天C端揽收单量',
        dataIndex: 'cCollectOrder',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '路区妥投单量',
        dataIndex: 'deliverOrder',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '路区妥投及时率',
        dataIndex: 'properTimelyRateAvg',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '路区C揽及时率',
        dataIndex: 'siteFirstPickupRateAvg',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '路区维度预计揽收及时率提升',
        dataIndex: 'roadCollectOntimeRateIncreaseAvg',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '站点维度预计揽收及时率提升',
        dataIndex: 'siteCollectOntimeIncreaseAvg',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '路区维度预计路区总人效提升',
        dataIndex: 'roadCourierProductionIncreaseAvg',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '站点维度预计路区总人效提升',
        dataIndex: 'siteCourierProductionIncreaseAvg',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '路区催揽单量',
        dataIndex: 'collectOverTimeCountAvg',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '路区消单率',
        dataIndex: 'cancelRateAllAvg',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '是否四轮车',
        dataIndex: 'useCar',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '路区单均重量',
        dataIndex: 'goodsWeight',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '路区单均体积',
        dataIndex: 'goodsVolume',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '接驳点',
        dataIndex: 'relayPoint',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '接驳点地址',
        dataIndex: 'relayPointAddress',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '是否需要接驳',
        dataIndex: 'needFlag',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '不需要接驳原因',
        dataIndex: 'reason',
        align: 'center',
        width: 200,
        ellipsis: true
    },
    {
        title: '操作',
        dataIndex: 'operation',
        align: 'center',
        width: 150,
        ellipsis: true,
        fixed: 'right',
    },
]

export const OperationConfig: FormConfig = {
    fields: [
        {
            fieldName: 'needFlag',
            label: '是否需要接驳',
            type: 'radioGroup',
            options: [
                { label: '是', value: YESNO.YES },
                { label: '否', value: YESNO.NO }
            ],
            required: true,
            validatorRules: [{ required: true, message: '请选择是否需要接驳' }],
            wrapperCol: { span: 24 },
        },
        {
            fieldName: 'reason',
            label: '不需要接驳原因',
            placeholder: '请输入不需要接驳原因',
            type: 'input',
            maxLength: 50,
            wrapperCol: { span: 24 },
            hidden: true
        },
    ],
    linkRules: {
        needFlag: [
            {
                linkFieldName: 'reason',
                rule: 'visible',
                dependenceData: [YESNO.NO],
            }
        ]
    }
}