import { createSlice } from '@reduxjs/toolkit';
// eslint-disable-next-line no-unused-vars
import { RootState } from '../store';

const vehicleManageState = createSlice({
  name: 'vehicleManage',
  initialState: {
    selectVehicle: [],
    selectedRowKeys: [],
  },
  reducers: {
    handleVehicleManage(state, action) {
      state.selectVehicle = action.payload;
      state.selectedRowKeys = action.payload;
    },
    clearVehicleManage(state, action) {
      state.selectVehicle = [];
    },
  },
});

export const { handleVehicleManage, clearVehicleManage } =
  vehicleManageState.actions;
export const vehicleManageSelector = (state: RootState) => {
  return state.vehicleManage;
};
export default vehicleManageState.reducer;
