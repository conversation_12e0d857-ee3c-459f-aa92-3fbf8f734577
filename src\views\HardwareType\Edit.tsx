import { Form, Radio, message, Row, Col, Select, Input } from 'antd';
import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { CommonEdit } from '@/components';
import StatusItem from './component/EditStatus';
import AddUsage from './component/EditUsage';
import { HardwareTypeApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { formatLocation } from '@/utils/utils';
import { YESNO } from '@/utils/enum';
import { useEditPageData } from '@/utils/hooks';
import { HardwareTypeTitle, PageType } from '@/utils/EditTitle';

const HardwareTypeEdit = () => {
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const fetchApi = new HardwareTypeApi();
  const [form] = Form.useForm();
  const { id, type } = formatLocation(window.location.search);
  const useStyle: React.CSSProperties = {
    color: '#31C2A6',
    marginLeft: 20,
  };
  const unuseStyle: React.CSSProperties = {
    color: 'red',
  };

  const [detailData, setDetailData] = useState<any>({
    enable: 1,
  });

  useEffect(() => {
    if (id) {
      getDetail();
    } else {
      form.setFieldsValue(detailData);
    }
  }, []);

  const getDetail = async () => {
    const res = await fetchApi.fetchHardwearTypeDetail(id);
    if (res.code === HttpStatusCode.Success) {
      setDetailData(res.data);
      form.setFieldsValue(res.data);
    } else {
      message.error(res.message);
      navigator(-1);
    }
  };

  const onSubmitClick = async () => {
    const formValue = await form.validateFields();
    if (formValue.isBox == '1' && formValue.isSensor == '1') {
      message.error('硬件类型不能同时选择传感器和箱体');
      return;
    }
    const result: any = await fetchApi.submitHardwareType({
      type,
      requestBody: formValue,
    });
    if (result.code === HttpStatusCode.Success) {
      message.success(result.message);
      navigator('/app/hardwareType');
    } else {
      message.error(result.message);
    }
  };

  const onCancleClick = () => {
    navigator('/app/hardwareType');
  };

  return (
    <div style={{ padding: '10px' }}>
      <CommonEdit
        title={HardwareTypeTitle[type!]}
        breadCrumbConfig={[
          {
            title: '硬件类型管理',
            route: '',
          },
          {
            title: HardwareTypeTitle[type!],
            route: '',
          },
        ]}
        onSubmitClick={onSubmitClick}
        onCancleClick={onCancleClick}
        hideSubmit={type === PageType.READONLY}
        cancelTitle={type === PageType.READONLY ? '返回' : '取消'}
      >
        <Form
          form={form}
          labelCol={{
            span: 4,
          }}
          wrapperCol={{
            span: 18,
          }}
        >
          <Form.Item
            label="类型名称"
            name="name"
            rules={[{ required: true, message: '请输入类型名称' }]}
          >
            <Input
              disabled={type === PageType.READONLY}
              maxLength={20}
              placeholder="请输入类型名称"
            />
          </Form.Item>
          <Form.Item required={true} label="类型Id" name="id">
            <Input maxLength={20} placeholder="系统生成" disabled />
          </Form.Item>
          <Form.Item
            name="isSensor"
            label="是否属于传感器"
            rules={[{ required: true, message: '请确认是否属于传感器' }]}
          >
            <Radio.Group
              disabled={type === PageType.READONLY}
              options={[
                {
                  label: '是',
                  value: 1,
                  style: useStyle,
                },
                {
                  label: '否',
                  value: 0,
                  style: unuseStyle,
                },
              ]}
            />
          </Form.Item>
          <Form.Item
            name="isBox"
            label="是否属于箱体"
            rules={[{ required: true, message: '请确认是否属于箱体' }]}
          >
            <Radio.Group
              disabled={type === PageType.READONLY}
              options={[
                {
                  label: '是',
                  value: 1,
                  style: useStyle,
                },
                {
                  label: '否',
                  value: 0,
                  style: unuseStyle,
                },
              ]}
            />
          </Form.Item>
          <AddUsage
            disable={type === PageType.READONLY}
            initValues={detailData && detailData.hardwareTypeUsageList}
            form={form}
          />
          <Form.Item
            name="enable"
            label="类型状态"
            rules={[{ required: true, message: '请选择类型状态' }]}
          >
            <Radio.Group
              disabled={type === PageType.READONLY}
              options={[
                {
                  label: '有效',
                  value: 1,
                  style: useStyle,
                },
                {
                  label: '无效',
                  value: 0,
                  style: unuseStyle,
                },
              ]}
            />
          </Form.Item>
          <Form.Item label=" " colon={false}>
            <div style={{ color: '#808080' }}>
              <div>状态说明：</div>
              <div>
                1、只有“有效”的硬件类型，才能被引用成为一个类型选择项，“无效”的类型，不能被引用；
              </div>
              <div>
                2、类型状态从“有效”改为“无效”，历史有被引用，历史数据不受影响，再修改，该无效类型不在选择项范围内。
              </div>
            </div>
          </Form.Item>
        </Form>
      </CommonEdit>
    </div>
  );
};

export default React.memo(HardwareTypeEdit);
