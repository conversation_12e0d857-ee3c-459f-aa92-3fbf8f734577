import React, { useEffect, useRef, useState } from 'react';
import qs from 'qs';
import './index.scss';
import { useLocation } from 'react-router-dom';
import {
  addGlobalEventListener,
  removeGlobalEventListener,
} from '@/utils/emit';
const CommonIframePage = () => {
  const location = useLocation();
  // const queryObj: any = qs.parse(location.search.slice(11));
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [iframeKey, setIframeKey] = useState<any>('');
  const [iframeUrl, setIframeUrl] = useState<any>('');

  useEffect(() => {
    const savedIframeUrl = sessionStorage.getItem('iframeUrl');
    if (savedIframeUrl) {
      setIframeUrl(savedIframeUrl);
    } else {
      setIframeUrl(location.search.slice(11));
    }
  }, [location]);
  useEffect(() => {
    window.addEventListener(
      'message',
      (event) => {
        if (event.data.eventName === 'CHANGE_LOCATION') {
          sessionStorage.setItem('iframeUrl', event.data.data);
        }
      },
      false,
    );
    addGlobalEventListener('forUpdateIframe', (iframeUrl: string) => {
      setIframeKey(Date.now);
    });
    return () => {
      removeGlobalEventListener('forUpdateIframe', (iframeUrl: string) => {
        setIframeKey(Date.now);
      });
      window.removeEventListener('message', () => {});
    };
  }, []);
  return (
    <>
      <iframe
        key={iframeKey}
        ref={iframeRef}
        className="iframe-content"
        src={iframeUrl}
      />
    </>
  );
};

export default CommonIframePage;
