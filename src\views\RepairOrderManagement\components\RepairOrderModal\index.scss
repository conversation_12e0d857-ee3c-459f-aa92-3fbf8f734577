.repair-model-out {
  .#{$ant-prefix}-modal-content {
    border: 1px solid;
    .#{$ant-prefix}-modal-footer {
      border-top: none !important;
      margin-bottom: 20px;
    }
    .#{$ant-prefix}-modal-body {
      padding: 0 !important;
    }
  }
}
.repair-order {
  // border: 1px solid;
  // margin: 20px;
  .handle-result {
    border-top: 1px solid rgb(233, 231, 231);
  }
  .confirm-complete {
    border-top: 1px solid rgb(233, 231, 231);
  }
  .module-title {
    padding-left: 25px;
    display: flex;
    align-items: center;
    width: 150px;
    height: 40px;
    font-size: 16px;
    font-weight: 700;
    // text-indent: 20px;
    color: black;
  }
}
.repair-model {
  .repair-btn {
    display: flex;
    background-color: white;
    justify-content: center;

    button {
      margin-left: 30px;
      height: 30px;
      width: 150px;
      background: inherit;
      border-radius: 5px;
    }
  }
}
