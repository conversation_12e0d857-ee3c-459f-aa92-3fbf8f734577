import { request } from '@/fetch/core';
import { parseResponseBody } from '@/utils/utils';

export enum downLoadUrlType {
  CARD = 2,
  VEHICLE = 1,
}
export class CommonApi {
  getStationDepartment(params: {
    stationProductType?: 'vehicle' | 'robot' | 'integrate';
    cityIdList?: any[];
    stationUseCaseList?: any[];
    stationType?: string;
    enable?: any;
    companyNumber?: string;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common/get_cascade_station_address_list',
      body: params,
    };
    return request(options);
  }

  getCityDepartment(params: Object) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common/get_state_city_address_list',
      body: params,
    };
    return request(options);
  }

  getCommonDropDown({ keyList }: { keyList: Array<string> }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common/common_get_down_list',
      body: { keyList },
    };
    return request(options);
  }

  fetchERP() {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common/common_get_erp_list',
    };
    return request(options);
  }

  getUserNameList(userName: string) {
    const options: RequestOptions = {
      method: 'GET',
      path: '/permission/server/basic/user/get_user_info_of_company_by_username',
      urlParams: {
        userName,
        appCode: 'operation-platform-ui',
      },
    };
    return request(options);
  }

  changeUser(userName: string) {
    const options: RequestOptions = {
      method: 'GET',
      path: '/authentication/server/token/change_token',
      urlParams: {
        userName,
      },
    };
    return request(options);
  }
  getDownloadURL(type: downLoadUrlType) {
    const options: RequestOptions = {
      method: 'GET',
      path: '/k2/management/upload/get_download_url',
      urlParams: {
        type,
      },
    };
    return request(options);
  }

  async upload(files: any[], requestType?: string) {
    const initReq: RequestInit = {};
    initReq.method = 'POST';
    initReq.mode = 'cors';
    initReq.credentials = 'include';
    const formData = new FormData();
    files.forEach((file: any) => {
      formData.append('file', file);
    });
    formData.append('requestType', requestType ?? 'COMMON_FILE_UPLOAD');
    formData.append('eventType', requestType ?? 'COMMON_FILE_UPLOAD');
    formData.append('pathParameter', '');
    formData.append('requestBody', '');
    formData.append('requestId', `${Date.now()}`);
    initReq.body = formData;
    let uploadUrl = '/k2/management/common/common_file_upload';
    if (requestType == 'VEHICLE_BATCH_ADD') {
      uploadUrl = '/k2/management/upload/device_batch_add';
    } else if (requestType == 'VEHICLE_CARD_NO_BATCH_ADD') {
      uploadUrl = '/k2/management/upload/device_card_no_batch_add';
    }

    const response = await window.fetch(
      `//${process.env.JDX_APP_FETCH_DATA_DOMAIN}${uploadUrl}`,
      initReq,
    );
    const respBody = await parseResponseBody(response, 'application/json');
    if (respBody) {
      return respBody;
    } else {
      return {
        code: '-200',
        message: '上传失败',
      };
    }
  }
}
