import React, { useState } from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import './index.scss';
import Header from './components/Header';
import { Modal } from 'antd';
import CommonMenu from './components/CommonMenu';
import { LoginState } from '@/utils/enum';

import { LoginApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';

const MainLayout = () => {
  const [menuVisible, setMenuVisible] = useState(true);

  const changeMenu = () => {
    setMenuVisible(!menuVisible);
  };
  return (
    <div className="main-layout-content">
      <Header />
      <div className="main-body-content">
        <div
          className="main-menu-content"
          style={{
            display: menuVisible ? 'block' : 'none',
          }}
        >
          <CommonMenu />
        </div>

        <div className="menu-wrap">
          <img
            src={
              menuVisible
                ? require('@/assets/image/common/menu-retract.png')
                : require('@/assets/image/common/menu-spread.png')
            }
            className="menu-btn"
            onClick={changeMenu}
          />
        </div>

        <div className="main">
          <div id="qiankun-root"></div>
          <Outlet></Outlet>
        </div>
      </div>
    </div>
  );
};
export default MainLayout;
