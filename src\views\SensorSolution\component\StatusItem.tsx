/* eslint-disable no-unused-vars */

import { Form, FormInstance, Radio } from 'antd';
import React, { useEffect, useState } from 'react';

const StatusItem = ({
  disable,
  name,
  label,
  form,
  items,
  defaultValue,
  initValues,
}: {
  disable?: boolean;
  name: string;
  label: string;
  form: FormInstance;
  items: any[] | null;
  defaultValue?: any;
  initValues?: any;
}) => {
  const [value, setValue] = useState(defaultValue);
  useEffect(() => {
    if (initValues !== null && initValues !== undefined) {
      setValue(initValues);
      form.setFieldsValue({
        [name]: initValues,
      });
    } else {
      form.setFieldsValue({
        [name]: defaultValue,
      });
    }
  }, [initValues]);

  const useStyle: React.CSSProperties = {
    color: '#31C2A6',
    marginLeft: 20,
  };
  const unuseStyle: React.CSSProperties = {
    color: 'red',
  };

  const makeRadioOptions = () => {
    return items?.map((item: any) => {
      return {
        label: item.name,
        value: item.code,
        style: item.code === 1 ? useStyle : unuseStyle,
      };
    });
  };
  return (
    <Form.Item
      name={name}
      label={label}
      rules={[{ required: true, message: '请确认是否属于传感器' }]}
    >
      <Radio.Group
        disabled={disable}
        onChange={(e: any) => {
          setValue(e.target.value);
          form.setFieldsValue({
            [name]: e.target.value,
          });
        }}
        options={makeRadioOptions()}
      />
    </Form.Item>
  );
};

export default React.memo(StatusItem);
