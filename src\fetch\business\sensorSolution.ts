import { YESNO } from '@/utils/enum';
import { request } from '../core';
import { PageType } from '@/utils/enum';

export class SensorSolutionApi {
  // 分页查询传感器方案数据列表
  fetchTableList({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/sensor_scheme/sensor_scheme_get_page_list',
      urlParams: {
        pageNum,
        pageSize,
      },
      body: searchForm,
    };
    return request(options);
  }
  //  新增编辑传感器
  submitSensorScheme({
    type,
    requestBody,
  }: {
    type: PageType;
    requestBody: Object;
  }) {
    const options: RequestOptions = {
      method: type === PageType.EDIT ? 'PUT' : 'POST',
      path:
        type === PageType.EDIT
          ? '/k2/management/sensor_scheme/sensor_scheme_edit'
          : '/k2/management/sensor_scheme/sensor_scheme_add',
      body: requestBody,
    };
    return request(options);
  }
  // 获取传感器方案详情
  fetchSensorSchemeDetail(id: number | string) {
    const options: RequestOptions = {
      method: 'GET',
      path: '/k2/management/sensor_scheme/sensor_scheme_get_detail',
      urlParams: { id },
    };
    return request(options);
  }
  // 启用停用传感器方案
  updateSensorSchemeStatus({ id, enable }: { id: number; enable: YESNO }) {
    const options: RequestOptions = {
      method: 'PUT',
      path: '/k2/management/sensor_scheme/sensor_scheme_update_enable',
      body: {
        id,
        enable,
      },
    };
    return request(options);
  }

  fetchLinkedListData(
    types: 'sensorList' | 'usageList' | 'hardwearList',
    id: any,
  ) {
    const params =
      types === 'sensorList'
        ? { id: id }
        : types === 'usageList'
        ? { hardwareTypeId: id }
        : { hardwareTypeUsageId: id };
    let reqType: any = null;
    if (types === 'sensorList') {
      reqType =
        '/k2/management/hardware_type/hardware_type_sensor_get_select_list';
    } else if (types === 'usageList') {
      reqType =
        '/k2/management/hardware_type/hardware_type_usage_get_select_list';
    } else if (types === 'hardwearList') {
      reqType = '/k2/management/hardware_model/hardware_model_get_select_list';
    } else {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve({
            code: '-200',
            message: '请求错误',
          });
        }, 1000);
      });
    }
    const options: RequestOptions = {
      method: 'GET',
      path: reqType,
      urlParams: params,
    };
    return request(options);
  }

  // 是否可以删除传感器方案
  canDelete({ id }: { id: number }) {
    const options: RequestOptions = {
      method: 'GET',
      path: '/k2/management/sensor_scheme/sensor_scheme_can_delete',
      urlParams: {
        id,
      },
    };
    return request(options);
  }
}
