import React, { useEffect, useState } from 'react';
import BasicInfo from './components/BasicInfo';
import BreadCrumb from '@/components/BreadCrumb';
import './index.scss';
import ContactStation from './components/ContactStation';
import { Button, Form, message, Tabs, Col, Row, Input } from 'antd';
import FormTitle from '@/components/FormTitle';
import { formatLocation } from '@/utils/utils';
import { BindCrossStopPageType, BindStoppointTab } from '@/utils/constant';
// eslint-disable-next-line no-unused-vars
import {
  findSelectedPoint,
  formatCrossStopPointList,
  formatLinPointList,
} from './utils/format';
import { useNavigate } from 'react-router';
import { HttpStatusCode } from '@/fetch/core/constant';
import BindCrossStop from './components/BindCrossStop';
import EditModuleTitle from '@/components/EditModuleTitle';
import { StationFetchApi } from '@/fetch/business';
import { StationDetailTabKeys } from '../../StationDetail';
// 车辆配置，数据初始化格式
const basic = {
  basicForm: {
    id: 0,
    productTypeName: '',
    businessTypeName: '',
    boxTemplateName: '',
    deviceName: '',
    serialNo: '',
    gridList: [],
  },
  vcode: '',
  linkPoint: {
    homeList: [],
    loadList: [],
    pickList: [],
    unloadList: [],
  },
};
const requestParams = {
  deviceBaseId: 0,
  vcode: '',
};
const breadCrumbItems = [
  { title: '关联车辆', route: '' },
  { title: '车辆配置', route: '' },
];
const fetchApi = new StationFetchApi();
const VehicleEdit = () => {
  const locationMap = formatLocation(window.location.search);
  const vehicleId = locationMap.deviceBaseId;
  const stationBaseId = locationMap.stationBaseId;
  const [form] = Form.useForm();
  requestParams.deviceBaseId = parseInt(vehicleId);
  const [vehicleManage, setVehicleManage] = useState<any>(basic);
  const navigator = useNavigate();
  const [bindStopTab, setBindStopTab] = useState<string>(
    BindStoppointTab.BINDCURRENTSTOPPOINT,
  );
  const tabMenu = [
    {
      key: BindStoppointTab.BINDCURRENTSTOPPOINT,
      name: '本站停靠点',
    },
    {
      key: BindStoppointTab.BINDCROSSSTOPPOINT,
      name: '跨站停靠点',
    },
  ];
  const [stationInfo, setStationInfo] = useState<any>(null);
  const [crossStopInfo, setCrossStopInfo] = useState<any[]>([]);
  const [selectedPoint, setSelectedPoint] = useState<any>({
    homeList: [],
    loadList: [],
    pickList: [],
    vendingList: [],
  });
  const [allPoint, setAllPoint] = useState<any>({
    homeList: [],
    loadList: [],
    pickList: [],
    vendingList: [],
  });

  useEffect(() => {
    fetchDetail();
  }, []);
  const fetchDetail = async () => {
    try {
      const res: any = await fetchApi.getStationVehicleDetail(vehicleId); // 获取车辆详情
      if (res.code === HttpStatusCode.Success) {
        const resp = res.data; // 处理获取到的车辆详情信息,将信息分类
        setAllPoint(formatLinPointList(res.data.linkPointList));
        setSelectedPoint({
          ...findSelectedPoint(
            formatLinPointList(res.data.linkPointList),
            false,
          ),
        });
        setStationInfo({
          cityId: resp.cityId,
          stationId: resp.stationBaseId,
        });
        requestParams.vcode = resp.vcode;
        form.setFieldValue('vcode', resp.vcode);
        setVehicleManage({
          basicForm: {
            productTypeName: resp.productTypeName,
            businessTypeName: resp.businessTypeName,
            deviceName: resp.deviceName,
            serialNo: resp.serialNo,
            deviceTypeName: resp.deviceTypeName,
            boxTemplateName: resp.boxTemplateName,
            gridList: resp.gridList,
            deviceTypeBaseId: resp.deviceTypeBaseId,
            boxTemplateId: resp.boxTemplateId,
          },
          vcode: resp.vcode,
          linkPoint: resp.linkPoint,
        });
        setCrossStopInfo(
          formatCrossStopPointList(res.data.jumpStationStopList),
        );
      }
    } catch (e) {
      console.log(e);
    }
  };

  const goBack = () => {
    navigator(
      `/app/stationManagement/detail?stationBaseId=${stationBaseId}&tabKey=${StationDetailTabKeys.LINKED_VEHICLE}`,
    );
  };

  /**
   * 在用车辆编辑、调度池车辆编辑提交
   */
  const confirm = async () => {
    try {
      // 在用车辆
      if (selectedPoint.homeList.length < 1) {
        message.error('本站home点不能为空');
        return;
      }
      if (requestParams.vcode == '') {
        message.info('表单填写不完成，请重新填写');
      } else {
        const invalidStation: any[] = [];
        const invalidHome: any[] = [];
        crossStopInfo.forEach((item: any) => {
          if (!item.stationInfo) {
            invalidStation.push(item);
            return;
          }
          if (item.homeList.length <= 0) {
            invalidHome.push(item);
            return;
          }
        });
        if (invalidStation.length > 0) {
          message.error('跨站站点名称不能为空');
          return;
        }
        if (invalidHome.length > 0) {
          message.error(
            `操作失败，站点名称${invalidHome[0].stationInfo.name}home点不能为空！`,
          );
          return;
        }
        const linkedStopList: number[] = [];
        let homeStopId: number = 0;
        crossStopInfo.length > 0 &&
          crossStopInfo.forEach((item: any) => {
            item.homeList.forEach((homePoint: any) => {
              linkedStopList.push(homePoint.id);
            });
            item.stopList.forEach((stopPoint: any) => {
              linkedStopList.push(stopPoint.id);
            });
          });

        homeStopId = selectedPoint.homeList && selectedPoint.homeList[0].id;
        selectedPoint.loadList &&
          selectedPoint.loadList.forEach((item) => {
            linkedStopList.push(item.id);
          });
        selectedPoint.pickList &&
          selectedPoint.pickList.forEach((item) => {
            linkedStopList.push(item.id);
          });
        selectedPoint.vendingList &&
          selectedPoint.vendingList.forEach((item) => {
            linkedStopList.push(item.id);
          });
        const para: any = {
          ...requestParams,
          homeStopId: homeStopId,
          vcode: vehicleManage.vcode,
          linkedStopList: linkedStopList,
        };
        const res: any = await fetchApi.editStationVehicleDetail(para);
        if (res.code === HttpStatusCode.Success) {
          message.success(res.message);
          navigator(
            `/app/stationManagement/detail?stationBaseId=${stationBaseId}&tabKey=${StationDetailTabKeys.LINKED_VEHICLE}`,
          );
        } else {
          message.error(res.message);
        }
      }
    } catch (e) {
      console.log(e);
    }
  };

  const onChangePoint = (values: any) => {
    setSelectedPoint(values);
  };

  return (
    <div style={{ padding: '10px' }}>
      <BreadCrumb items={breadCrumbItems} />
      <div className="manage-edit">
        <FormTitle title={'车辆配置'} />
        {/* 基本信息 */}
        <BasicInfo basicForm={vehicleManage.basicForm} />
        <div className="business-info">
          <EditModuleTitle title="业务信息"></EditModuleTitle>
          <Row justify="center">
            <Col span={19}>
              <Form className="reform" form={form}>
                <Form.Item
                  name="vcode"
                  label="所属系统业务编号"
                  extra="如对于京东业务，指车辆在青龙系统中的编号。"
                  rules={[{ required: true, message: '请输入业务编号' }]}
                >
                  <Input
                    placeholder="请输入所属系统业务编号"
                    onChange={(e) => {
                      setVehicleManage({
                        ...vehicleManage,
                        vcode: e.target.value,
                      });
                    }}
                  ></Input>
                </Form.Item>
              </Form>
            </Col>
          </Row>
        </div>
        <div className="contact-station">
          <EditModuleTitle title={'关联点位'} />
          <Row justify="center">
            <Col span={19}>
              <Tabs
                size="small"
                activeKey={bindStopTab}
                onChange={(activeKey: string) => setBindStopTab(activeKey)}
                type="line"
              >
                {tabMenu.map((item) => {
                  return (
                    <Tabs.TabPane tab={item.name} key={item.key}></Tabs.TabPane>
                  );
                })}
              </Tabs>
            </Col>
          </Row>
          {bindStopTab === BindStoppointTab.BINDCURRENTSTOPPOINT && (
            <ContactStation
              allPoint={allPoint}
              selectedPoint={selectedPoint}
              onChangePoint={onChangePoint}
              stationId={stationBaseId}
            />
          )}
          {bindStopTab === BindStoppointTab.BINDCROSSSTOPPOINT && (
            <BindCrossStop
              type={BindCrossStopPageType.SINGLE}
              cityId={stationInfo.cityId}
              stationId={stationBaseId}
              crossStopInfo={crossStopInfo}
              onHandleStop={(values: any) => setCrossStopInfo(values)}
            />
          )}
        </div>

        <div className="submit">
          <Button type="primary" onClick={confirm}>
            确认
          </Button>
          <Button onClick={goBack}>取消</Button>
        </div>
      </div>
    </div>
  );
};

export default VehicleEdit;
