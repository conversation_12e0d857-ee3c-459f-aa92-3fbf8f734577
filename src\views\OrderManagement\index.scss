
.order-list-content {
  padding: 10px;

  .search-form{

    // .#{$ant-prefix}-col-xxl-23{
    //   flex: 0 0 27%;
    // }
    // .#{$ant-prefix}-col-md-23{
    //   flex: 0 0 27%;
    // }
  }

  .gridSearch {
    display: flex;
    justify-content: center;
    align-items: center;

    p {
      margin-bottom: 0%;
    }

    .question {
      height: 22px;
      line-height: 22px;
      color: #1677ff;
      display: flex;
    }

    .#{$ant-prefix}-tooltip-inner {
      background-color: #1677ff;
      color: white;
    }

    .#{$ant-prefix}-tooltip-arrow-content {
      --antd-arrow-background-color: #1677ff;
    }
  }

  .packageNo {
    display: flex;
    justify-content: center;
    align-items: center;
    p {
      margin-bottom: 0%;
    }

    .question {
      height: 22px;
      line-height: 22px;
      color: #1677ff;
      margin-top: 4px;
    }

    .#{$ant-prefix}-tooltip-inner {
      background-color: #1677ff;
      color: white;
    }

    .#{$ant-prefix}-tooltip-arrow-content {
      --antd-arrow-background-color: #1677ff;
    }
  }

  .table-list {
    background-color: white;
    margin-top: 16px;
    padding: 16px;

    .#{$ant-prefix}-table-thead tr th:nth-child(2) {
      overflow: visible;
    }
    .#{$ant-prefix}-table-thead tr th:nth-child(3) {
      overflow: visible;
    }
    .#{$ant-prefix}-table-thead tr th:nth-child(4) {
      overflow: visible;
    }

    .phone {
      display: flex;
      justify-content: center;
    }
  }
  .field_stationInfo{
    .#{$ant-prefix}-select-selector{
      max-height: 50px;
      overflow-y: auto;
    }
  }
}
