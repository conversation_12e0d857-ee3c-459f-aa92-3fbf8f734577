import React from 'react';
import { Tabs } from 'antd';
import PointsTable from './PointsTable';
import AreaTable from './AreaTable';
const ResourceManagement = (props: {
  stationBaseId: number;
  warehouseNo: string;
}) => {
  return (
    <Tabs
      items={[
        {
          key: 'POINTS',
          label: '点',
          children: (
            <PointsTable
              stationBaseId={props.stationBaseId}
              warehouseNo={props.warehouseNo}
            />
          ),
        },
        {
          key: 'AREAS',
          label: '管控区',
          children: (
            <AreaTable
              stationBaseId={props.stationBaseId}
              warehouseNo={props.warehouseNo}
            />
          ),
        },
      ]}
    ></Tabs>
  );
};

export default ResourceManagement;
