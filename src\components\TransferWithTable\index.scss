.#{$ant-prefix}-modal.cross-stop-modal {
  .#{$ant-prefix}-modal-header {
    text-align: center;
  }

  .#{$ant-prefix}-modal-footer {
    border-top: none;
    text-align: center;
  }
}

.#{$ant-prefix}-transfer-list {
  .transfer-content {
    padding: 0px 10px 20px 10px;

    .selected-count {
      margin-top: 10px;
      color: rgb(160, 156, 156);
      font-size: 10px;
    }
  }

  .#{$ant-prefix}-transfer-list-header-selected {
    display: none;
  }

  .#{$ant-prefix}-transfer-list-header {
    background-color: #3c6ef0;
    color: white;

    .transfer-title {
      text-align: center;
    }
  }
}

.#{$ant-prefix}-transfer-operation {
  button {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}
