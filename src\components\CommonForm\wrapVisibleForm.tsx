import React, { useEffect, useState } from "react";
function wrapVisibleCommonForm(CommonFormComponent) {
  return function ({ ...props }) {
    const [formVisible, setFormVisible] = useState<boolean>(true);
    const changeFormVisible = () => {
      setFormVisible(prevState => !prevState);
    };
    useEffect(() => {
      if (props.onVisibilityChange) {
        props.onVisibilityChange();
      }
    }, [formVisible]);
    return (
      <>
        <div
          style={{
            display: formVisible ? 'block' : 'none',
          }}
        >
          <CommonFormComponent {...props} />
        </div>
        <div
          style={{
            position: 'relative',
          }}
        >
          <img
            src={
              formVisible
                ? require('@/assets/image/common/menu-retract.png')
                : require('@/assets/image/common/menu-spread.png')
            }
            onClick={changeFormVisible}
            style={{
              width: '12px',
              height: '42px',
              position: 'absolute',
              left: '50%',
              top: formVisible ? '-15px' : '-30px',
              zIndex: '1000',
              cursor: 'pointer',
              rotate: '90deg',
            }}
          />
        </div>
      </>
    );
  }
}

export default wrapVisibleCommonForm;