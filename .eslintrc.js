module.exports = {
  env: {
    browser: true,
    es6: true,
  },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/eslint-recommended',
    'plugin:react/recommended',
  ],
  globals: {
    DEFINED_IS_OFFICIAL: 'readonly',
    DEFINED_IS_PRODUCTION: 'readonly',
    DEFINED_DEBUG_H5_SERVER_HOST_NAME: 'readonly',
    DEFINED_REMOTE_FS: 'readonly',
    Atomics: 'readonly',
    SharedArrayBuffer: 'readonly',
    alert2: 'readonly',
  },
  parserOptions: {
    parser: '@typescript-eslint/parser',
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 2018,
    sourceType: 'module',
    project: ['./tsconfig.json'],
  },
  plugins: ['react', '@typescript-eslint'],
  rules: {
    'no-var': 'error',
    indent: ['error', 2, { SwitchCase: 1 }],
    'no-unused-vars': 0,
    'no-empty': 0,
    'require-atomic-updates': 0,
    eqeqeq: ['error', 'always'],
    semi: ['error', 'always'],
    quotes: ['error', 'single'],
    'comma-dangle': [
      'error',
      {
        arrays: 'always-multiline',
        objects: 'always-multiline',
        imports: 'always-multiline',
        exports: 'always-multiline',
        functions: 'never',
      },
    ],
    'no-trailing-spaces': ['error'],
  },
};
