import React, { useEffect, useState } from 'react';
import BusinessInfo from '.';
import StaticBusinessInfo from './StaticBusinessInfo';
import { StationBusinessInfo } from '@/types';
import { Button, Form, message } from 'antd';
import { AnyFunc } from '@/global';
import { StationFetchApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { ProductType } from '@/utils/enum';
import { isNumber } from '@/utils/utils';
const requestApi = new StationFetchApi();
const EditableBusinessInfo = (props: {
  businessInfo: StationBusinessInfo | null;
  updateDataCb?: AnyFunc;
  stationType: ProductType | undefined;
  stationBaseId: number;
}) => {
  const { businessInfo, updateDataCb } = props;
  const [editing, setEditing] = useState(false);
  const [form] = Form.useForm();
  useEffect(() => {
    if (businessInfo) {
      form.setFieldsValue({
        minDropOffTime: businessInfo.minDropOffTime,
        number: businessInfo.number,
        autoChargeLimit: businessInfo.autoChargeLimit,
        missionChargeLimit: businessInfo.missionChargeLimit,
        forceChargeLimit: businessInfo.forceChargeLimit,
        reminderTime: businessInfo.reminderTime,
        chargeType: businessInfo.chargeType,
      });
    }
  }, [businessInfo]);
  const handleSubmit = async () => {
    try {
      if (businessInfo) {
        const values = await form.validateFields();
        if (props.stationType !== ProductType.INTEGRATE) {
          const editedBusinessInfo = {
            stationBaseId: props.stationBaseId,
            minDropOffTime: Number(values.minDropOffTime),
            number: values.number,
            autoChargeLimit: values.autoChargeLimit,
            missionChargeLimit: values.missionChargeLimit,
            forceChargeLimit: values.forceChargeLimit,
          };
          const res = await requestApi.editVehicleStationBusinessInfo(
            editedBusinessInfo,
          );
          if (res.code === HttpStatusCode.Success) {
            message.success('编辑运营部基础信息成功！');
            updateDataCb && updateDataCb();
          } else {
            message.error(res.message);
          }
        } else {
          const editedBusinessInfo = {
            stationBaseId: props.stationBaseId,
            minDropOffTime: Number(values.minDropOffTime),
            stationNumber: values.number,
            autoChargeLimit: values.autoChargeLimit,
            missionChargeLimit: values.missionChargeLimit,
            forceChargeLimit: values.forceChargeLimit,
            reminderTime: values.reminderTime,
            chargeType: values.chargeType,
            groupBusinessList: values?.groupBusinessList?.map((item: any) => ({
              name: item.name,
              code: item.code,
              shelfTypeId: item?.shelfTypeList?.map((i) => i.shelfTypeId),
            })),
          };
          const res = await requestApi.editWarehouseStationBusinessInfo(
            editedBusinessInfo,
          );
          if (res.code === HttpStatusCode.Success) {
            message.success('编辑运营部基础信息成功！');
            updateDataCb && updateDataCb();
          } else {
            message.error(res.message);
          }
        }
      }
    } catch (errorInfo) {
      message.error(errorInfo as string);
      console.log('Failed:', errorInfo);
    }
  };

  return (
    <>
      {!editing ? (
        <>
          <Button
            type="primary"
            style={{ marginBottom: '8px' }}
            onClick={() => {
              setEditing(true);
            }}
          >
            修改
          </Button>
          <StaticBusinessInfo
            stationBusinessInfo={businessInfo}
            stationType={props.stationType}
          />
        </>
      ) : (
        <>
          <Form
            form={form}
            wrapperCol={{ span: 19 }}
            labelCol={{ span: 6 }}
            colon={false}
          >
            <BusinessInfo
              form={form}
              businessInfo={businessInfo}
              stationType={props.stationType}
            />
          </Form>
          <div
            className="operation-btn"
            style={{ display: 'flex', justifyContent: 'center' }}
          >
            <Button
              type="primary"
              style={{ marginRight: '10px' }}
              onClick={() => {
                setEditing(false);
                handleSubmit();
              }}
            >
              确认
            </Button>
            <Button
              onClick={() => {
                setEditing(false);
              }}
            >
              取消
            </Button>
          </div>
        </>
      )}
    </>
  );
};
export default React.memo(EditableBusinessInfo);
