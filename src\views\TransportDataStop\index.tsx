import { CommonForm, CommonTable, FieldItem, FormConfig, useTableData } from "@jd/x-coreui";
import React, { useEffect, useState } from "react";
import { SearchConfig, TableConfig } from "./config";
import { RootState } from "@/redux/store";
import { useSelector } from "react-redux";
import { TransportDataApi } from "@/fetch/business/transportDataStop";
import { HttpStatusCode } from "@/fetch/core/constant";
import dayjs from "dayjs";

const fetchApi = new TransportDataApi();
const TransportDataStop = () => {

    const [searchConfig, setSearchConfig] = useState<FormConfig>(SearchConfig);
    const initSearchCondition = {
        searchForm: {
            cityIdList: null,
            stationIdList: null,
            stopIdList: null,
            dt: [dayjs().subtract(2, 'day'), dayjs().subtract(2, 'day')],
        },
        pageNum: 1,
        pageSize: 10,
    };

    const historySearchValues = useSelector(
        (state: RootState) => state.searchForm,
    );

    const [searchCondition, setSearchCondition] = useState<SearchCondition>(
        () => {
            return historySearchValues.searchValues
                ? historySearchValues.searchValues
                : initSearchCondition;
        },
    );

    const onSearchClick = (val) => {
        if (JSON.stringify(val) === JSON.stringify(searchCondition.searchForm)) {
            return;
        }
        const data = {
            ...searchCondition,
            searchForm: val,
        };
        setSearchCondition(data);
    };

    const { tableData, loading }: any = useTableData(
        {
            searchForm: {
                cityIdList: searchCondition.searchForm.cityIdList,
                stationIdList: searchCondition.searchForm.stationIdList,
                stopIdList: searchCondition.searchForm.stopIdList,
                startDay: searchCondition.searchForm.dt[0].format('YYYY-MM-DD'),
                endDay: searchCondition.searchForm.dt[1].format('YYYY-MM-DD'),
            },
            pageNum: searchCondition.pageNum,
            pageSize: searchCondition.pageSize,
        },
        fetchApi.fetchStopTableList,
    );

    const formatColumns = () => {
        return TableConfig.map((col) => {
            switch (col.dataIndex) {
                case 'transportTimes':
                    return {
                        ...col,
                        render: (text: any) => {
                            if (text === 0) {
                                return <span style={{ color: 'red' }}>0</span>;
                            } else if (text) {
                                return text;
                            } else {
                                return '-';
                            }
                        },
                    };
                default:
                    return {
                        ...col,
                        render: (text: any) => `${text || '-'}`,
                    };
            }
        });
    }

    useEffect(() => {
        const params = {
            dimType: 'CITY',
            filterIds: []
        };
        fetchApi.getDimSelect(params).then((res) => {
            if (res.code === HttpStatusCode.Success) {
                const field = searchConfig.fields.find((item: FieldItem) => item?.fieldName === 'cityIdList');
                field!.options = res.data?.map((val) => ({
                    value: val.dimValue,
                    label: val.dimName,
                }));
                setSearchConfig({ ...searchConfig });
            }
        });
    }, []);

    return (
        <>
            <CommonForm
                formConfig={searchConfig}
                defaultValue={searchCondition.searchForm}
                layout="inline"
                formType="search"
                colon={false}
                onSearchClick={onSearchClick}
                onResetClick={() => setSearchCondition({ ...initSearchCondition })}
            />
            <CommonTable
                tableListData={{
                    list: tableData?.list ?? [],
                    totalNumber: tableData?.total,
                    totalPage: tableData?.pages,
                }}
                columns={formatColumns()}
                loading={loading}
                rowKey={'stopId'}
                searchCondition={searchCondition}
                onPageChange={(value: any) => setSearchCondition(value)}
            />
        </>
    )
}

export default TransportDataStop;