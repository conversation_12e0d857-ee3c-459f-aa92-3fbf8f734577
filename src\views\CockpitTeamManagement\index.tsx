import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { message, Form, Table, Button, Modal } from 'antd';
import PackageTable from '@/components/PackageTable';
import Searchform from '@/components/SearchForm';
import {
  saveSearchValues,
  removeSearchValues,
} from '@/redux/reducer/searchForm';
import { HttpStatusCode } from '@/fetch/core/constant';
import FetchApi from './utils/fetchApi';
import {
  tableConfigData,
  searchConfig,
  driveTeamVehiceTableConfig,
} from './utils/column';
import CommonEnableBtn from '@/components/CommonEnableBtn';
import './index.scss';
import showModal from '@/components/CommonModal';
import { ENABLE } from '@/utils/enum';
import { RootState } from '@/redux/store';
const EnableKeyMap = new Map([
  [0, 1],
  [1, 0],
]);
const CockpitTeamManagement = () => {
  const fetchApi = new FetchApi();
  const navigator = useNavigate();
  const [loading, setLoading] = useState<boolean>(false);
  const [formRef] = Form.useForm();
  const dispatch = useDispatch();
  const historySearchValue: any = useSelector(
    (state: RootState) => state.searchForm,
  );
  const initSearchCondition = {
    searchForm: {
      cockpitTeamName: null,
    },
    page: 1,
    size: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValue.searchValues
        ? historySearchValue.searchValues
        : initSearchCondition;
    },
  );

  const [tableListData, setTableListData] = useState<{
    list: any[];
    totalNumber: number;
    totalPage: number;
  }>({
    list: [],
    totalNumber: 0,
    totalPage: 0,
  });
  const middleBtns: any[] = [
    {
      show: true,
      title: '新建',
      key: 'cockpitteam',
      onClick: () => navigator(`/app/cockpitteam/add`),
    },
  ];

  const formatColumns = (columns: any) => {
    return columns.map((item: any) => {
      switch (item.dataIndex) {
        case 'rowIndex':
          return {
            ...item,
            render: (text: any, record: any, index: number) => {
              return `${
                (searchCondition.page - 1) * searchCondition.size + index + 1
              }`;
            },
          };
        case 'enableName':
          return {
            ...item,
            render: (text: any, record: any, index: number) => {
              const statusStyle =
                record.enable === ENABLE.ENABLE ? 'status-use' : 'status-unuse';
              return <div className={statusStyle}>{record.enableName}</div>;
            },
          };
        case 'operation':
          return {
            ...item,
            render: (text: any, record: any, index: number) => {
              return (
                <div className="operate">
                  <a
                    className="operate-btn"
                    onClick={() => {
                      navigator(
                        `/app/cockpitteam/edit/${
                          record.cockpitTeamNumber
                        }/${encodeURIComponent(record.cockpitTeamName)}`,
                      );
                    }}
                  >
                    编辑
                  </a>
                  <span className="operate-btn">
                    <CommonEnableBtn
                      message={
                        record.enable === 1
                          ? `确认停用远驾团队{${record.cockpitTeamName}}吗？`
                          : `确定启用远驾团队{${record.cockpitTeamName}}吗？`
                      }
                      onConfirm={() => changeDriveTeamStatus(record)}
                      status={record.enable}
                    />
                  </span>
                  <a
                    onClick={() => {
                      fetchCockpitTeamStationList(record);
                    }}
                    className="operate-btn"
                  >
                    查看已绑站点
                  </a>
                </div>
              );
            },
          };
        default:
          return {
            ...item,
            render: (text: any) => `${text}`,
          };
      }
    });
  };

  const formatVehicleColumns = (columns: any) => {
    return columns.map((item: any) => {
      switch (item.dataIndex) {
        case 'rowIndex':
          return {
            ...item,
            render: (text: any, record: any, index: number) => {
              return `${index + 1}`;
            },
          };
        default:
          return {
            ...item,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };
  useEffect(() => {
    getTableList(searchCondition);
  }, []);

  const fetchCockpitTeamStationList = async (record: any) => {
    const res = await fetchApi.fetchCockpitTeamStationList(
      record.cockpitTeamNumber,
    );
    if (res.code === HttpStatusCode.Success) {
      showModal({
        width: 500,
        title: `${record.cockpitTeamName}已绑的站点`,
        content: (
          <>
            <div className="drive-team-vehilce-list-container">
              <Table
                bordered
                pagination={false}
                dataSource={res.data}
                rowKey={'stationName'}
                scroll={{
                  y: 500,
                }}
                columns={formatVehicleColumns(driveTeamVehiceTableConfig)}
                locale={{
                  emptyText: '暂无数据',
                }}
              ></Table>
            </div>
          </>
        ),
        footer: {
          showCancel: true,
          cancelText: '关闭',
          cancelFunc: (cb) => {
            cb();
          },
        },
      });
    } else {
      message.error(res.message);
    }
  };

  const changeDriveTeamStatus = async (record: any) => {
    const params = {
      cockpitTeamNumber: record.cockpitTeamNumber,
      status: EnableKeyMap.get(record.enable)!,
    };
    const res = await fetchApi.changeCockpitTeamStatus(params);
    if (res.code === HttpStatusCode.Success) {
      message.success('修改远驾团队状态成功');
      getTableList(searchCondition);
    } else {
      message.error(res.message);
    }
  };

  const getTableList = async (searchCondition: any) => {
    try {
      setLoading(true);
      const params = {
        pageNum: searchCondition.page,
        pageSize: searchCondition.size,
        cockpitTeamName: searchCondition.searchForm.cockpitTeamName,
      };
      const res = await fetchApi.fetchCockpitTeamInfoPageList(params);
      if (res.code === HttpStatusCode.Success) {
        setTableListData({
          list: res.data.list,
          totalNumber: res.data.total,
          totalPage: res.data.pages,
        });
      }
    } catch (e: any) {
      message.error(e);
    } finally {
      setLoading(false);
    }
  };
  const onSearchClick = () => {
    const values = formRef.getFieldsValue();
    const data = {
      searchForm: values,
      size: 10,
      page: 1,
    };
    setSearchCondition(data);
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: data,
      }),
    );
    getTableList(data);
  };

  const onResetClick = () => {
    formRef.setFieldsValue(initSearchCondition.searchForm);
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: initSearchCondition,
      }),
    );
    setSearchCondition({ ...initSearchCondition });
    getTableList(initSearchCondition);
  };

  const onPageChange = (value: any) => {
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: value,
      }),
    );
    setSearchCondition(value);
    getTableList(value);
  };
  return (
    <div className="drive-team-manage">
      <div className="search-form">
        <Searchform
          configData={searchConfig}
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
          initValues={searchCondition.searchForm}
          formRef={formRef}
          key={'driveTeamManagementSearchform'}
        />
      </div>
      <PackageTable
        key={'driveTeamManagementTable'}
        loading={loading}
        columns={formatColumns(tableConfigData)}
        tableListData={tableListData}
        rowKey={'id'}
        middleBtns={middleBtns}
        searchCondition={searchCondition}
        onPageChange={(value: any) => onPageChange(value)}
      />
    </div>
  );
};
export default React.memo(CockpitTeamManagement);
