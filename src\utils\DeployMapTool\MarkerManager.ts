import { Map as OlMap, Feature } from 'ol';
import { Point } from 'ol/geom';
import { Style, Icon } from 'ol/style';
import { Coordinate } from 'ol/coordinate';
import RBush from 'ol/structs/RBush';
import { Vector as VectorSource } from 'ol/source';
import { Vector as VectorLayer } from 'ol/layer';

export interface MarkerOptions {
  id: string;
  lngLat: Coordinate;
  iconPath: string;
  selectedIconPath: string;
  scale?: number;
  map: OlMap;
}

export class Marker {
  protected id: string;
  protected lngLat: Coordinate;
  protected iconPath: string;
  protected selectedIconPath: string;
  protected map: OlMap;
  protected feature: Feature<Point>;
  protected selected: boolean = false;
  protected scale: number | undefined = 0.5;

  constructor(options: MarkerOptions) {
    this.id = options.id;
    this.lngLat = options.lngLat;
    this.iconPath = options.iconPath;
    this.selectedIconPath = options.selectedIconPath;
    this.map = options.map;
    this.scale = options.scale;
    this.feature = this.createFeature();
    this.addToMap();
  }

  private createFeature(): Feature<Point> {
    const feature = new Feature({
      geometry: new Point(this.lngLat),
    });
    feature.setId(this.id);
    feature.setStyle(this.createStyle());
    feature.set('name', 'marker');
    return feature;
  }

  private createStyle(): Style {
    try {
      return new Style({
        image: new Icon({
          src: this.selected ? this.selectedIconPath : this.iconPath,
          scale: this.scale,
          anchor: [0.5, 1],
          anchorXUnits: 'fraction',
          anchorYUnits: 'fraction',
        }),
      });
    } catch (error) {
      console.error(`Error creating style for marker ${this.id}:`, error);
      // 返回一个默认样式或null
      return new Style({
        image: new Icon({
          src: this.iconPath,
          scale: this.scale,
          anchor: [0.5, 1],
          anchorXUnits: 'fraction',
          anchorYUnits: 'fraction',
        }),
      });
    }
  }
  private createDimStyle(): Style {
    return new Style({
      image: new Icon({
        src: this.selected ? this.selectedIconPath : this.iconPath,
        scale: 0.5,
        anchor: [0.5, 1],
        anchorXUnits: 'fraction',
        anchorYUnits: 'fraction',
        opacity: 0,
      }),
    });
  }
  private addToMap(): void {
    const vectorLayer = this.map
      .getLayers()
      .getArray()
      .find((layer) => layer.get('name') === 'markerLayer');
    if (vectorLayer) {
      vectorLayer.getSource().addFeature(this.feature);
    }
  }

  select(): void {
    this.selected = true;
    this.updateStyle();
  }

  deselect(): void {
    this.selected = false;
    this.updateStyle();
  }
  useNormalStyles(): void {
    this.feature.setStyle(this.createStyle());
  }
  useDimStyles(): void {
    this.feature.setStyle(this.createDimStyle());
  }
  private updateStyle(): void {
    this.feature.setStyle(this.createStyle());
  }
  protected getLngLat(): Coordinate {
    return this.lngLat;
  }
  protected getIconPath(): string {
    return this.iconPath;
  }

  protected getSelectedIconPath(): string {
    return this.selectedIconPath;
  }
  public getId(): string {
    return this.id;
  }
  public getPosition(): Coordinate {
    return this.lngLat;
  }
  public getFeature(): Feature<Point> {
    return this.feature;
  }

  public isSelected(): boolean {
    return this.selected;
  }
}

class MarkerManager {
  protected markers: Map<string, Marker> = new Map();
  protected selectedMarker: Marker | null = null;
  protected map: OlMap;
  protected rbush: RBush<Feature<Point>> = new RBush();
  protected markerLayer: any;
  private selectionEnabled: boolean = true;
  private onMarkerSelect: (marker: Marker | null) => void;
  constructor(map: OlMap, onMarkerSelect: (marker: Marker | null) => void) {
    this.map = map;
    this.setupMarkerLayer();
    this.setupMapClickListener();
    this.setupViewChangeListener();
    this.onMarkerSelect = onMarkerSelect || (() => {});
  }

  protected setupMarkerLayer(): void {
    this.markerLayer = new VectorLayer({
      source: new VectorSource(),
    });
    this.markerLayer.set('name', 'markerLayer');
    this.map.addLayer(this.markerLayer);
  }

  addMarker(options: MarkerOptions): Marker {
    if (this.markers.has(options.id)) {
      this.markers
        .get(options.id)
        ?.getFeature()
        .getGeometry()
        ?.setCoordinates(options.lngLat);
    }
    const marker = new Marker(options);
    marker.useNormalStyles();
    this.markers.set(options.id, marker);
    this.rbush.insert(
      marker.getFeature().getGeometry()!.getExtent(),
      marker.getFeature(),
    );
    this.updateVisibleMarkers();
    return marker;
  }

  addMarkers(optionsArray: MarkerOptions[]): Marker[] {
    const newMarkers: Marker[] = [];
    optionsArray.forEach((options) => {
      if (this.markers.has(options.id)) {
        this.markers
          .get(options.id)
          ?.getFeature()
          .getGeometry()
          ?.setCoordinates(options.lngLat);
        return;
      }
      const marker = new Marker(options);
      this.markers.set(options.id, marker);
      this.rbush.insert(
        marker.getFeature().getGeometry()!.getExtent(),
        marker.getFeature(),
      );
      newMarkers.push(marker);
    });
    this.updateVisibleMarkers();
    return newMarkers;
  }

  selectMarker(id: string): void {
    const marker = this.markers.get(id);
    if (marker) {
      if (this.selectedMarker) {
        this.selectedMarker.deselect();
      }
      marker.select();
      this.selectedMarker = marker;
      this.onMarkerSelect(marker);
    }
  }

  deselectAll(): void {
    if (this.selectedMarker) {
      this.selectedMarker.deselect();
      this.selectedMarker = null;
      this.onMarkerSelect(null);
    }
  }

  getSelectedMarker(): Marker | null {
    return this.selectedMarker;
  }

  removeMarker(id: string): boolean {
    const marker = this.markers.get(id);
    if (marker) {
      if (this.selectedMarker === marker) {
        this.selectedMarker = null;
      }
      this.rbush.remove(marker.getFeature());
      this.markerLayer.getSource().removeFeature(marker.getFeature());
      return this.markers.delete(id);
    }
    return false;
  }

  getAllMarkers(): Marker[] {
    return Array.from(this.markers.values());
  }

  clearAllMarkers(): void {
    this.deselectAll();
    this.markerLayer.getSource().clear();
    this.markers.clear();
    this.rbush.clear();
  }
  dimSelectedMarker(id: string): void {
    const marker = this.markers.get(id);
    if (marker) {
      marker.useDimStyles();
    }
  }
  showSelectedMarker(id: string): void {
    const marker = this.markers.get(id);
    marker?.deselect();
    if (marker) {
      marker.useNormalStyles();
    }
  }
  protected updateVisibleMarkers(): void {
    const extent = this.map.getView().calculateExtent(this.map.getSize());
    const visibleFeatures = this.rbush.getInExtent(extent);
    const source = this.markerLayer.getSource();

    source.clear();
    visibleFeatures.forEach((feature) => {
      if (!feature.getStyle()) {
        const marker = this.markers.get(feature.getId() as string);
        if (marker) {
          marker.useNormalStyles();
        }
      }
      source.addFeature(feature);
    });
  }

  protected setupMapClickListener(): void {
    this.map.on('click', (event) => {
      const clickedFeature = this.map.forEachFeatureAtPixel(
        event.pixel,
        (feature) => feature,
        { hitTolerance: 5, layerFilter: (layer) => layer === this.markerLayer },
      );
      if (clickedFeature && clickedFeature.get('name') === 'marker') {
        const marker = this.markers.get(clickedFeature.getId() as string);
        if (marker) {
          this.selectMarker(marker.getId());
        }
      } else {
        this.deselectAll();
      }
    });
  }

  protected setupViewChangeListener(): void {
    this.map
      .getView()
      .on('change:center', this.updateVisibleMarkers.bind(this));
    this.map
      .getView()
      .on('change:resolution', this.updateVisibleMarkers.bind(this));
  }
  // 新增方法：启用选择
  public enableSelection(): void {
    this.selectionEnabled = true;
  }

  // 新增方法：禁用选择
  public disableSelection(): void {
    this.selectionEnabled = false;
  }
  public getSelectionEnabled(): boolean {
    return this.selectionEnabled;
  }
}
export default MarkerManager;
