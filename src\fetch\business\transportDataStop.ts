import { request } from "../core";

export class TransportDataApi {

    fetchDomain = `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}`;

    // 获取dim下拉框
    getDimSelect = (params: Object) => {
        const options: RequestOptions = {
            method: 'POST',
            absoluteURL: `${this.fetchDomain}/k2/management/businessData/getDimSelect`,
            headers: {
                'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
            },
            body: {
                ...params
            }
        };
        return request(options);
    }

    // 分页查询停靠点明细列表
    fetchStopTableList = ({
        pageNum,
        pageSize,
        searchForm,
    }: {
        pageNum: number;
        pageSize: number;
        searchForm: Object;
    }) => {
        const options: RequestOptions = {
            method: 'POST',
            absoluteURL: `${this.fetchDomain}/datacenter/web/transport/businessData/getStopDataPage`,
            headers: {
                'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
            },
            body: { ...searchForm, pageNum, pageSize },
        };
        return request(options);
    }

    // 分页查询调度明细列表
    fetchScheduleTableList = ({
        pageNum,
        pageSize,
        searchForm,
    }: {
        pageNum: number;
        pageSize: number;
        searchForm: Object;
    }) => {
        const options: RequestOptions = {
            method: 'POST',
            absoluteURL: `${this.fetchDomain}/datacenter/web/transport/businessData/getScheduleDataPage`,
            headers: {
                'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
            },
            body: { ...searchForm, pageNum, pageSize },
        };
        return request(options);
    }

    // 分页查询站点数据列表
    fetchStationTableList = ({
        pageNum,
        pageSize,
        searchForm,
    }: {
        pageNum: number;
        pageSize: number;
        searchForm: Object;
    }) => {
        const options: RequestOptions = {
            method: 'POST',
            absoluteURL: `${this.fetchDomain}/datacenter/web/transport/businessData/getStationDataPage`,
            headers: {
                'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
            },
            body: { ...searchForm, pageNum, pageSize },
        };
        return request(options);
    }

    // 查询站点数据总计
    fetchStationTableSummary = ({
        pageNum,
        pageSize,
        searchForm,
    }: {
        pageNum: number;
        pageSize: number;
        searchForm: Object;
    }) => {
        const options: RequestOptions = {
            method: 'POST',
            absoluteURL: `${this.fetchDomain}/datacenter/web/transport/businessData/getStationDataTotal`,
            headers: {
                'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
            },
            body: { ...searchForm, pageNum, pageSize },
        };
        return request(options);
    }

    // 分页查询城市数据列表
    fetchCityTableList = ({
        pageNum,
        pageSize,
        searchForm,
    }: {
        pageNum: number;
        pageSize: number;
        searchForm: Object;
    }) => {
        const options: RequestOptions = {
            method: 'POST',
            absoluteURL: `${this.fetchDomain}/datacenter/web/transport/businessData/getCityDataPage`,
            headers: {
                'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
            },
            body: { ...searchForm, pageNum, pageSize },
        };
        return request(options);
    }

    // 查询城市数据总计
    fetchCityTableSummary = ({
        pageNum,
        pageSize,
        searchForm,
    }: {
        pageNum: number;
        pageSize: number;
        searchForm: Object;
    }) => {
        const options: RequestOptions = {
            method: 'POST',
            absoluteURL: `${this.fetchDomain}/datacenter/web/transport/businessData/getCityDataTotal`,
            headers: {
                'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
            },
            body: { ...searchForm, pageNum, pageSize },
        };
        return request(options);
    }

    // 分页查询省份数据列表
    fetchStateTableList = ({
        pageNum,
        pageSize,
        searchForm,
    }: {
        pageNum: number;
        pageSize: number;
        searchForm: Object;
    }) => {
        const options: RequestOptions = {
            method: 'POST',
            absoluteURL: `${this.fetchDomain}/datacenter/web/transport/businessData/getStateDataPage`,
            headers: {
                'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
            },
            body: { ...searchForm, pageNum, pageSize },
        };
        return request(options);
    }

    // 查询省份数据总计
    fetchStateTableSummary = ({
        pageNum,
        pageSize,
        searchForm,
    }: {
        pageNum: number;
        pageSize: number;
        searchForm: Object;
    }) => {
        const options: RequestOptions = {
            method: 'POST',
            absoluteURL: `${this.fetchDomain}/datacenter/web/transport/businessData/getStateDataTotal`,
            headers: {
                'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
            },
            body: { ...searchForm, pageNum, pageSize },
        };
        return request(options);
    }
}