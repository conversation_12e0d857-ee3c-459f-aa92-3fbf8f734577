.geo-location-select {
  width: 100%;

  .geo-select {
    width: 100%;
  }

  .geo-location-dropdown {
    padding: 8px 0;
    width: 300px !important; // 固定下拉框宽度

    .geo-location-tabs {
      .ant-tabs-nav {
        margin-bottom: 8px;
        padding: 0 8px;
      }

      // 防止Tab切换时下拉框关闭
      .ant-tabs-tab {
        cursor: pointer;
        user-select: none;
      }
    }

    // 确保下拉面板内容区域的点击不会关闭下拉框
    .ant-empty,
    .ant-select-item {
      pointer-events: auto;
    }
  }

  .option-item {
    padding: 4px 0;
    width: 100%;

    .option-name {
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 280px;
    }

    .option-address {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
      line-height: 20px;
      margin-top: 2px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 280px;
    }
  }

  // 暗色主题样式
  &.dark-theme {
    .option-item {
      .option-address {
        color: rgba(255, 255, 255, 0.45);
      }
    }
  }
}
