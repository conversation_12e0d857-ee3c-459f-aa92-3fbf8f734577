import { FormConfig } from '@jd/x-coreui';
export const tableColumns: any[] = [
  {
    title: '问题分类',
    dataIndex: 'bugCategoryName',
    align: 'center',
    width: 150,
    fixed: 'left',
  },
  {
    title: '问题分类ID',
    dataIndex: 'bugCategoryId',
    align: 'center',
    width: 250,
  },
  {
    title: '模块',
    dataIndex: 'moduleName',
    align: 'center',
    width: 250,
  },
  {
    title: '关联问题标签',
    dataIndex: 'labelCount',
    align: 'center',
    width: 100,
  },

  {
    title: '操作',
    dataIndex: 'operation',
    width: 150,
    align: 'center',
    fixed: 'right',
  },
];

export const searchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'bugCategoryName',
      label: '问题分类',
      placeholder: '请输入分类名称',
      type: 'input',
      allowClear: true,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xl: 6,
      xxl: 6,
    },
  ],
};
