import { Input, message, Modal } from 'antd';
import React, { Component } from 'react';
import {
  TencentMap,
  transformLocationGcj02towgs84,
  transformLocationWgs84togcj02,
} from '@/utils/tencentMap';
import './index.scss';
import DebounceAsyncSelect from '../DebounceSelect';
import { fetchPoiList } from '../../utils/dataSearch';
import { isEmpty } from '@/utils/utils';
export default class ChooseLocation extends Component<any> {
  tMap: TencentMap | null = null;
  initilaized = false;
  searchRef: any = React.createRef<any>();
  state: {
    searchText: string | null;
    choosedLocation: any;
    options: any[];
  } = {
    searchText: null,
    choosedLocation: null,
    options: [],
  };
  _initTencentMap() {
    this.tMap = new TencentMap({
      target: 'tencentmap-station',
      initLocation: null,
      onMarked: (info: any) => {
        this.initilaized = true;
        this.setState({
          searchText: info.address,
          choosedLocation: {
            lon: info.lon,
            lat: info.lat,
            head: this.state.choosedLocation?.head ?? 0,
          },
        });
      },
    });
  }

  _initPoi() {
    fetchPoiList(this.props.address || this.props.city).then((res) => {
      if (!isEmpty(res)) {
        const pos = res[0] || {};
        const [lon, lat] = pos?.value?.split('-');
        this.tMap?.setMapCenter({
          position: {
            lon: Number(lon),
            lat: Number(lat),
          },
          addMarker: true,
        });
      }
    });
  }
  componentDidMount() {
    this.setState({
      searchText: this.props.address ?? this.props.city,
    });
    this._initTencentMap();
    this._initPoi();
  }

  componentDidUpdate() {
    if (this.state.choosedLocation && this.state.choosedLocation.lat) {
      this.tMap?.setMapCenter({
        position: {
          lon: this.state.choosedLocation.lon,
          lat: this.state.choosedLocation.lat,
        },
        addMarker: false,
      });
    } else {
      const { initPosition, city, address } = this.props;
      if (initPosition) {
        const wgs02 = transformLocationWgs84togcj02({
          lon: initPosition.lon,
          lat: initPosition.lat,
        });
        this.tMap?.setMapCenter({ position: wgs02, addMarker: true });
      }
    }
  }

  shouldComponentUpdate(nextProps: any, nextState: any, nextContext: any) {
    const { initPosition, city, address } = nextProps;
    if (city != this.props.city || this.props.address != address) {
      this.setState({
        searchText: address,
      });
      if (initPosition) {
        const wgs02 = transformLocationWgs84togcj02({
          lon: initPosition.lon,
          lat: initPosition.lat,
        });
        this.tMap?.setMapCenter({
          position: wgs02,
          addMarker: true,
        });
      }
    }
    return true;
  }

  render() {
    return (
      <Modal
        closable={false}
        maskClosable={false}
        open={this.props.show}
        onCancel={() => this.props.onClose()}
        onOk={() => {
          if (this.tMap && this.tMap.selectedMark) {
            const { lat, lon, address } = this.tMap.selectedMark;
            const wgs84 = transformLocationGcj02towgs84({
              lon: lon,
              lat: lat,
            });
            const position = {
              lat: parseFloat(Number(wgs84.lat).toFixed(8)),
              lon: parseFloat(Number(wgs84.lon).toFixed(8)),
            };

            this.props.onOk && this.props.onOk({ position, address: address });
          } else {
            message.error('请在地图上标记一个地址');
          }
        }}
        width={1200}
        title="站点选点"
      >
        <div className="choose-location-container">
          <DebounceAsyncSelect
            placeholder={this.props.address || this.props.city || '请搜索'}
            onChange={(data: any) => {
              const pos = data.value
                ?.split('-')
                ?.reverse()
                ?.map((i: string) => parseFloat(i));
              this.tMap?._configMarker(
                data?.label,
                { lat: pos[0], lng: pos[1] },
                data?.label,
              );
              this.tMap?.setMapCenter({
                addMarker: false,
                position: { lat: pos[0], lon: pos[1] },
              });
            }}
          />
          <div className="map-container" id="tencentmap-station" />
        </div>
      </Modal>
    );
  }
}
