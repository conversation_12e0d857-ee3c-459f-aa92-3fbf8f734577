import React, { useEffect, useRef, useState } from 'react';
import { message, Modal, Table } from 'antd';
import ConfigBlackList from './component/Edit';
import SecurityPhone from '../../components/SecurityPhone';
import './index.scss';
import { remakeTableItemData } from './dataProvider';
import { deleteBlackUser, fetchBlackList, fetchUpdateStatus } from './fetchApi';
import StatusModal from '@/components/StatusModal';
import CommonEnableBtn from '@/components/CommonEnableBtn';
import CustomButton, { ButtonType } from '@/components/CustomButton';
import { pageSizeOptions } from '@/utils/constant';
import { CommonForm } from '@/components';
import { BlackListFormData } from './constant';
import { HttpStatusCode } from '@/fetch/core/constant';

const BlackListManager = () => {
  const formInstance = useRef<any>(null);
  const tableConfigData: any[] = [
    {
      title: '序号',
      dataIndex: 'id',
      align: 'center',
      width: 90,
      render: (text: any, record: any, index: number) =>
        `${
          (searchCondition.current - 1) * searchCondition.pageSize + index + 1
        }`,
    },
    {
      title: '用户手机号',
      dataIndex: 'customerPhone',
      align: 'center',
      width: 180,
      render: (record: any, params: any) => {
        return (
          <SecurityPhone
            phone={params.customerPhone}
            security={params.secret ?? true}
            onClick={(security: boolean) => {
              const currentList: any = tableListData.list;
              currentList.forEach((element: any) => {
                if (element.customerPhone === params.customerPhone) {
                  element.secret = security;
                } else {
                  element.secret = true;
                }
              });
              setTableListData({
                ...tableListData,
                list: currentList,
              });
            }}
          />
        );
      },
    },
    {
      title: '用户姓名',
      dataIndex: 'customerName',
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '黑名单状态',
      dataIndex: 'enableName',
      align: 'center',
      width: 120,
      ellipsis: true,
      render: (params: any) => {
        const statusStyle = params === '启用' ? 'status-use' : 'status-unuse';
        return <div className={statusStyle}>{params}</div>;
      },
    },
    {
      title: '添加人',
      dataIndex: 'jdErp',
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '添加时间',
      dataIndex: 'createTime',
      align: 'center',
      width: 200,
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: '',
      width: 140,
      fixed: 'right',
      align: 'center',
      render: (params: any) => {
        const statusStyle =
          params.enableName != '启用' ? 'status-use' : 'status-unuse';
        return (
          <div className="operate">
            <CommonEnableBtn
              message={
                params.enable === 1
                  ? '确定停用吗？停用将从黑名单中释放。'
                  : '确定启用吗？启用后，用户进入黑名单。'
              }
              onConfirm={() => changeStatus(params)}
              status={params.enable}
            />
            <a
              onClick={() => {
                setEditShow({
                  userId: params.id,
                  customerName: params.customerName,
                  customerPhone: params.customerPhone,
                  enableName: params.enableName,
                  loading: false,
                  visible: true,
                });
              }}
            >
              编辑
            </a>
          </div>
        );
      },
    },
  ];
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [searchCondition, setSearchCondition] = useState({
    searchForm: {
      customerPhone: null,
      customerName: null,
      enable: null,
      createUserErp: null,
      startTime: null,
      endTime: null,
      jdErp: null,
    },
    pageSize: 10,
    current: 1,
  });
  const [tableDataReload, setTableDataReload] = useState<number>(0);
  const [tableListData, setTableListData] = useState({
    list: null,
    totalPage: 1,
    totalNumber: 1,
  });
  const [dataLoading, setDataLoading] = useState(false);

  const [editShow, setEditShow] = useState({
    userId: null,
    customerName: null,
    customerPhone: null,
    enableName: null,
    visible: false,
    loading: false,
  });
  const [statusConfirmVisiable, setStatusConfirmVisiable] = useState<{
    userIdList: string[];
    enable: string;
    visible: boolean;
    loading: boolean;
  }>({
    userIdList: [],
    enable: '',
    visible: false,
    loading: false,
  });

  const [deleteConfirm, setDeleteConfirm] = useState({
    loading: false,
    visible: false,
  });

  const changeStatus = async (record: any) => {
    await updateBlackListStatus([record.id], record.enable === 1 ? '0' : '1');
    reloadTableData();
  };

  const reloadTableData = () => {
    setTableDataReload(tableDataReload + 1);
  };
  const loadBlackListData = async () => {
    setDataLoading(true);
    const response: any = await fetchBlackList(searchCondition);
    if (response.code === HttpStatusCode.Success) {
      const list = remakeTableItemData(response.data.list);
      setTableListData({
        totalNumber: response.data.total,
        totalPage: response.data.pages,
        list,
      });
      setSelectedRowKeys([]);
      setSelectedRows([]);
    }
    setDataLoading(false);
  };

  const updateBlackListStatus = async (
    userIdList: string[],
    enable: string,
  ) => {
    const response: any = await fetchUpdateStatus(userIdList, enable);
    message.success(response.message);
    return response;
  };

  const deleteUsers = async (userIdList: string[]) => {
    const response: any = await deleteBlackUser(userIdList);
    message.success(response.message);
    return response;
  };

  const onSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    setSelectedRowKeys(selectedRowKeys);
    setSelectedRows(selectedRows);
  };
  const handleTableChange = (
    paginationData: any,
    filters: any,
    sorter: any,
    extra: any,
  ) => {
    if (extra.action === 'paginate') {
      const { current, pageSize } = paginationData;
      setSearchCondition({
        ...searchCondition,
        current,
        pageSize,
      });
    }
  };

  const onSearchClick = () => {
    const allValues = formInstance?.current?.getFieldsValue();
    let startTime = null;
    let endTime = null;
    if (allValues.operationTime && allValues.operationTime.length > 0) {
      const startMoment = allValues.operationTime[0];
      if (startMoment) {
        startTime = startMoment.format('YYYY-MM-DD HH:mm:ss');
      }
      const endMoment = allValues.operationTime[1];
      if (endMoment) {
        endTime = endMoment.format('YYYY-MM-DD HH:mm:ss');
      }
    }
    const searchForm = {
      ...allValues,
      enable: allValues?.enable?.value,
      startTime,
      endTime,
    };
    delete searchForm.operationTime;
    setSearchCondition({
      current: 1,
      pageSize: 10,
      searchForm,
    });
  };

  useEffect(() => {
    loadBlackListData();
  }, [searchCondition, tableDataReload]);

  return (
    <div className="blacklist-content">
      <div className="searchForm">
        <CommonForm
          formConfig={BlackListFormData}
          formType="search"
          onSearchClick={onSearchClick}
          onResetClick={() => {
            const searchForm = {
              customerPhone: null,
              customerName: null,
              enable: null,
              createUserErp: null,
              startTime: null,
              endTime: null,
              jdErp: null,
              operationTime: null,
            };
            setSearchCondition({
              pageSize: 10,
              current: 1,
              searchForm,
            });
            formInstance.current?.setFieldsValue(searchForm);
          }}
          getFormInstance={(formRef: any) => {
            formInstance.current = formRef;
          }}
        />
      </div>

      <div className="table-container">
        <div className="functions">
          <CustomButton
            title="新建黑名单"
            onSubmitClick={() => {
              setEditShow({
                ...editShow,
                visible: true,
              });
            }}
          />
          <CustomButton
            title="批量启用"
            otherCSSProperties={{ marginLeft: 10 }}
            onSubmitClick={async () => {
              const enableItem = selectedRows.filter(
                (item: any) => item.enable === 1,
              );
              if (selectedRowKeys?.length <= 0) {
                message.error('请至少选择一条数据进行提交');
                return;
              }
              if (enableItem?.length > 0) {
                message.error('仅全部停用状态才能批量启用！');
                return;
              }
              setStatusConfirmVisiable({
                userIdList: selectedRowKeys,
                enable: '1',
                visible: true,
                loading: false,
              });
            }}
          />
          <CustomButton
            title="批量停用"
            otherCSSProperties={{ marginLeft: 10 }}
            buttonType={ButtonType.WarnningButton}
            onSubmitClick={async () => {
              const disableItem = selectedRows.filter(
                (item: any) => item.enable === 0,
              );
              if (selectedRowKeys?.length <= 0) {
                message.error('请至少选择一条数据进行提交');
                return;
              }
              if (disableItem?.length > 0) {
                message.error('仅全部启用状态才能批量停用！');
                return;
              }
              setStatusConfirmVisiable({
                userIdList: selectedRowKeys,
                enable: '0',
                visible: true,
                loading: false,
              });
            }}
          />
          <CustomButton
            title="批量删除"
            otherCSSProperties={{ marginLeft: 10 }}
            buttonType={ButtonType.WarnningButton}
            onSubmitClick={async () => {
              const enableItem = selectedRows.filter(
                (item: any) => item.enable === 1,
              );
              if (selectedRowKeys?.length <= 0) {
                message.error('请至少选择一条数据进行提交');
                return;
              }
              if (enableItem?.length > 0) {
                message.error('仅有停用状态的用户才可删除！');
                return;
              }
              setDeleteConfirm({
                loading: false,
                visible: true,
              });
            }}
          />
        </div>
        <div className="table">
          <Table
            rowSelection={{
              selectedRowKeys,
              onChange: onSelectChange,
            }}
            columns={tableConfigData}
            dataSource={tableListData.list ?? []}
            pagination={{
              ...searchCondition,
              total: tableListData.totalNumber,
              position: ['bottomCenter'],
              showQuickJumper: true,
              showSizeChanger: true,
              pageSizeOptions: pageSizeOptions,
              showTotal: (total) =>
                `共 ${tableListData.totalPage}页,${total} 条记录`,
            }}
            onChange={handleTableChange}
            loading={dataLoading}
            scroll={{
              y: window.innerHeight - 200,
            }}
            bordered
          />
        </div>
      </div>
      {editShow.visible ? (
        <ConfigBlackList
          visiable={editShow.visible}
          userId={editShow.userId}
          customerName={editShow.customerName}
          customerPhone={editShow.customerPhone}
          enableName={editShow.enableName}
          onSubmit={(succ: boolean) => {
            if (succ) {
              reloadTableData();
            }
            setEditShow({
              userId: null,
              customerName: null,
              customerPhone: null,
              enableName: null,
              loading: false,
              visible: false,
            });
          }}
        />
      ) : null}
      <StatusModal
        number={statusConfirmVisiable.userIdList.length}
        enable={statusConfirmVisiable.enable} // 车辆停用和启用ENABLE、DISABLE， 其他为1、0
        modalVisible={statusConfirmVisiable.visible}
        type={'blackList'}
        submiting={statusConfirmVisiable.loading}
        handleOk={async () => {
          if (statusConfirmVisiable.loading) return;
          setStatusConfirmVisiable({
            ...statusConfirmVisiable,
            loading: true,
          });
          await updateBlackListStatus(
            statusConfirmVisiable.userIdList,
            statusConfirmVisiable.enable,
          );
          setStatusConfirmVisiable({
            userIdList: [],
            enable: '',
            visible: false,
            loading: false,
          });
          reloadTableData();
        }}
        handleCancel={() => {
          if (statusConfirmVisiable.loading) return;
          setStatusConfirmVisiable({
            userIdList: [],
            enable: '',
            visible: false,
            loading: false,
          });
        }}
      />
      <Modal
        title="确认删除"
        visible={deleteConfirm.visible}
        confirmLoading={deleteConfirm.loading}
        onOk={async () => {
          if (selectedRowKeys && selectedRowKeys.length > 0) {
            setDeleteConfirm({
              loading: true,
              visible: true,
            });
            await deleteUsers(selectedRowKeys);
            setDeleteConfirm({
              loading: false,
              visible: false,
            });
            reloadTableData();
          } else {
            message.error('请至少选择一条数据进行提交');
          }
        }}
        onCancel={() => {
          setDeleteConfirm({
            loading: false,
            visible: false,
          });
        }}
      >
        <p>{`确定批量删除共计${selectedRows?.length}位用户吗？`}</p>
      </Modal>
    </div>
  );
};

export default React.memo(BlackListManager);
