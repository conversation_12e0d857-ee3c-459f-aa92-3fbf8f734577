import React from 'react';
import { Descriptions } from 'antd';
import { StationBusinessInfo } from '@/types';
import ModulePart from '@/components/ModulePart';
import { ProductType } from '@/utils/enum';
import { ChargeTypeText } from '@/utils/constant';
import GroupBusinessList from '../GroupBusinessList';

const StaticBusinessInfo = (props: {
  stationBusinessInfo: StationBusinessInfo | null;
  stationType: ProductType | undefined;
}) => {
  const { stationBusinessInfo, stationType } = props;
  const businessInfoItems: any = [
    stationType !== ProductType.INTEGRATE && {
      key: 'minDropOffTime',
      label: '揽件接单最小时长设置(min)',
      children: stationBusinessInfo?.minDropOffTime,
    },
    {
      key: 'number',
      label: '所在业务系统的站点编号',
      children: stationBusinessInfo?.number,
    },
    stationType === ProductType.INTEGRATE && {
      key: 'reminderTime',
      label: '车端亮灯提醒设置=波次时间(最晚生产时间)-车端当前时间≤',
      children: `${stationBusinessInfo?.reminderTime || '-'} min`,
    },
    stationType === ProductType.INTEGRATE && {
      key: 'chargeType',
      label: 'AMR充电方式',
      children: ChargeTypeText[stationBusinessInfo?.chargeType || ''] || '-',
    },
    stationType === ProductType.INTEGRATE && {
      key: 'missionChargeLimit',
      label: '可接任务电量(%)',
      children: stationBusinessInfo?.missionChargeLimit,
    },
    stationType === ProductType.INTEGRATE && {
      key: 'autoChargeLimit',
      label: '自动回充电量(%)',
      children: stationBusinessInfo?.autoChargeLimit,
    },
    stationType === ProductType.INTEGRATE && {
      key: 'forceChargeLimit',
      label: '强制回充电量(%)',
      children: stationBusinessInfo?.forceChargeLimit,
    },
  ].filter(Boolean);
  return (
    <>
      <ModulePart title="业务配置">
        <Descriptions items={businessInfoItems}></Descriptions>
      </ModulePart>
      {stationType === ProductType.INTEGRATE && (
        <ModulePart title="业务接口配置">
          <GroupBusinessList
            isEdit={false}
            businessList={stationBusinessInfo?.groupBusinessList || []}
          />
        </ModulePart>
      )}
    </>
  );
};
export default React.memo(StaticBusinessInfo);
