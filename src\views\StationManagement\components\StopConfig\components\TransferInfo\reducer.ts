export interface User {
  id: any;
  name: string;
  contact: string;
  enable: any;
  statusName: string;
  isDefault: any;
  isDefaultName: string;
  secret?: boolean;
  newAdd?: boolean;
}

export interface State {
  userList: {
    stopCollectionUserList: User[];
    stopConnectionUserList: User[];
  };
  showEdit: {
    user: User | null;
    show: boolean;
    type: string;
  };
  showUpdateStatus: {
    user: User | null;
    show: boolean;
    type: string;
  };
  showDelete: {
    user: User | null;
    show: boolean;
    type: string;
  };
}

export const reducer = (
  state: State,
  action: {
    type:
      | 'fetchInit'
      | 'addNewUser'
      | 'deleteUser'
      | 'updateUser'
      | 'updatePhoneSecret'
      | 'showEdit'
      | 'showUpdateStatus'
      | 'showDelete';
    payload: any;
  },
) => {
  switch (action.type) {
    case 'fetchInit': {
      const { userList } = action.payload;
      state.userList = userList;
      return { ...state };
    }
    case 'showEdit': {
      const { user, show, type } = action.payload;
      const { showEdit } = state;
      showEdit.show = show;
      showEdit.user = user;
      showEdit.type = type;
      state.showEdit = showEdit;
      return { ...state };
    }
    case 'showDelete': {
      const { user, show, type } = action.payload;
      const { showDelete } = state;
      showDelete.show = show;
      showDelete.user = user;
      showDelete.type = type;
      state.showDelete = showDelete;
      return { ...state };
    }
    case 'showUpdateStatus': {
      const { user, show, type } = action.payload;
      const { showUpdateStatus } = state;

      showUpdateStatus.type = type;
      showUpdateStatus.show = show;
      showUpdateStatus.user = user;
      state.showUpdateStatus = showUpdateStatus;

      return { ...state };
    }
    case 'updateUser': {
      const { user, type } = action.payload;
      const { userList } = state;
      if (type === 'collection') {
        if (user.isDefault === 1) {
          userList.stopCollectionUserList &&
            userList.stopCollectionUserList.forEach((item: any) => {
              item.isDefault = 0;
            });
        }
        const newList = userList.stopCollectionUserList.map((item) => {
          if (item.id === user.id) {
            return {
              ...user,
            };
          }
          return item;
        });
        state.userList.stopCollectionUserList = [...newList];
      } else if (type === 'connection') {
        if (user.isDefault === 1) {
          userList.stopConnectionUserList &&
            userList.stopConnectionUserList.forEach((item: any) => {
              item.isDefault = 0;
            });
        }
        const newList = userList.stopConnectionUserList.map((item) => {
          if (item.id === user.id) {
            return {
              ...user,
            };
          }
          return item;
        });
        state.userList.stopConnectionUserList = [...newList];
      }
      return { ...state };
    }
    case 'updatePhoneSecret': {
      const { userId, type } = action.payload;
      const { userList } = state;
      if (type === 'collection') {
        userList.stopCollectionUserList &&
          userList.stopCollectionUserList.forEach((item) => {
            if (item.id === userId) {
              item.secret = !item.secret;
            }
          });
      } else if (type === 'connection') {
        userList.stopConnectionUserList &&
          userList.stopConnectionUserList.forEach((item) => {
            if (item.id === userId) {
              item.secret = !item.secret;
            }
          });
      }
      return { ...state };
    }
    case 'addNewUser': {
      const { newUser, type } = action.payload;
      const { userList } = state;
      if (type === 'collection') {
        if (newUser.isDefault === 1) {
          userList.stopCollectionUserList &&
            userList.stopCollectionUserList.forEach((item: any) => {
              item.isDefault = 0;
            });
        } else if (
          (userList.stopCollectionUserList &&
            userList.stopCollectionUserList.length <= 0) ||
          !userList.stopCollectionUserList
        ) {
          newUser.isDefault = 1;
        }
        newUser.newAdd = true;
        const newList = userList.stopCollectionUserList
          ? [...userList.stopCollectionUserList, newUser]
          : [newUser];
        state.userList.stopCollectionUserList = newList;
      } else if (type === 'connection') {
        if (newUser.isDefault === 1) {
          userList.stopConnectionUserList &&
            userList.stopConnectionUserList.forEach((item: any) => {
              item.isDefault = 0;
            });
        }
        newUser.newAdd = true;
        const newList = userList.stopConnectionUserList
          ? [...userList.stopConnectionUserList, newUser]
          : [newUser];
        state.userList.stopConnectionUserList = newList;
      }
      return { ...state };
    }
    case 'deleteUser': {
      const { userId, type } = action.payload;
      const { userList } = state;
      if (type === 'collection') {
        const newList = userList.stopCollectionUserList.filter(
          (item) => item.id !== userId,
        );
        state.userList.stopCollectionUserList = [...newList];
      } else if (type === 'connection') {
        const newList = userList.stopConnectionUserList.filter(
          (item) => item.id !== userId,
        );
        state.userList.stopConnectionUserList = [...newList];
      }
      return { ...state };
    }
    default:
      return state;
  }
};

export {};
