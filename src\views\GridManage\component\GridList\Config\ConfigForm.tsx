/* eslint-disable react/display-name */
/* eslint-disable no-unused-vars */
import { Form, Button, Input, message } from 'antd';
import React, { useState, useEffect } from 'react';
import {
  sendGlobalEvent,
  addGlobalEventListener,
  removeGlobalEventListener,
} from '@/utils/emit';
import { GlobalEventName } from '@/utils/constant';
import {
  setGridListAction,
  setGridSizeListAction,
  setGridPositionValueAction,
  boxGridSelector,
} from '@/redux/reducer/boxGrid';
import { ProductType } from '@/utils/enum';
import { useDispatch, useSelector } from 'react-redux';
const ConfigForm = ({
  formIndex,
  formCon,
  disabled,
  productionType,
}: {
  formIndex: number;
  productionType: string;
  formCon: any;
  disabled?: boolean;
}) => {
  const [configForm] = Form.useForm();
  const gridData = useSelector(boxGridSelector);

  useEffect(() => {
    // 监听格口增删改查
    addGlobalEventListener(GlobalEventName.EVENT_VALID_CONFIG, () => {
      configForm.validateFields();
    });
    return () => {
      removeGlobalEventListener(GlobalEventName.EVENT_VALID_CONFIG, () => {
        configForm.validateFields();
      });
    };
  }, []);

  useEffect(() => {
    Object.keys(formCon).map((key: any) => {
      configForm.setFieldsValue({ [key]: formCon[key] });
    });
  }, [formCon]);

  const addClick = () => {
    sendGlobalEvent(GlobalEventName.EVENT_ADD_CONFIG, gridData.gridSizeList);
  };
  const deleteClick = () => {
    sendGlobalEvent(GlobalEventName.EVENT_DELETE_CONFIG, {
      index: formIndex,
      list: gridData.gridSizeList,
    });
  };
  const blurNum = (value: any, key: any) => {
    if (productionType !== 'vehicle') {
      return;
    }
    value = value.replace(/[^0-9]*$/g, '');
    value = value ? parseInt(value) : '';
    configForm.setFieldsValue({ [key]: value });
    sendGlobalEvent(GlobalEventName.EVENT_UPDATE_CONFIG, {
      value: value,
      index: formIndex,
      key: key,
    });
  };
  return (
    <Form
      form={configForm}
      initialValues={formCon}
      autoComplete="off"
      layout="inline"
      style={{ marginBottom: '10px' }}
    >
      <Form.Item
        name="width"
        rules={[{ required: true, message: '请输入宽度' }]}
      >
        <Input
          placeholder="请输入宽度"
          disabled={
            formIndex !== 0 ||
            disabled ||
            productionType !== ProductType.VEHICLE
          }
          onBlur={(e) => blurNum(e.target.value, 'width')}
        />
      </Form.Item>
      <Form.Item>×</Form.Item>
      <Form.Item
        name="length"
        rules={[{ required: true, message: '请输入深度' }]}
      >
        <Input
          placeholder="请输入深度"
          disabled={
            formIndex !== 0 ||
            disabled ||
            productionType !== ProductType.VEHICLE
          }
          onBlur={(e) => blurNum(e.target.value, 'length')}
        />
      </Form.Item>
      <Form.Item>×</Form.Item>
      <Form.Item
        name="height"
        rules={[{ required: true, message: '请输入高度' }]}
      >
        <Input
          placeholder="请输入高度"
          disabled={disabled || productionType !== ProductType.VEHICLE}
          onBlur={(e) => blurNum(e.target.value, 'height')}
        />
      </Form.Item>
      <Form.Item>
        {disabled ? null : formIndex == 0 ? (
          <Button
            type="link"
            onClick={() => {
              addClick();
            }}
          >
            添 加
          </Button>
        ) : (
          <Button
            type="link"
            onClick={() => {
              deleteClick();
            }}
          >
            删 除
          </Button>
        )}
      </Form.Item>
    </Form>
  );
};
export default React.memo(ConfigForm);
