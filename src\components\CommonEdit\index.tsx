import { PerButton, ButtonType, BreadCrumb } from '@/components';
import React from 'react';
import './index.scss';
import { debounce } from '@/utils/utils';

const CommonEdit = (props: {
  children: any;
  title?: string;
  breadCrumbConfig?: {
    title: string;
    route: string;
  }[];
  submitTitle?: string;
  cancelTitle?: string;
  hideSubmit?: boolean;
  onSubmitClick?: Function;
  onCancleClick?: Function;
}) => {
  return (
    <div className="common-edit-container">
      {props.breadCrumbConfig ? (
        <div className="bread-crub">
          <BreadCrumb items={props.breadCrumbConfig} />
        </div>
      ) : null}
      <div className="content">
        {props.title ? <div className="title">{props.title}</div> : null}
        <div className="module-container">{props.children}</div>
        <div className="function">
          {props.hideSubmit ? null : (
            <PerButton
              title={props.submitTitle ? props.submitTitle : '确定'}
              clickCallBack={debounce(() => {
                props.onSubmitClick ? props.onSubmitClick() : null;
              }, 200)}
            />
          )}
          <PerButton
            btnType="default"
            title={props.cancelTitle ? props.cancelTitle : '取消'}
            clickCallBack={() => {
              props.onCancleClick ? props.onCancleClick() : null;
            }}
            otherStyle={{ marginLeft: '10px' }}
          />
        </div>
      </div>
    </div>
  );
};

export default React.memo(CommonEdit);
