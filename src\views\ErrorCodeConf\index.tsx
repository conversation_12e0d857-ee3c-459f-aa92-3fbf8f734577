import React, { useState, useEffect } from 'react';
import { CommonTable, CommonForm, TableOperateBtn } from '@/components';
import { searchConfig, tableColumns } from './utils/columns';
import { useTableData } from '@/components/CommonTable/useTableData';
import { ErrorCodeConfApi } from '@/fetch/business';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/store';
import { useNavigate } from 'react-router-dom';
import { ProductType, YESNO } from '@/utils/enum';
import { saveSearchValues } from '@/redux/reducer/searchForm';
import { HttpStatusCode } from '@/fetch/core/constant';
import { message } from 'antd';
import { PageType } from '@/utils/EditTitle';
import { Popconfirm } from 'antd';

const ErrorCodeConf = () => {
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const fetchApi = new ErrorCodeConfApi();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const initSearchCondition = {
    searchForm: {
      errorCode: null,
      errorMessage: null,
      errorTranslate: null,
      modifyUser: null,
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] = useState<SearchCondition>(
    () => {
      return historySearchValues.searchValues
        ? historySearchValues.searchValues
        : initSearchCondition;
    },
  );
  const [tableKey, setTableKey] = useState('');
  const { tableData, loading } = useTableData(
    {
      ...searchCondition,
      searchForm: {
        ...searchCondition.searchForm,
        enable: searchCondition.searchForm.enable?.value,
        module: searchCondition.searchForm.module?.value,
      },
    },
    fetchApi.fetchTableList,
    tableKey,
  );

  const middleBtns: any[] = [
    {
      show: true,
      title: '新建',
      onClick: () => handleEdit(PageType.ADD),
    },
  ];
  const formateColumns = () => {
    return tableColumns.map((col) => {
      switch (col.dataIndex) {
        case 'order':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              const { pageSize, pageNum } = searchCondition;
              return <div>{(pageNum - 1) * pageSize + index + 1}</div>;
            },
          };
        case 'enableName':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              const enableStyle =
                record.enable === YESNO.YES ? 'enable' : 'unenable';
              return <div className={enableStyle}>{record.enableName}</div>;
            },
          };
        case 'operation':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              const { id } = record;
              return (
                <div className="operate">
                  <TableOperateBtn
                    title="编辑"
                    handleClick={() => handleEdit(PageType.EDIT, id)}
                  />
                  <Popconfirm
                    placement="left"
                    title={'确认是否删除此条错误码翻译？'}
                    onConfirm={() => {
                      handleDelete(id);
                    }}
                    okText="确定"
                    cancelText="取消"
                    overlayStyle={{ maxWidth: 800 }}
                  >
                    <a
                      style={{
                        marginRight: '5px',
                        marginLeft: '5px',
                        color: 'red',
                        cursor: 'pointer',
                      }}
                    >
                      删除
                    </a>
                  </Popconfirm>
                </div>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };

  const handleDelete = async (id: number) => {
    const res = await fetchApi.updateStatus(id);
    if (res.code === HttpStatusCode.Success) {
      message.success(res.message);
      setTableKey(Date.now().toString());
    } else {
      message.error(res.message);
    }
  };

  const handleEdit = (type: PageType, id?: number) => {
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: searchCondition,
      }),
    );
    navigator(
      id
        ? `/app/errorCodeConf/edit?type=${type}&id=${id}`
        : `/app/errorCodeConf/edit?type=${type}`,
    );
  };

  const onSearchClick = (val) => {
    const data = {
      searchForm: val,
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(data);
  };
  return (
    <>
      <CommonForm
        formConfig={searchConfig}
        defaultValue={searchCondition.searchForm}
        layout="inline"
        formType="search"
        colon={false}
        onSearchClick={onSearchClick}
        onResetClick={() => setSearchCondition({ ...initSearchCondition })}
      />
      <CommonTable
        tableListData={{
          list: tableData?.list ?? [],
          totalNumber: tableData?.total,
          totalPage: tableData?.pages,
        }}
        columns={formateColumns()}
        loading={loading}
        rowKey={'id'}
        middleBtns={middleBtns}
        searchCondition={searchCondition}
        onPageChange={(value: any) => setSearchCondition(value)}
      />
    </>
  );
};

export default React.memo(ErrorCodeConf);
