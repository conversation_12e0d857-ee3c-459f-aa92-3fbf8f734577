import { FormConfig } from '@/components';

export const productionColumns: any[] = [
  { title: '序号', width: 70, dataIndex: 'order', align: 'center' },
  {
    title: '车牌号',
    width: 120,
    dataIndex: 'deviceName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车架号',
    width: 90,
    dataIndex: 'serialNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车型名称',
    width: 90,
    dataIndex: 'deviceTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '产品类型',
    width: 90,
    dataIndex: 'productTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '设备类型',
    dataIndex: 'businessTypeName',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '所属站点',
    width: 200,
    dataIndex: 'stationName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '站点用途',
    width: 120,
    dataIndex: 'stationUseCaseName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车辆归属方',
    width: 120,
    dataIndex: 'ownerUseCaseName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车辆生命周期',
    width: 120,
    dataIndex: 'hardwareStatusName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '标定检测状态',
    width: 150,
    dataIndex: 'checkStatusName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    width: 315,
    dataIndex: 'product',
    align: 'center',
    fixed: 'right',
  },
];

export const vehicleScheduleColumns: any[] = [
  { title: '序号', dataIndex: 'order', align: 'center', width: 70 },
  {
    title: '车牌号',
    width: 120,
    dataIndex: 'deviceName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车架号',
    width: 90,
    dataIndex: 'serialNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '产品类型',
    width: 90,
    dataIndex: 'productTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '设备类型',
    dataIndex: 'businessTypeName',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '所属站点',
    dataIndex: 'stationName',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '站点用途',
    dataIndex: 'stationUseCaseName',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '车辆归属方',
    dataIndex: 'ownerUseCaseName',
    align: 'center',
    width: 110,
    ellipsis: true,
  },
  {
    title: '车辆生命周期',
    dataIndex: 'hardwareStatusName',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '标定检测状态',
    dataIndex: 'checkStatusName',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '存在未完成维修单',
    dataIndex: 'isRequireName',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'schedule',
    align: 'center',
    width: 330,
    fixed: 'right',
  },
];

export const vehicleUseColumns: any[] = [
  {
    title: '序号',
    dataIndex: 'order',
    align: 'center',
    width: 65,
  },
  {
    title: '车牌号',
    width: 120,
    dataIndex: 'deviceName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车架号',
    width: 90,
    dataIndex: 'serialNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '产品类型',
    width: 90,
    dataIndex: 'productTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '设备类型',
    dataIndex: 'businessTypeName',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '供应商',
    dataIndex: 'supplierName',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '所属站点',
    dataIndex: 'stationName',
    width: 200,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '站点用途',
    dataIndex: 'stationUseCaseName',
    width: 100,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车辆归属方',
    dataIndex: 'ownerUseCaseName',
    width: 110,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车辆生命周期',
    dataIndex: 'hardwareStatusName',
    width: 120,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '标定检测状态',
    dataIndex: 'checkStatusName',
    width: 150,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '推流模式',
    dataIndex: 'videoModeName',
    width: 150,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 300,
    fixed: 'right',
  },
];

export const vehicleRepairColumns: any[] = [
  { title: '序号', dataIndex: 'order', align: 'center', width: 70 },
  {
    title: '车牌号',
    width: 120,
    dataIndex: 'deviceName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车架号',
    width: 90,
    dataIndex: 'serialNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '产品类型',
    width: 90,
    dataIndex: 'productTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '设备类型',
    dataIndex: 'businessTypeName',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '所属站点',
    width: 200,
    dataIndex: 'stationName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '站点用途',
    width: 140,
    dataIndex: 'stationUseCaseName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车辆归属方',
    dataIndex: 'ownerUseCaseName',
    align: 'center',
    width: 110,
    ellipsis: true,
  },
  {
    title: '车辆生命周期',
    dataIndex: 'hardwareStatusName',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '标定检测状态',
    dataIndex: 'checkStatusName',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'schedule',
    align: 'center',
    width: 380,
    fixed: 'right',
  },
];

export const vehicleServiceColumns: any[] = [
  {
    title: '序号',
    dataIndex: 'order',
    width: 80,
    align: 'center',
  },
  {
    title: '车牌号',
    width: 200,
    dataIndex: 'number',
    key: 'number',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '车型名称',
    dataIndex: 'typeName',
    key: 'typeName',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '所属站点',
    dataIndex: 'stationName',
    key: 'stationName',
    align: 'center',
    width: 200,
    ellipsis: true,
  },
  {
    title: '站点用途',
    dataIndex: 'stationUseCaseName',
    key: 'stationUseCaseName',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
];

export const gridColumns: any[] = [
  {
    title: '格口号',
    align: 'center',
    dataIndex: 'gridNo',
  },
  {
    title: '深度(mm)',
    dataIndex: 'length',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '宽度(mm)',
    dataIndex: 'width',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '高度(mm)',
    dataIndex: 'height',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '体积（cm³）',
    dataIndex: 'volume',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '格口内小格口',
    dataIndex: 'pallet',
    align: 'center',
    ellipsis: true,
  },
];

export const vehicleTypeColumns: any[] = [
  {
    title: '',
    dataIndex: 'name',
    key: 'name',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '底盘',
    dataIndex: 'chassis',
    key: 'chassis',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '计算平台',
    dataIndex: 'compute',
    key: 'compute',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '推流相机',
    dataIndex: 'videoCamera',
    key: 'videoCamera',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '传感器方案',
    dataIndex: 'sensorScheme',
    key: 'sensorScheme',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '组装厂商',
    dataIndex: 'manufactory',
    key: 'manufactory',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '推流类型',
    dataIndex: 'videoType',
    key: 'videoType',
    align: 'center',
    ellipsis: true,
  },
];

export const sensorSchemeColumns: any[] = [
  {
    title: '',
    dataIndex: 'order',
    key: 'order',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '传感器设备',
    dataIndex: 'hardwareTypeName',
    key: 'hardwareTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '用途',
    dataIndex: 'hardwareTypeUsageName',
    key: 'hardwareTypeUsageName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '硬件名称',
    dataIndex: 'hardwareModelName',
    key: 'hardwareModelName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '型号',
    dataIndex: 'hardwareModel',
    key: 'hardwareModel',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '数量',
    dataIndex: 'useNumber',
    key: 'useNumber',
    align: 'center',
    ellipsis: true,
  },
];

export const checkResultColumns: any[] = [
  {
    title: '操作人',
    dataIndex: 'modifyUser',
    key: 'modifyUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'modifyTime',
    key: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'checkOperate',
    key: 'checkOperate',
    align: 'center',
    ellipsis: true,
  },
];
