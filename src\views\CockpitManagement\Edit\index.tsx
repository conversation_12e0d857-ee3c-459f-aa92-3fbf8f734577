import React, { useEffect, useState, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { Form, Input, Radio, Select, message } from 'antd';
import CommonEdit from '@/components/CommonEdit';
import EditModuleTitle from '@/components/EditModuleTitle';
import Loading from '@/components/Loading';
import { HttpStatusCode } from '@/fetch/core/constant';
import FetchApi from '../utils/fetchApi';
import { editVehicleSearchConfig } from '../utils/column';
import './index.scss';
import { FieldItem, FormConfig } from '@/components';
import { CommonApi } from '@/fetch/business';
const fetchApi = new FetchApi();
const commonApi = new CommonApi();
const DriveCabinEdit = () => {
  const [formRef] = Form.useForm();
  const { cockpitNumber } = useParams();
  const [searchFormConf, setSearchFormConf] = useState<FormConfig>(
    editVehicleSearchConfig,
  );
  const [breadCrumbItems, setBreadCrumbItems] = useState([
    { title: '座席管理', route: '' },
    { title: '新建座席', route: '' },
  ]);
  const [loading, setLoading] = useState(false);
  const [loadingMsg, setLoadingMsg] = useState<string>('');
  const [cockpitTeamNumberList, setCockpitTeamNumberList] = useState<any>([]);
  const [cockpitTeamNumber, setCockpitTeamNumber] = useState<any>();
  const [cockpitTypeList, setCockpitTypeList] = useState<any[]>([]);
  const [cockpitType, setCockpitType] = useState<string | null>(null);
  const bindVehicleInfo = useRef<any>();
  useEffect(() => {
    getCockpitTeamList();
    getCockpitTypeList();
    if (cockpitNumber) {
      setBreadCrumbItems([
        { title: '座席管理', route: '' },
        { title: '编辑座席', route: '' },
      ]);
      fetchCockpitInfo();
    }
  }, []);

  const fetchCockpitInfo = async () => {
    setLoadingMsg('加载中...');
    setLoading(true);
    const res = await fetchApi.getCockpitInfo(cockpitNumber ?? '');
    if (res.code === HttpStatusCode.Success) {
      const detail = res.data;
      formRef.setFieldsValue({ ...detail });
      setCockpitTeamNumber(detail.cockpitTeamNumber);
    } else {
      message.error(res.message);
    }
    setLoading(false);
  };
  const getCockpitTeamList = async () => {
    const res = await fetchApi.fetchCockpitTeamList();
    if (res.code === HttpStatusCode.Success && res.data) {
      setCockpitTeamNumberList(
        res.data.map((item: any) => {
          return {
            label: item.cockpitTeamName,
            value: item.cockpitTeamNumber,
          };
        }),
      );
    }
  };
  const getCockpitTypeList = async () => {
    const res = await commonApi.getCommonDropDown({
      keyList: ['COCKPIT_TYPE'],
    });
    if (res.code === HttpStatusCode.Success && res.data) {
      setCockpitTypeList(
        res.data.cockpitTypeList.map((item) => {
          return {
            label: item.name,
            value: item.code,
          };
        }),
      );
    }
  };
  const confirmClick = () => {
    formRef.validateFields().then(async (formValue) => {
      setLoadingMsg('保存中...');
      setLoading(true);
      const reqParams = {
        ...formValue,
        deviceNameList: bindVehicleInfo.current,
      };
      let response: any = null;
      if (cockpitNumber) {
        reqParams.cockpitNumber = cockpitNumber;
        response = await fetchApi.editCockpitInfo(reqParams);
      } else {
        response = await fetchApi.addCockpit(reqParams);
      }

      setLoading(false);
      if (response.code === HttpStatusCode.Success) {
        if (cockpitNumber) {
          message.success('编辑座席成功！');
        } else {
          message.success('新建座席成功！');
        }

        window.history.back();
      } else {
        message.error(response.message);
      }
    }).catch(e=>{
      
    });
  };

  const formateAllVehicleList = (list: any[]): any[] => {
    return list.map((item: any) => {
      const newType =
        item.type === 'vehicle' ? 'vehicleNameList' : `${item.type}IdList`;
      if (item.children) {
        return {
          key: `${item.type}_${item.code}`,
          [item.type]: item.name,
          code: item.code,
          type: newType,
          children: formateAllVehicleList(item.children),
        };
      } else {
        return {
          key: `${item.type}_${item.code}`,
          [item.type]: item.name,
          code: item.code,
          type: newType,
          children: null,
        };
      }
    });
  };

  const fetchAllVehicle = async (values: any) => {
    if (!cockpitTeamNumber) {
      return [];
    }
    const stationInfo = values.stationInfo || [];
    const res = await fetchApi.fetchStandardVehicle({
      cockpitTeamNumber: cockpitTeamNumber,
      provinceIdList: stationInfo[0] ? [stationInfo[0]] : [],
      cityIdList: stationInfo[1] ? [stationInfo[1]] : [],
      stationBaseIdList: stationInfo[2] ? [stationInfo[2]] : [],
      vehicleNameList: values.vehicle?.map((item: any) => item.value),
    });
    if (res.code === HttpStatusCode.Success) {
      return res.data ? formateAllVehicleList(res.data) : [];
    }
    return [];
  };

  const onFieldItemFocus = (fieldName: string, allValues: any) => {
    if (!cockpitTeamNumber) {
      message.error('请先选择归属远驾团队');
      return;
    }
    if (fieldName === 'vehicle') {
      const opt = {
        cockpitTeamNumber,
        level: fieldName,
        vehicleNameList: allValues[fieldName] || [],
      };
      fetchApi.getDepartmentOptions(opt).then((res) => {
        const costormField = searchFormConf?.fields?.find(
          (field: FieldItem) => field.fieldName === fieldName,
        );
        if (costormField) {
          costormField.options =
            res.data?.map((item: any) => {
              return { label: item.name, value: item.code };
            }) ?? [];
        }
        setSearchFormConf({ ...searchFormConf });
      });
    }
  };

  return (
    <CommonEdit
      title={cockpitNumber ? '编辑座席' : '新建座席'}
      breadCrumbConfig={breadCrumbItems}
      onSubmitClick={confirmClick}
      onCancleClick={() => {
        window.history.back();
      }}
    >
      <EditModuleTitle title="基础信息" />
      <Form
        form={formRef}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 18 }}
        autoComplete="off"
      >
        {cockpitNumber ? (
          <Form.Item name="cockpitNumber" label="座席编号	">
            {cockpitNumber}
          </Form.Item>
        ) : (
          <Form.Item
            name="cockpitNumber"
            label="座席编号"
            rules={[
              {
                required: true,
                message: '请输入座席编号',
              },
              {
                pattern: /^[a-zA-Z0-9]+$/,
                message: '仅能输入字母、数字',
              },
            ]}
          >
            <Input
              maxLength={10}
              placeholder="请输入座席编号，仅能输入字母、数字，最多输入10位"
            />
          </Form.Item>
        )}
        <Form.Item
          name="cockpitName"
          label="座席名称"
          rules={[{ required: true, message: '请输入座席名称' }]}
        >
          <Input maxLength={30} placeholder="请输入座席名称，最多输入30位" />
        </Form.Item>
        <Form.Item
          name="cockpitType"
          label="座席类型"
          rules={[{ required: true, message: '请选择座席类型' }]}
        >
          <Radio.Group
            onChange={(e) => {
              setCockpitType(e.target.value);
            }}
            value={cockpitType}
          >
            {cockpitTypeList.map((item) => {
              return (
                <Radio value={item.value} key={item.value}>
                  {item.label}
                </Radio>
              );
            })}
          </Radio.Group>
        </Form.Item>
        <Form.Item
          name="cockpitTeamNumber"
          label="归属远驾团队"
          rules={[{ required: true, message: '请选择归属远驾团队' }]}
        >
          <Select
            options={cockpitTeamNumberList}
            placeholder="请选择"
            onSelect={(val: any) => {
              setCockpitTeamNumber(val);
            }}
          />
        </Form.Item>
      </Form>
      <Loading fetching={loading} tips={loadingMsg} />
    </CommonEdit>
  );
};
export default React.memo(DriveCabinEdit);
