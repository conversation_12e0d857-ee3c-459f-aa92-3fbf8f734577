import { CommonApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { FormConfig } from '@jd/x-coreui';
const fetchApi = new CommonApi();
export const formConfig: FormConfig = {
  fields: [
    {
      fieldName: 'cityId',
      label: '修改城市',
      placeholder: '请选择省市信息',
      type: 'cascader',
      multiple: false,
      changeOnSelect: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      mapRelation: { label: 'name', value: 'id', children: 'children' },
      validatorRules: [{ required: true, message: '请选择省份城市' }],
    },
    {
      fieldName: 'productType',
      label: '站点类型',
      placeholder: '请选择站点类型',
      type: 'select',
      labelInValue: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      validatorRules: [{ required: true, message: '请选择站点类型' }],
    },
    {
      fieldName: 'useCase',
      label: '站点用途',
      placeholder: '请选择站点用途',
      type: 'select',
      labelInValue: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      validatorRules: [{ required: true, message: '请选择站点用途' }],
    },
    {
      fieldName: 'personName',
      label: '站点负责人调整为',
      placeholder: '请选择ERP',
      type: 'select',
      showSearch: true,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      validatorRules: [{ required: true, message: '请选择站点负责人' }],
    },
    {
      fieldName: 'contact',
      label: '站点负责人手机号调整为',
      placeholder: '系统带入手机号，允许修改',
      type: 'input',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      validatorRules: [{ required: true, message: '请输入站点负责人手机号' }],
    },
  ],
  linkRules: {
    fetchData: [
      {
        linkFieldName: 'cityId',
        rule: 'fetchData',
        fetchFunc: async (val) => {
          const res = await fetchApi.getCityDepartment({});
          if (res.code === HttpStatusCode.Success) {
            return res.data;
          }
          return [];
        },
      },
    ],
  },
};
