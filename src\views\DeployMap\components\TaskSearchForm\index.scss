.task-search-form {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  .drawer-expand-icon,
  .drawer-collapse-icon {
    width: 15px;
    height: 66px;
    background-color: rgb(60, 110, 240);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 30px 0 0 30px;
    position: absolute;
    left: -15px;
    box-shadow: 0 2px 8px 0 rgba(35, 37, 43, 0.1);
    transition: all 0.3s ease-in-out;
    cursor: pointer;
    &:hover {
      transform: scale(1.1);
    }
  }
  .drawer-content {
    height: calc(100vh - 80px);
    background: #fff;
    .task-search-form-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid #e4e5e9;
    }
  }

  .task-search-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 150px);

    .task-search-form-content {
      flex-shrink: 0;
    }

    .task-list-container {
      height: calc(100vh - 460px);
      overflow-y: auto;
      padding: 8px 16px;
      background: #f1f1f1;
      // 设置滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #e8e8e8;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background-color: transparent;
      }
    }

    .pagination-container {
      flex-shrink: 0;
      padding: 16px;
      border-top: 1px solid #f0f0f0;
      background: #fff;
      display: flex;
      justify-content: flex-end;
    }
  }

  .no-task-list {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #999;
  }
}
