import { YESNO } from '@/utils/enum';
import { request } from '../core';
import { PageType } from '@/utils/enum';

export class DeviceInfoApi {
  fetchDomain = `${location.protocol}//${process.env.JDX_APP_CLOUD_FETCH_DOMAIN}`;
  // 分页查询
  fetchTableList({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/device/device_get_page_list',
      urlParams: {
        pageNum,
        pageSize,
      },
      body: searchForm,
    };
    return request(options);
  }
  //  新增编辑
  submitInfo({
    type,
    requestBody,
  }: {
    type: PageType.EDIT | PageType.ADD;
    requestBody: Object;
  }) {
    const options: RequestOptions = {
      method: type === PageType.EDIT ? 'PUT' : 'POST',
      path:
        type === PageType.EDIT
          ? '/k2/management/device/device_edit'
          : '/k2/management/device/device_add',
      body: requestBody,
    };
    return request(options);
  }
  // 获取详情
  fetchDetail(id: number | string) {
    const options: RequestOptions = {
      method: 'GET',
      path: '/k2/management/device/device_get_detail',
      urlParams: { id },
    };
    return request(options);
  }
  // 获取api key
  getApiKey(deviceId: number) {
    const options: RequestOptions = {
      method: 'GET',
      path: '/k2/management/device/device_get_api_key',
      urlParams: {
        deviceId,
      },
    };
    return request(options);
  }

  // 搜索站点列表
  getStation(params: object) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/station_base/get_station_address_list',
      body: { ...params },
    };
    return request(options);
  }

  //   获取设备硬件模板信息
  getDeviceTemplate({
    type,
    productType,
  }: {
    type: 'device_type' | 'device';
    productType: 'vehicle' | 'robot';
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/device_template/get_device_template_info',
      body: {
        type,
        productType,
      },
    };
    return request(options);
  }

  // 车型名称后的详情按钮展示信息
  getDeviceTypeInfo(deviceTypeBaseId: number) {
    const options: RequestOptions = {
      method: 'GET',
      path: '/k2/management/device_type/get_device_type_info',
      urlParams: {
        deviceTypeBaseId,
      },
    };
    return request(options);
  }

  // 批量上传车辆信息
  uploadDeviceInfo(deviceTypeBaseId: number) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/upload/device_batch_add',
      urlParams: {
        deviceTypeBaseId,
      },
    };
    return request(options);
  }

  // 车型名称后的详情按钮展示信息
  getUploadedInfo({
    pageNum,
    pageSize,
    searchForm,
  }: {
    pageNum: number;
    pageSize: number;
    searchForm: Object;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/upload/device_batch_add_log',
      urlParams: {
        pageNum,
        pageSize,
      },
      body: searchForm,
    };
    return request(options);
  }

  unbindAndroidDeviceService(deviceName: string) {
    const options: RequestOptions = {
      method: 'PUT',
      path: '/k2/management/device/device_unbind_android_device',
      urlParams: {
        deviceName,
      },
    };
    return request(options);
  }

  queryDeviceTypeList(params: {
    supplier?: string;
    productType?: string;
    enable?: 1 | 0;
  }) {
    const headers = {
      'LOP-DN': process.env.JDX_APP_REQUEST_HEADER!,
    };
    const options: RequestOptions = {
      method: 'POST',
      absoluteURL: `${this.fetchDomain}/k2/management/device_type/query_device_type_list`,
      headers,
      body: params,
    };
    return request(options);
  }
}
