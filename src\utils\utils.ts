import { AnyFunc } from '@/global';
import { message } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { transformLocationGcj02towgs84 } from './tencentMap';
// 格式日期到秒
export const formatDateToSecond = (time: any) => {
  let startTime: string | null = null;
  let endTime: string | null = null;
  if (time && time.length > 0) {
    const startMoment: Dayjs = time[0];
    if (startMoment) {
      startTime = startMoment.format('YYYY-MM-DD HH:mm:ss');
    }
    const endMoment: Dayjs = time[1];
    if (endMoment) {
      endTime = endMoment.format('YYYY-MM-DD HH:mm:ss');
    }
  }
  return {
    startTime: startTime,
    endTime: endTime,
  };
};
// 格式日期到日
export const formatDateToDate = (time: any) => {
  let startTime: string | null = null;
  let endTime: string | null = null;
  if (time && time.length > 0) {
    const startMoment: Dayjs = time[0];
    if (startMoment) {
      startTime = startMoment.format('YYYY-MM-DD');
    }
    const endMoment: Dayjs = time[1];
    if (endMoment) {
      endTime = endMoment.format('YYYY-MM-DD');
    }
  }
  return {
    startTime: startTime,
    endTime: endTime,
  };
};
// 初始日期到秒
export const createInitialDateToSecond = (
  searchStartTime: any,
  searchEndTime: any,
) => {
  if (searchStartTime && searchEndTime) {
    const startStime = dayjs(searchStartTime, 'YYYY-MM-DD HH:mm:ss');
    const endStime = dayjs(searchEndTime, 'YYYY-MM-DD HH:mm:ss');
    return [startStime, endStime];
  }
  return [];
};

// 初始时间到秒
export const createInitialTimeToSecond = (startTime: any, endTime: any) => {
  if (startTime && endTime) {
    const startStime = dayjs(startTime, 'HH:mm:ss');
    const endStime = dayjs(endTime, 'HH:mm:ss');
    return [startStime, endStime];
  }
  return [];
};

// 格式化时间到秒
export const formateTimeToSecond = (time: any[]) => {
  if (time && time.length > 0 && time[0] && time[1]) {
    return {
      startTime: time[0].format('HH:mm:ss'),
      endTime: time[1].format('HH:mm:ss'),
    };
  } else {
    return {
      startTime: null,
      endTime: null,
    };
  }
};

// 格式日期到秒
export const formatDateStringToSecond = (time: any) => {
  let date: any = new Date(time);
  let year: any = date.getFullYear();
  let month: any = date.getMonth() + 1;
  let day: any = date.getDate();
  let hours: any = date.getHours();
  let minutes: any = date.getMinutes();
  let seconds: any = date.getSeconds();

  month = month.toString().padStart(2, '0');
  day = day.toString().padStart(2, '0');
  hours = hours.toString().padStart(2, '0');
  minutes = minutes.toString().padStart(2, '0');
  seconds = seconds.toString().padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

/**
 * 获取权限按钮
 * @param sourceCode
 * @returns
 */
export const getPermissionBtn = (sourceCode: string) => {
  if (!sourceCode) {
    return false;
  }
  const win = window as any;
  const { buttonMap } = win.store.getState().commonReducer;
  if (buttonMap && buttonMap.has(sourceCode)) {
    return true;
  }
  return false;
};

export const checkPhone = (mobile: string) => {
  const phone = /^((1[0-9]{1})+\d{9})$/;
  return phone.test(mobile);
};

export const isNullObject = (obj: any) => {
  if (!obj) {
    return true;
  }
  return Object.keys(obj).length === 0;
};

export const isEmpty = (param: any) => {
  if (Array.isArray(param) || typeof param === 'string') {
    return param.length === 0;
  }
  return true;
};

const decodeUriQuery = (value: any) => {
  let decoded: string | null = null;
  try {
    decoded = decodeURIComponent(value);
  } catch (e) {
    decoded = value;
  }
  return decoded;
};

const encodeUriQuery = (value: any) => {
  let encoded: string | null = null;
  try {
    encoded = encodeURIComponent(value);
  } catch (e) {
    encoded = value;
  }
  return encoded;
};

export const makeUrlQuery = (searchQuery: any) => {
  const paramsInQuery = Object.entries(searchQuery || {})
    .map((item: any) => {
      const [k, v] = item;
      return `${k}=${encodeUriQuery(v)}`;
    })
    .join('&');
  return paramsInQuery;
};

export const formatLocation = (locationSearch: string) => {
  let search = '';
  if (locationSearch.indexOf('?') != -1) {
    search = locationSearch.split('?')[1];
  } else {
    search = locationSearch;
  }
  const params = search.split('&');
  const objParams: any = {};
  params.forEach((item) => {
    const paramItem = item.split('=');
    objParams[paramItem[0]] = decodeUriQuery(paramItem[1]);
  });
  return objParams;
};

export const debounce = (fn: Function, time: number) => {
  let timer: any = null;
  return function (args: any) {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      fn.apply(null, args);
    }, time);
  };
};

export const formatOptions = (list) => {
  return Array.isArray(list) && list.length > 0
    ? list.map((item: any) => ({
        label: item.name,
        value: item.code,
      }))
    : [];
};

/**
 * 数组去重
 * @param arr 接收的原数组
 * @param key 如果是对象数组[{id: 1}, {id: 2}, {id: 3}]，则需要以什么key作为重复的标准，普通数组[1,2,3,2]不需要
 * @returns
 */

export const arrUnique = (arr: [], key?: string) => {
  let returnArr = [];
  if (key) {
    // 对象数组去重
    const obj = {};
    returnArr = arr.reduce((cur, next) => {
      obj[next[key]] ? '' : ((obj[next[key]] as any) = true && cur.push(next));
      return cur;
    }, []);
    return returnArr;
  }
  // 普通数组去重
  returnArr = arr.reduce((cur, next) => {
    !cur.includes(next) && cur.push(next);
    return cur;
  }, []);
  return returnArr;
};

// 货箱格口配置规则校验
const validationCaseData = (
  boxColumnNum: number,
  colHeigh: any,
  minHeight: any,
  gridsArr: any[],
  key: string,
) => {
  let tempHeight = 0;
  let colIdx = 0;
  const valid = {
    isValid: true,
    msg: '',
  };
  gridsArr.map((item: any, idx: number) => {
    const grid = Object.assign({}, item);
    if (colIdx == boxColumnNum) {
      valid.isValid = false;
      valid.msg = `生成失败，格口配置有误，无法生成${key}的列数【${boxColumnNum}】`;
      return;
    } else if (parseInt(grid.height) > colHeigh) {
      valid.isValid = false;
      valid.msg = `生成失败，【${key}】格口号【${grid.gridNo}】的高度大于货箱高度【${colHeigh}】mm`;
      return;
    }
    tempHeight += parseInt(grid.height);
    // 判断是否是最后一个格子:a.根据下标判断 b.累计格子高度+下一个格子高度大于最小列高的1/3,下一个格子是下一列的
    if (gridsArr.length === idx + 1) {
      grid.isLast = true;
    } else {
      const lastGrid = gridsArr[idx + 1];
      const nextSumHeight = tempHeight + parseInt(lastGrid.height);
      if (nextSumHeight - colHeigh > minHeight / 3) {
        grid.isLast = true;
      }
    }

    if (grid.isLast && gridsArr.length > idx + 1) {
      colIdx++;
      tempHeight = 0;
    }
  });
  return valid;
};
export const validationGrid = (
  editGridList: any[],
  gridPositionValue: any,
  gridCofigArr: any,
) => {
  let valid = {
    isValid: true,
    msg: '',
  };
  // 每一侧的列数
  const leftColumnNum = gridPositionValue.leftBoxColumnNum || 0;
  const rightColumnNum = gridPositionValue.rightBoxColumnNum || 0;
  // 每一侧的格口总高度
  let leftColSumHeight = 0;
  let rightColSumHeight = 0;

  let isValidatSize = true;
  let leftGridNum = 0;
  let rightGridNum = 0;
  const leftGrids: any[] = [];
  const rightGrids: any[] = [];
  let minLeftHeight = 0;
  let minRightHeight = 0;
  editGridList.map((grid: any) => {
    if (grid.side === '左侧') {
      leftGridNum++;
      leftColSumHeight += parseInt(grid.height);
      leftGrids.push(grid);
      if (parseInt(grid.height) < minLeftHeight || minLeftHeight == 0) {
        minLeftHeight = parseInt(grid.height);
      }
    }
    if (grid.side === '右侧') {
      rightGridNum++;
      rightColSumHeight += parseInt(grid.height);
      rightGrids.push(grid);
      if (parseInt(grid.height) < minRightHeight || minRightHeight == 0) {
        minRightHeight = parseInt(grid.height);
      }
    }
    if (
      gridCofigArr.findIndex((size: any) => size.sizeStr === grid.sizeStr) ===
        -1 &&
      isValidatSize
    ) {
      // 判断是否出现当前数据规格跟配置的不一致的情况
      isValidatSize = false;
    }
  });

  if (leftGridNum < leftColumnNum) {
    valid.isValid = false;
    valid.msg = '生成失败，左侧格口数不足！';
  } else if (rightGridNum < rightColumnNum) {
    valid.isValid = false;
    valid.msg = '生成失败，右侧格口数不足！';
  } else if (leftGridNum == 0 && rightGridNum == 0) {
    valid.isValid = false;
    valid.msg = '操作失败，请维护详细格口信息！';
  } else if (leftGridNum && leftColumnNum == 0) {
    valid.isValid = false;
    valid.msg = '生成失败，左侧按照实际显示列数为0，不应该存在格口数据！';
  } else if (rightGridNum && rightColumnNum == 0) {
    valid.isValid = false;
    valid.msg = '生成失败，右侧按照实际显示列数为0，不应该存在格口数据！';
  } else if (!isValidatSize) {
    valid.isValid = false;
    valid.msg = '生成失败，格口规格参数不一致，请检查！';
  } else {
    // 每一侧的列高度
    let leftColHeigh: any = 0;
    let rightColHeigh: any = 0;
    if (leftColumnNum) {
      leftColHeigh = (leftColSumHeight / leftColumnNum).toFixed(2);
    }
    if (rightGridNum) {
      rightColHeigh = (rightColSumHeight / rightColumnNum).toFixed(2);
    }
    const leftValidData = validationCaseData(
      leftColumnNum,
      leftColHeigh,
      minLeftHeight,
      leftGrids,
      '左侧',
    );
    const rightValidData = validationCaseData(
      rightColumnNum,
      rightColHeigh,
      minRightHeight,
      rightGrids,
      '右侧',
    );
    if (!leftValidData.isValid) {
      valid = leftValidData;
    } else if (!rightValidData.isValid) {
      valid = rightValidData;
    }
  }
  return valid;
};

export async function parseResponseBody(val: any, contentType?: string | null) {
  const ct = contentType ? String(contentType) : '';
  if (ct.indexOf('application/x-www-form-urlencoded') >= 0) {
    const resp = val as Response;
    const txt = await resp.text();
    return txt;
  } else if (ct.indexOf('application/json') >= 0) {
    const resp = val as Response;
    const json = await resp.json();
    return json;
  } else if (ct.indexOf('text/plain') >= 0) {
    const resp = val as Response;
    const txt = await resp.text();
    return txt;
  } else {
    return val ? String(val) : '';
  }
}

export const showMsg = ({
  msg,
  type,
  duration,
  onClose,
}: {
  msg: string;
  type: 'success' | 'error' | 'info' | 'warning' | 'loading';
  duration?: number;
  onClose?: AnyFunc;
}) => {
  switch (type) {
    case 'success':
      message.success(msg, duration, onClose);
      break;
    case 'error':
      message.error(msg, duration, onClose);
      break;
    case 'info':
      message.info(msg, duration, onClose);
      break;
    case 'warning':
      message.success(msg, duration, onClose);
      break;
    case 'loading':
      message.success(msg, duration, onClose);
      break;
  }
};

export const formatTabList = (resourceList: any[]) => {
  if (!resourceList) return [];
  return resourceList.filter((item) => {
    return item.type === 'tab';
  });
};

export const isNumber = (value: any) => {
  return typeof value === 'number' && !isNaN(value);
};

export function formatISODateToYYYYMMDD(isoDateString) {
  const date = new Date(isoDateString);

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，所以加 1，并确保两位数
  const day = String(date.getDate()).padStart(2, '0'); // 确保两位数

  return `${year}-${month}-${day}`;
}

// 手动上报点击事件
export const customReport = (clstag: any) => {
  try {
    (window as any).__qd__ &&
      (window as any).__qd__.click({
        cls: clstag,
      });
  } catch (e) {
    console.log(e);
  }
};

export const decompressLocation = (coords: any[]) => {
  const result: any = [];
  for (let i = 2; i < coords.length; i++) {
    coords[i] = coords[i - 2] + coords[i] / 1000000;
  }
  for (let i = 0; i < coords.length; i += 2) {
    // result.push(
    //   transformLocationGcj02towgs84({
    //     lat: coords[i],
    //     lon: coords[i + 1],
    //   }),
    // );
    result.push({
      lat: coords[i],
      lon: coords[i + 1],
    });
  }
  return result;
};
