/* eslint-disable no-unused-vars */
import { Form, FormInstance, Select, Table, Input, Descriptions } from 'antd';
import React, { useState, useEffect } from 'react';
import { TriangularContainer } from '@/components';
import { DeviceInfoApi } from '@/fetch/business';
import { vehicleTypeTableColumn, sensorTableColumn } from '../utils/columns';
import { HttpStatusCode } from '@/fetch/core/constant';
import { SUPPLIER } from '@/utils/constant';

const VehicleModel = ({
  disabled,
  form,
  options,
  productType,
  initialValue,
  supplier,
}: {
  disabled?: boolean;
  form: FormInstance;
  options: any[];
  productType: string;
  initialValue: any;
  supplier: any;
}) => {
  const fetchApi = new DeviceInfoApi();
  const [dataSource, setDataSource] = useState<any>(initialValue);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    initialValue && setDataSource(initialValue);
  }, [initialValue]);

  const formateColumns = (columns: any[], listName: string) => {
    return columns.map((col) => {
      switch (col.dataIndex) {
        case 'hardwareNumber':
          return {
            ...col,
            render: (text: any, record: any, index: number) => {
              return (
                <>
                  <Input
                    disabled={disabled}
                    allowClear
                    value={record.hardwareNumber}
                    maxLength={500}
                    onChange={(e) => {
                      const val = e.target.value.replace(
                        /[\u4e00-\u9fa5|\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5]*/g,
                        '',
                      );
                      const newList = dataSource[listName]?.map((item) => {
                        if (item.hardwareModelId === record.hardwareModelId) {
                          return { ...item, hardwareNumber: val };
                        } else {
                          return item;
                        }
                      });
                      setDataSource({
                        ...dataSource,
                        [listName]: newList,
                      });
                    }}
                  />
                  {/* {!record.hardwareNumber && (
                    <div style={{ color: 'red' }}>请输入序列号</div>
                  )} */}
                </>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };
  useEffect(() => {
    if (supplier) {
      if (supplier === SUPPLIER.NEOLIX || supplier === SUPPLIER.RINO) {
        getHardwareInfo(form.getFieldsValue().deviceTypeBaseId);
      } else {
        setDataSource({});
      }
    }
  }, [supplier, options]);
  const getHardwareInfo = async (deviceTypeBaseId: number) => {
    const res = await fetchApi.getDeviceTypeInfo(deviceTypeBaseId);
    if (res.code === HttpStatusCode.Success) {
      setDataSource(res.data);
    }
  };

  useEffect(() => {
    form.setFieldsValue({
      deviceHardwareModelList: dataSource,
      deviceTypeBaseId: dataSource?.deviceTypeBaseId,
    });
  }, [JSON.stringify(dataSource)]);

  return (
    <>
      <Form.Item
        name="deviceHardwareModelList"
        style={{ height: '0px' }}
      ></Form.Item>
      <Form.Item
        label="车型"
        name="deviceTypeBaseId"
        rules={[{ required: true, message: '请输入车型名称' }]}
      >
        <>
          <Select
            value={dataSource?.deviceTypeBaseId}
            options={options}
            labelInValue
            disabled={disabled}
            placeholder="请选择车型名称"
            showSearch
            filterOption={(input, option) => {
              const label: any = option?.label || '';
              return label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            }}
            onChange={(value: any) => {
              getHardwareInfo(value.value);
            }}
          />
          {dataSource?.deviceTypeBaseId && (
            <div
              key={dataSource?.deviceTypeBaseId}
              style={{
                display: dataSource?.deviceTypeBaseId !== null ? '' : 'none',
              }}
            >
              <TriangularContainer
                customStyle={{ backgroundColor: '#f1f1f1', padding: '10px' }}
              >
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}
                >
                  <div
                    style={{
                      height: 20,
                      width: 5,
                      backgroundColor: 'red',
                      marginRight: 6,
                    }}
                  />
                  {`车型id： ${dataSource?.deviceTypeBaseId}`}
                </div>
                <div style={{ marginTop: 10 }}>
                  <Descriptions
                    bordered
                    column={2}
                    items={[
                      {
                        key: 'sensorSchemeId',
                        label: '传感器方案id',
                        children: dataSource?.sensorSchemeId,
                      },
                      {
                        key: 'sensorSchemeName',
                        label: '传感器方案名称',
                        children: dataSource?.sensorSchemeName,
                      },
                      {
                        key: 'manufactoryId',
                        label: '生产厂商id',
                        children: dataSource?.manufactoryId,
                      },
                      {
                        key: 'manufactoryName',
                        label: '生产厂商名称',
                        children: dataSource?.manufactoryName,
                      },
                    ]}
                  />
                </div>

                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}
                >
                  <div
                    style={{
                      height: 20,
                      width: 5,
                      backgroundColor: 'red',
                      marginRight: 6,
                    }}
                  />
                  车型硬件信息
                </div>
                <div style={{ marginTop: 10 }}>
                  <Table
                    scroll={{
                      x: '600px',
                    }}
                    rowKey={(record) => record.hardwareModelId}
                    size="small"
                    loading={loading}
                    bordered
                    columns={formateColumns(
                      vehicleTypeTableColumn,
                      'deviceTypeHardwareInfoList',
                    )}
                    dataSource={dataSource?.deviceTypeHardwareInfoList}
                    pagination={false}
                  />
                </div>

                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginTop: 20,
                  }}
                >
                  <div
                    style={{
                      height: 20,
                      width: 5,
                      backgroundColor: 'red',
                      marginRight: 6,
                    }}
                  />
                  传感器硬件信息
                </div>
                <div style={{ marginTop: 10 }}>
                  <Table
                    rowKey={(record) => record.hardwareModelId}
                    size="small"
                    loading={loading}
                    bordered
                    columns={formateColumns(
                      sensorTableColumn,
                      'sensorSchemeDetailList',
                    )}
                    dataSource={dataSource?.sensorSchemeDetailList}
                    pagination={false}
                  />
                </div>
              </TriangularContainer>
            </div>
          )}
        </>
      </Form.Item>
    </>
  );
};

export default React.memo(VehicleModel);
