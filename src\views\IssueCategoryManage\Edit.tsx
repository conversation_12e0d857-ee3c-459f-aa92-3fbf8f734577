import React, { useEffect, useRef, useState } from 'react';
import { BreadCrumb } from '@/components';
import { CommonForm, FormConfig } from '@jd/x-coreui';
import { getCategoryInfo } from './utils/constant';
import './edit.scss';
import { Button, message } from 'antd';
import { IssueCateGoryManageApi } from '@/fetch/business';
import { formatLocation } from '@/utils/utils';
import { HttpStatusCode } from '@/fetch/core/constant';
import { PageType } from '@/utils/EditTitle';

const fetchApi = new IssueCateGoryManageApi();
const breadCrumbItems = [
  {
    title: '问题分类管理',
    route: '',
  },
  {
    title: '编辑问题分类',
    route: '',
  },
];
export const Title = {
  [PageType.ADD]: '新建问题分类',
  [PageType.EDIT]: '编辑问题分类',
};
const IssueCategoryEdit = () => {
  const { id, type } = formatLocation(window.location.search);
  const cateGoryInfoRef = useRef<any>(null);
  const [detail, setDetail] = useState<any>(null);
  const [config, setConfig] = useState<FormConfig>(getCategoryInfo(type));
  const submit = async () => {
    try {
      const value = await cateGoryInfoRef.current?.validateFields();
      if (type == PageType.EDIT) {
        fetchApi
          .editCateGoryInfo({
            bugCategoryId: detail.bugCategoryId,
            bugCategoryName: value.bugCategoryName,
          })
          .then((res) => {
            if (res.code === HttpStatusCode.Success) {
              message.success('编辑成功');
              history.go(-1);
            } else {
              message.error(res.message);
            }
          });
      } else {
        fetchApi
          .addCateGoryInfo({
            moduleCode: value.moduleCode,
            bugCategoryName: value.bugCategoryName,
          })
          .then((res) => {
            if (res.code === HttpStatusCode.Success) {
              message.success('新建成功');
              history.go(-1);
            } else {
              message.error(res.message);
            }
          });
      }
    } catch (e) {
      message.error('请填写必填项');
      console.log(e);
    }
  };

  const getMrDetailByMrId = (bugCategoryId: string) => {
    if (type === PageType.ADD) {
      return;
    }
    fetchApi.getCateGoryInfoById({ bugCategoryId }).then((res) => {
      if (res?.code === HttpStatusCode.Success) {
        setDetail(res?.data);
      }
    });
  };

  useEffect(() => {
    fetchApi.getModuleList().then((res) => {
      if (res?.code === HttpStatusCode.Success) {
        const field = config.fields.find(
          (item) => item.fieldName === 'moduleCode',
        );
        field!.options = res?.data?.map((item) => ({
          label: item.moduleName,
          value: item.moduleCode,
        }));
        setConfig({ ...config });
      }
    });
  }, []);

  useEffect(() => {
    getMrDetailByMrId(id);
  }, []);

  return (
    <div className="jira-edit-container" style={{ padding: '10px' }}>
      <div className="bread-crub">
        <BreadCrumb items={breadCrumbItems} />
      </div>
      <div className="content">
        <h1 className="title">{Title[type]}</h1>

        <CommonForm
          formConfig={config}
          defaultValue={detail}
          getFormInstance={(ref: any) => {
            cateGoryInfoRef.current = ref;
          }}
        />

        <div className="btn-group">
          <Button type="primary" onClick={submit}>
            确定
          </Button>
          <Button
            type="default"
            style={{ marginLeft: '20px' }}
            onClick={() => {
              history.go(-1);
            }}
          >
            取消
          </Button>
        </div>
      </div>
    </div>
  );
};

export default IssueCategoryEdit;
