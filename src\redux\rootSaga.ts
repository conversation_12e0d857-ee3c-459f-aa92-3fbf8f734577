import { all, put, fork, call, take, actionChannel } from 'redux-saga/effects';
import { request } from '@/fetch/core';
import ActionType from './actionType';
import { channel } from 'redux-saga';
import { menuData } from '@/layouts/mockmenu';
import {
  makeFetchFailedActionType,
  makeFetchSuccessActionType,
} from '@/event/fetchActionTypeProvider';
import { HttpStatusCode } from '@/fetch/core/constant';
import { MenuNameMap } from '@/utils/menuNameMap';

function getMenuItem(item: any, children?: any[]): any {
  const { path, resourceCode, type, label } = item;
  return {
    key: path,
    label,
    path: path.indexOf('/') > -1 ? path : null,
    type,
    children,
  };
}

const formatMenu = (data) => {
  const menuObj = {};
  const menuList: any = [];
  const menuType = ['menu', 'iframe'];
  if (!data) {
    return menuList;
  }
  for (let item of data) {
    const { orderBy, path, resourceCode, type } = item;
    if (resourceCode === 'common_login' || !menuType.includes(type)) {
      continue;
    }
    const level = resourceCode.split(':');
    const parentCode = level.slice(0, level.length - 1).join(':');
    // 用真实返回的资源数据
    menuObj[resourceCode] = menuObj[resourceCode]
      ? getMenuItem(
          { ...item, label: MenuNameMap.get(resourceCode) },
          menuObj[resourceCode].children,
        )
      : getMenuItem({ ...item, label: MenuNameMap.get(resourceCode) });
    // 用mock的全量菜单数据
    // menuObj[resourceCode] = menuObj[resourceCode]
    //   ? getMenuItem(item, menuObj[resourceCode].children)
    //   : getMenuItem(item);

    if (!parentCode) {
      menuList[orderBy - 1] = menuObj[resourceCode];
    } else {
      if (!menuObj[parentCode]) {
        menuObj[parentCode] = { children: undefined };
      }
      menuObj[parentCode].children = menuObj[parentCode].children ?? [];
      menuObj[parentCode].children[orderBy - 1] = menuObj[resourceCode];
    }
  }
  return menuList;
};

const fetchSuccess = (actionType: string, data: any) => {
  return {
    type: makeFetchSuccessActionType(actionType),
    payload: data,
    loading: false,
  };
};
const fetchFail = (actionType: string, data: any) => {
  return {
    type: makeFetchFailedActionType(actionType),
    payload: data,
    loading: false,
  };
};
function* myRequest(chan: any) {
  while (true) {
    // 查看chan中的是否有信息存入，有则取出，然后运行下面的逻辑
    const payload = yield take(chan);
    const { nextActionFunc, actionType, ...fetchParams } = payload;
    // TODO:目前用的mock菜单资源
    let res;
    if (actionType === ActionType.USER_INFO) {
      const resp = yield call(request, fetchParams);
      res = {
        ...resp,
        data: {
          ...resp.data,
          menuData: formatMenu(menuData),
        },
      };
    } else {
      res = yield call(request, fetchParams);
    }
    nextActionFunc && nextActionFunc(res, fetchParams);
    if (res.code === HttpStatusCode.Success) {
      yield put(fetchSuccess(actionType, res));
    } else {
      yield put(fetchFail(actionType, res));
    }
  }
}

function* watchHTTPRequest(): any {
  // 创建channel去存储传入信息
  const chan = yield call(channel);
  // 接口请求串行,创建1个'工作线程'
  yield fork(myRequest, chan);
  while (true) {
    const data = yield take(ActionType.SEND_HTTP_REQUEST);
    // 当有对应的action被派发，把action.payload存入到chan里
    yield put(chan, data.payload);
  }
}

export default function* rootSaga() {
  yield all([watchHTTPRequest()]);
}
