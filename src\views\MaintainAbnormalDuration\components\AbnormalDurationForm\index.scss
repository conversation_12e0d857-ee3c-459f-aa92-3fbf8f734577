.abnormal-duration-form {
  .common-form-container {
    padding-bottom: 0px;
  }
  .common-table {
    padding: 0px;
    margin-top: 0px;
    .x-coreui-table-cell {
      .#{$ant-prefix}-input-number {
        width: 100%;
      }
    }
  }
  .table-container {
    padding-left: 12px;
    padding-right: 24px;
  }
  .table-label {
    height: 32px;
    line-height: 32px;
    text-align: end;
    font-size: 14px;
    &::after {
      content: ':';
      position: relative;
      margin-block: 0;
      margin-inline-start: 2px;
      margin-inline-end: 8px;
    }
    &::before {
      display: inline-block;
      margin-inline-end: 4px;
      color: #ff4d4f;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: '*';
    }
  }
  .effectTime-input {
    &.red {
      border: 1px solid #ff4d4f;
      border-radius: 6px;
    }
  }
}
