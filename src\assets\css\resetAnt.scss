$tableHeaderBg: #f5f5f5;
$tableHeaderFontColor: #333333;
$tableRowFontColor: #666666;

// 业务主色
$themeNormal: #3c6ef0;
$themeHover: #3c6ef0;

/*----- 表格 -----*/
.#{$ant-prefix}-table {
  .#{$ant-prefix}-table-thead>tr>th:not(:last-child):not(.#{$ant-prefix}-table-selection-column):not(.#{$ant-prefix}-table-row-expand-icon-cell):not([colspan]) {
    &::before {
      height: 0px;
    }
  }

  .#{$ant-prefix}-table-header {
    th {
      background-color: $tableHeaderBg;
      color: $tableHeaderFontColor;
    }
  }

  .#{$ant-prefix}-table-tbody,
  .#{$ant-prefix}-table-row {
    color: $tableRowFontColor;
  }

  .#{$ant-prefix}-table-body {
    &::-webkit-scrollbar {
      height: 5px;
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: #b5b5b5;
    }

    &::-webkit-scrollbar-track {
      -webkit-box-shadow: none;
      border-radius: 0;
      background: #fff;
    }
  }
}

/*----- button -----*/
.#{$ant-prefix}-btn-default {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(217, 217, 217, 1);
  border-radius: 4px;
  color: rgba(51, 51, 51, 1);

  &:hover {
    border-color: $themeHover;
    color: $themeHover;
  }
}

.#{$ant-prefix}-btn-primary {
  background: $themeNormal;
  border: 1px solid $themeNormal;
  border-radius: 4px;

  &:hover {
    border-color: $themeHover;
    background: $themeHover;
    color: white;
  }
}

/*----- form -----*/
.#{$ant-prefix}-form {

  .#{$ant-prefix}-form-item,
  .#{$ant-prefix}-form-item-has-success {
    margin-bottom: 16px;
  }

  .#{$ant-prefix}-form-item {
    min-width: 200px;

    .#{$ant-prefix}-col-8 {
      max-width: 40%;
      flex: 0 0 40%;
    }
  }
}

/*----- pagination -----*/
.#{$ant-prefix}-pagination {
  position: relative;

  .#{$ant-prefix}-pagination-prev,
  .#{$ant-prefix}-pagination-next,
  .#{$ant-prefix}-pagination-options {

    span,
    input {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(102, 102, 102, 1);

      &:hover {
        border-color: $themeHover;
      }
    }

    .#{$ant-prefix}-select,
    .#{$ant-prefix}-select-selector {
      &:hover {
        border-color: $themeHover;
      }
    }

    .#{$ant-prefix}-pagination-next,
    button {
      &:hover {
        border-color: $themeHover;
      }
    }

    .#{$ant-prefix}-pagination-prev,
    button {
      &:hover {
        border-color: $themeHover;
      }
    }

    button,
    .#{$ant-prefix}-select-selector,
    input {
      border-radius: 4px;
    }
  }

  .#{$ant-prefix}-pagination-item {
    border-radius: 4px;

    a {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(102, 102, 102, 1);
    }

    &:hover {
      border-color: $themeHover;
    }

    &.#{$ant-prefix}-pagination-item-active {
      background: rgba(60, 110, 240, 1);
      border-radius: 4px;
      border-color: rgba(60, 110, 240, 1);

      a {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(255, 255, 255, 1);
      }
    }
  }

  .#{$ant-prefix}-pagination-total-text {
    position: absolute;
    left: 0px;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(153, 153, 153, 1);
  }
}

/*----- input -----*/
.#{$ant-prefix}-input-affix-wrapper:not(.#{$ant-prefix}-input-affix-wrapper-disabled):hover {
  border-color: $themeHover;
}

.#{$ant-prefix}-input {
  &:hover {
    border-color: $themeHover;
  }
}

.#{$ant-prefix}-input-affix-wrapper,
.#{$ant-prefix}-input-affix-wrapper-status-success {
  height: 32px;
}

/*----- select -----*/
.#{$ant-prefix}-select:hover,
.#{$ant-prefix}-select-selector:hover {
  border-color: $themeHover;
}

.#{$ant-prefix}-select:not(.#{$ant-prefix}-select-disabled):hover {
  .#{$ant-prefix}-select-selector {
    border-color: $themeHover;
  }
}

/*----- breadCrumb -----*/
.#{$ant-prefix}-breadcrumb {
  li:last-child>.#{$ant-prefix}-breadcrumb-link>a {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 500;
    color: #333333;
  }

  a {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: #999999;
  }

  .#{$ant-prefix}-breadcrumb-separator {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: #999999;
  }
}

/*----- checkbox -----*/
.#{$ant-prefix}-checkbox {
  .#{$ant-prefix}-checkbox-input {

    &:hover,
    &:active,
    &:focus {
      border-color: $themeNormal;
    }
  }

  &-checked {
    .#{$ant-prefix}-checkbox-inner {
      background: $themeNormal;
      border-color: $themeNormal !important;

      &:hover,
      &:active,
      &:focus {
        border-color: $themeNormal;
      }
    }

    &::after {
      border-color: $themeNormal !important;
    }
  }
}

.ant-modal {
  .ant-modal-header {
    padding: 0 10px !important;
  }

  .ant-modal-close {
    top: 15px !important;
  }
}

.ant-modal-footer {
  button {
    margin-left: 8px;
  }
}

.main-app-checkbox-disabled .main-app-checkbox-inner {
  background: rgba(0, 0, 0, 0.04)!important;
  border-color: #d9d9d9!important;
}

.main-app-input-number{
  width: unset;
}