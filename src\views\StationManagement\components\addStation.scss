.add-station-container {
  .#{$ant-prefix}-col,
  .#{$ant-prefix}-col-14,
  .#{$ant-prefix}-form-item-label {
    overflow: visible;
  }

  .anticon-question-circle {
    color: #3c6ef0;
    svg {
      height: 22px;
    }
  }

  .coordinate-conatiner {
    position: relative;
    .choose-location {
      position: absolute;
      top: 0px;
      right: -87.6px;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(217, 217, 217, 1);
      border-radius: 0 4px 4px 0;
      height: 31.6px;
      width: 87.6px;
      background-color: white;
      color: #3c6ef0;
    }
  }
}

.content-container {
  .ant-input-number {
    width: auto;
  }
}
.create-plan-modal {
  .createDeployForm2 {
    .x-coreui-col.x-coreui-col-md-23.x-coreui-col-lg-23.x-coreui-col-xl-23.x-coreui-col-xxl-23 {
      background-color: #f5f5f6;
      padding-top: 10px;
      margin-bottom: 10px;
    }
  }
  .ant-divider {
    margin: 0px !important;
  }
}

.col-item{
  flex: 1;
}